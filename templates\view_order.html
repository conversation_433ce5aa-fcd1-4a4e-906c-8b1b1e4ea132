<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details - #{{ order._id }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 1000px;
            margin-top: 30px;
        }
        .card {
            border: none;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 15px;
        }
        .card-header {
            background-color: #4a4a4a;
            color: white;
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            padding: 20px;
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            border-top: none;
        }
        .totals-section {
            background-color: #f1f3f5;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .grand-total {
            font-size: 1.2em;
            font-weight: bold;
            color: #28a745;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
        .message-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
        }
        .message-card {
            border-left: 4px solid #007bff;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .message-card.customer {
            border-left-color: #28a745;
        }
        .message-meta {
            font-size: 0.8em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>Order #{{ order._id }}
                </h2>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <h3 class="h5"><i class="fas fa-user me-2"></i>Customer Information</h3>
                        <p><strong>Name:</strong> {{ order.customer_name }}</p>
                        <p><strong>Email:</strong> {{ order.customer_email }}</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="h5"><i class="fas fa-info-circle me-2"></i>Order Information</h3>
                        <p><strong>Date:</strong> {{ order.date.strftime('%B %d, %Y') }}</p>
                        <p><strong>Status:</strong> <span class="badge bg-primary">{{ order.orderStatus }}</span></p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="h5"><i class="fas fa-shipping-fast me-2"></i>Send to Address</h3>
                        <p>{{ order.seller_shipping_address|replace('\r\n', '<br>')|safe }}</p>
                    </div>
                </div>
                <h3 class="h5 mb-3"><i class="fas fa-list me-2"></i>Order Items</h3>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Item</th>
                                <th>Set/Expansion</th>
                                <th>Printing</th>
                                <th>Condition</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Type</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set total_cash = namespace(value=0) %}
                            {% set total_credit = namespace(value=0) %}
                            {% for item in order.line_items %}
                            {% set item_price = item.offeredPrice|float if item.offeredPrice is defined else 0 %}
                            {% set item_total = item.quantity * item_price %}
                            {% if item.type|lower == 'cash' %}
                                {% set total_cash.value = total_cash.value + item_total %}
                            {% elif item.type|lower == 'credit' %}
                                {% set total_credit.value = total_credit.value + item_total %}
                            {% endif %}
                            <tr>
                                <td>{{ item.name }}</td>
                                <td>{{ item.expansionName or 'N/A' }}</td>
                                <td>{{ item.printType or 'Regular' }}</td>
                                <td>{{ item.condition }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>${{ "%.2f"|format(item_price) }}</td>
                                <td><span class="badge bg-{{ 'success' if item.type|lower == 'cash' else 'info' }}">{{ item.type|capitalize }}</span></td>
                                <td>${{ "%.2f"|format(item_total) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="totals-section">
                    <h4 class="mb-3"><i class="fas fa-calculator me-2"></i>Order Totals</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Total Cash:</strong> <span class="text-success">${{ "%.2f"|format(total_cash.value) }}</span></p>
                            <p><strong>Total Credit:</strong> <span class="text-info">${{ "%.2f"|format(total_credit.value) }}</span></p>
                        </div>
                        <div class="col-md-6">
                            <p class="grand-total"><strong>Grand Total:</strong> ${{ "%.2f"|format(total_cash.value + total_credit.value) }}</p>
                        </div>
                    </div>
                </div>
                {% if not order.sent %}
                <div class="mt-4">
                    <h4><i class="fas fa-paper-plane me-2"></i>Mark Order as Sent</h4>
                    <form id="markSentForm">
                        <div class="mb-3">
                            <label for="trackingNumber" class="form-label">Tracking Number (optional)</label>
                            <input type="text" class="form-control" id="trackingNumber" name="trackingNumber" placeholder="Enter tracking number">
                        </div>
                        <button type="submit" class="btn btn-primary"><i class="fas fa-check me-2"></i>Mark as Sent</button>
                    </form>
                </div>
                {% else %}
                <div class="mt-4">
                    <h4><i class="fas fa-truck me-2"></i>Order Status</h4>
                    <p><strong>Sent:</strong> <span class="badge bg-success">Yes</span></p>
                    <p><strong>Date Sent:</strong> {{ order.date_sent.strftime('%B %d, %Y') }}</p>
                    {% if order.tracking_number %}
                    <p><strong>Tracking Number:</strong> {{ order.tracking_number }}</p>
                    {% endif %}
                </div>
                {% endif %}
                
                <!-- Messages Section -->
                <div class="mt-4">
                    <h4><i class="fas fa-comments me-2"></i>Conversation History</h4>
                    <div id="messageList" class="message-list mb-3">
                        {% if order.messages %}
                            {% for message in order.messages|reverse %}
                            <div class="message-card {{ 'customer' if message.type == 'customer' else 'seller' }}">
                                <p class="mb-1">{{ message.message }}</p>
                                <p class="message-meta mb-0">
                                    {{ message.timestamp.strftime('%B %d, %Y %H:%M:%S') }} - 
                                    {{ 'Customer' if message.type == 'customer' else current_user.business_name }}
                                </p>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No messages yet.</p>
                        {% endif %}
                    </div>
                    <form id="sendMessageForm">
                        <div class="mb-3">
                            <label for="messageText" class="form-label">New Message</label>
                            <textarea class="form-control" id="messageText" rows="3" required placeholder="Type your message here..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane me-2"></i>Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            $('#markSentForm').on('submit', function(e) {
                e.preventDefault();
                var trackingNumber = $('#trackingNumber').val();
                $.ajax({
                    url: '/mark_order_sent/{{ order._id }}',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ tracking_number: trackingNumber }),
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: response.message,
                                confirmButtonColor: '#28a745'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    location.reload();
                                }
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: response.message,
                                confirmButtonColor: '#dc3545'
                            });
                        }
                    },
                    error: function(xhr) {
                        var errorMessage = 'An error occurred';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.status === 404) {
                            errorMessage = 'Order not found';
                        } else if (xhr.status === 400) {
                            errorMessage = 'Invalid request or order already marked as sent';
                        } else if (xhr.statusText) {
                            errorMessage = xhr.statusText;
                        }
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: errorMessage,
                            confirmButtonColor: '#dc3545'
                        });
                    }
                });
            });

            $('#sendMessageForm').on('submit', function(e) {
                e.preventDefault();
                var messageText = $('#messageText').val();
                $.ajax({
                    url: '/send_message/{{ order._id }}',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ message: messageText }),
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: response.message,
                                confirmButtonColor: '#dc3545'
                            });
                        }
                    },
                    error: function(xhr) {
                        var errorMessage = 'An error occurred';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.statusText) {
                            errorMessage = xhr.statusText;
                        }
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: errorMessage,
                            confirmButtonColor: '#dc3545'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
