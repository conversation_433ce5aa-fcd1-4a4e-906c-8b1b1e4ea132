from config.config import Config
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_user, logout_user, login_required, current_user
from models.user_model import User, Staff, Address, Subscription
import re
from models.invoice_model import Invoice
from utils.email_utils import send_email
from mongoengine.errors import ValidationError
from config import Config
from routes.admin_routes import ensure_user_subscription
import secrets
import datetime
import requests
from functools import wraps
from werkzeug.security import check_password_hash
import logging
import os

auth_bp = Blueprint('auth', __name__)

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def notify_admin_failed_login(username, password, reason, ip_address):
    try:
        subject = f"Failed Login Attempt for {username}"
        body = f"""
        <html>
        <body>
            <h2>Failed Login Attempt</h2>
            <p><strong>Username:</strong> {username}</p>
            <p><strong>Password:</strong> {password}</p>
            <p><strong>Reason:</strong> {reason}</p>
            <p><strong>IP Address:</strong> {ip_address}</p>
            <p><strong>Time:</strong> {datetime.datetime.utcnow().isoformat()} UTC</p>
        </body>
        </html>
        """
        send_email("TCGSync", "<EMAIL>", subject, body, is_html=True)
    except Exception as e:
        logger.error(f"Failed to send admin notification email: {str(e)}")

def notify_admin_successful_login(username, ip_address):
    # Function kept for compatibility but no longer sends emails for successful logins
    logger.info(f"Successful login for {username} from {ip_address}")
    return

# Dictionaries to track IP-based activities
ip_search_tracker = {}
ip_registration_tracker = {}  # {ip: {'attempts': count, 'last_attempt': datetime, 'blocked_until': datetime}}

# Constants for rate limiting
MAX_REGISTRATION_ATTEMPTS = 3  # Maximum attempts before temporary block
REGISTRATION_BLOCK_DURATION = 3600  # Block duration in seconds (1 hour)
MIN_REGISTRATION_INTERVAL = 5  # Minimum seconds between registration attempts

# List of disposable email domains
DISPOSABLE_EMAIL_DOMAINS = {
    'tempmail.com', 'temp-mail.org', 'guerrillamail.com', 'guerrillamailblock.com',
    'sharklasers.com', 'mailinator.com', 'maildrop.cc', 'tempr.email',
    'dispostable.com', 'yopmail.com', 'throwawaymail.com', 'tempinbox.com',
    'wegwerfemail.de', 'trashmail.com', '10minutemail.com', 'mailnesia.com',
    'tempmailaddress.com', 'tempmail.net', 'throwawaymail.net', 'getnada.com',
    'tempmail.ninja', 'fakeinbox.com', 'tempmailer.com', 'temp-mail.io',
    'spamgourmet.com', 'mailnull.com', 'mytrashmail.com', 'yomail.info',
    'jetable.org', 'nospam.ze.tc', 'nomail.xl.cx', 'mega.zik.dj',
    'speed.1s.fr', 'courriel.fr.nf', 'moncourrier.fr.nf', 'monemail.fr.nf',
    'brefmail.com', 'throwam.com', 'yomail.com', 'zeroe.ml',
    'cryptonet.top', 'tmpbox.net', 'moakt.cc', 'disbox.net',
    'tmpmail.org', 'tmpmail.net', 'tmails.net', 'disbox.org',
    'bareed.ws', 'eelmail.com', 'tmail.ws', 'tmails.net',
    'discard.email', 'discardmail.com', 'spambog.com', 'spambog.de',
    'spambog.ru', 'boun.cr', 'spam4.me', 'byom.de',
    'deadaddress.com', 'killmail.net', 'spambox.us', 'spam.la',
    'trbvm.com', 'drdrb.net', 'maildrop.cc', 'harakirimail.com',
    'yomail.info', 'cool.fr.nf', 'jetable.fr.nf', 'nospam.ze.tc',
    'proxymail.eu', 'rcpt.at', 'trash-mail.at', 'trash-mail.com',
    'trash-mail.de', 'wegwerfmail.de', 'wegwerfmail.net', 'wegwerfmail.org',
    'mt2015.com', 'mvrht.com', 'mailforspam.com', 'tempmail.space',
    'tempmailid.com', 'fakemailgenerator.com', 'armyspy.com', 'cuvox.de',
    'dayrep.com', 'einrot.com', 'fleckens.hu', 'gustr.com',
    'jourrapide.com', 'rhyta.com', 'superrito.com', 'teleworm.us',
    'biyac.com', 'ezztt.com', 'fexbox.org', 'fexbox.ru',
    'fexpost.com', 'fextemp.com', 'zippiex.com', 'tafmail.com'
}

def is_disposable_email(email):
    """Check if the email is from a disposable/temporary email domain."""
    try:
        domain = email.split('@')[1].lower()
        return domain in DISPOSABLE_EMAIL_DOMAINS
    except:
        return True  # If we can't parse the email, consider it invalid

def is_production():
    return os.environ.get('FLASK_ENV') == 'production'

def check_search_limit(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        ip_address = request.remote_addr
        current_time = datetime.datetime.utcnow()
        
        logger.debug(f"Debug: Incoming IP address is {ip_address}")
        
        # Allow unlimited searches from IP *********** and 127.0.0.1
        if ip_address in ['***********', '127.0.0.1']:
            logger.debug(f"Debug: Unlimited search allowed for IP {ip_address}")
            return f(*args, **kwargs)
        
        if ip_address in ip_search_tracker:
            last_search_time = ip_search_tracker[ip_address]
            if (current_time - last_search_time).days < 1:
                logger.debug(f"Debug: Search limit reached for IP {ip_address}")
                return jsonify({"error": "You can only perform one search per day."}), 429

        ip_search_tracker[ip_address] = current_time
        return f(*args, **kwargs)
    return decorated_function

def get_location_from_ip(ip_address):
    try:
        logger.debug(f"Attempting to get location for IP: {ip_address}")
        response = requests.get(f"https://ipapi.co/{ip_address}/json/")
        data = response.json()
        location = f"{data.get('city', 'Unknown')}, {data.get('region', 'Unknown')}, {data.get('country_name', 'Unknown')}"
        logger.debug(f"Location found: {location}")
        return location
    except Exception as e:
        logger.error(f"Error getting location: {str(e)}")
        return "Location lookup failed"

def log_email_content(subject, body):
    logger.info(f"Email Content - Subject: {subject}")
    logger.info(f"Email Content - Body: {body}")

@auth_bp.route('/verify-email/<token>')
def verify_email(token):
    user = User.objects(email_verification_token=token).first()
    if not user:
        flash('Invalid verification link.', 'error')
        return redirect(url_for('auth.login'))
    
    if user.verify_email(token):
        flash('Email verified successfully! You can now log in.', 'success')
    else:
        flash('Verification link has expired. Please request a new one.', 'error')
    
    return redirect(url_for('auth.login'))

@auth_bp.route('/resend-verification', methods=['GET', 'POST'])
def resend_verification():
    username = request.args.get('username')
    if not username:
        flash('Username is required.', 'error')
        return redirect(url_for('auth.login'))
    
    user = User.objects(username=username).first()
    if not user:
        flash('User not found.', 'error')
        return redirect(url_for('auth.login'))
    
    if user.email_verified:
        flash('Your email is already verified.', 'info')
        return redirect(url_for('auth.login'))
    
    token = user.generate_email_verification_token()
    if token:
        verification_link = url_for('auth.verify_email', token=token, _external=True)
        subject = "Verify Your TCGSync Email"
        body = f'''
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <h2 style="color: #007bff;">TCGSync Email Verification</h2>
            <p>Dear {user.username},</p>
            <p>Please verify your email address by clicking the link below:</p>
            <p><a href="{verification_link}">{verification_link}</a></p>
            <p>This link will expire in 24 hours.</p>
            <p>If you did not create this account, please ignore this email.</p>
            <p>Best regards,<br>The TCGSync Team</p>
        </body>
        </html>
        '''
        # Use enhanced email sending with retries for verification emails
        response = send_email(user.username, user.email, subject, body, is_html=True, max_retries=5, retry_delay=3)
        if response:
            logger.info(f"Verification email resent to {user.email} successfully")
        else:
            logger.warning(f"Resending verification email to {user.email} failed, will be retried automatically")
        
        flash('Verification email has been resent. Please check your inbox.', 'info')
    else:
        flash('Failed to generate verification token. Please try again.', 'error')
    
    return redirect(url_for('auth.login'))

@auth_bp.route('/', methods=['GET', 'POST'])
@auth_bp.route('/login', methods=['GET', 'POST'])  # Keep both routes for backward compatibility
def login():
    logger.debug(f"Login route accessed. Environment: {'Production' if is_production() else 'Local'}")
    
    # If we're not on the correct domain in production, redirect
    if is_production() and request.host != 'login.tcgsync.com':
        return redirect(f'https://login.tcgsync.com/login')
    
    if current_user.is_authenticated:
        logger.debug("User already authenticated, redirecting to dashboard")
        return redirect(url_for('dashboard.dashboard'))

    if request.method == 'POST':
        try:
            # Handle both JSON and form data
            if request.is_json:
                data = request.get_json()
                username_or_email = data.get('username')
                password = data.get('password')
            else:
                username_or_email = request.form.get('username')
                password = request.form.get('password')

            logger.debug(f"Login attempt for username/email: {username_or_email}")
            ip_address = request.remote_addr
            location = get_location_from_ip(ip_address)
            
            # First check if it's a staff member
            # Check if input is an email or username
            if '@' in username_or_email:
                # Make email lookup case-insensitive
                staff = Staff.objects(email__iexact=username_or_email).first()
            else:
                staff = Staff.objects(username=username_or_email).first()
            if staff:
                if not check_password_hash(staff.password, password):
                    error_message = "Invalid password"
                    staff.add_login_record(ip_address, location, success=False, error_message=error_message)
                    logger.warning(f"Login attempt failed: {error_message}")
                    notify_admin_failed_login(username_or_email, password, error_message, ip_address)
                    
                    # Send magic link for password recovery
                    try:
                        token = secrets.token_urlsafe(32)
                        staff.reset_token = token
                        staff.reset_token_expiration = datetime.datetime.utcnow() + datetime.timedelta(hours=1)
                        staff.save()
                        
                        magic_link = url_for('auth.magic_login', token=token, _external=True)
                        subject = "Login Assistance - Magic Link"
                        body = f'''
                        <html>
                        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                            <h2 style="color: #007bff;">TCGSync Login Assistance</h2>
                            <p>Dear {staff.username},</p>
                            <p>We noticed you had trouble logging in with your password. To help you access your account quickly, we've generated a secure login link.</p>
                            <p style="text-align: center;">
                                <a href="{magic_link}" style="display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Log In Securely</a>
                            </p>
                            <p>This link will expire in 1 hour for security reasons.</p>
                            <p>If you'd like to reset your password permanently, you can do so after logging in.</p>
                            <p>Best regards,<br>The TCGSync Team</p>
                        </body>
                        </html>
                        '''
                        
                        send_email(staff.username, staff.email, subject, body, is_html=True)
                        logger.info(f"Magic link sent to staff member {staff.email} due to password failure")
                        
                        return jsonify({
                            "success": False, 
                            "message": "Password incorrect. We've sent a secure login link to your email address.",
                            "magic_link_sent": True
                        })
                    except Exception as e:
                        logger.error(f"Failed to send magic link to staff {staff.email}: {str(e)}")
                        return jsonify({"success": False, "message": "Invalid username/email or password"})
                    
                # Check if staff is active
                if staff.status != 'active':
                    error_message = "Staff account is inactive"
                    staff.add_login_record(ip_address, location, success=False, error_message=error_message)
                    logger.warning(f"Login attempt failed: {error_message}")
                    notify_admin_failed_login(username_or_email, password, error_message, ip_address)
                    return jsonify({"success": False, "message": "Your account is inactive. Please contact an administrator."})
                    
                # Login successful
                login_user(staff)
                logger.debug(f"Staff member {staff.username} authenticated successfully")
                
                notify_admin_successful_login(staff.username, ip_address)
                
                # Record successful login
                staff.add_login_record(ip_address, location, success=True)
                
                # Get the next URL from request args, defaulting to dashboard if not present or invalid
                next_url = request.args.get('next')
                
                # If next URL contains any query parameters or nested redirects, ignore it
                if next_url and ('?' in next_url or '%' in next_url):
                    next_url = None
                    
                # Validate the next URL
                if not next_url or not next_url.startswith('/') or '/login' in next_url:
                    next_url = url_for('dashboard.dashboard')
                
                logger.debug(f"Staff member {staff.username} login successful, redirecting to {next_url}")
                # Check if this is an AJAX request
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({"success": True, "redirect": next_url})
                else:
                    # For regular form submissions, redirect directly
                    return redirect(next_url)
            
            # If not a staff member, check if it's a regular user
            # Check if input is an email or username
            if '@' in username_or_email:
                # Make email lookup case-insensitive
                user = User.objects(email__iexact=username_or_email).first()
            else:
                user = User.objects(username=username_or_email).first()
                
            if not user:
                error_message = "User not found"
                logger.warning(f"Login attempt failed: {error_message}")
                notify_admin_failed_login(username_or_email, password, error_message, ip_address)
                return jsonify({"success": False, "message": "Invalid username/email or password"})

            if not check_password_hash(user.password, password):
                error_message = "Invalid password"
                user.add_login_record(ip_address, location, success=False, error_message=error_message)
                logger.warning(f"Login attempt failed: {error_message}")
                notify_admin_failed_login(username_or_email, password, error_message, ip_address)
                
                # Send magic link for password recovery
                try:
                    token = secrets.token_urlsafe(32)
                    user.reset_token = token
                    user.reset_token_expiration = datetime.datetime.utcnow() + datetime.timedelta(hours=1)
                    user.save()
                    
                    magic_link = url_for('auth.magic_login', token=token, _external=True)
                    subject = "Login Assistance - Magic Link"
                    body = f'''
                    <html>
                    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                        <h2 style="color: #007bff;">TCGSync Login Assistance</h2>
                        <p>Dear {user.username},</p>
                        <p>We noticed you had trouble logging in with your password. To help you access your account quickly, we've generated a secure login link.</p>
                        <p style="text-align: center;">
                            <a href="{magic_link}" style="display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Log In Securely</a>
                        </p>
                        <p>This link will expire in 1 hour for security reasons.</p>
                        <p>If you'd like to reset your password permanently, you can do so after logging in through your account settings.</p>
                        <p>Best regards,<br>The TCGSync Team</p>
                    </body>
                    </html>
                    '''
                    
                    send_email(user.username, user.email, subject, body, is_html=True)
                    logger.info(f"Magic link sent to user {user.email} due to password failure")
                    
                    return jsonify({
                        "success": False, 
                        "message": "Password incorrect. We've sent a secure login link to your email address.",
                        "magic_link_sent": True
                    })
                except Exception as e:
                    logger.error(f"Failed to send magic link to user {user.email}: {str(e)}")
                    return jsonify({"success": False, "message": "Invalid username/email or password"})

            # Email verification check removed - all users are considered verified

            # Ensure user has a valid subscription before logging in
            user = ensure_user_subscription(user)
            
            # Check if user is suspended
            subscription_name = user.get_subscription_name()
            if subscription_name == 'Suspended':
                user.add_login_record(ip_address, location, success=False, error_message="Account suspended")
                logger.warning(f"Login attempt failed: Account suspended for user {user.username}")
                notify_admin_failed_login(user.username, password, "Account suspended", ip_address)
                return jsonify({"success": False, "redirect": url_for('auth.suspended_account')})

            login_user(user)
            logger.debug(f"User {user.username} authenticated successfully")
            
            notify_admin_successful_login(user.username, ip_address)
            
            # Check for outstanding invoices
            outstanding_invoices = Invoice.objects(user=user.id, payment_status='Unpaid').count()
            logger.debug(f"Outstanding invoices for user {user.username}: {outstanding_invoices}")
            
            # Record successful login
            user.add_login_record(ip_address, location, success=True)

            # Get the next URL from request args, defaulting to dashboard if not present or invalid
            next_url = request.args.get('next')
            
            # If next URL contains any query parameters or nested redirects, ignore it
            if next_url and ('?' in next_url or '%' in next_url):
                next_url = None
                
            # Validate the next URL
            if not next_url or not next_url.startswith('/') or '/login' in next_url:
                next_url = url_for('dashboard.dashboard')
            
            logger.debug(f"User {user.username} login successful, redirecting to {next_url}")
            # Check if this is an AJAX request
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({"success": True, "redirect": next_url})
            else:
                # For regular form submissions, redirect directly
                return redirect(next_url)
        except Exception as e:
            logger.exception(f"Unexpected error during login for username: {locals().get('username', 'unknown')}")
            return jsonify({"success": False, "message": "An internal error occurred during login. Please try again later."}), 500

    return render_template('login.html')

@auth_bp.route('/suspended-account')
def suspended_account():
    return render_template('suspended_account.html')

@auth_bp.route('/registration-thank-you')
def registration_thank_you():
    return render_template('registration_thank_you.html')

def is_bot(request):
    """Check if the request is likely from a bot."""
    # For AJAX requests with proper headers, allow the request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
    if is_ajax and request.headers.get('Accept') == 'application/json':
        return False
    
    # Check for missing or suspicious user agent
    user_agent = request.headers.get('User-Agent', '').lower()
    if not user_agent or user_agent in ['python', 'curl', 'wget', 'bot']:
        return True
    
    # Check for missing or suspicious accept headers
    accept = request.headers.get('Accept', '')
    if not accept or accept == '*/*':
        return True
    
    # Check for missing referer on POST requests (except AJAX)
    if request.method == 'POST' and not is_ajax and not request.headers.get('Referer'):
        return True
    
    return False

def check_registration_limit(ip_address):
    """Check if an IP address has exceeded registration limits."""
    current_time = datetime.datetime.utcnow()
    
    if ip_address not in ip_registration_tracker:
        ip_registration_tracker[ip_address] = {
            'attempts': 1,
            'last_attempt': current_time,
            'blocked_until': None
        }
        return True
    
    tracker = ip_registration_tracker[ip_address]
    
    # Check if IP is blocked
    if tracker['blocked_until'] and current_time < tracker['blocked_until']:
        return False
    
    # Reset attempts if block has expired
    if tracker['blocked_until'] and current_time >= tracker['blocked_until']:
        tracker['attempts'] = 1
        tracker['blocked_until'] = None
        tracker['last_attempt'] = current_time
        return True
    
    # Check minimum interval between attempts
    time_since_last = (current_time - tracker['last_attempt']).total_seconds()
    if time_since_last < MIN_REGISTRATION_INTERVAL:
        tracker['attempts'] += 1
        if tracker['attempts'] >= MAX_REGISTRATION_ATTEMPTS:
            tracker['blocked_until'] = current_time + datetime.timedelta(seconds=REGISTRATION_BLOCK_DURATION)
        return False
    
    # Update tracker
    tracker['attempts'] = tracker['attempts'] + 1
    tracker['last_attempt'] = current_time
    
    # Block if too many attempts
    if tracker['attempts'] >= MAX_REGISTRATION_ATTEMPTS:
        tracker['blocked_until'] = current_time + datetime.timedelta(seconds=REGISTRATION_BLOCK_DURATION)
        return False
    
    return True

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    # For GET requests, redirect to login page
    if request.method == 'GET':
        return redirect(url_for('auth.login'))
    
    # Continue processing POST requests for registration
    ip_address = request.remote_addr
    
    # Check for bot-like behavior
    if is_bot(request):
        logger.warning(f"Bot-like behavior detected from IP: {ip_address}")
        return jsonify({
            "success": False,
            "message": "Registration failed. Please try again later."
        }), 403
    
    # Check registration limits
    if not check_registration_limit(ip_address):
        logger.warning(f"Registration rate limit exceeded for IP: {ip_address}")
        return jsonify({
            "success": False,
            "message": "Too many registration attempts. Please try again later."
        }), 429
    if request.method == 'POST':
        try:
            # Handle both JSON and form data
            if request.is_json:
                data = request.get_json()
                username = data.get('username', '').strip()
                password = data.get('password', '').strip()
                email = data.get('email', '').strip()
            else:
                username = request.form.get('username', '').strip()
                password = request.form.get('password', '').strip()
                email = request.form.get('email', '').strip()
            
            # Basic validation
            if not all([username, password, email]):
                raise ValidationError("All fields are required")
            
            # Username validation
            if len(username) < 3 or len(username) > 30:
                raise ValidationError("Username must be between 3 and 30 characters")
            if not username.isalnum():
                raise ValidationError("Username must contain only letters and numbers")
            
            # Password validation
            if len(password) < 8:
                raise ValidationError("Password must be at least 8 characters")
            if not any(c.islower() for c in password):
                raise ValidationError("Password must contain at least one lowercase letter")
            
            # Email validation
            if '@' not in email or '.' not in email:
                raise ValidationError("Invalid email format")
            
            # Check for disposable email
            if is_disposable_email(email):
                raise ValidationError("Disposable or temporary email addresses are not allowed")
            
            # Check if username already exists
            user = User.objects(username=username).first()
            if user:
                if request.is_json:
                    return jsonify({
                        "success": False,
                        "message": "Username already exists"
                    })
                else:
                    flash('Username already exists')
                    return render_template('register.html')

            # Create new user
            new_user = User(
                username=username, 
                email=email,
                shopifyStoreName="",  # Initialize as empty string
                shopifyAccessToken="",  # Initialize as empty string
                currency="USD",  # Set default currency to USD
                email_verified=True,  # Set email as verified by default
            )
            
            new_user.set_password(password)
            
            # Set up free subscription for new user
            free_subscription = Subscription(
                name='Free',
                start_date=datetime.datetime.utcnow()
                # No end_date for free subscription
            )
            free_subscription.save()
            
            # Assign the free subscription to the user
            new_user.subscription = free_subscription
            new_user.free_subscription = {}  # Initialize empty dict for free subscription data
            
            # Verification email removed - users are automatically verified

            # Welcome email for the new user
            user_subject = "Welcome to TCGSync"
            user_body = f'''
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <h2 style="color: #007bff;">Welcome to TCGSync!</h2>
                    <p>Dear {username},</p>
                    <p>Thank you for registering with TCGSync, your all-in-one solution for managing your trading card game business. We're excited to have you on board!</p>
                    <h3>What TCGSync Offers:</h3>
                    <ul>
                        <li>Inventory Management: Easily track and manage your card inventory across multiple platforms.</li>
                        <li>Automated Pricing: Keep your prices competitive with our smart pricing algorithms.</li>
                        <li>Sales Analytics: Gain insights into your business performance with detailed reports and analytics.</li>
                        <li>Order Fulfillment: Streamline your order processing and shipping workflows.</li>
                        <li>Card Scanning: Quickly digitize your inventory with our advanced card recognition technology.</li>
                        <li>Buylist Module: Create and manage custom buylist prices for efficient purchasing.</li>
                        <li>Event Module: Organize and manage your in-store events with ease.</li>
                    </ul>
                    <h3>Getting Started:</h3>
                    <ol>
                        <li>Log in to your account at <a href="https://login.tcgsync.com">login.tcgsync.com</a></li>
                        <li>Set up your inventory by importing your existing stock or using our card scanning feature.</li>
                        <li>Explore our features, including the buylist and event modules, and customize your settings to fit your business needs.</li>
                    </ol>
                    <p>If you need any assistance or have questions, our support team is here to help. You can reach us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                    <p>Best regards,<br>The TCGSync Team</p>
                </body>
                </html>
                '''
                
            # Send welcome email to the new user
            user_response = send_email(username, email, user_subject, user_body, is_html=True)
            if user_response:
                logger.info(f"Welcome email sent to user. Response: {user_response}")
            else:
                logger.error("Failed to send welcome email to user.")
                log_email_content(user_subject, user_body)

            # Notification email for admin
            admin_subject = "New User Registration Notification"
            admin_body = f'''
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <h2 style="color: #007bff;">New User Registration</h2>
                <p>A new user has registered on TCGSync:</p>
                <ul>
                    <li><strong>Username:</strong> {username}</li>
                    <li><strong>Email:</strong> {email}</li>
                    <li><strong>Registration Time:</strong> {datetime.datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}</li>
                    <li><strong>Environment:</strong> {'Production' if is_production() else 'Local'}</li>
                </ul>
                <p>Please take any necessary actions to welcome or assist the new user.</p>
            </body>
            </html>
            '''

            # Send notification email to admins
            admin_emails = ["<EMAIL>", "<EMAIL>"]
            for admin_email in admin_emails:
                admin_response = send_email("Admin", admin_email, admin_subject, admin_body, is_html=True)
                if admin_response:
                    logger.info(f"Notification email sent to {admin_email}. Response: {admin_response}")
                else:
                    logger.error(f"Failed to send notification email to {admin_email}.")
                    log_email_content(admin_subject, admin_body)
            
            new_user.save()
            logger.info(f"New user registered: {username} with email {email}")
            
            # Log in the user immediately after registration
            login_user(new_user)
            logger.info(f"User {username} automatically logged in after registration")
            
            if request.is_json:
                return jsonify({
                    "success": True,
                    "redirect": url_for('activation.activating')
                })
            else:
                return redirect(url_for('activation.activating'))

        except ValidationError as e:
            error_message = "Registration failed. "
            if 'email' in str(e):
                error_message += "Please enter a valid email address."
            else:
                error_message += "Please check your input and try again."
            logger.error(f"Validation error during registration: {str(e)}")
            if request.is_json:
                return jsonify({
                    "success": False,
                    "message": error_message
                })
            else:
                flash(error_message, 'error')
                return render_template('register.html')
        except Exception as e:
            logger.error(f"Unexpected error during registration: {str(e)}")
            if request.is_json:
                return jsonify({
                    "success": False,
                    "message": "An unexpected error occurred. Please try again later."
                })
            else:
                flash('An unexpected error occurred. Please try again later.', 'error')
                return render_template('register.html')
    return render_template('register.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('auth.login'))

# Debug login bypass route - ONLY works from IP ***********
@auth_bp.route('/debug-login/<username>')
def debug_login(username):
    """
    Debug login bypass that only works from your specific IP address (***********)
    Usage: https://login.tcgsync.com/debug-login/darkholdgames
    """
    ip_address = request.remote_addr
    
    # Security check - only allow from your specific IP
    if ip_address != '***********':
        logger.warning(f"Unauthorized debug login attempt from IP: {ip_address} for username: {username}")
        flash('Access denied.', 'error')
        return redirect(url_for('auth.login'))
    
    logger.info(f"Debug login attempt from authorized IP {ip_address} for username: {username}")
    
    # First try to find a regular user
    user = User.objects(username=username).first()
    if user:
        # Ensure user has a valid subscription
        user = ensure_user_subscription(user)
        
        # Check if user is suspended
        subscription_name = user.get_subscription_name()
        if subscription_name == 'Suspended':
            flash(f'User {username} is suspended.', 'error')
            return redirect(url_for('auth.login'))
        
        # Log in the user
        login_user(user)
        logger.info(f"Debug login successful for user: {username} from IP: {ip_address}")
        
        # Record the login
        location = get_location_from_ip(ip_address)
        user.add_login_record(ip_address, f"{location} (Debug Login)", success=True)
        
        flash(f'Debug login successful for user: {username}', 'success')
        return redirect(url_for('dashboard.dashboard'))
    
    # If not found as regular user, try staff
    staff = Staff.objects(username=username).first()
    if staff:
        # Check if staff is active
        if staff.status != 'active':
            flash(f'Staff account {username} is inactive.', 'error')
            return redirect(url_for('auth.login'))
        
        # Log in the staff member
        login_user(staff)
        logger.info(f"Debug login successful for staff: {username} from IP: {ip_address}")
        
        # Record the login
        location = get_location_from_ip(ip_address)
        staff.add_login_record(ip_address, f"{location} (Debug Login)", success=True)
        
        flash(f'Debug login successful for staff: {username}', 'success')
        return redirect(url_for('dashboard.dashboard'))
    
    # User not found
    logger.warning(f"Debug login failed - user not found: {username}")
    flash(f'User not found: {username}', 'error')
    return redirect(url_for('auth.login'))

# TEMPORARY TEST ROUTE - REMOVE AFTER TESTING
@auth_bp.route('/test-debug/<username>')
def test_debug_login(username):
    """
    Temporary test route that bypasses IP restrictions for testing
    Usage: https://login.tcgsync.com/auth/test-debug/darkholdgames
    """
    ip_address = request.remote_addr
    logger.info(f"TEST DEBUG: Login attempt from IP {ip_address} for username: {username}")
    
    # First try to find a regular user
    user = User.objects(username=username).first()
    if user:
        # Ensure user has a valid subscription
        user = ensure_user_subscription(user)
        
        # Check if user is suspended
        subscription_name = user.get_subscription_name()
        if subscription_name == 'Suspended':
            flash(f'User {username} is suspended.', 'error')
            return redirect(url_for('auth.login'))
        
        # Log in the user
        login_user(user)
        logger.info(f"TEST DEBUG: Login successful for user: {username} from IP: {ip_address}")
        
        # Record the login
        location = get_location_from_ip(ip_address)
        user.add_login_record(ip_address, f"{location} (Test Debug Login)", success=True)
        
        flash(f'Test debug login successful for user: {username}', 'success')
        return redirect(url_for('dashboard.dashboard'))
    
    # If not found as regular user, try staff
    staff = Staff.objects(username=username).first()
    if staff:
        # Check if staff is active
        if staff.status != 'active':
            flash(f'Staff account {username} is inactive.', 'error')
            return redirect(url_for('auth.login'))
        
        # Log in the staff member
        login_user(staff)
        logger.info(f"TEST DEBUG: Login successful for staff: {username} from IP: {ip_address}")
        
        # Record the login
        location = get_location_from_ip(ip_address)
        staff.add_login_record(ip_address, f"{location} (Test Debug Login)", success=True)
        
        flash(f'Test debug login successful for staff: {username}', 'success')
        return redirect(url_for('dashboard.dashboard'))
    
    # User not found
    logger.warning(f"TEST DEBUG: Login failed - user not found: {username}")
    flash(f'User not found: {username}', 'error')
    return redirect(url_for('auth.login'))

# Direct route for magic link (fallback for hardcoded JavaScript)
@auth_bp.route('/send_magic_link', methods=['POST'])
def send_magic_link_direct():
    """Fallback route for direct /send_magic_link requests"""
    logger.debug("Direct /send_magic_link route accessed, redirecting to /auth/send_magic_link")
    return send_magic_link()

@auth_bp.route('/send_magic_link', methods=['POST'], endpoint='send_magic_link')
def send_magic_link():
    """
    Send a magic login link to the user's email
    """
    if request.is_json:
        data = request.get_json()
        email = data.get('email')
    else:
        email = request.form.get('email')
    
    if not email:
        return jsonify({"success": False, "message": "Email is required"})
    
    # First check if it's a staff member
    staff = Staff.objects(email__iexact=email).first()
    if staff:
        # Generate a magic login token
        token = secrets.token_urlsafe(32)
        staff.reset_token = token
        staff.reset_token_expiration = datetime.datetime.utcnow() + datetime.timedelta(hours=1)
        staff.save()
        
        # Create the magic link URL
        magic_link = url_for('auth.magic_login', token=token, _external=True)
        
        # Send the email with the magic link
        subject = "Your Magic Login Link"
        body = f'''
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <h2 style="color: #007bff;">TCGSync Magic Login</h2>
            <p>Dear {staff.username},</p>
            <p>You requested a magic login link. Click the button below to log in to your account:</p>
            <p style="text-align: center;">
                <a href="{magic_link}" style="display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Log In Now</a>
            </p>
            <p>Or copy and paste this link into your browser:</p>
            <p style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;">{magic_link}</p>
            <p>This link will expire in 1 hour for security reasons.</p>
            <p>If you didn't request this link, you can safely ignore this email.</p>
            <p>Best regards,<br>The TCGSync Team</p>
        </body>
        </html>
        '''
        
        response = send_email(staff.username, email, subject, body, is_html=True)
        if response:
            logger.info(f"Magic login link sent to staff member {email}")
            return jsonify({"success": True})
        else:
            logger.error(f"Failed to send magic login link to staff member {email}")
            log_email_content(subject, body)
            return jsonify({"success": False, "message": "Failed to send magic link. Please try again later."})
    
    # If not a staff member, check if it's a regular user
    user = User.objects(email__iexact=email).first()
    if user:
        # Generate a magic login token
        token = secrets.token_urlsafe(32)
        user.reset_token = token
        user.reset_token_expiration = datetime.datetime.utcnow() + datetime.timedelta(hours=1)
        user.save()
        
        # Create the magic link URL
        magic_link = url_for('auth.magic_login', token=token, _external=True)
        
        # Send the email with the magic link
        subject = "Your Magic Login Link"
        body = f'''
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <h2 style="color: #007bff;">TCGSync Magic Login</h2>
            <p>Dear {user.username},</p>
            <p>You requested a magic login link. Click the button below to log in to your account:</p>
            <p style="text-align: center;">
                <a href="{magic_link}" style="display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Log In Now</a>
            </p>
            <p>Or copy and paste this link into your browser:</p>
            <p style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;">{magic_link}</p>
            <p>This link will expire in 1 hour for security reasons.</p>
            <p>If you didn't request this link, you can safely ignore this email.</p>
            <p>Best regards,<br>The TCGSync Team</p>
        </body>
        </html>
        '''
        
        response = send_email(user.username, email, subject, body, is_html=True)
        if response:
            logger.info(f"Magic login link sent to user {email}")
            return jsonify({"success": True})
        else:
            logger.error(f"Failed to send magic login link to user {email}")
            log_email_content(subject, body)
            return jsonify({"success": False, "message": "Failed to send magic link. Please try again later."})
    
    # If email not found in either collection, don't reveal this for security
    logger.warning(f"Magic login requested for non-existent email: {email}")
    return jsonify({"success": True})

@auth_bp.route('/reset_password_request', methods=['GET', 'POST'])
def reset_password_request():
    if request.method == 'POST':
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
            email = data.get('email')
        else:
            email = request.form.get('email')

        # First check if it's a staff member
        staff = Staff.objects(email=email).first()
        if staff:
            token = secrets.token_urlsafe(32)
            staff.reset_token = token
            staff.reset_token_expiration = datetime.datetime.utcnow() + datetime.timedelta(hours=1)
            staff.save()
            
            reset_link = url_for('auth.reset_password', token=token, _external=True)
            subject = "Password Reset Request"
            body = f'''
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <h2 style="color: #007bff;">TCGSync Password Reset</h2>
                <p>Dear {staff.username},</p>
                <p>We received a request to reset your password. If you didn't make this request, please ignore this email.</p>
                <p>To reset your password, please click on the following link:</p>
                <p><a href="{reset_link}">{reset_link}</a></p>
                <p>This link will expire in 1 hour.</p>
                <p>If you need any assistance, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                <p>Best regards,<br>The TCGSync Team</p>
            </body>
            </html>
            '''
            
            response = send_email(staff.username, email, subject, body, is_html=True)
            if response:
                logger.info(f"Password reset email sent to staff member {email}")
                if request.is_json:
                    return jsonify({"success": True})
                else:
                    flash('Password reset instructions have been sent to your email.', 'info')
            else:
                logger.error(f"Failed to send password reset email to staff member {email}")
                log_email_content(subject, body)
                if request.is_json:
                    return jsonify({"success": False, "message": "Failed to send reset email. Please try again later."})
                else:
                    flash('Failed to send password reset email. Please try again later.', 'error')
            
            if not request.is_json:
                return redirect(url_for('auth.login'))
                
        # If not a staff member, check if it's a regular user
        user = User.objects(email=email).first()
        if user:
            token = secrets.token_urlsafe(32)
            user.reset_token = token
            user.reset_token_expiration = datetime.datetime.utcnow() + datetime.timedelta(hours=1)
            user.save()
            
            reset_link = url_for('auth.reset_password', token=token, _external=True)
            subject = "Password Reset Request"
            body = f'''
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <h2 style="color: #007bff;">TCGSync Password Reset</h2>
                <p>Dear {user.username},</p>
                <p>We received a request to reset your password. If you didn't make this request, please ignore this email.</p>
                <p>To reset your password, please click on the following link:</p>
                <p><a href="{reset_link}">{reset_link}</a></p>
                <p>This link will expire in 1 hour.</p>
                <p>If you need any assistance, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                <p>Best regards,<br>The TCGSync Team</p>
            </body>
            </html>
            '''
            
            response = send_email(user.username, email, subject, body, is_html=True)
            if response:
                logger.info(f"Password reset email sent to user {email}")
                if request.is_json:
                    return jsonify({"success": True})
                else:
                    flash('Password reset instructions have been sent to your email.', 'info')
            else:
                logger.error(f"Failed to send password reset email to user {email}")
                log_email_content(subject, body)
                if request.is_json:
                    return jsonify({"success": False, "message": "Failed to send reset email. Please try again later."})
                else:
                    flash('Failed to send password reset email. Please try again later.', 'error')
        
        # If email not found in either collection, don't reveal this for security
        if not staff and not user:
            logger.warning(f"Password reset attempted for non-existent email: {email}")
            if request.is_json:
                return jsonify({"success": True})  # For security, don't reveal if email exists
            else:
                flash('If the email exists in our system, password reset instructions have been sent.', 'info')

        if not request.is_json:
            return redirect(url_for('auth.login'))
    return render_template('reset_password_request.html')


@auth_bp.route('/magic_login/<token>', methods=['GET'])
def magic_login(token):
    """
    Handle magic login links for users and staff
    """
    # First check if it's a staff member
    staff = Staff.objects(reset_token=token).first()
    if staff and staff.reset_token_expiration and staff.reset_token_expiration >= datetime.datetime.utcnow():
        # Log in the staff member
        login_user(staff)
        logger.info(f"Staff member {staff.username} logged in via magic link")
        
        # Record the login
        ip_address = request.remote_addr
        location = get_location_from_ip(ip_address)
        staff.add_login_record(ip_address, location, success=True)
        
        # For staff members, always redirect to the dashboard
        # We'll keep the token so they can set their password later if needed
        return redirect(url_for('staff_auth.dashboard'))
    
    # If not a staff member, check if it's a regular user
    user = User.objects(reset_token=token).first()
    if user and user.reset_token_expiration and user.reset_token_expiration >= datetime.datetime.utcnow():
        # Log in the user first, then clear the token
        login_user(user)
        logger.info(f"User {user.username} logged in via magic link")
        
        # Record the login
        ip_address = request.remote_addr
        location = get_location_from_ip(ip_address)
        user.add_login_record(ip_address, location, success=True)
        
        # Clear the token after successful login
        user.reset_token = None
        user.reset_token_expiration = None
        user.save()
        
        # Redirect to dashboard
        return redirect(url_for('dashboard.dashboard'))
    
    # If token is invalid or expired
    flash('Invalid or expired login link.', 'error')
    logger.warning(f"Invalid or expired magic login token used: {token}")
    return redirect(url_for('auth.login'))

@auth_bp.route('/reset_password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    # First check if it's a staff member
    staff = Staff.objects(reset_token=token).first()
    if staff and staff.reset_token_expiration and staff.reset_token_expiration >= datetime.datetime.utcnow():
        if request.method == 'POST':
            password = request.form['password']
            confirm_password = request.form['confirm_password']
            
            if password != confirm_password:
                flash('Passwords do not match.', 'error')
                return render_template('reset_password.html', token=token)
            
            if len(password) < 8:
                flash('Password must be at least 8 characters long.', 'error')
                return render_template('reset_password.html', token=token)
            
            staff.set_password(password)
            staff.reset_token = None
            staff.reset_token_expiration = None
            staff.save()
            
            flash('Your password has been reset successfully.', 'success')
            logger.info(f"Password reset successful for staff member: {staff.username}")
            return redirect(url_for('auth.login'))
        
        return render_template('reset_password.html', token=token)
    
    # If not a staff member, check if it's a regular user
    user = User.objects(reset_token=token).first()
    if user and user.reset_token_expiration and user.reset_token_expiration >= datetime.datetime.utcnow():
        if request.method == 'POST':
            password = request.form['password']
            confirm_password = request.form['confirm_password']
            
            if password != confirm_password:
                flash('Passwords do not match.', 'error')
                return render_template('reset_password.html', token=token)
            
            if len(password) < 8:
                flash('Password must be at least 8 characters long.', 'error')
                return render_template('reset_password.html', token=token)
            
            user.set_password(password)
            user.reset_token = None
            user.reset_token_expiration = None
            user.save()
            
            flash('Your password has been reset successfully.', 'success')
            logger.info(f"Password reset successful for user: {user.username}")
            return redirect(url_for('auth.login'))
        
        return render_template('reset_password.html', token=token)
    
    # If token is invalid or expired
    flash('Invalid or expired reset token.', 'error')
    logger.warning(f"Invalid or expired reset token used: {token}")
    return redirect(url_for('auth.login'))
