from config.config import Config
import os
import sys
import json
import time
import uuid
import logging
import threading
import traceback
import re
import math
from datetime import datetime, timedelta, timezone
from functools import wraps
from typing import Dict, Any, Optional, List, Tuple

from flask import Flask, request, jsonify, Response
from flask_cors import CORS
from pymongo import MongoClient, UpdateOne
from pymongo.errors import PyMongoError
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('reprice_service.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
MONGO_URI = '*******************************************************************'
MONGO_DBNAME = 'test'
MONGO_POOL_SIZE = 200

# Constants
API_KEYS = {
    "main_app": "IPBRMIwlm5a7DWoZqPnxyamQSIPMyG19"  # API key for the main application
}
MAX_CONCURRENT_JOBS = 5
JOB_TIMEOUT_MINUTES = 60

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# MongoDB connection
try:
    mongo_client = MongoClient(
        MONGO_URI,
        maxPoolSize=MONGO_POOL_SIZE,
        waitQueueTimeoutMS=5000,
        connectTimeoutMS=5000,
        serverSelectionTimeoutMS=5000,
        retryWrites=True,
        w='majority'
    )
    db = mongo_client[MONGO_DBNAME]
    shopify_collection = db['shProducts']
    user_collection = db['user']
    tcgplayer_key_collection = db['tcgplayerKey']
    autopricer_collection = db['autopricerShopify']
    reprice_logs_collection = db['repriceLogs']
    pricing_transactions_collection = db['pricingTransactions']
    verification_queue_collection = db['verificationQueue']
    
    logger.info("Successfully connected to MongoDB")
except Exception as e:
    logger.error(f"Failed to connect to MongoDB: {str(e)}")
    sys.exit(1)

# In-memory job storage
active_jobs: Dict[str, Dict[str, Any]] = {}
job_threads: Dict[str, threading.Thread] = {}

# Retry decorator for database operations
def retry_database_operation(max_attempts=3, initial_backoff=1):
    """
    Decorator to retry database operations with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        initial_backoff: Initial backoff time in seconds
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            backoff = initial_backoff
            last_error = None
            
            while attempt < max_attempts:
                try:
                    result = func(*args, **kwargs)
                    # Verify the result
                    if hasattr(result, 'acknowledged') and not result.acknowledged:
                        raise PyMongoError("Operation not acknowledged by MongoDB")
                    return result
                except Exception as e:
                    last_error = e
                    attempt += 1
                    if attempt >= max_attempts:
                        logger.error(f"Failed after {max_attempts} attempts: {str(e)}")
                        break
                    
                    logger.warning(f"Attempt {attempt} failed: {str(e)}. Retrying in {backoff} seconds...")
                    time.sleep(backoff)
                    backoff *= 2  # Exponential backoff
            
            raise last_error
        return wrapper
    return decorator

@retry_database_operation(max_attempts=3, initial_backoff=1)
def verified_insert_one(collection, document):
    """
    Perform an insert_one operation with verification and retry logic.
    
    Args:
        collection: MongoDB collection
        document: Document to insert
        
    Returns:
        InsertOneResult object
    """
    result = collection.insert_one(document)
    
    # Verify the result
    if result.acknowledged:
        logger.info(f"Insert successful: inserted_id={result.inserted_id}")
        return result
    else:
        raise PyMongoError("Insert not acknowledged by MongoDB")

@retry_database_operation(max_attempts=3, initial_backoff=1)
def verified_bulk_write(collection, operations, ordered=False):
    """
    Perform a bulk write operation with verification and retry logic.
    
    Args:
        collection: MongoDB collection
        operations: List of write operations
        ordered: Whether operations should be executed in order
        
    Returns:
        BulkWriteResult object
    """
    result = collection.bulk_write(operations, ordered=ordered)
    
    # Verify the result
    if result.acknowledged:
        logger.info(f"Bulk write successful: {result.bulk_api_result}")
        return result
    else:
        raise PyMongoError("Bulk write not acknowledged by MongoDB")

def log_pricing_transaction(username, product_id, variant_id, old_price, new_price, status, error=None):
    """
    Log a pricing transaction to track the status of price changes.
    
    Args:
        username: The username
        product_id: The product ID
        variant_id: The variant ID
        old_price: The old price
        new_price: The new price
        status: The status of the transaction (success, error)
        error: Error message if status is error
    """
    try:
        pricing_transactions_collection.insert_one({
            "timestamp": datetime.now(timezone.utc),
            "username": username,
            "product_id": product_id,
            "variant_id": variant_id,
            "old_price": old_price,
            "new_price": new_price,
            "status": status,
            "error": error,
            "verified": False
        })
    except Exception as e:
        logger.error(f"Failed to log pricing transaction: {str(e)}")

def schedule_verification(product_id, expected_prices):
    """
    Schedule a verification task for a product.
    
    Args:
        product_id: The product ID
        expected_prices: Dictionary mapping variant IDs to expected prices
    """
    try:
        verification_queue_collection.insert_one({
            "product_id": product_id,
            "expected_prices": expected_prices,
            "created_at": datetime.now(timezone.utc),
            "status": "pending",
            "attempts": 0,
            "max_attempts": 3
        })
    except Exception as e:
        logger.error(f"Failed to schedule verification: {str(e)}")

def require_api_key(f):
    """Decorator to require API key for a route."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if api_key not in API_KEYS.values():
            return jsonify({"error": "Invalid or missing API key"}), 401
        return f(*args, **kwargs)
    return decorated_function

def update_job_progress(job_id, processed_count, total_count=None):
    """
    Update the progress of a repricing job.
    
    Args:
        job_id (str): The unique ID of the job
        processed_count (int): The number of products processed so far
        total_count (int, optional): The total number of products to process
    """
    if job_id in active_jobs:
        active_jobs[job_id]['processed_products'] = processed_count
        
        # If total_count is provided, update total_products
        if total_count is not None:
            active_jobs[job_id]['total_products'] = total_count
        
        # If total_products is still 0, try to count products from the database
        total_products = active_jobs[job_id].get('total_products', 0)
        if total_products == 0 and 'username' in active_jobs[job_id]:
            try:
                username = active_jobs[job_id]['username']
                user_config = autopricer_collection.find_one({"username": username})
                
                if user_config and 'selectedProductTypes' in user_config:
                    selected_product_types = [pt.lower() for pt in user_config['selectedProductTypes']]
                    
                    # Create a query that filters by username and selected product types
                    query = {
                        'username': username,
                        'product_type': {
                            '$regex': f'^({"|".join(map(re.escape, selected_product_types))})$',
                            '$options': 'i',
                            '$not': {'$regex': r'.*seal.*', '$options': 'i'}
                        },
                        'rarity': {'$not': {'$regex': r'.*seal.*', '$options': 'i'}},
                        '$or': [
                            {'manualOverride': {'$exists': False}},
                            {'manualOverride': False}
                        ],
                        'variants': {
                            '$elemMatch': {
                                'inventory_quantity': { '$gt': 0 }
                            }
                        }
                    }
                    
                    # Count products for this user with selected product types
                    total_products = shopify_collection.count_documents(query)
                    active_jobs[job_id]['total_products'] = total_products
                    logger.info(f"Counted {total_products} products for user {username} with selected product types")
            except Exception as e:
                logger.error(f"Error counting products in update_job_progress: {str(e)}")
        
        # Calculate progress percentage
        total_products = active_jobs[job_id].get('total_products', 0)
        if total_products > 0:
            progress = (processed_count / total_products) * 100
            active_jobs[job_id]['progress_percent'] = min(round(progress, 1), 100)
            logger.info(f"Updated progress for job {job_id}: {processed_count} of {total_products} products processed ({active_jobs[job_id]['progress_percent']}%)")
        else:
            logger.info(f"Updated progress for job {job_id}: {processed_count} products processed (total count unknown)")

def process_repricing_job(job_id, username):
    """
    Process a repricing job in a background thread.
    
    Args:
        job_id (str): The unique ID of the job
        username (str): The username of the user whose inventory is being repriced
    """
    try:
        logger.info(f"Starting background repricing job {job_id} for user {username}")
        
        # Update job status to 'processing'
        active_jobs[job_id]['status'] = 'processing'
        
        # Get user's autopricer configuration to get selected product types
        user_config = autopricer_collection.find_one({"username": username})
        if not user_config:
            active_jobs[job_id]['status'] = 'error'
            active_jobs[job_id]['error'] = f"No autopricer configuration found for {username}"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            return
            
        # Get selected product types
        selected_product_types = user_config.get('selectedProductTypes', [])
        if not selected_product_types:
            active_jobs[job_id]['status'] = 'error'
            active_jobs[job_id]['error'] = f"No product types selected for {username}"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            return
            
        # Log the selected product types
        logger.info(f"Selected product types for user {username}: {selected_product_types}")
        
        # Check if we already have a total_products count from preparation
        total_products = active_jobs[job_id].get('total_products', 0)
        
        # Only recount if we don't have a count yet
        if total_products == 0:
            try:
                # Create a query that filters by username and selected product types
                selected_product_types_lower = [pt.lower() for pt in selected_product_types]
                query = {
                    'username': username,
                    'product_type': {
                        '$regex': f'^({"|".join(map(re.escape, selected_product_types_lower))})$',
                        '$options': 'i',
                        '$not': {'$regex': r'.*seal.*', '$options': 'i'}
                    },
                    'rarity': {'$not': {'$regex': r'.*seal.*', '$options': 'i'}},
                    '$or': [
                        {'manualOverride': {'$exists': False}},
                        {'manualOverride': False}
                    ],
                    'variants': {
                        '$elemMatch': {
                            'inventory_quantity': { '$gt': 0 }
                        }
                    }
                }
                
                # Count products for this user with selected product types
                total_products = shopify_collection.count_documents(query)
                active_jobs[job_id]['total_products'] = total_products
                logger.info(f"Found {total_products} products to process for user {username} with selected product types")
            except Exception as e:
                logger.error(f"Error counting products: {str(e)}")
                total_products = 0
        else:
            logger.info(f"Using existing count of {total_products} products for user {username}")
            
        # Always reset processed_products to 0 at the start
        active_jobs[job_id]['processed_products'] = 0
        
        # Import the necessary functions from saautopricing
        from saautopricing import reprice_user_inventory, prepare_shopify_settings
        
        # Call the reprice_user_inventory function
        result = reprice_user_inventory(username, job_id=job_id, progress_callback=update_job_progress)
        
        # Handle both string and tuple returns
        if isinstance(result, tuple):
            result_msg, detailed_data = result
        else:
            result_msg = result
            detailed_data = None
        
        # Store the result message in the job data
        active_jobs[job_id]['result'] = result_msg
        active_jobs[job_id]['detailed_data'] = detailed_data
        
        # Get user settings for logging
        user_profile = user_collection.find_one({'username': username})
        user_config = autopricer_collection.find_one({'username': username})
        
        # Log the product types that will be repriced
        selected_product_types = user_config.get('selectedProductTypes', []) if user_config else []
        logger.info(f"Selected product types for user {username}: {selected_product_types}")
        
        # Parse the result message to extract stats
        import re
        stats = {"total_processed": 0, "total_updated": 0, "total_price_changes": 0}
        if isinstance(result_msg, str):
            parts = result_msg.split(", ")
            if len(parts) >= 3:
                processed_match = re.search(r'Processed (\d+)', parts[0])
                updated_match = re.search(r'Updated (\d+)', parts[1])
                changed_match = re.search(r'Changed (\d+)', parts[2])
                
                if processed_match:
                    stats["total_processed"] = int(processed_match.group(1))
                if updated_match:
                    stats["total_updated"] = int(updated_match.group(1))
                if changed_match:
                    stats["total_price_changes"] = int(changed_match.group(1))
        
        # Get user settings
        settings = prepare_shopify_settings(user_profile) if user_profile else {}
        
        # Create log entry
        log_data = {
            "timestamp": datetime.now(timezone.utc),
            "username": username,
            "execution_time_seconds": (datetime.now(timezone.utc) - active_jobs[job_id]['start_time']).total_seconds(),
            "results": [result_msg] if isinstance(result_msg, str) else [],
            "settings": {
                "use_highest_price": settings.get('use_highest_price', False),
                "price_comparison_pairs": settings.get('price_comparison_pairs', []),
                "price_modifiers": settings.get('price_modifiers', {}),
                "price_preference_order": settings.get('price_preference_order', ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']),
                "minPrice": float(settings.get('minPrice', 0.2)),
                "game_minimum_prices": settings.get('game_minimum_prices', {}),
                "advancedPricingRules": settings.get('advancedPricingRules', {}),
                "customStepping": settings.get('customStepping', {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50}),
                "price_rounding_enabled": settings.get('price_rounding_enabled', False),
                "price_rounding_thresholds": settings.get('price_rounding_thresholds', [49, 99]),
                "tcg_trend_increasing": settings.get('tcg_trend_increasing', 0.0),
                "tcg_trend_decreasing": settings.get('tcg_trend_decreasing', 0.0)
            },
            "product_types": user_config.get('selectedProductTypes', []) if user_config else [],
            "total_processed": stats["total_processed"],
            "total_updated": stats["total_updated"],
            "total_price_changes": stats["total_price_changes"],
            "service": "reprice_vps",  # Mark that this was processed by the VPS service
            "job_id": job_id,  # Store the job ID for reference
            "status": active_jobs[job_id]['status']  # Store the job status
        }
        
        # Add detailed data if available
        if isinstance(detailed_data, dict):
            log_data["random_changed_records"] = detailed_data
        
        # Save log to database with verification
        try:
            verified_insert_one(reprice_logs_collection, log_data)
            
            # Log successful repricing job
            if stats["total_price_changes"] > 0:
                log_pricing_transaction(
                    username=username,
                    product_id="batch_job",
                    variant_id="multiple",
                    old_price=0,
                    new_price=0,
                    status='success',
                    error=None
                )
            
        except Exception as db_error:
            logger.error(f"Failed to save reprice log: {str(db_error)}")
            # Still mark the job as complete even if logging fails
        
        # Update job status to 'complete'
        active_jobs[job_id]['status'] = 'complete'
        active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
        
        logger.info(f"Completed background repricing job {job_id} for user {username}")
        
    except Exception as e:
        error_message = f"Error in background repricing job: {str(e)}"
        logger.error(error_message)
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Log error in transaction
        try:
            log_pricing_transaction(
                username=username,
                product_id="batch_job",
                variant_id="multiple",
                old_price=0,
                new_price=0,
                status='error',
                error=error_message
            )
        except Exception as log_error:
            logger.error(f"Failed to log error transaction: {str(log_error)}")
        
        # Update job status to 'error'
        active_jobs[job_id]['status'] = 'error'
        active_jobs[job_id]['error'] = error_message
        active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)

def cleanup_old_jobs():
    """Remove completed jobs that are older than 24 hours."""
    current_time = datetime.now(timezone.utc)
    jobs_to_remove = []
    
    for job_id, job_data in active_jobs.items():
        if job_data.get('status') in ['complete', 'error']:
            completion_time = job_data.get('completion_time')
            if completion_time and (current_time - completion_time) > timedelta(hours=24):
                jobs_to_remove.append(job_id)
    
    for job_id in jobs_to_remove:
        del active_jobs[job_id]
        if job_id in job_threads:
            del job_threads[job_id]
    
    if jobs_to_remove:
        logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    try:
        # Check MongoDB connection
        mongo_client.admin.command('ping')
        mongo_status = True
    except Exception:
        mongo_status = False
    
    # Count active jobs
    total_jobs = len(active_jobs)
    active_count = sum(1 for job in active_jobs.values() if job.get('status') == 'processing')
    
    return jsonify({
        'status': 'healthy',
        'mongo_connected': mongo_status,
        'total_jobs': total_jobs,
        'active_jobs': active_count,
        'server_time': datetime.now(timezone.utc).isoformat()
    })

@app.route('/api/check_product', methods=['POST'])
@require_api_key
def check_product():
    """Check if a product exists in the database."""
    data = request.json
    username = data.get('username')
    product_id = data.get('product_id')
    
    if not username:
        return jsonify({'error': 'Username is required'}), 400
    
    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400
    
    # Check if user exists
    user = user_collection.find_one({'username': username})
    if not user:
        return jsonify({'error': f'User {username} not found', 'exists': False}), 404
    
    # Check if product exists
    product = shopify_collection.find_one({'username': username, 'productId': product_id})
    if not product:
        return jsonify({'error': f'Product {product_id} not found for user {username}', 'exists': False}), 404
    
    # Convert ObjectId to string for JSON serialization
    if '_id' in product:
        product['_id'] = str(product['_id'])
    
    return jsonify({
        'exists': True,
        'product': product
    })

@app.route('/api/reprice_debug', methods=['POST'])
@require_api_key
def start_repricing_debug():
    """Start a repricing job with debug information."""
    data = request.json
    username = data.get('username')
    product_id = data.get('product_id')
    
    if not username:
        return jsonify({'error': 'Username is required'}), 400
    
    if not product_id:
        return jsonify({'error': 'Product ID is required for debug repricing'}), 400
    
    # Check if user exists
    user = user_collection.find_one({'username': username})
    if not user:
        return jsonify({'error': f'User {username} not found'}), 404
    
    # Check if user has autopricer configuration
    user_config = autopricer_collection.find_one({"username": username})
    if not user_config:
        return jsonify({'error': f'No autopricer configuration found for {username}'}), 400
        
    # Check if user has selected product types
    selected_product_types = user_config.get('selectedProductTypes', [])
    if not selected_product_types:
        return jsonify({'error': f'No product types selected for {username}'}), 400
    
    # Log the selected product types
    logger.info(f"Selected product types for user {username}: {selected_product_types}")
        
    # Get the product to check if its type is in selected product types
    product = shopify_collection.find_one({'username': username, 'productId': product_id})
    if not product:
        return jsonify({'error': f'Product {product_id} not found for user {username}'}), 404
        
    # Check if product type is in selected product types
    product_type = product.get('product_type', '').lower()
    if product_type not in [pt.lower() for pt in selected_product_types]:
        return jsonify({
            'error': f'Product type {product_type} is not in selected product types {selected_product_types}',
            'product_type': product_type,
            'selected_product_types': selected_product_types
        }), 400
    
    # Check if there's already an active job for this user
    for job_id, job_data in active_jobs.items():
        if job_data.get('username') == username and job_data.get('status') == 'processing':
            return jsonify({
                'message': f'A repricing job is already in progress for {username}',
                'job_id': job_id,
                'status': job_data.get('status'),
                'progress': job_data.get('progress_percent', 0)
            }), 409
    
    # Check if we have too many active jobs
    active_count = sum(1 for job in active_jobs.values() if job.get('status') == 'processing')
    if active_count >= MAX_CONCURRENT_JOBS:
        return jsonify({'error': 'Too many active jobs, please try again later'}), 429
    
    # Generate a unique job ID
    job_id = str(uuid.uuid4())
    
    # Create a job entry with debug flag
    active_jobs[job_id] = {
        'id': job_id,
        'username': username,
        'product_id': product_id,  # Store the product ID for debugging
        'status': 'pending',
        'start_time': datetime.now(timezone.utc),
        'processed_products': 0,
        'total_products': 0,
        'progress_percent': 0,
        'result': None,
        'detailed_data': None,
        'error': None,
        'debug': True,  # Flag to indicate this is a debug job
        'debug_info': {}  # Store debug information
    }
    
    # Start a background thread to process the repricing with debug
    thread = threading.Thread(
        target=process_repricing_job_debug,
        args=(job_id, username, product_id),
        daemon=True
    )
    thread.start()
    
    # Store the thread reference
    job_threads[job_id] = thread
    
    return jsonify({
        'message': f'Repricing job started for {username} with debug for product {product_id}',
        'job_id': job_id,
        'status': 'pending'
    })

def process_repricing_job_debug(job_id, username, product_id):
    """
    Process a repricing job in a background thread with debug information.
    
    Args:
        job_id (str): The unique ID of the job
        username (str): The username of the user whose inventory is being repriced
        product_id (int): The ID of the product to reprice
    """
    try:
        logger.info(f"Starting background repricing job {job_id} for user {username} with debug for product {product_id}")
        
        # Update job status to 'processing'
        active_jobs[job_id]['status'] = 'processing'
        
        # Import necessary functions from saautopricing
        from saautopricing import prepare_shopify_settings, PricingCalculator, fetch_pricing_data, get_exchange_rate, has_valid_price, determine_printing_type, extract_condition
        
        # Get user profile and settings
        user_profile = user_collection.find_one({'username': username})
        if not user_profile:
            active_jobs[job_id]['status'] = 'error'
            active_jobs[job_id]['error'] = f"User profile not found for {username}"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            return
        
        # Store user settings in debug info
        settings = prepare_shopify_settings(user_profile)
        active_jobs[job_id]['debug_info']['user_settings'] = settings
        
        # Get user's autopricer configuration
        user_config = autopricer_collection.find_one({"username": username})
        if not user_config:
            active_jobs[job_id]['status'] = 'error'
            active_jobs[job_id]['error'] = f"No autopricer configuration found for {username}"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            return
        
        # Store selected product types in debug info
        selected_product_types = user_config.get('selectedProductTypes', [])
        active_jobs[job_id]['debug_info']['selected_product_types'] = selected_product_types
        
        # Get the product from the database
        product = shopify_collection.find_one({'username': username, 'productId': product_id})
        if not product:
            active_jobs[job_id]['status'] = 'error'
            active_jobs[job_id]['error'] = f"Product {product_id} not found for user {username}"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            return
        
        # Store product info in debug info
        product_info = {
            'title': product.get('title'),
            'product_type': product.get('product_type'),
            'gameName': product.get('gameName'),
            'expansionName': product.get('expansionName'),
            'rarity': product.get('rarity'),
            'variants': product.get('variants')
        }
        active_jobs[job_id]['debug_info']['product_info'] = product_info
        
        # Check if product type is in selected product types
        product_type = product.get('product_type', '').lower()
        if product_type not in [pt.lower() for pt in selected_product_types]:
            active_jobs[job_id]['status'] = 'complete'
            active_jobs[job_id]['result'] = f"Product type {product_type} is not in selected product types {selected_product_types}"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            active_jobs[job_id]['debug_info']['reason'] = "Product type not in selected product types"
            return
        
        # Check if product has manual override
        if product.get('manualOverride', False):
            active_jobs[job_id]['status'] = 'complete'
            active_jobs[job_id]['result'] = f"Product {product_id} has manual override set to true"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            active_jobs[job_id]['debug_info']['reason'] = "Product has manual override"
            return
        
        # Get TCGPlayer API key
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            active_jobs[job_id]['status'] = 'error'
            active_jobs[job_id]['error'] = "TCGPlayer API key not found"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            return
        
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        
        # Get pricing data from TCGPlayer
        product_id_str = str(product_id)
        pricing_data = fetch_pricing_data([product_id_str], tcgplayer_api_key)
        
        # Store pricing data in debug info
        active_jobs[job_id]['debug_info']['tcgplayer_pricing_data'] = pricing_data
        
        if not pricing_data or product_id_str not in pricing_data:
            active_jobs[job_id]['status'] = 'complete'
            active_jobs[job_id]['result'] = f"No pricing data found for product {product_id}"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            active_jobs[job_id]['debug_info']['reason'] = "No pricing data found"
            return
        
        # Get user currency and exchange rate
        user_currency = user_profile.get('currency', 'USD')
        exchange_rate = get_exchange_rate(user_currency)
        
        # Store currency info in debug info
        active_jobs[job_id]['debug_info']['user_currency'] = user_currency
        active_jobs[job_id]['debug_info']['exchange_rate'] = exchange_rate
        
        # Create pricing calculator with user settings
        calculator = PricingCalculator(settings, user_currency)
        
        # Process the product
        product_pricing = pricing_data[product_id_str]
        
        # Only include subtypes that have valid prices
        valid_subtypes = [p.get('subTypeName') for p in product_pricing 
                        if p.get('subTypeName') and has_valid_price(p)]
        
        # Store valid subtypes in debug info
        active_jobs[job_id]['debug_info']['valid_subtypes'] = valid_subtypes
        
        variants_changed = False
        variants = product['variants']
        variant_results = []
        
        for variant in variants:
            try:
                old_price = float(variant.get('price', 0))
                printing_type = determine_printing_type(variant['title'], valid_subtypes)
                
                # Store variant info in debug info
                variant_info = {
                    'title': variant.get('title'),
                    'old_price': old_price,
                    'printing_type': printing_type
                }
                
                # Find the price data for this printing type
                matched_price = None
                printing_type_lower = printing_type.lower()
                
                # Create lookup dictionaries for faster matching
                exact_matches = {}
                holofoil_matches = {}
                normal_matches = {}
                valid_prices = {}
                
                # Single pass through product_pricing to categorize all matches
                for p in product_pricing:
                    if not has_valid_price(p):
                        continue
                        
                    subtype = p.get('subTypeName', '').lower()
                    if not subtype:
                        continue
                        
                    # Store in appropriate category
                    if subtype == printing_type_lower:
                        exact_matches[subtype] = p
                    elif 'holofoil' in subtype:
                        holofoil_matches[subtype] = p
                    elif subtype == 'normal':
                        normal_matches[subtype] = p
                    
                    # Store all valid prices
                    valid_prices[subtype] = p
                
                # Try matches in priority order
                if exact_matches:
                    matched_price = next(iter(exact_matches.values()))
                    variant_info['match_type'] = 'exact'
                elif holofoil_matches:
                    matched_price = next(iter(holofoil_matches.values()))
                    variant_info['match_type'] = 'holofoil'
                elif normal_matches:
                    matched_price = next(iter(normal_matches.values()))
                    variant_info['match_type'] = 'normal'
                elif valid_prices:
                    matched_price = next(iter(valid_prices.values()))
                    variant_info['match_type'] = 'any_valid'
                
                variant_info['matched_price'] = matched_price
                
                if matched_price:
                    # Extract pricing info for this printing type and convert from USD to user currency
                    pricing_info = {}
                    
                    # Get market price first as it's often the most reliable
                    market_price = matched_price.get('marketPrice')
                    if market_price is not None:
                        # Convert from USD to user currency
                        pricing_info['marketPrice'] = float(market_price) * exchange_rate
                    
                    # Get low price (try directLowPrice first, then lowPrice)
                    low_price = matched_price.get('directLowPrice')
                    if low_price is None:
                        low_price = matched_price.get('lowPrice')
                    if low_price is not None:
                        # Convert from USD to user currency
                        pricing_info['lowPrice'] = float(low_price) * exchange_rate
                    
                    # Get mid price (fallback to market price if not available)
                    mid_price = matched_price.get('midPrice')
                    if mid_price is None and market_price is not None:
                        mid_price = market_price
                    if mid_price is not None:
                        # Convert from USD to user currency
                        pricing_info['midPrice'] = float(mid_price) * exchange_rate
                    
                    # Get high price (fallback to market price if not available)
                    high_price = matched_price.get('highPrice')
                    if high_price is None and market_price is not None:
                        high_price = market_price
                    if high_price is not None:
                        # Convert from USD to user currency
                        pricing_info['highPrice'] = float(high_price) * exchange_rate
                    
                    variant_info['pricing_info'] = pricing_info
                    
                    # Update product info to include printing type and condition
                    product['printingType'] = printing_type
                    condition = extract_condition(variant.get('option1', ''))
                    variant_info['condition'] = condition
                    
                    # Create sku_info for price calculation
                    sku_info = {
                        'pricingInfo': pricing_info,
                        'condName': condition,
                        'printingName': printing_type,
                        'skuId': variant.get('id'),
                        'variantTitle': variant.get('title')
                    }
                    
                    # Calculate new price
                    new_price, is_missing, price_history = calculator.calculate_final_price(sku_info, product)
                    
                    variant_info['new_price'] = new_price
                    variant_info['is_missing'] = is_missing
                    variant_info['price_history'] = price_history
                    
                    if not is_missing and new_price is not None:
                        if abs(old_price - new_price) > 0.01:
                            variant['price'] = str(new_price)
                            variants_changed = True
                            variant_info['price_changed'] = True
                        else:
                            variant_info['price_changed'] = False
                    else:
                        variant_info['price_changed'] = False
                else:
                    variant_info['error'] = "No matching price data found"
            except Exception as e:
                variant_info['error'] = f"Error processing variant: {str(e)}"
            
            variant_results.append(variant_info)
        
        # Store variant results in debug info
        active_jobs[job_id]['debug_info']['variant_results'] = variant_results
        
        # Update the product in the database if variants changed
        if variants_changed:
            update_fields = {
                'variants': variants,
                'needsPushing': True,
                'last_repriced': datetime.now(timezone.utc)
            }
            
            shopify_collection.update_one(
                {'_id': product['_id']},
                {'$set': update_fields}
            )
            
            active_jobs[job_id]['status'] = 'complete'
            active_jobs[job_id]['result'] = f"Processed 1 product, Updated 1 product, Changed {sum(1 for v in variant_results if v.get('price_changed', False))} prices"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
        else:
            # Update last_repriced timestamp even if no changes
            shopify_collection.update_one(
                {'_id': product['_id']},
                {'$set': {'last_repriced': datetime.now(timezone.utc)}}
            )
            
            active_jobs[job_id]['status'] = 'complete'
            active_jobs[job_id]['result'] = "Processed 0 products, Updated 0 products, Changed 0 prices"
            active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)
            active_jobs[job_id]['debug_info']['reason'] = "No price changes needed"
        
    except Exception as e:
        error_message = f"Error in background repricing job with debug: {str(e)}"
        logger.error(error_message)
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Update job status to 'error'
        active_jobs[job_id]['status'] = 'error'
        active_jobs[job_id]['error'] = error_message
        active_jobs[job_id]['completion_time'] = datetime.now(timezone.utc)

@app.route('/api/prepare_reprice', methods=['POST'])
@require_api_key
def prepare_repricing():
    """Prepare a repricing job and return details without starting it."""
    data = request.json
    username = data.get('username')
    product_id = data.get('product_id')  # Optional product ID for single product repricing
    
    if not username:
        return jsonify({'error': 'Username is required'}), 400
    
    # Check if user exists
    user = user_collection.find_one({'username': username})
    if not user:
        return jsonify({'error': f'User {username} not found'}), 404
    
    # Check if user has autopricer configuration
    user_config = autopricer_collection.find_one({"username": username})
    if not user_config:
        return jsonify({'error': f'No autopricer configuration found for {username}'}), 400
        
    # Check if user has selected product types
    selected_product_types = user_config.get('selectedProductTypes', [])
    if not selected_product_types:
        return jsonify({'error': f'No product types selected for {username}'}), 400
    
    # If a specific product ID is provided, check if its type is in the selected product types
    if product_id:
        product = shopify_collection.find_one({'username': username, 'productId': product_id})
        if not product:
            return jsonify({'error': f'Product {product_id} not found for user {username}'}), 404
            
        # Check if product type is in selected product types
        product_type = product.get('product_type', '').lower()
        if product_type not in [pt.lower() for pt in selected_product_types]:
            return jsonify({
                'error': f'Product type {product_type} is not in selected product types {selected_product_types}',
                'product_type': product_type,
                'selected_product_types': selected_product_types
            }), 400
    
    # Count products that match the criteria
    query = {
        'username': username,
        'product_type': {
            '$regex': f'^({"|".join(map(re.escape, [pt.lower() for pt in selected_product_types]))})$',
            '$options': 'i',
            '$not': {'$regex': r'.*seal.*', '$options': 'i'}
        },
        'rarity': {'$not': {'$regex': r'.*seal.*', '$options': 'i'}},
        '$or': [
            {'manualOverride': {'$exists': False}},
            {'manualOverride': False}
        ],
        'variants': {
            '$elemMatch': {
                'inventory_quantity': { '$gt': 0 }
            }
        }
    }
    
    # If product_id is provided, add it to the query
    if product_id:
        query['productId'] = product_id
    
    total_products = shopify_collection.count_documents(query)
    
    # Get user settings
    from saautopricing import prepare_shopify_settings
    settings = prepare_shopify_settings(user)
    
    # Generate a unique job ID for this preparation
    job_id = str(uuid.uuid4())
    
    # Store the preparation details
    active_jobs[job_id] = {
        'id': job_id,
        'username': username,
        'product_id': product_id,  # Store the product ID if provided
        'status': 'prepared',  # New status to indicate job is prepared but not started
        'preparation_time': datetime.now(timezone.utc),
        'total_products': total_products,
        'selected_product_types': selected_product_types,
        'settings': settings
    }
    
    # Return the preparation details
    return jsonify({
        'job_id': job_id,
        'username': username,
        'total_products': total_products,
        'selected_product_types': selected_product_types,
        'settings': {
            'minPrice': settings.get('minPrice'),
            'price_preference_order': settings.get('price_preference_order'),
            'use_highest_price': settings.get('use_highest_price'),
            'price_modifiers': settings.get('price_modifiers'),
            'customStepping': settings.get('customStepping'),
            'price_rounding_enabled': settings.get('price_rounding_enabled'),
            'price_rounding_thresholds': settings.get('price_rounding_thresholds'),
            'tcg_trend_increasing': settings.get('tcg_trend_increasing'),
            'tcg_trend_decreasing': settings.get('tcg_trend_decreasing')
        }
    })

@app.route('/api/reprice', methods=['POST'])
@require_api_key
def start_repricing():
    """Start a repricing job."""
    data = request.json
    username = data.get('username')
    product_id = data.get('product_id')  # Optional product ID for single product repricing
    job_id = data.get('job_id')  # New parameter for confirmation
    
    # If job_id is provided, this is a confirmation of a prepared job
    if job_id:
        if job_id not in active_jobs:
            return jsonify({'error': 'Job not found'}), 404
            
        job_data = active_jobs[job_id]
        
        # Check if the job is in 'prepared' status
        if job_data.get('status') != 'prepared':
            return jsonify({'error': 'Job is not in prepared status'}), 400
            
        # Check if the username matches
        if job_data.get('username') != username:
            return jsonify({'error': 'Username does not match prepared job'}), 400
            
        # Update job status to 'pending'
        job_data['status'] = 'pending'
        job_data['start_time'] = datetime.now(timezone.utc)
        
        # Get product_id from job data if it exists
        product_id = job_data.get('product_id')
        
        # Ensure we keep the total_products count from preparation
        # and initialize processed_products to 0
        total_products = job_data.get('total_products', 0)
        job_data['processed_products'] = 0
        
        logger.info(f"Confirmed job {job_id} for user {username} with {total_products} products to process")
        
        # Start the background thread for processing
        thread = threading.Thread(
            target=process_repricing_job,
            args=(job_id, username),
            daemon=True
        )
        thread.start()
        
        # Store the thread reference
        job_threads[job_id] = thread
        
        return jsonify({
            'message': f'Repricing job confirmed and started for {username}',
            'job_id': job_id,
            'status': 'pending'
        })
    
    # Original code for starting a new job without preparation
    # (keeping this for backward compatibility)
    if not username:
        return jsonify({'error': 'Username is required'}), 400
    
    # Check if user exists
    user = user_collection.find_one({'username': username})
    if not user:
        return jsonify({'error': f'User {username} not found'}), 404
    
    # Check if user has autopricer configuration
    user_config = autopricer_collection.find_one({"username": username})
    if not user_config:
        return jsonify({'error': f'No autopricer configuration found for {username}'}), 400
        
    # Check if user has selected product types
    selected_product_types = user_config.get('selectedProductTypes', [])
    if not selected_product_types:
        return jsonify({'error': f'No product types selected for {username}'}), 400
    
    # If a specific product ID is provided, check if its type is in the selected product types
    if product_id:
        product = shopify_collection.find_one({'username': username, 'productId': product_id})
        if not product:
            return jsonify({'error': f'Product {product_id} not found for user {username}'}), 404
            
        # Check if product type is in selected product types
        product_type = product.get('product_type', '').lower()
        if product_type not in [pt.lower() for pt in selected_product_types]:
            return jsonify({
                'error': f'Product type {product_type} is not in selected product types {selected_product_types}',
                'product_type': product_type,
                'selected_product_types': selected_product_types
            }), 400
    
    # Log the user settings and product types for debugging
    from saautopricing import prepare_shopify_settings
    settings = prepare_shopify_settings(user)
    selected_product_types = user_config.get('selectedProductTypes', [])
    
    logger.info(f"User settings for {username}:")
    logger.info(f"  Min Price: {settings.get('minPrice')}")
    logger.info(f"  Price Preference Order: {settings.get('price_preference_order')}")
    logger.info(f"  Use Highest Price: {settings.get('use_highest_price')}")
    logger.info(f"  Custom Stepping: {settings.get('customStepping')}")
    logger.info(f"  Price Rounding Enabled: {settings.get('price_rounding_enabled')}")
    logger.info(f"  Price Rounding Thresholds: {settings.get('price_rounding_thresholds')}")
    logger.info(f"  TCG Trend Increasing: {settings.get('tcg_trend_increasing')}%")
    logger.info(f"  TCG Trend Decreasing: {settings.get('tcg_trend_decreasing')}%")
    
    logger.info(f"Selected product types for {username}: {selected_product_types}")
    
    # Check if there's already an active job for this user
    for job_id, job_data in active_jobs.items():
        if job_data.get('username') == username and job_data.get('status') == 'processing':
            return jsonify({
                'message': f'A repricing job is already in progress for {username}',
                'job_id': job_id,
                'status': job_data.get('status'),
                'progress': job_data.get('progress_percent', 0)
            }), 409
    
    # Check if we have too many active jobs
    active_count = sum(1 for job in active_jobs.values() if job.get('status') == 'processing')
    if active_count >= MAX_CONCURRENT_JOBS:
        return jsonify({'error': 'Too many active jobs, please try again later'}), 429
    
    # Generate a unique job ID
    job_id = str(uuid.uuid4())
    
    # Create a job entry
    active_jobs[job_id] = {
        'id': job_id,
        'username': username,
        'product_id': product_id,  # Store the product ID if provided
        'status': 'pending',
        'start_time': datetime.now(timezone.utc),
        'processed_products': 0,
        'total_products': 0,
        'progress_percent': 0,
        'result': None,
        'detailed_data': None,
        'error': None
    }
    
    # Start a background thread to process the repricing
    thread = threading.Thread(
        target=process_repricing_job,
        args=(job_id, username),
        daemon=True
    )
    thread.start()
    
    # Store the thread reference
    job_threads[job_id] = thread
    
    return jsonify({
        'message': f'Repricing job started for {username}',
        'job_id': job_id,
        'status': 'pending'
    })

@app.route('/api/status/<job_id>', methods=['GET'])
@require_api_key
def check_job_status(job_id):
    """Check the status of a repricing job."""
    # Clean up old jobs first
    cleanup_old_jobs()
    
    if job_id not in active_jobs:
        return jsonify({'status': 'not_found'}), 404
    
    job_data = active_jobs[job_id]
    
    # Check if the job has timed out
    if job_data.get('status') == 'processing':
        start_time = job_data.get('start_time')
        if start_time and (datetime.now(timezone.utc) - start_time) > timedelta(minutes=JOB_TIMEOUT_MINUTES):
            job_data['status'] = 'error'
            job_data['error'] = f'Job timed out after {JOB_TIMEOUT_MINUTES} minutes'
            job_data['completion_time'] = datetime.now(timezone.utc)
    
    response_data = {
        'job_id': job_id,
        'status': job_data.get('status'),
        'username': job_data.get('username'),
        'start_time': job_data.get('start_time').isoformat() if job_data.get('start_time') else None,
        'processed_products': job_data.get('processed_products', 0),
        'total_products': job_data.get('total_products', 0),
        'progress_percent': job_data.get('progress_percent', 0)
    }
    
    if job_data.get('status') == 'complete':
        response_data['result'] = job_data.get('result')
        response_data['completion_time'] = job_data.get('completion_time').isoformat() if job_data.get('completion_time') else None
        
        # Include debug info if this is a debug job
        if job_data.get('debug', False) and 'debug_info' in job_data:
            response_data['debug_info'] = job_data.get('debug_info')
    elif job_data.get('status') == 'error':
        response_data['error'] = job_data.get('error')
        response_data['completion_time'] = job_data.get('completion_time').isoformat() if job_data.get('completion_time') else None
        
        # Include debug info if this is a debug job
        if job_data.get('debug', False) and 'debug_info' in job_data:
            response_data['debug_info'] = job_data.get('debug_info')
    
    return jsonify(response_data)

@app.route('/api/cancel/<job_id>', methods=['POST'])
@require_api_key
def cancel_job(job_id):
    """Cancel a repricing job."""
    if job_id not in active_jobs:
        return jsonify({'status': 'not_found'}), 404
    
    job_data = active_jobs[job_id]
    
    if job_data.get('status') != 'processing':
        return jsonify({'message': f'Job {job_id} is not processing and cannot be cancelled'}), 400
    
    # We can't actually stop the thread, but we can mark it as cancelled
    job_data['status'] = 'cancelled'
    job_data['completion_time'] = datetime.now(timezone.utc)
    
    return jsonify({
        'message': f'Job {job_id} has been marked for cancellation',
        'job_id': job_id,
        'status': 'cancelled'
    })

@app.route('/api/jobs', methods=['GET'])
@require_api_key
def list_jobs():
    """List all active jobs."""
    # Clean up old jobs first
    cleanup_old_jobs()
    
    # Filter jobs by username if provided
    username = request.args.get('username')
    
    jobs_list = []
    for job_id, job_data in active_jobs.items():
        if username and job_data.get('username') != username:
            continue
            
        job_info = {
            'job_id': job_id,
            'username': job_data.get('username'),
            'status': job_data.get('status'),
            'start_time': job_data.get('start_time').isoformat() if job_data.get('start_time') else None,
            'processed_products': job_data.get('processed_products', 0),
            'total_products': job_data.get('total_products', 0),
            'progress_percent': job_data.get('progress_percent', 0)
        }
        
        if job_data.get('status') in ['complete', 'error', 'cancelled']:
            job_info['completion_time'] = job_data.get('completion_time').isoformat() if job_data.get('completion_time') else None
            
        if job_data.get('status') == 'error':
            job_info['error'] = job_data.get('error')
            
        jobs_list.append(job_info)
    
    return jsonify({
        'jobs': jobs_list,
        'total': len(jobs_list)
    })

@app.route('/api/previous_jobs', methods=['GET'])
@require_api_key
def list_previous_jobs():
    """List previous completed jobs from the repriceLogs collection."""
    # Filter jobs by username if provided
    username = request.args.get('username')
    
    # Pagination parameters
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 10))
    
    # Build the query
    query = {}
    if username:
        query['username'] = username
    
    # Query the repriceLogs collection
    try:
        # Count total documents matching the query
        total_count = reprice_logs_collection.count_documents(query)
        
        # Get paginated results
        cursor = reprice_logs_collection.find(
            query,
            {
                'timestamp': 1,
                'username': 1,
                'execution_time_seconds': 1,
                'results': 1,
                'total_processed': 1,
                'total_updated': 1,
                'total_price_changes': 1,
                'job_id': 1,
                'status': 1
            }
        ).sort('timestamp', -1).skip((page - 1) * per_page).limit(per_page)
        
        # Convert cursor to list and format the results
        previous_jobs = []
        for job in cursor:
            # Convert ObjectId to string for JSON serialization
            job['_id'] = str(job['_id'])
            
            # Format timestamp
            if 'timestamp' in job:
                job['timestamp'] = job['timestamp'].isoformat()
            
            # Extract result message if available
            if 'results' in job and job['results'] and isinstance(job['results'], list):
                job['result_message'] = job['results'][0]
                del job['results']
            
            previous_jobs.append(job)
        
        # Return the results with pagination info
        return jsonify({
            'previous_jobs': previous_jobs,
            'total': total_count,
            'page': page,
            'per_page': per_page,
            'total_pages': math.ceil(total_count / per_page)
        })
        
    except Exception as e:
        logger.error(f"Error fetching previous jobs: {str(e)}")
        return jsonify({
            'error': f"Error fetching previous jobs: {str(e)}"
        }), 500

if __name__ == '__main__':
    # Start the Flask app
    app.run(host='0.0.0.0', port=5001, debug=False, threaded=True)
