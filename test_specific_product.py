from config.config import Config
#!/usr/bin/env python3
"""
Test script for the Repricing Service with a specific product.

This script tests the repricing service by repricing a specific product
for a specific user.

Usage:
    python test_specific_product.py [--url URL] [--api-key API_KEY] [--username USERNAME] [--product-id PRODUCT_ID]

Options:
    --url URL          The URL of the repricing service [default: https://webhooks.tcgsync.com]
    --api-key API_KEY  The API key for the repricing service [default: savyQYfxgyzdzZNUvk59PAajTKTSUbzu]
    --username USERNAME The username to test repricing with [default: admintcg]
    --product-id PRODUCT_ID The product ID to test repricing with [default: 87]
"""

import argparse
import sys
import time
import json
import requests
from datetime import datetime

try:
    from colorama import init, Fore, Style
    init()  # Initialize colorama
except ImportError:
    print("Required packages not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "colorama"])
    from colorama import init, Fore, Style
    init()  # Initialize colorama

def print_success(message):
    """Print a success message in green."""
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

def print_error(message):
    """Print an error message in red."""
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

def print_info(message):
    """Print an info message in blue."""
    print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")

def print_warning(message):
    """Print a warning message in yellow."""
    print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

def print_header(message):
    """Print a header message in cyan."""
    print(f"\n{Fore.CYAN}=== {message} ==={Style.RESET_ALL}")

def print_json(data):
    """Print JSON data in a formatted way."""
    print(json.dumps(data, indent=2))

def test_specific_product(url, api_key, username, product_id):
    """Test repricing a specific product."""
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }

    # Test health endpoint
    print_header("Testing Health Endpoint")
    try:
        health_url = f"{url}/api/health"
        print_info(f"GET {health_url}")
        response = requests.get(health_url, headers=headers)
        
        if response.status_code == 200:
            print_success(f"Health check successful (Status: {response.status_code})")
            print_info("Raw response content:")
            print(response.text)
            
            try:
                json_data = response.json()
                print_info("Parsed JSON response:")
                print_json(json_data)
            except json.JSONDecodeError:
                print_warning("Response is not valid JSON. This might be expected if the server is returning HTML or plain text.")
        else:
            print_error(f"Health check failed (Status: {response.status_code})")
            print(response.text)
            return
    except Exception as e:
        print_error(f"Error testing health endpoint: {str(e)}")
        print_info("Continuing with other tests...")

    # Start repricing job
    print_header(f"Starting Repricing Job for Product ID {product_id} and User {username}")
    try:
        reprice_url = f"{url}/api/reprice"
        data = {
            'username': username,
            'product_id': product_id
        }
        print_info(f"POST {reprice_url}")
        print_info(f"Data: {data}")
        
        response = requests.post(reprice_url, headers=headers, json=data)
        
        if response.status_code in [200, 201]:
            print_success(f"Start repricing successful (Status: {response.status_code})")
            result = response.json()
            print_json(result)
            
            if 'job_id' in result:
                job_id = result['job_id']
                print_success(f"Job ID: {job_id}")
            else:
                print_error("No job ID returned")
                return
        else:
            print_error(f"Start repricing failed (Status: {response.status_code})")
            print(response.text)
            return
    except Exception as e:
        print_error(f"Error starting repricing job: {str(e)}")
        return

    # Check job status
    print_header("Checking Job Status")
    try:
        status_url = f"{url}/api/status/{job_id}"
        print_info(f"GET {status_url}")
        
        # Check status multiple times to see progress
        for i in range(10):
            response = requests.get(status_url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status', 'unknown')
                print_success(f"Check #{i+1}: Status is '{status}' (Status code: {response.status_code})")
                print_json(result)
                
                # If the job is complete or has an error, stop checking
                if status in ['complete', 'error']:
                    break
            else:
                print_error(f"Check status failed (Status: {response.status_code})")
                print(response.text)
                break
                
            # Wait before checking again
            if i < 9:  # Don't wait after the last check
                print_info("Waiting 2 seconds before checking again...")
                time.sleep(2)
    except Exception as e:
        print_error(f"Error checking job status: {str(e)}")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test the Repricing Service with a specific product.')
    parser.add_argument('--url', default='https://webhooks.tcgsync.com',
                        help='The URL of the repricing service')
    parser.add_argument('--api-key', default='savyQYfxgyzdzZNUvk59PAajTKTSUbzu',
                        help='The API key for the repricing service')
    parser.add_argument('--username', default='admintcg',
                        help='The username to test repricing with')
    parser.add_argument('--product-id', type=int, default=87,
                        help='The product ID to test repricing with')
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    print_header("Starting Specific Product Repricing Test")
    print_info(f"Base URL: {args.url}")
    print_info(f"API Key: {'*' * len(args.api_key)}")
    print_info(f"Username: {args.username}")
    print_info(f"Product ID: {args.product_id}")
    print_info(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_specific_product(args.url, args.api_key, args.username, args.product_id)

if __name__ == '__main__':
    main()

