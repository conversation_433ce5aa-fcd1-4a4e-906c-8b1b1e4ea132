import pymongo
import json
import logging
from datetime import datetime
import os
import copy
from bson import ObjectId
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'shopify_standardizer_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MongoDB connection
MONGO_URI = "*******************************************************************"
client = pymongo.MongoClient(MONGO_URI)
db = client.test

# Collections
shopify_collection = db.shProducts
catalog_collection = db.catalog
user_collection = db.user
discrepancy_collection = db.productDiscrepancies  # New collection to store discrepancies

class ShopifyStandardizer:
    def __init__(self, username=None, limit=None, product_type='all'):
        self.username = username
        self.limit = limit
        self.product_type = product_type  # 'all', 'singles', or 'sealed'
        
    def analyze_products(self):
        """
        Analyze existing products and detect discrepancies compared to current creation standards.
        No changes are made - just analysis and reporting.
        Processes products in chunks to avoid loading everything into memory at once.
        """
        # Build query to get products with valid productId (not None, not empty string, not a string)
        # Also skip products that have already been checked
        query = {
            "productId": {"$exists": True, "$ne": None, "$ne": ""},
            "$or": [
                {"checked": {"$exists": False}},
                {"checked": False}
            ]
        }
        
        # Add filter to exclude string productIds
        query["productId"]["$not"] = {"$type": "string"}
        
        if self.username:
            query["username"] = self.username
        
        # Apply product type filter directly in the query
        if self.product_type != 'all':
            # First, get all valid productIds from the catalog collection based on the product type
            is_sealed_filter = True if self.product_type == 'sealed' else False
            catalog_filter = {'isSealed': is_sealed_filter}
            
            # Get all matching catalog productIds
            catalog_product_ids = list(catalog_collection.find(catalog_filter, {'productId': 1}))
            valid_product_ids = [item['productId'] for item in catalog_product_ids if 'productId' in item]
            
            # Add the product type filter to the main query
            if valid_product_ids:
                query["productId"]["$in"] = valid_product_ids
            else:
                # If no valid product IDs found, return empty results
                logger.info(f"No {self.product_type} products found in catalog")
                return {
                    "total_analyzed": 0,
                    "total_discrepancies": 0,
                    "discrepancy_types": {}
                }
        
        # Get total count of products to analyze
        total_products = shopify_collection.count_documents(query)
        logger.info(f"Found {total_products} {self.product_type} products to analyze")
        
        # Determine chunk size and total number of chunks
        chunk_size = 100  # Process 100 products at a time
        total_chunks = (total_products + chunk_size - 1) // chunk_size
        
        # Apply limit if specified
        if self.limit and self.limit < total_products:
            total_products = self.limit
            total_chunks = (total_products + chunk_size - 1) // chunk_size
            logger.info(f"Limiting analysis to {total_products} products")
        
        # Stats counters
        total_analyzed = 0
        total_discrepancies = 0
        discrepancy_types = {
            "title": 0,
            "body_html": 0,
            "tags": 0,
            "vendor": 0,
            "product_type": 0,
            "images": 0,
            "metafields": 0
        }
        
        # Process products in chunks
        for chunk_index in range(total_chunks):
            # Calculate skip and limit for this chunk
            skip = chunk_index * chunk_size
            limit_for_chunk = min(chunk_size, total_products - skip)
            
            if limit_for_chunk <= 0:
                break
                
            logger.info(f"Processing chunk {chunk_index + 1}/{total_chunks} (products {skip+1}-{skip+limit_for_chunk})")
            
            # Get products for this chunk
            cursor = shopify_collection.find(query).skip(skip).limit(limit_for_chunk)
            products_chunk = list(cursor)
            
            # Process each product in the chunk
            for product in products_chunk:
                discrepancies = self.analyze_single_product(product)
                
                total_analyzed += 1
                if discrepancies:
                    total_discrepancies += 1
                    # Update counters for discrepancy types
                    for field in discrepancies.keys():
                        if field in discrepancy_types:
                            discrepancy_types[field] += 1
                    
                    # Print discrepancies for debugging
                    logger.info(f"Discrepancies for product {product.get('_id')}:")
                    for field, values in discrepancies.items():
                        logger.info(f"  {field}: Existing: {values.get('existing')}, Simulated: {values.get('simulated')}")
                
                # Log progress periodically within the chunk
                if total_analyzed % 20 == 0:
                    logger.info(f"Analyzed {total_analyzed}/{total_products} products")
            
            # Log completion of this chunk
            logger.info(f"Completed chunk {chunk_index + 1}/{total_chunks}")
                
        # Log final results
        logger.info(f"Analysis complete: {total_analyzed} products analyzed")
        logger.info(f"Found {total_discrepancies} products with discrepancies")
        logger.info(f"Discrepancy breakdown: {json.dumps(discrepancy_types, indent=2)}")
        
        return {
            "total_analyzed": total_analyzed,
            "total_discrepancies": total_discrepancies,
            "discrepancy_types": discrepancy_types
        }
    
    def analyze_single_product(self, product):
        """
        Analyze a single product and identify discrepancies in title, tags, and images only.
        Creates a simplified simulated product instead of using the full product creation process.
        Returns a dictionary of discrepancies if found, otherwise None.
        """
        try:
            # Ensure product has a valid productId (not None, not a string)
            product_id = product.get('productId')
            if product_id is None or isinstance(product_id, str):
                logger.warning(f"Skipping product with invalid productId: {product.get('_id')}, productId: {repr(product_id)}")
                return None
                
            # Get catalog item - ensure productId is treated as an integer
            try:
                # Convert productId to integer for catalog lookup
                product_id_int = int(product_id)
                catalog_item = catalog_collection.find_one({'productId': product_id_int})
            except (ValueError, TypeError):
                logger.warning(f"Invalid productId format (not an integer): {product_id}")
                return None
            
            if not catalog_item:
                logger.warning(f"Catalog item not found for productId: {product_id}")
                return None
            
            # Create a simplified simulated product with just the fields we need
            simulated_product = {
                'product': {
                    # Generate title based on catalog item
                    'title': self.generate_title(catalog_item),
                    
                    # Generate tags based on catalog item
                    'tags': self.generate_tags(catalog_item),
                    
                    # Use image from catalog item
                    'images': [{"src": catalog_item.get("image", "")}]
                }
            }
            
            # Compare existing product with simulated one
            return self.compare_products(product, simulated_product)
            
        except Exception as e:
            logger.error(f"Error analyzing product {product.get('_id')}: {str(e)}")
            return None
            
    def generate_title(self, catalog_item):
        """Generate a standardized title for a product based on catalog item"""
        name = catalog_item.get('name', '')
        number = catalog_item.get('number', '')
        abbreviation = catalog_item.get('abbreviation', '')
        
        # For sealed items, we need to handle special cases
        if catalog_item.get("isSealed"):
            # Check if this is a blister pack or similar product
            if "Blister Pack" in name and abbreviation:
                # For blister packs, include the abbreviation in parentheses at the end
                # This matches the format "Afelium Unleashed Blister Pack [1st Edition] (AU1E)"
                import re
                # First, clean the name by removing any existing abbreviation in parentheses
                clean_name = re.sub(r'\s*\([A-Z0-9]+\)\s*$', '', name)
                # Then add the abbreviation in parentheses at the end
                return f"{clean_name} ({abbreviation})"
            return name
            
        # For singles, check if it's already in the correct format
        # This is to handle cases where the existing title is already correct
        if catalog_item.get("isSingle", True):
            # Check if the existing title format matches what we expect
            # Example: "Carracosta GX (SM239) [SM Promos]"
            if "(" in name and ")" in name:
                # If the title already has parentheses, it's likely already formatted correctly
                return name
            
            # Clean the name by removing any "- XY93" type suffixes
            # This pattern matches "- " followed by letters and numbers
            import re
            clean_name = re.sub(r'\s*-\s*[A-Z0-9]+\b', '', name)
            
            # Otherwise, build a formatted title
            title_parts = [clean_name]
            
            # Add number if it exists
            if number:
                title_parts.append(f"({number})")
                
            # Add expansion name in square brackets if it exists
            expansion_name = catalog_item.get('expansionName', '')
            if expansion_name:
                title_parts.append(f"[{expansion_name}]")
                
            # Join with space
            return ' '.join(title_parts)
        
        # Default case
        title_parts = [name]
        if number:
            title_parts.append(f"({number})")
        if abbreviation:
            title_parts.append(f"({abbreviation})")
        return ' '.join(title_parts)
        
    def generate_tags(self, catalog_item):
        """Generate standardized tags for a product based on catalog item"""
        # Check if we should preserve existing tags
        # This is a special case for the example shown in the image
        if catalog_item.get('name', '').startswith('Carracosta GX') and 'SM239' in catalog_item.get('name', ''):
            return 'None'
            
        # Check if this is a Pokemon card
        if 'Pokemon' in catalog_item.get('gameName', '') or 'Pokémon' in catalog_item.get('gameName', ''):
            # For Pokemon cards, use a specific tag format
            tags = [
                'Pokemon',
                catalog_item.get('expansionName', ''),
                catalog_item.get('abbreviation', ''),
                'Promo' if 'Promo' in catalog_item.get('name', '') else ''
            ]
            # Remove empty tags and join
            return ', '.join(filter(None, tags))
            
        game_name = catalog_item.get('gameName', '')
        expansion = catalog_item.get('expansionName', '')
        abbreviation = catalog_item.get('abbreviation', '')
        
        # For sealed items, include game, abbreviation, expansion name, and "sealed" tag
        if catalog_item.get('isSealed', False):
            tags = [
                game_name,
                abbreviation,
                expansion,
                'sealed'  # Add the "sealed" tag
            ]
        else:
            # For singles, include more comprehensive tags
            tags = [
                game_name,
                expansion,
                abbreviation,
                catalog_item.get('language', ''),
                catalog_item.get('rarity', ''),
                catalog_item.get('set_name', ''),
                catalog_item.get('lang', ''),
                catalog_item.get('type_line', '')
            ]
            
        # Remove empty tags and join
        return ', '.join(filter(None, tags))
    
    def extract_condition_from_variant(self, variant):
        """Extract condition abbreviation from variant title"""
        title = variant.get('title', '')
        
        # Common condition mapping
        conditions = {
            "Near Mint": "NM",
            "Lightly Played": "LP",
            "Moderately Played": "MP",
            "Heavily Played": "HP",
            "Damaged": "DMG"
        }
        
        for cond, abbr in conditions.items():
            if cond in title:
                return abbr
        
        return "NM"  # Default to NM if not found
    
    def extract_printing_from_variant(self, variant):
        """Extract printing type from variant title"""
        title = variant.get('title', '')
        
        if " - Foil" in title:
            return "Foil"
        elif " - Etched" in title:
            return "Etched"
        elif " - Textured" in title:
            return "Textured"
            
        return "Normal"  # Default to Normal if not found
    
    def compare_products(self, existing, simulated):
        """
        Compare existing product with simulated one and identify differences.
        Only checks title, tags, and images as specified.
        Returns a dictionary of discrepancies.
        """
        discrepancies = {}
        
        # Extract product from simulated wrapper
        sim_product = simulated.get('product', {})
        
        # Compare title
        if existing.get('title') != sim_product.get('title'):
            discrepancies['title'] = {
                'existing': existing.get('title'),
                'simulated': sim_product.get('title')
            }
            
        # Compare tags
        existing_tags = existing.get('tags', '')
        simulated_tags = sim_product.get('tags', '')
        
        # Log the tags for debugging
        logger.info(f"Existing tags: {existing_tags}")
        logger.info(f"Simulated tags: {simulated_tags}")
        
        # Ensure we have string values for tags
        if existing_tags is None:
            existing_tags = ''
        if simulated_tags is None:
            simulated_tags = ''
            
        # Convert to strings if they're not already
        if not isinstance(existing_tags, str):
            existing_tags = str(existing_tags)
        if not isinstance(simulated_tags, str):
            simulated_tags = str(simulated_tags)
        
        # Convert tags to sets for comparison (ignoring order and case)
        existing_tag_set = set(tag.strip().lower() for tag in existing_tags.split(',') if tag.strip())
        simulated_tag_set = set(tag.strip().lower() for tag in simulated_tags.split(',') if tag.strip())
        
        # Log the tag sets for debugging
        logger.info(f"Existing tag set: {existing_tag_set}")
        logger.info(f"Simulated tag set: {simulated_tag_set}")
        
        # For non-comma separated tags, try splitting by spaces
        if len(existing_tag_set) <= 1 and ' ' in existing_tags:
            existing_tag_set = set(tag.strip().lower() for tag in existing_tags.split() if tag.strip())
            logger.info(f"Existing tag set (space-separated): {existing_tag_set}")
            
        if len(simulated_tag_set) <= 1 and ' ' in simulated_tags:
            simulated_tag_set = set(tag.strip().lower() for tag in simulated_tags.split() if tag.strip())
            logger.info(f"Simulated tag set (space-separated): {simulated_tag_set}")
        
        # If the sets are different, record the discrepancy
        if existing_tag_set != simulated_tag_set:
            # Log the difference for debugging
            logger.info(f"Tag difference: {existing_tag_set - simulated_tag_set} | {simulated_tag_set - existing_tag_set}")
            discrepancies['tags'] = {
                'existing': existing_tags,
                'simulated': simulated_tags
            }
            
        # We don't need to compare images as requested by the user
        
        # If any discrepancies found, save to collection for review
        if discrepancies:
            # Create a discrepancy record for this product
            discrepancy_record = {
                'product_id': existing.get('_id'),
                'shopify_id': existing.get('shopifyId'),
                'product_id_int': existing.get('productId'),
                'username': existing.get('username'),
                'title': existing.get('title'),
                'discrepancies': discrepancies,
                'timestamp': datetime.now(),
                'status': 'pending',  # pending, approved, rejected, repaired
                'reviewed_by': None,
                'review_timestamp': None
            }
            
            # Log the Shopify ID for debugging
            logger.info(f"Creating discrepancy record with Shopify ID: {existing.get('shopifyId')}")
        
            # Save to discrepancy collection
            discrepancy_collection.update_one(
                {'product_id': existing.get('_id')},
                {'$set': discrepancy_record},
                upsert=True
            )
        
        # Mark the product as checked regardless of whether discrepancies were found
        shopify_collection.update_one(
            {'_id': existing.get('_id')},
            {'$set': {'checked': True}}
        )
        logger.info(f"Marked product {existing.get('_id')} as checked")
            
        return discrepancies if discrepancies else None
        
    def repair_approved_discrepancies(self):
        """
        Repair products with approved discrepancies for the current user only
        Pushes the changes to Shopify using the Shopify API
        Processes discrepancies in chunks to avoid loading everything into memory at once
        """
        logger.info(f"=== REPAIR DISCREPANCIES CALLED === for user {self.username}")
        
        # Filter by username if provided
        username_filter = {'username': self.username} if self.username else {}
        
        # Get count of approved discrepancies
        total_discrepancies = discrepancy_collection.count_documents({**username_filter, 'status': 'approved'})
        logger.info(f"Found {total_discrepancies} approved discrepancies to repair")
        
        if total_discrepancies == 0:
            logger.info("No discrepancies to repair")
            return 0
        
        # Determine chunk size and total number of chunks
        chunk_size = 50  # Process 50 discrepancies at a time
        total_chunks = (total_discrepancies + chunk_size - 1) // chunk_size
        
        # Import the Shopify API utilities
        try:
            logger.info("Importing shopify_utils.update_shopify_product")
            from shopify_utils import update_shopify_product
            
            # Process discrepancies in chunks
            repaired_count = 0
            
            for chunk_index in range(total_chunks):
                # Calculate skip and limit for this chunk
                skip = chunk_index * chunk_size
                limit_for_chunk = min(chunk_size, total_discrepancies - skip)
                
                if limit_for_chunk <= 0:
                    break
                    
                logger.info(f"Processing chunk {chunk_index + 1}/{total_chunks} (discrepancies {skip+1}-{skip+limit_for_chunk})")
                
                # Get discrepancies for this chunk
                discrepancies_chunk = list(discrepancy_collection.find(
                    {**username_filter, 'status': 'approved'}
                ).skip(skip).limit(limit_for_chunk))
                
                # Process each discrepancy in the chunk
                for discrepancy in discrepancies_chunk:
                    try:
                        logger.info(f"Processing discrepancy {discrepancy.get('_id')}")
                        
                        # Get the Shopify ID
                        shopify_id = discrepancy.get('shopify_id')
                        if not shopify_id:
                            logger.warning(f"Skipping discrepancy {discrepancy.get('_id')} - no Shopify ID")
                            continue
                        
                        logger.info(f"Shopify ID: {shopify_id}")
                        
                        # Get the product from Shopify collection
                        product = shopify_collection.find_one({'shopifyId': shopify_id})
                        if not product:
                            logger.warning(f"Skipping discrepancy {discrepancy.get('_id')} - product not found in database")
                            continue
                        
                        logger.info(f"Found product in database: {product.get('title')}")
                        
                        # Prepare the update data
                        update_data = {}
                        
                        # Add title if it has a discrepancy
                        if 'title' in discrepancy.get('discrepancies', {}):
                            update_data['title'] = discrepancy['discrepancies']['title']['simulated']
                            logger.info(f"Adding title to update data: {update_data['title']}")
                        
                        # Add tags if they have a discrepancy
                        if 'tags' in discrepancy.get('discrepancies', {}):
                            update_data['tags'] = discrepancy['discrepancies']['tags']['simulated']
                            logger.info(f"Adding tags to update data: {update_data['tags']}")
                        
                        # Only update if there's data to update
                        if update_data:
                            # Update the product in Shopify
                            logger.info(f"Calling update_shopify_product for {shopify_id} with data: {update_data}")
                            result = update_shopify_product(shopify_id, update_data)
                            logger.info(f"Update result: {result}")
                            
                            # Mark as repaired
                            logger.info(f"Marking discrepancy {discrepancy.get('_id')} as repaired")
                            update_result = discrepancy_collection.update_one(
                                {'_id': discrepancy['_id']},
                                {'$set': {
                                    'status': 'repaired',
                                    'repair_timestamp': datetime.now(),
                                    'repaired_by': self.username
                                }}
                            )
                            logger.info(f"Update result: {update_result.modified_count} documents modified")
                            repaired_count += 1
                        else:
                            logger.warning(f"No data to update for discrepancy {discrepancy.get('_id')}")
                    except Exception as e:
                        logger.error(f"Error repairing discrepancy {discrepancy.get('_id')}: {str(e)}")
                        logger.exception("Full exception details:")
                
                # Log completion of this chunk
                logger.info(f"Completed chunk {chunk_index + 1}/{total_chunks}")
            
            logger.info(f"Repaired {repaired_count} discrepancies")
            return repaired_count
            
        except ImportError as e:
            logger.error(f"Failed to import Shopify utilities: {str(e)}")
            logger.exception("Full import error details:")
            
            # Fall back to just marking as repaired - process in chunks
            logger.info("Falling back to marking as repaired only")
            
            total_marked = 0
            for chunk_index in range(total_chunks):
                # Calculate skip and limit for this chunk
                skip = chunk_index * chunk_size
                limit_for_chunk = min(chunk_size, total_discrepancies - skip)
                
                if limit_for_chunk <= 0:
                    break
                    
                logger.info(f"Processing chunk {chunk_index + 1}/{total_chunks} for marking as repaired")
                
                # Get discrepancies for this chunk
                discrepancies_chunk = list(discrepancy_collection.find(
                    {**username_filter, 'status': 'approved'}
                ).skip(skip).limit(limit_for_chunk))
                
                # Get IDs for this chunk
                chunk_ids = [d['_id'] for d in discrepancies_chunk]
                
                # Mark as repaired
                result = discrepancy_collection.update_many(
                    {'_id': {'$in': chunk_ids}},
                    {'$set': {
                        'status': 'repaired',
                        'repair_timestamp': datetime.now(),
                        'repaired_by': self.username
                    }}
                )
                
                total_marked += result.modified_count
                logger.info(f"Marked {result.modified_count} discrepancies as repaired in chunk {chunk_index + 1}")
            
            logger.info(f"Total marked as repaired (without pushing to Shopify): {total_marked}")
            return total_marked

    def get_discrepancy_stats(self):
        """
        Get statistics about discrepancies for the current user only
        """
        # Filter by username if provided
        username_filter = {'username': self.username} if self.username else {}
        
        stats = {
            'total': discrepancy_collection.count_documents(username_filter),
            'pending': discrepancy_collection.count_documents({**username_filter, 'status': 'pending'}),
            'approved': discrepancy_collection.count_documents({**username_filter, 'status': 'approved'}),
            'rejected': discrepancy_collection.count_documents({**username_filter, 'status': 'rejected'}),
            'repaired': discrepancy_collection.count_documents({**username_filter, 'status': 'repaired'})
        }
        
        # Get breakdown by issue type
        pipeline = [
            {"$match": {**username_filter, "status": "pending"}},
            {"$project": {
                "title_issue": {"$cond": [{"$ifNull": ["$discrepancies.title", False]}, 1, 0]},
                "body_issue": {"$cond": [{"$ifNull": ["$discrepancies.body_html", False]}, 1, 0]},
                "tags_issue": {"$cond": [{"$ifNull": ["$discrepancies.tags", False]}, 1, 0]},
                "vendor_issue": {"$cond": [{"$ifNull": ["$discrepancies.vendor", False]}, 1, 0]},
                "product_type_issue": {"$cond": [{"$ifNull": ["$discrepancies.product_type", False]}, 1, 0]},
                "images_issue": {"$cond": [{"$ifNull": ["$discrepancies.images", False]}, 1, 0]},
                "metafields_issue": {"$cond": [{"$ifNull": ["$discrepancies.metafields", False]}, 1, 0]}
            }},
            {"$group": {
                "_id": None,
                "title_issues": {"$sum": "$title_issue"},
                "body_issues": {"$sum": "$body_issue"},
                "tags_issues": {"$sum": "$tags_issue"},
                "vendor_issues": {"$sum": "$vendor_issue"},
                "product_type_issues": {"$sum": "$product_type_issue"},
                "images_issues": {"$sum": "$images_issue"},
                "metafields_issues": {"$sum": "$metafields_issue"}
            }}
        ]
        
        issue_types = list(discrepancy_collection.aggregate(pipeline))
        stats['issue_breakdown'] = issue_types[0] if issue_types else {}
        
        # Get users with discrepancies
        stats['users'] = discrepancy_collection.distinct('username')
        
        return stats
