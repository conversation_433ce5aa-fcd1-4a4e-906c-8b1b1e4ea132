from config.config import Config
import logging
from typing import List, Dict, Any
from datetime import datetime
from pymongo import UpdateOne, InsertOne
from pymongo.errors import BulkWriteError
import time
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebhookProcessor:
    def __init__(self, db=None):
        if db is None:
            from utils.webhook_db import mongo
            db = mongo.db
        self.db = db
        self.shProducts_collection = self.db['shProducts']
        self.shOrders_collection = self.db['shOrders']
        self.shCustomers_collection = self.db['shCustomers']
        self.staged_inventory_collection = self.db['staged']
        self.inventory_collection = self.db['inventory']
        self.catalog_collection = self.db['catalog']
        
        # Cache for usernames
        self._username_cache = {}
        self._cache_expiry = {}
        self._cache_ttl = 3600  # 1 hour cache TTL
        
        # Ensure indexes
        self._ensure_indexes()
        
    def _ensure_indexes(self):
        """Ensure required indexes exist"""
        try:
            # Products indexes
            self.shProducts_collection.create_index([
                ('username', 1),
                ('productId', 1)
            ], unique=True, background=True)
            
            # Orders indexes
            self.shOrders_collection.create_index([
                ('username', 1),
                ('orderId', 1)
            ], unique=True, background=True)
            
            # Customers indexes
            self.shCustomers_collection.create_index([
                ('username', 1),
                ('id', 1)
            ], unique=True, background=True)
            
            # Add email index for customer duplicate detection
            self.shCustomers_collection.create_index([
                ('username', 1),
                ('email', 1)
            ], background=True)
            
            # User collection index
            self.db.user.create_index('shopifyStoreName', unique=True, background=True)
            
        except Exception as e:
            logger.error(f"Error ensuring indexes: {str(e)}")
        
    def process_batch(self, webhooks: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        Process a batch of webhooks using bulk operations
        """
        stats = {
            'products': 0,
            'orders': 0,
            'customers': 0,
            'errors': 0
        }
        
        # Group webhooks by type
        product_webhooks = []
        order_webhooks = []
        customer_webhooks = []
        
        for webhook in webhooks:
            topic = webhook.get('topic')
            if topic in ['products/create', 'products/update']:
                product_webhooks.append(webhook)
            elif topic in ['orders/create', 'orders/updated']:
                order_webhooks.append(webhook)
            elif topic in ['customers/create', 'customers/update']:
                customer_webhooks.append(webhook)
                
        # Process each group in bulk
        if product_webhooks:
            stats['products'] = self._process_product_webhooks(product_webhooks)
        if order_webhooks:
            stats['orders'] = self._process_order_webhooks(order_webhooks)
        if customer_webhooks:
            stats['customers'] = self._process_customer_webhooks(customer_webhooks)
            
        return stats
        
    def _process_product_webhooks(self, webhooks: List[Dict[str, Any]]) -> int:
        """
        Process product webhooks in bulk
        """
        try:
            bulk_operations = []
            
            for webhook in webhooks:
                try:
                    data = webhook.get('data', {})
                    username = self._get_username_from_webhook(webhook)
                    
                    if not username or not data:
                        continue
                        
                    product_id = str(data.get('id'))
                    variants = data.get('variants', [])
                    
                    # Extract productId from HTML content
                    html_content = data.get('body_html', '') or data.get('description_html', '')
                    extracted_product_id = None
                    if html_content:
                        match = re.search(r'data-tcgid="(\d+)"', html_content)
                        if match:
                            extracted_product_id = match.group(1)
                            try:
                                extracted_product_id_int = int(extracted_product_id)
                            except (ValueError, TypeError):
                                logger.warning(f"Invalid TCG product ID format: {extracted_product_id}")
                                extracted_product_id = None
                    
                    # Prepare update data
                    update_data = {
                        'username': username,
                        'productId': product_id,
                        'title': data.get('title'),
                        'variants': [{
                            'skuId': variant.get('sku'),
                            'inventory_item_id': str(variant.get('inventory_item_id')),
                            'quantity': variant.get('inventory_quantity', 0)
                        } for variant in variants],
                        'last_updated': datetime.utcnow()
                    }
                    
                    # Add TCG product ID and matching flags if found
                    if extracted_product_id:
                        update_data['extractedProductId'] = extracted_product_id
                        update_data['tcgItem'] = True
                        update_data['needsMatching'] = True
                        
                        # Look up catalog details
                        try:
                            catalog_record = self.catalog_collection.find_one(
                                {"productId": extracted_product_id_int}
                            )
                            if catalog_record:
                                # Add catalog details to update data
                                catalog_fields = {
                                    "abbreviation": catalog_record.get("abbreviation"),
                                    "expansionName": catalog_record.get("expansionName"),
                                    "rarity": catalog_record.get("rarity"),
                                    "number": catalog_record.get("number"),
                                    "groupId": catalog_record.get("groupId"),
                                    "gameName": catalog_record.get("gameName"),
                                    "categoryId": catalog_record.get("categoryId")
                                }
                                update_data.update(catalog_fields)
                                logger.info(f"Added catalog details for product {product_id}")
                            else:
                                logger.warning(f"No catalog record found for TCG ID: {extracted_product_id}")
                        except Exception as e:
                            logger.error(f"Error looking up catalog details: {str(e)}")
                    else:
                        update_data['tcgItem'] = False
                        update_data['needsMatching'] = False
                        logger.warning(f"No TCG product ID found in webhook for product {product_id}")
                    
                    # Add additional product data
                    if data.get('body_html'):
                        update_data['body_html'] = data.get('body_html')
                    if data.get('vendor'):
                        update_data['vendor'] = data.get('vendor')
                    if data.get('product_type'):
                        update_data['product_type'] = data.get('product_type')
                    if data.get('tags'):
                        update_data['tags'] = data.get('tags')
                    if data.get('images') and len(data.get('images', [])) > 0:
                        update_data['image'] = data.get('images')[0].get('src')
                    
                    # Create bulk operation with index hint
                    bulk_operations.append(
                        UpdateOne(
                            {
                                'username': username,
                                'productId': product_id
                            },
                            {
                                '$set': update_data
                            },
                            upsert=True,
                            hint=[('username', 1), ('productId', 1)]
                        )
                    )
                    
                except Exception as e:
                    logger.error(f"Error processing product webhook: {str(e)}")
                    continue
            
            # Execute bulk operation
            if bulk_operations:
                result = self.shProducts_collection.bulk_write(bulk_operations, ordered=False)
                return result.modified_count + result.upserted_count
                
            return 0
            
        except BulkWriteError as bwe:
            logger.error(f"Bulk write error: {str(bwe.details)}")
            return len(webhooks) - len(bwe.details['writeErrors'])
        except Exception as e:
            logger.error(f"Error in bulk product processing: {str(e)}")
            return 0
            
    def _process_order_webhooks(self, webhooks: List[Dict[str, Any]]) -> int:
        """
        Process order webhooks in bulk
        """
        try:
            bulk_operations = []
            
            for webhook in webhooks:
                try:
                    data = webhook.get('data', {})
                    username = self._get_username_from_webhook(webhook)
                    
                    if not username or not data:
                        continue
                        
                    order_id = str(data.get('id'))
                    
                    # Prepare update data
                    update_data = {
                        'username': username,
                        'orderId': order_id,
                        'orderNumber': data.get('order_number'),
                        'totalPrice': data.get('total_price'),
                        'createdAt': data.get('created_at'),
                        'updatedAt': data.get('updated_at'),
                        'status': data.get('financial_status'),
                        'items': [{
                            'productId': str(item.get('product_id')),
                            'variantId': str(item.get('variant_id')),
                            'quantity': item.get('quantity'),
                            'price': item.get('price')
                        } for item in data.get('line_items', [])],
                        'last_updated': datetime.utcnow()
                    }
                    
                    # Create bulk operation with index hint
                    bulk_operations.append(
                        UpdateOne(
                            {
                                'username': username,
                                'orderId': order_id
                            },
                            {
                                '$set': update_data
                            },
                            upsert=True,
                            hint=[('username', 1), ('orderId', 1)]
                        )
                    )
                    
                except Exception as e:
                    logger.error(f"Error processing order webhook: {str(e)}")
                    continue
            
            # Execute bulk operation
            if bulk_operations:
                result = self.shOrders_collection.bulk_write(bulk_operations, ordered=False)
                return result.modified_count + result.upserted_count
                
            return 0
            
        except BulkWriteError as bwe:
            logger.error(f"Bulk write error: {str(bwe.details)}")
            return len(webhooks) - len(bwe.details['writeErrors'])
        except Exception as e:
            logger.error(f"Error in bulk order processing: {str(e)}")
            return 0
            
    def _process_customer_webhooks(self, webhooks: List[Dict[str, Any]]) -> int:
        """
        Process customer webhooks in bulk
        """
        try:
            bulk_operations = []
            
            for webhook in webhooks:
                try:
                    data = webhook.get('data', {})
                    username = self._get_username_from_webhook(webhook)
                    
                    if not username or not data:
                        continue
                        
                    customer_id = int(data.get('id'))
                    email = data.get('email')
                    
                    # Prepare update data
                    update_data = {
                        'username': username,
                        'id': customer_id,
                        'email': email,
                        'first_name': data.get('first_name'),
                        'last_name': data.get('last_name'),
                        'orders_count': data.get('orders_count', 0),
                        'last_updated': datetime.utcnow()
                    }
                    
                    # Check if customer exists with this email but different ID
                    if email:
                        existing_email_customer = self.shCustomers_collection.find_one({
                            'username': username,
                            'email': email,
                            'id': {'$ne': customer_id}  # Different ID
                        })
                        
                        if existing_email_customer:
                            logger.info(f"Found existing customer with email {email} but different ID. "
                                      f"Updating existing record instead of creating duplicate.")
                            
                            # Update the existing customer record with new data
                            bulk_operations.append(
                                UpdateOne(
                                    {
                                        'username': username,
                                        'email': email
                                    },
                                    {
                                        '$set': update_data
                                    },
                                    hint=[('username', 1), ('email', 1)]
                                )
                            )
                            continue
                    
                    # Normal update by ID if no email duplicate found
                    bulk_operations.append(
                        UpdateOne(
                            {
                                'username': username,
                                'id': customer_id
                            },
                            {
                                '$set': update_data
                            },
                            upsert=True,
                            hint=[('username', 1), ('id', 1)]
                        )
                    )
                    
                except Exception as e:
                    logger.error(f"Error processing customer webhook: {str(e)}")
                    continue
            
            # Execute bulk operation
            if bulk_operations:
                result = self.shCustomers_collection.bulk_write(bulk_operations, ordered=False)
                return result.modified_count + result.upserted_count
                
            return 0
            
        except BulkWriteError as bwe:
            logger.error(f"Bulk write error: {str(bwe.details)}")
            return len(webhooks) - len(bwe.details['writeErrors'])
        except Exception as e:
            logger.error(f"Error in bulk customer processing: {str(e)}")
            return 0
            
    def _get_username_from_webhook(self, webhook: Dict[str, Any]) -> str:
        """
        Extract username from webhook data with caching
        """
        try:
            shop_domain = webhook.get('shop_domain')
            if not shop_domain:
                return None
                
            current_time = time.time()
            
            # Check cache
            if shop_domain in self._username_cache:
                if current_time < self._cache_expiry.get(shop_domain, 0):
                    return self._username_cache[shop_domain]
                else:
                    # Cache expired
                    del self._username_cache[shop_domain]
                    del self._cache_expiry[shop_domain]
            
            # Query database with index hint
            user = self.db.user.find_one(
                {'shopifyStoreName': shop_domain},
                hint=[('shopifyStoreName', 1)]
            )
            
            if user and user.get('username'):
                # Update cache
                self._username_cache[shop_domain] = user['username']
                self._cache_expiry[shop_domain] = current_time + self._cache_ttl
                return user['username']
                
            return None
            
        except Exception as e:
            logger.error(f"Error getting username from webhook: {str(e)}")
            return None
