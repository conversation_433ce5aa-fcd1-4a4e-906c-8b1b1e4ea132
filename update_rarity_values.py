from config.config import Config
from pymongo import MongoClient, UpdateOne
import logging
import sys
import time
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_rarity.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = '*******************************************************************'
mongo_client = MongoClient(
    mongo_uri,
    maxPoolSize=200,
    waitQueueTimeoutMS=5000,
    connectTimeoutMS=5000,
    serverSelectionTimeoutMS=5000,
    retryWrites=True,
    w='majority'
)
db = mongo_client['test']
shopify_collection = db['shProducts']
catalog_collection = db['catalog']
rarities_collection = db['rarities']

def get_rarity_mappings():
    """
    Get all rarity mappings from the rarities collection.
    Returns a dictionary of dictionaries:
    {
        "Magic: The Gathering": {
            "C": "Common",
            "U": "Uncommon",
            ...
        },
        "Pokemon": {
            ...
        }
    }
    """
    mappings = {}
    
    try:
        # Get all rarities documents
        rarities_docs = list(rarities_collection.find({}))
        logger.info(f"Found {len(rarities_docs)} game rarities in the rarities collection")
        
        for doc in rarities_docs:
            game_name = doc.get('gameName')
            if not game_name:
                continue
                
            game_mappings = {}
            for rarity in doc.get('rarities', []):
                db_value = rarity.get('dbValue')
                display_text = rarity.get('displayText')
                
                if db_value and display_text:
                    game_mappings[db_value] = display_text
            
            if game_mappings:
                mappings[game_name] = game_mappings
                logger.info(f"Added {len(game_mappings)} rarity mappings for {game_name}")
        
        return mappings
    except Exception as e:
        logger.error(f"Error getting rarity mappings: {str(e)}")
        return {}

def get_rarity_display_text_from_db(rarities_collection, game_name, rarity_code):
    """
    Look up the display text for a rarity code directly from the rarities collection.
    
    Args:
        rarities_collection: MongoDB rarities collection
        game_name: Game name to match
        rarity_code: Rarity code to look up
        
    Returns:
        str: Display text if found, None otherwise
    """
    try:
        # Find a game that matches or starts with the provided game name
        game_doc = None
        for doc in rarities_collection.find({}):
            db_game_name = doc.get('gameName')
            if db_game_name and (game_name.startswith(db_game_name) or db_game_name.startswith(game_name)):
                game_doc = doc
                break
        
        if not game_doc:
            return None
            
        # Find the matching rarity
        for rarity_info in game_doc.get('rarities', []):
            if rarity_info.get('dbValue') == rarity_code:
                return rarity_info.get('displayText')
        
        return None
    except Exception as e:
        logger.error(f"Error getting rarity display text from DB: {str(e)}")
        return None

def update_collection_rarities(collection, collection_name, rarity_mappings, batch_size=1000, max_workers=10):
    """
    Update rarity values in the specified collection.
    
    Args:
        collection: MongoDB collection to update
        collection_name: Name of the collection (for logging)
        rarity_mappings: Dictionary of rarity mappings by game
        batch_size: Number of documents to process in each batch
        max_workers: Number of worker threads
    """
    try:
        # Count total documents
        total_docs = collection.count_documents({'rarity': {'$exists': True}})
        logger.info(f"Found {total_docs} documents with rarity field in {collection_name}")
        
        if total_docs == 0:
            logger.info(f"No documents to update in {collection_name}")
            return
        
        # Process in batches
        processed = 0
        updated = 0
        skipped = 0
        errors = 0
        
        # Create a pipeline to get all unique game name and rarity combinations
        pipeline = [
            {"$match": {"rarity": {"$exists": True}}},
            {"$group": {"_id": {"gameName": "$gameName", "rarity": "$rarity"}, "count": {"$sum": 1}}},
            {"$sort": {"_id.gameName": 1, "_id.rarity": 1}}
        ]
        
        unique_combinations = list(collection.aggregate(pipeline))
        logger.info(f"Found {len(unique_combinations)} unique game name and rarity combinations in {collection_name}")
        
        # Get reference to rarities collection for direct lookups
        rarities_collection = db['rarities']
        
        # Process each unique combination
        for combo in unique_combinations:
            try:
                game_name = combo['_id'].get('gameName')
                rarity_code = combo['_id'].get('rarity')
                count = combo.get('count', 0)
                
                if not game_name or not rarity_code:
                    logger.warning(f"Skipping combination with missing game name or rarity: {combo}")
                    skipped += count
                    continue
                
                # First try to find the mapping in the static dictionary
                display_text = None
                game_mappings = None
                
                for mapping_game_name, mappings in rarity_mappings.items():
                    if game_name.startswith(mapping_game_name):
                        game_mappings = mappings
                        break
                
                if game_mappings:
                    display_text = game_mappings.get(rarity_code)
                
                # If not found in static mappings, try to look it up directly from the database
                if not display_text:
                    display_text = get_rarity_display_text_from_db(rarities_collection, game_name, rarity_code)
                
                # If we still don't have a display text, skip this combination
                if not display_text:
                    logger.warning(f"No display text found for rarity code '{rarity_code}' in game '{game_name}'")
                    skipped += count
                    continue
                
                # Update all documents with this game name and rarity
                result = collection.update_many(
                    {"gameName": game_name, "rarity": rarity_code},
                    {"$set": {"rarity": display_text}}
                )
                
                updated += result.modified_count
                processed += count
                
                logger.info(f"Updated {result.modified_count} documents for game '{game_name}', rarity '{rarity_code}' -> '{display_text}'")
                
            except Exception as e:
                logger.error(f"Error processing combination {combo}: {str(e)}")
                errors += 1
        
        logger.info(f"Completed updating {collection_name}:")
        logger.info(f"  Processed: {processed}")
        logger.info(f"  Updated: {updated}")
        logger.info(f"  Skipped: {skipped}")
        logger.info(f"  Errors: {errors}")
        
    except Exception as e:
        logger.error(f"Error updating {collection_name}: {str(e)}")

def main():
    try:
        start_time = time.time()
        logger.info("Starting rarity update process")
        
        # Get rarity mappings
        rarity_mappings = get_rarity_mappings()
        if not rarity_mappings:
            logger.error("No rarity mappings found, aborting")
            return
        
        # Update shProducts collection
        logger.info("Updating shProducts collection")
        update_collection_rarities(shopify_collection, "shProducts", rarity_mappings)
        
        # Update catalog collection
        logger.info("Updating catalog collection")
        update_collection_rarities(catalog_collection, "catalog", rarity_mappings)
        
        execution_time = time.time() - start_time
        logger.info(f"Rarity update process completed in {execution_time:.2f} seconds")
        
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
    finally:
        mongo_client.close()

if __name__ == "__main__":
    main()
