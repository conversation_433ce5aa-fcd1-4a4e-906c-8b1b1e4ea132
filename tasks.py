from config.config import Config
import dramatiq
import base64
import os
import re
import requests
import logging
import json
from flask import Flask
from config import Config
from pymongo import MongoClient, ReadPreference
from pymongo.operations import UpdateOne, InsertOne
from bson import ObjectId
from datetime import datetime
import concurrent.futures
from dramatiq_config import broker
from routes.payment_processor import process_card_payment

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# JSON encoder that handles ObjectId
class MongoJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

def mongo_to_json(data):
    """Convert MongoDB data to JSON-serializable format"""
    return json.loads(json.dumps(data, cls=MongoJSONEncoder))

# Create a minimal Flask app
app = Flask(__name__)
app.config.from_object(Config)

# MongoDB connection with connection pooling
client = MongoClient(
    Config.MONGO_URI,
    maxPoolSize=50,
    waitQueueTimeoutMS=5000,
    read_preference=ReadPreference.PRIMARY_PREFERRED
)
db = client[Config.MONGO_DBNAME]
catalog_collection = db.catalog
user_collection = db.user

# Ximilar API configuration
XIMILAR_API_KEY = Config.XIMILAR_API_KEY
XIMILAR_API_ENDPOINT = "https://api.ximilar.com/similarity/custom/v2/visualKNN"
XIMILAR_COLLECTION_ID = "c4355a27-cea0-4344-8524-39bee5881b7b"

# Batch size for parallel processing
BATCH_SIZE = 20
MAX_WORKERS = 10

# Set the broker before defining actors
dramatiq.set_broker(broker)

def extract_product_id(body_html):
    """Extract TCGPlayer ID from product body HTML"""
    if body_html is None:
        return None
    try:
        # Use a more flexible regex pattern and add detailed logging
        match = re.search(r'data-tcgid="([^"]+)"', body_html)
        if match:
            # Log the extracted value for debugging
            raw_value = match.group(1).strip()
            logger.info(f"Extracted raw product ID value: {raw_value}")
            
            try:
                # First try float conversion, then convert to int
                float_val = float(raw_value)
                int_val = int(float_val)
                logger.info(f"Converted value {raw_value} to int {int_val}")
                return int_val
            except (ValueError, TypeError) as e:
                logger.error(f"Failed to convert product ID '{raw_value}' to number: {e}")
                return None
        else:
            logger.debug("No product ID found in HTML")
            return None
    except Exception as e:
        logger.error(f"Error extracting product ID: {e}")
        logger.debug(f"HTML content: {body_html[:200]}...")
        return None

@dramatiq.actor(store_results=True, max_retries=3)
def fetch_all_products(next_url, headers, username):
    """Fetch all Shopify products"""
    try:
        with app.app_context():
            # Delete existing records for this user
            db.shProducts.delete_many({"username": username})
            logger.info(f"Deleted existing records for user {username}")

            user = user_collection.find_one({"username": username})
            if not user:
                raise Exception(f"User not found: {username}")

            response = requests.get(next_url, headers=headers)
            response.raise_for_status()
            data = response.json()

            # Process products in batches
            if 'products' in data:
                batch_operations = []
                for product in data['products']:
                    try:
                        product['username'] = username
                        
                        # Extract and log the body_html content
                        body_html = product.get('body_html')
                        if body_html:
                            logger.debug(f"Product body_html excerpt: {body_html[:100]}...")
                        else:
                            logger.debug("No body_html content found for product")
                        
                        product_id = extract_product_id(body_html)
                        logger.info(f"Extracted product_id: {product_id}")
                        
                        # Add error handling for product ID processing
                        if product_id is not None:
                            try:
                                # Convert to integer
                                product_id_int = int(product_id)
                                
                                # Look up in catalog
                                catalog_record = catalog_collection.find_one({"productId": product_id_int})
                                
                                if catalog_record:
                                    # Update with catalog data
                                    product.update({
                                        "productId": product_id,
                                        "tcgItem": True,
                                        "needsMatching": False,
                                        "abbreviation": catalog_record.get("abbreviation"),
                                        "expansionName": catalog_record.get("expansionName"),
                                        "rarity": catalog_record.get("rarity"),
                                        "number": catalog_record.get("number"),
                                        "groupId": catalog_record.get("groupId"),
                                        "gameName": catalog_record.get("gameName"),
                                        "categoryId": catalog_record.get("categoryId"),
                                        "lastUpdated": datetime.utcnow()
                                    })
                                    logger.info(f"Successfully processed product ID {product_id} and found catalog data")
                                else:
                                    # No catalog data found
                                    product.update({
                                        "productId": product_id,
                                        "tcgItem": True,
                                        "needsMatching": True
                                    })
                                    logger.warning(f"No catalog record found for productId: {product_id}")
                            except (ValueError, TypeError) as e:
                                logger.error(f"Error processing product ID {product_id}: {e}")
                                product['productId'] = None
                                product['tcgItem'] = False
                                product['needsMatching'] = False
                        else:
                            logger.debug(f"No valid product ID found for product {product.get('id')}")
                            product['productId'] = None
                            product['tcgItem'] = False
                            product['needsMatching'] = False
                        
                        # Add to batch operations
                        batch_operations.append(UpdateOne(
                            {"username": username, "productId": product.get('productId')},
                            {"$set": product},
                            upsert=True
                        ))
                    except Exception as e:
                        logger.error(f"Error processing product {product.get('id', 'unknown')}: {str(e)}")
                        continue
                
                if batch_operations:
                    db.shProducts.bulk_write(batch_operations)

            # Handle pagination
            link_header = response.headers.get('Link')
            if link_header and 'next' in link_header:
                next_link = link_header.split(';')[0].strip('<>')
                user_collection.update_one(
                    {"username": username},
                    {"$set": {"lastProductsPaginationLink": next_link}}
                )
                # Queue next batch
                fetch_all_products.send(next_link, headers, username)
            else:
                # Clear pagination link when done
                user_collection.update_one(
                    {"username": username},
                    {"$unset": {"lastProductsPaginationLink": ""}}
                )

            return {"success": True}

    except Exception as e:
        logger.error(f"Error fetching Shopify products: {str(e)}")
        return {"error": str(e)}

@dramatiq.actor(store_results=True, max_retries=3)
def fetch_all_orders(next_url, headers, username):
    """Fetch all Shopify orders"""
    try:
        with app.app_context():
            user = user_collection.find_one({"username": username})
            if not user:
                raise Exception(f"User not found: {username}")

            response = requests.get(next_url, headers=headers)
            response.raise_for_status()
            data = response.json()

            # Process orders in batches
            if 'orders' in data:
                batch_operations = []
                for order in data['orders']:
                    batch_operations.append(UpdateOne(
                        {"id": order['id']},
                        {"$set": order},
                        upsert=True
                    ))
                
                if batch_operations:
                    db.shopify_orders.bulk_write(batch_operations)

            # Handle pagination
            link_header = response.headers.get('Link')
            if link_header and 'next' in link_header:
                next_link = link_header.split(';')[0].strip('<>')
                user_collection.update_one(
                    {"username": username},
                    {"$set": {"lastOrdersPaginationLink": next_link}}
                )
                # Queue next batch
                fetch_all_orders.send(next_link, headers, username)
            else:
                # Clear pagination link when done
                user_collection.update_one(
                    {"username": username},
                    {"$unset": {"lastOrdersPaginationLink": ""}}
                )

            return {"success": True}

    except Exception as e:
        logger.error(f"Error fetching Shopify orders: {str(e)}")
        return {"error": str(e)}

@dramatiq.actor(store_results=True, max_retries=3)
def fetch_all_customers(next_url, headers, username):
    """Fetch all Shopify customers"""
    try:
        with app.app_context():
            user = user_collection.find_one({"username": username})
            if not user:
                raise Exception(f"User not found: {username}")

            response = requests.get(next_url, headers=headers)
            response.raise_for_status()
            data = response.json()

            # Process customers in batches
            if 'customers' in data:
                batch_operations = []
                for customer in data['customers']:
                    batch_operations.append(UpdateOne(
                        {"id": customer['id']},
                        {"$set": customer},
                        upsert=True
                    ))
                
                if batch_operations:
                    db.shopify_customers.bulk_write(batch_operations)

            # Handle pagination
            link_header = response.headers.get('Link')
            if link_header and 'next' in link_header:
                next_link = link_header.split(';')[0].strip('<>')
                user_collection.update_one(
                    {"username": username},
                    {"$set": {"lastCustomersPaginationLink": next_link}}
                )
                # Queue next batch
                fetch_all_customers.send(next_link, headers, username)
            else:
                # Clear pagination link when done
                user_collection.update_one(
                    {"username": username},
                    {"$unset": {"lastCustomersPaginationLink": ""}}
                )

            return {"success": True}

    except Exception as e:
        logger.error(f"Error fetching Shopify customers: {str(e)}")
        return {"error": str(e)}

@dramatiq.actor(store_results=True, max_retries=3)
def process_card_image(image_data, game_name, expansion_name, card_type, condition):
    """Process a single card image using Ximilar API"""
    try:
        with app.app_context():
            # Process with Ximilar API
            headers = {
                "Authorization": f"Token {XIMILAR_API_KEY}",
                "Content-Type": "application/json",
                "collection-id": XIMILAR_COLLECTION_ID
            }
            payload = {
                "from": 0,
                "k": 5,
                "query_record": {
                    "_base64": image_data
                },
                "fields_to_return": ["_id"]
            }

            response = requests.post(XIMILAR_API_ENDPOINT, headers=headers, json=payload)
            response.raise_for_status()
            result = response.json()

            # Process the result
            return process_ximilar_results(result, game_name, expansion_name, card_type, condition)

    except Exception as e:
        logger.error(f"Error processing card image: {str(e)}")
        return {"error": str(e)}

@dramatiq.actor(store_results=True, max_retries=3)
def fetch_tcgplayer_prices_batch(sku_ids):
    """Fetch TCGPlayer prices in batches"""
    try:
        with app.app_context():
            # Get TCGPlayer API key
            tcgplayer_key_doc = db.tcgplayerKey.find_one()
            if not tcgplayer_key_doc or 'latestKey' not in tcgplayer_key_doc:
                raise Exception("TCGPlayer API key not found")
            
            tcgplayer_key = tcgplayer_key_doc['latestKey']
            all_prices = []
            
            # Split into smaller batches for TCGPlayer API
            for i in range(0, len(sku_ids), BATCH_SIZE):
                batch = sku_ids[i:i+BATCH_SIZE]
                url = f"https://api.tcgplayer.com/pricing/sku/{','.join(map(str, batch))}"
                headers = {
                    "Accept": "application/json",
                    "Authorization": f"Bearer {tcgplayer_key}"
                }
                
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                data = response.json()
                
                if 'results' in data:
                    for result in data['results']:
                        price_data = {
                            'skuId': result.get('skuId'),
                            'lowPrice': result.get('lowPrice'),
                            'marketPrice': result.get('marketPrice')
                        }
                        all_prices.append(price_data)
            
            return all_prices
    
    except Exception as e:
        logger.error(f"Error fetching TCGPlayer prices: {str(e)}")
        return []

def process_ximilar_results(result, game_name, expansion_name, card_type, condition):
    """Process Ximilar API results"""
    if 'answer_records' not in result:
        return {"error": "No matching card found"}

    potential_matches = []
    
    for match in result['answer_records']:
        ximilar_id = match.get('_id', 'Unknown')
        card_details = catalog_collection.find_one({"productId": int(ximilar_id)})
        
        if card_details:
            if game_name:
                query = {
                    "name": card_details.get('name'),
                    "number": card_details.get('number'),
                    "gameName": game_name
                }
                if expansion_name:
                    query["expansionName"] = expansion_name
                
                card_variants = list(catalog_collection.find(query))
                potential_matches.extend(card_variants)
            else:
                potential_matches.append(card_details)

    if not potential_matches:
        return {"error": "No matching card found"}

    # Process the matches and return the best result
    return format_card_result(potential_matches[0], card_type, condition)

@dramatiq.actor(store_results=True, max_retries=3)
def process_pos_transaction(transaction_data, username, shopify_access_token, shopify_store_name):
    """
    Process a POS transaction in the background.
    
    This function handles all the heavy lifting of creating Shopify orders,
    updating inventory, and processing payments without blocking the POS UI.
    """
    logger.info(f"Processing POS transaction in background for user {username}")
    try:
        with app.app_context():
            # Extract transaction data
            items = transaction_data.get('items', [])
            total = transaction_data.get('total')
            payment_method = transaction_data.get('payment_method')
            customer_id = transaction_data.get('customer_id')
            gift_card_id = transaction_data.get('gift_card_id')
            gift_card_amount = float(transaction_data.get('gift_card_amount', 0.0))
            use_gift_card = transaction_data.get('use_gift_card', False)
            store_credit_account_id = transaction_data.get('store_credit_account_id')
            store_credit_amount = float(transaction_data.get('store_credit_amount', 0.0))
            use_store_credit = transaction_data.get('use_store_credit', False)
            employee_name = transaction_data.get('employee_name')
            held_sale_id = transaction_data.get('held_sale_id')
            till_id = transaction_data.get('till_id')
            
            # Get till information
            till = db.posSetting.find_one({"_id": ObjectId(till_id), "username": username})
            if not till:
                raise Exception(f"Till not found with ID: {till_id}")
            
            # Find customer if customer_id is provided
            customer = None
            if customer_id:
                # Try different formats to find the customer
                customer = db.shCustomers.find_one({"id": customer_id, "username": username})
                if not customer:
                    try:
                        customer = db.shCustomers.find_one({"id": int(customer_id), "username": username})
                    except (ValueError, TypeError):
                        pass
                if not customer:
                    customer = db.shCustomers.find_one({
                        "id": {"$numberLong": str(customer_id)}, 
                        "username": username
                    })
                
                if not customer:
                    logger.warning(f"Customer not found with ID: {customer_id}")
                    customer = {
                        "id": None,
                        "first_name": "Guest",
                        "last_name": "Customer",
                        "email": None
                    }
            else:
                customer = {
                    "id": None,
                    "first_name": "Guest",
                    "last_name": "Customer",
                    "email": None
                }
            
            # Check if any items are custom
            has_custom_items = any(
                item.get("is_custom") or 
                (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
                for item in items
            )
            
            # Prepare Shopify order data
            order_data = {
                "order": {
                    "line_items": [],
                    "transactions": [],
                    "source_name": "TCGSyncPos",
                    "tags": "pos, tcgsync",
                    "fulfillment_status": "fulfilled" if not has_custom_items else None,
                    "customer": {
                        "id": int(customer_id) if customer_id else None,
                        "first_name": customer.get("first_name"),
                        "last_name": customer.get("last_name"),
                        "email": customer.get("email")
                    },
                    "total_price": total,
                    "total_tax": round(total * till.get("tax_rate", 0) / 100, 2),
                    "tax_lines": [
                        {
                            "price": round(total * till.get("tax_rate", 0) / 100, 2)
                        }
                    ],
                    "financial_status": "paid",
                    "draft": has_custom_items
                }
            }
            
            # Add line items
            for item in items:
                is_custom = item.get("is_custom") or (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
                
                if is_custom:
                    line_item = {
                        "title": item["title"],
                        "quantity": item["quantity"],
                        "price": item["price"],
                        "requires_shipping": False
                    }
                else:
                    line_item = {
                        "variant_id": item["variant_id"],
                        "quantity": item["quantity"],
                        "price": item["price"]
                    }
                
                order_data["order"]["line_items"].append(line_item)
            
            # Calculate remaining total after applying credits
            remaining_total = float(total)
            total_gift_card_applied = 0.0
            total_store_credit_applied = 0.0
            
            # Process store credit if specified
            if use_store_credit and customer_id and store_credit_account_id and store_credit_amount > 0:
                logger.info(f"Processing store credit for customer {customer_id}")
                
                # Format customer ID for GraphQL query
                formatted_customer_id = customer_id
                if isinstance(customer_id, dict) and "$numberLong" in customer_id:
                    formatted_customer_id = customer_id["$numberLong"]
                else:
                    formatted_customer_id = str(customer_id)
                
                # Set up GraphQL request
                graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"
                graphql_headers = {
                    'X-Shopify-Access-Token': shopify_access_token,
                    'Content-Type': 'application/json'
                }
                
                # Get user's currency from User model
                user_obj = db.user.find_one({"username": username})
                currency_code = user_obj.get('currency', 'GBP') if user_obj else 'GBP'
                
                # Apply store credit
                amount_to_charge = min(store_credit_amount, remaining_total)
                
                if amount_to_charge > 0:
                    mutation = """
                    mutation {
                      storeCreditAccountDebit(
                        id: "gid://shopify/Customer/%s", 
                        debitInput: {
                          debitAmount: {
                            amount: "%s"
                            currencyCode: %s
                          }
                        }
                      ) {
                        storeCreditAccountTransaction {
                          id
                          amount {
                            amount
                            currencyCode
                          }
                        }
                        userErrors {
                          field
                          message
                        }
                      }
                    }
                    """ % (formatted_customer_id, str(amount_to_charge), currency_code)
                    
                    try:
                        debit_response = requests.post(
                            graphql_endpoint,
                            headers=graphql_headers,
                            json={"query": mutation}
                        )
                        
                        if debit_response.status_code == 200:
                            debit_result = debit_response.json()
                            user_errors = debit_result.get('data', {}).get('storeCreditAccountDebit', {}).get('userErrors', [])
                            
                            if not user_errors:
                                # Transaction was successful
                                remaining_total -= amount_to_charge
                                total_store_credit_applied += amount_to_charge
                                
                                order_data["order"]["transactions"].append({
                                    "kind": "store_credit",
                                    "status": "success",
                                    "amount": amount_to_charge
                                })
                                
                                logger.info(f"Successfully applied store credit payment: {amount_to_charge}")
                            else:
                                error_messages = [error.get('message') for error in user_errors]
                                logger.error(f"Error applying store credit: {', '.join(error_messages)}")
                        else:
                            logger.error(f"Error applying store credit: {debit_response.status_code} - {debit_response.text}")
                    except Exception as e:
                        logger.error(f"Error processing store credit payment: {str(e)}")
            
            # Process gift card if specified
            if use_gift_card and customer_id and gift_card_id and gift_card_amount > 0:
                logger.info(f"Processing gift card for customer {customer_id}")
                
                # Set up Shopify API request
                shopify_headers = {
                    "Content-Type": "application/json",
                    "X-Shopify-Access-Token": shopify_access_token
                }
                
                # Apply the gift card payment
                amount_to_charge = min(gift_card_amount, remaining_total)
                
                if amount_to_charge > 0:
                    gift_card_adjustment_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/{gift_card_id}/adjustments.json"
                    adjustment_data = {
                        "adjustment": {
                            "amount": -amount_to_charge,
                            "note": "POS transaction deduction"
                        }
                    }
                    
                    try:
                        gift_card_adjustment_response = requests.post(gift_card_adjustment_url, json=adjustment_data, headers=shopify_headers)
                        gift_card_adjustment_response.raise_for_status()
                        
                        # Update remaining total and add transaction
                        remaining_total -= amount_to_charge
                        total_gift_card_applied += amount_to_charge
                        
                        order_data["order"]["transactions"].append({
                            "kind": "gift_card",
                            "status": "success",
                            "amount": amount_to_charge
                        })
                        
                        logger.info(f"Successfully applied gift card payment: {amount_to_charge}")
                    except requests.RequestException as e:
                        logger.error(f"Error processing gift card payment: {str(e)}")
            
            # Add remaining payment if needed
            if remaining_total > 0:
                order_data["order"]["transactions"].append({
                    "kind": payment_method,
                    "status": "success",
                    "amount": remaining_total
                })
            
            # Create the Shopify order
            shopify_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/orders.json"
            shopify_headers = {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": shopify_access_token
            }
            
            try:
                shopify_response = requests.post(shopify_url, json=order_data, headers=shopify_headers)
                shopify_response.raise_for_status()
                shopify_order = shopify_response.json().get('order', {})
                
                # Record the transaction in the database
                transaction_record = {
                    "username": username,
                    "shopify_order_id": shopify_order.get('id'),
                    "items": items,
                    "total": total,
                    "payment_method": payment_method,
                    "customer_id": customer_id,
                    "employee_name": employee_name,
                    "gift_card_amount": total_gift_card_applied,
                    "store_credit_amount": total_store_credit_applied,
                    "timestamp": datetime.utcnow(),
                    "status": "completed"
                }
                
                db.posTransactions.insert_one(transaction_record)
                
                # Update the till's running total for cash transactions
                if payment_method == 'cash':
                    db.posSetting.update_one(
                        {"_id": ObjectId(till_id)},
                        {"$inc": {"running_total": round(remaining_total, 2)}}
                    )
                
                # Remove held sale if applicable
                if held_sale_id:
                    db.heldSale.delete_one({"_id": ObjectId(held_sale_id), "username": username})
                
                logger.info(f"Successfully completed POS transaction. Shopify Order ID: {shopify_order.get('id')}")
                return {"success": True, "order_id": shopify_order.get('id')}
                
            except requests.RequestException as e:
                logger.error(f"Error creating Shopify order: {str(e)}")
                return {"success": False, "error": f"Error creating Shopify order: {str(e)}"}
                
    except Exception as e:
        logger.error(f"Error processing POS transaction: {str(e)}")
        return {"success": False, "error": f"Error processing POS transaction: {str(e)}"}

def format_card_result(card, card_type, condition):
    """Format the card result with the necessary fields"""
    skus = card.get('skus', [])
    matching_sku = next((
        sku for sku in skus 
        if sku.get('printingName') == card_type
    ), skus[0] if skus else None)

    result = {
        "name": card.get('name', 'Unknown'),
        "set": card.get('expansionName', 'Unknown'),
        "set_code": card.get('setCode', 'Unknown'),
        "number": card.get('number', 'Unknown'),
        "condition": condition,
        "foil": card_type != 'Normal',
        "sku_id": str(matching_sku.get('skuId')) if matching_sku else 'Unknown',
        "printing": card_type,
        "category_id": card.get('categoryId', 'Unknown'),
        "product_id": card.get('productId', 'Unknown'),
        "id_product": card.get('idProduct', 'Unknown'),
        "idProduct": card.get('idProduct', 'Unknown'),
        "game": card.get('gameName', 'Unknown'),
        "skus": card.get('skus', []),
        "image": card.get('image', ''),
        "uuid": card.get('uuid', ''),
        "matched": True
    }

    # Convert any remaining ObjectId instances to strings
    return mongo_to_json(result)

@dramatiq.actor(max_retries=3)
def cleanup_expired_holds(hours_threshold=24):
    """
    Automatically clean up expired held sales and restore inventory.
    This task can be scheduled to run periodically to ensure that inventory
    is properly restored for any held sales that are forgotten.
    
    Args:
        hours_threshold (int): Number of hours after which a held sale is considered expired
    """
    logger.info(f"Starting cleanup of expired holds older than {hours_threshold} hours")
    try:
        with app.app_context():
            # Calculate expiration time
            expiration_time = datetime.utcnow() - timedelta(hours=hours_threshold)
            
            # Find all users with expired held sales
            users_with_expired_holds = db.heldSale.distinct("username", {"timestamp": {"$lt": expiration_time}})
            
            total_processed = 0
            total_restored_items = 0
            
            for username in users_with_expired_holds:
                logger.info(f"Processing expired holds for user: {username}")
                
                # Get user's Shopify credentials
                user = user_collection.find_one({"username": username})
                if not user:
                    logger.error(f"User not found: {username}")
                    continue
                
                store_name = user.get('shopifyStoreName', '').strip().replace('.myshopify.com', '')
                access_token = user.get('shopifyAccessToken', '').strip()
                
                if not store_name or not access_token:
                    logger.error(f"Missing Shopify credentials for user {username}")
                    continue
                
                # Find expired held sales for this user
                expired_sales = db.heldSale.find({"username": username, "timestamp": {"$lt": expiration_time}})
                
                user_processed = 0
                user_restored_items = 0
                
                for sale in expired_sales:
                    sale_id = str(sale.get('_id'))
                    logger.info(f"Processing expired held sale {sale_id}")
                    
                    # Get reserved items for this hold
                    reserved_items = list(db.reservedItems.find({
                        "reservation_type": "hold",
                        "reservation_id": sale_id,
                        "username": username
                    }))
                    
                    # If we have reserved items, restore inventory
                    if reserved_items:
                        items_to_restore = []
                        for item in reserved_items:
                            items_to_restore.append({
                                "variant_id": item.get("variant_id"),
                                "quantity": item.get("quantity", 0)
                            })
                        
                        # Positive adjustment to restore inventory
                        if items_to_restore:
                            try:
                                # Set up Shopify headers
                                shopify_headers = {
                                    "Content-Type": "application/json",
                                    "X-Shopify-Access-Token": access_token
                                }
                                
                                # Get location ID
                                locations_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/locations.json"
                                locations_response = requests.get(locations_url, headers=shopify_headers)
                                locations_response.raise_for_status()
                                locations = locations_response.json().get('locations', [])
                                
                                if not locations:
                                    logger.error(f"No locations found for Shopify store {store_name}")
                                    continue
                                    
                                location_id = locations[0]['id']
                                
                                # Process each item
                                for item in items_to_restore:
                                    variant_id = item.get("variant_id")
                                    quantity = item.get("quantity", 0)
                                    
                                    if not variant_id or quantity <= 0:
                                        continue
                                    
                                    # Get inventory_item_id for this variant
                                    try:
                                        # Normalize variant_id
                                        normalized_variant_id = str(variant_id)
                                        
                                        # Try different query formats to find the variant
                                        variant_info = None
                                        query_formats = [
                                            {"variants.id": normalized_variant_id},
                                            {"variants.id": int(normalized_variant_id)} if normalized_variant_id.isdigit() else None,
                                            {"variants.id": {"$numberLong": normalized_variant_id}}
                                        ]
                                        
                                        for query in query_formats:
                                            if query:
                                                variant_info = db.shProducts.find_one(
                                                    {**query, "username": username},
                                                    {"variants.$": 1}
                                                )
                                                if variant_info:
                                                    break
                                        
                                        if not variant_info or not variant_info.get('variants'):
                                            logger.error(f"Variant info not found for variant {variant_id}")
                                            continue
                                            
                                        inventory_item_id = variant_info['variants'][0].get('inventory_item_id')
                                        if not inventory_item_id:
                                            logger.error(f"No inventory_item_id found for variant {variant_id}")
                                            continue
                                            
                                        # Normalize inventory_item_id
                                        if isinstance(inventory_item_id, dict) and '$numberLong' in inventory_item_id:
                                            inventory_item_id = str(inventory_item_id['$numberLong'])
                                        else:
                                            inventory_item_id = str(inventory_item_id)
                                        
                                        # Calculate adjustment value (positive for restore)
                                        adjustment_value = quantity
                                        
                                        # Adjust inventory in Shopify
                                        inventory_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/inventory_levels/adjust.json"
                                        inventory_data = {
                                            "inventory_item_id": inventory_item_id,
                                            "location_id": location_id,
                                            "available_adjustment": adjustment_value
                                        }
                                        
                                        inventory_response = requests.post(inventory_url, json=inventory_data, headers=shopify_headers)
                                        inventory_response.raise_for_status()
                                        
                                        logger.info(f"Successfully restored inventory for variant {variant_id}, quantity {quantity}")
                                        user_restored_items += 1
                                        
                                    except Exception as e:
                                        logger.error(f"Error restoring inventory for variant {variant_id}: {str(e)}")
                                
                                # Remove reservation records
                                db.reservedItems.delete_many({
                                    "reservation_type": "hold",
                                    "reservation_id": sale_id,
                                    "username": username
                                })
                                
                                # Delete the held sale
                                db.heldSale.delete_one({"_id": ObjectId(sale_id)})
                                user_processed += 1
                                
                            except Exception as e:
                                logger.error(f"Error processing expired hold {sale_id}: {str(e)}")
                    else:
                        # No reserved items, just delete the held sale
                        db.heldSale.delete_one({"_id": ObjectId(sale_id)})
                        user_processed += 1
                
                logger.info(f"Processed {user_processed} expired holds for user {username}, restored {user_restored_items} items")
                total_processed += user_processed
                total_restored_items += user_restored_items
            
            logger.info(f"Completed cleanup of expired holds. Processed {total_processed} holds, restored {total_restored_items} items")
            return {
                "success": True,
                "processed_count": total_processed,
                "restored_items_count": total_restored_items
            }
            
    except Exception as e:
        logger.error(f"Error cleaning up expired holds: {str(e)}")
        return {"success": False, "error": str(e)}

