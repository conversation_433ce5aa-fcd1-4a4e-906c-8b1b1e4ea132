from config.config import Config
import paramiko

def setup_webdir():
    # Server credentials
    hostname = "*************"
    username = "ubuntu"
    password = "Reggie2805!"

    try:
        # Initialize SSH client
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        print("Connecting to server...")
        ssh.connect(hostname=hostname, username=username, password=password)
        
        print("Setting up web directory...")
        
        # Create a single command that chains all operations
        setup_command = """
        sudo rm -rf /home/<USER>/app && \
        sudo mkdir -p /var/www/html/cfc.tcgsync.com && \
        sudo chown -R ubuntu:ubuntu /var/www/html/cfc.tcgsync.com && \
        sudo chmod -R 755 /var/www/html/cfc.tcgsync.com
        """
        
        # Execute the command
        print("Executing setup commands...")
        stdin, stdout, stderr = ssh.exec_command(setup_command)
        
        # Print output in real-time
        while True:
            line = stdout.readline()
            if not line:
                break
            print(line.strip())
            
        # Print any errors
        for line in stderr.readlines():
            print(f"Error: {line.strip()}")
        
        print("Web directory setup completed!")
        
    except Exception as e:
        print(f"Setup failed: {str(e)}")
        
    finally:
        try:
            ssh.close()
        except:
            pass

if __name__ == '__main__':
    setup_webdir()

