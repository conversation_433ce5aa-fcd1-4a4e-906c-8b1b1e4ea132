#!/usr/bin/env python3
from pymongo import MongoClient
from datetime import datetime, timezone
import sys

def ensure_indexes(db):
    """Ensure required indexes exist for better performance."""
    db.exportedcm.create_index([("gameName", 1), ("skuId", 1)])
    db.catalog.create_index([("number", 1), ("expansionName", 1)])
    db.catalog.create_index([("name", 1), ("expansionName", 1)])

def map_expansion(expansion: str) -> str:
    """Map exportedcm expansion names to catalog expansion names."""
    if not expansion:
        return None
    
    # First remove ": Extras" suffix if present
    if ": Extras" in expansion:
        expansion = expansion.replace(": Extras", "")
    
    # Then apply specific mappings
    expansion_map = {
        'The List': 'The List Reprints'
    }
    return expansion_map.get(expansion, expansion)

def get_print_type(record: dict) -> str:
    """Get print type based on card flags."""
    if not record:
        return 'Normal'
        
    if record.get('isReverseHolo'):
        return 'Reverse Holo'
    elif record.get('isFoil'):
        return 'Foil'
    else:
        return 'Normal'

def find_unmapped(db):
    """Find and process unmapped records."""
    try:
        # Get first non-MTG record without skuId that hasn't been checked
        record = db.exportedcm.find_one({
            'skuId': {'$exists': False},
            'checked': {'$ne': True},
            'gameName': {'$ne': 'Magic the Gathering'}
        })
        
        if not record:
            print("No more records need mapping")
            return True
        
        # Check for required fields
        missing_fields = []
        for field in ['expansionName', 'enName', 'number']:
            if not record.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"\n❌ Record missing required fields: {', '.join(missing_fields)}")
            print(f"Record ID: {record['_id']}")
            print("Marking as checked and skipping...")
            db.exportedcm.update_one({'_id': record['_id']}, {'$set': {'checked': True}})
            return True
        
        # Try to find catalog match
        mapped_expansion = map_expansion(record['expansionName'])
        print_type = get_print_type(record)
        
        print("\n=== Card Details ===")
        print(f"Game: {record.get('gameName', 'Unknown')}")
        print(f"Name: {record['enName']}")
        print(f"Number: {record['number']}")
        print(f"Expansion: {record['expansionName']}")
        print(f"Mapped Expansion: {mapped_expansion}")
        print(f"Print Type: {print_type}")
        print(f"Condition: {record.get('condition', 'Unknown')}")
        
        if not mapped_expansion:
            print("\n❌ Could not map expansion name")
            print("Add mapping to map_expansion() function:")
            print(f"'{record['expansionName']}': '[matching catalog expansion]'")
            while True:
                response = input("\nContinue to next card? (y/n): ").lower()
                if response in ['y', 'n']:
                    if response == 'y':
                        db.exportedcm.update_one({'_id': record['_id']}, {'$set': {'checked': True}})
                    return response == 'y'
        
        # First try exact number match
        number_query = {
            'number': record['number'],
            'expansionName': mapped_expansion
        }
        print(f"\nTrying number match: {number_query}")
        
        catalog_items = list(db.catalog.find(number_query, {
            'name': 1,
            'expansionName': 1,
            'number': 1,
            'skus': 1
        }))
        
        if catalog_items:
            print("\n✓ Found catalog matches by number:")
            for item in catalog_items:
                print(f"  - {item['name']} ({item['expansionName']}, Number: {item['number']})")
                if 'skus' in item:
                    print("    Available print types:")
                    print_types = {sku.get('printingName') for sku in item['skus'] if sku.get('printingName')}
                    for pt in sorted(print_types):
                        print(f"    * {pt}")
            # Mark as checked
            db.exportedcm.update_one({'_id': record['_id']}, {'$set': {'checked': True}})
            return True
        
        # If no match by number, try by name
        name_query = {
            'name': record['enName'],
            'expansionName': mapped_expansion
        }
        print(f"\nTrying name match: {name_query}")
        
        name_matches = list(db.catalog.find(name_query, {
            'name': 1,
            'expansionName': 1,
            'number': 1,
            'skus': 1
        }))
        
        if name_matches:
            print("\n✓ Found catalog matches by name:")
            for item in name_matches:
                print(f"  - {item['name']} ({item['expansionName']}, Number: {item['number']})")
                if 'skus' in item:
                    print("    Available print types:")
                    print_types = {sku.get('printingName') for sku in item['skus'] if sku.get('printingName')}
                    for pt in sorted(print_types):
                        print(f"    * {pt}")
            print("\nNumber format mismatch:")
            print(f"  Card number: {record['number']}")
            print(f"  Catalog number: {name_matches[0]['number']}")
            # Mark as checked
            db.exportedcm.update_one({'_id': record['_id']}, {'$set': {'checked': True}})
            return True
        
        print("\n❌ NO CATALOG MATCH FOUND")
        
        # Show some cards from this expansion
        expansion_items = list(db.catalog.find({
            'expansionName': mapped_expansion
        }, {
            'name': 1,
            'expansionName': 1,
            'number': 1,
            'skus': 1
        }).limit(5))
        
        if expansion_items:
            print("\nSome cards in this expansion:")
            for item in expansion_items:
                print(f"  - {item['name']} (Number: {item['number']})")
                if 'skus' in item:
                    print("    Available print types:")
                    print_types = {sku.get('printingName') for sku in item['skus'] if sku.get('printingName')}
                    for pt in sorted(print_types):
                        print(f"    * {pt}")
        
        # Show similar expansions
        similar_expansions = list(db.catalog.distinct('expansionName', {
            'expansionName': {'$regex': f".*{record['expansionName'].split(':')[0]}.*"}
        }))
        
        if similar_expansions:
            print("\nSimilar expansion names in catalog:")
            for exp in sorted(similar_expansions):
                print(f"  - {exp}")
        
        if mapped_expansion != record['expansionName']:
            print(f"\nNote: Expansion was mapped from '{record['expansionName']}' to '{mapped_expansion}'")
            print("But still no match was found. Check if:")
            print("1. The mapping is correct")
            print("2. The card exists in this expansion")
        else:
            print("\nAdd mapping to map_expansion() function:")
            print(f"'{record['expansionName']}': '[matching catalog expansion]'")
        
        while True:
            response = input("\nContinue to next card? (y/n): ").lower()
            if response in ['y', 'n']:
                if response == 'y':
                    # Mark as checked before continuing
                    db.exportedcm.update_one({'_id': record['_id']}, {'$set': {'checked': True}})
                return response == 'y'
    
    except Exception as e:
        print(f"Error processing record: {str(e)}")
        return False

def main():
    """Main function to process unmapped records."""
    client = MongoClient('mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin')
    db = client['test']
    
    try:
        ensure_indexes(db)
        
        while True:
            if not find_unmapped(db):
                break
    
    except Exception as e:
        print(f"Error: {str(e)}")
        raise
    
    finally:
        client.close()

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"Process failed: {str(e)}")
        sys.exit(1)
