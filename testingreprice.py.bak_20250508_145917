from concurrent.futures import Thread<PERSON>oolExecutor
from pymongo import MongoClient, UpdateOne
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from datetime import datetime, timedelta
from tqdm import tqdm
from itertools import islice
import requests
import sys
import logging
import time
import schedule
from threading import Lock
import signal
import multiprocessing
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('repricer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Pre-compile regex patterns
CONDITION_PATTERNS = {
    re.compile(r'near\s*mint|nm\b', re.I): 'nm',
    re.compile(r'lightly\s*played|lp\b', re.I): 'lp',
    re.compile(r'moderately\s*played|mp\b', re.I): 'mp',
    re.compile(r'heavily\s*played|hp\b', re.I): 'hp',
    re.compile(r'damaged|dm\b', re.I): 'dm'
}

SEALED_PATTERN = re.compile(r'.*seal.*', re.I)

# Constants
EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"
BATCH_SIZE = 500
DEFAULT_MIN_PRICE_USD = 0.2
TEST_USERNAME = "Khaoz"
MONGO_POOL_SIZE = 200

# Global caching
global_exchange_rates = {}

# MongoDB connection
mongo_uri = 'mongodb://admin:Reggie2805!@*************:27017/?authSource=admin'
mongo_client = None

def get_mongo_client():
    global mongo_client
    if mongo_client is None:
        mongo_client = MongoClient(
            mongo_uri,
            maxPoolSize=MONGO_POOL_SIZE,
            waitQueueTimeoutMS=5000,
            connectTimeoutMS=5000,
            serverSelectionTimeoutMS=5000,
            retryWrites=True,
            w='majority'
        )
    return mongo_client

# Currency cache implementation
class CurrencyCache:
    def __init__(self, ttl_hours=24):
        self.cache = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.lock = Lock()

    def get(self, currency):
        with self.lock:
            if currency in self.cache:
                rate_data = self.cache[currency]
                if datetime.utcnow() - rate_data['timestamp'] < self.ttl:
                    return rate_data['rate']
                else:
                    del self.cache[currency]
            return None

    def set(self, currency, rate):
        with self.lock:
            self.cache[currency] = {'rate': rate, 'timestamp': datetime.utcnow()}

currency_cache = CurrencyCache()

def get_exchange_rate(target_currency):
    if target_currency == 'USD':
        return 1.0
    if target_currency in global_exchange_rates:
        return global_exchange_rates[target_currency]
    
    cached_rate = currency_cache.get(target_currency)
    if cached_rate:
        return cached_rate
    
    try:
        url = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        rates = response.json().get('conversion_rates', {})
        rate = rates.get(target_currency, 1.0)
        
        currency_cache.set(target_currency, rate)
        global_exchange_rates[target_currency] = rate
        return rate
    except Exception as e:
        logger.error(f"Error fetching exchange rate for {target_currency}: {str(e)}")
        return 1.0

def fetch_pricing_data(product_ids, tcgplayer_api_key):
    if not product_ids:
        return {}

    headers = {
        'Authorization': f'Bearer {tcgplayer_api_key}',
        'Accept': 'application/json',
    }

    new_data = {}

    def fetch_chunk(chunk):
        try:
            url = f"https://api.tcgplayer.com/pricing/product/{','.join(chunk)}"
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            return response.json().get('results', [])
        except Exception as e:
            logger.error(f"Error fetching pricing data: {str(e)}")
            return []

    chunks = [product_ids[i:i + 100] for i in range(0, len(product_ids), 100)]
    with ThreadPoolExecutor(max_workers=10) as executor:
        results = executor.map(fetch_chunk, chunks)

    for result in results:
        for item in result:
            product_id = str(item.get('productId'))
            if product_id:
                if product_id not in new_data:
                    new_data[product_id] = []
                new_data[product_id].append(item)

    return new_data

def process_user(config, tcgplayer_api_key):
    try:
        client = get_mongo_client()
        db = client['test']
        shopify_collection = db['shProducts']
        user_collection = db['user']
        
        username = config['username']
        user_profile = user_collection.find_one({'username': username})
        if not user_profile:
            logger.error(f"User profile not found for {username}")
            return

        price_preferences = user_profile.get('price_preference_order', ['lowPrice', 'marketPrice'])
        user_currency = user_profile.get('currency', 'USD')
        exchange_rate = get_exchange_rate(user_currency)
        
        query = {'username': username}
        total_products = shopify_collection.count_documents(query)
        if not total_products:
            return

        cursor = shopify_collection.find(query, batch_size=BATCH_SIZE)
        bulk_updates = []
        total_updates = 0

        with tqdm(total=total_products, desc=f"Processing {username}", leave=True) as pbar:
            while True:
                batch = list(islice(cursor, BATCH_SIZE))
                if not batch:
                    break
                
                product_ids = [str(p.get('productId')) for p in batch if p.get('productId')]
                pricing_data = fetch_pricing_data(product_ids, tcgplayer_api_key)

                for product in batch:
                    product_id = str(product.get('productId'))
                    if product_id not in pricing_data:
                        continue
                    
                    new_price = 5.0  # Placeholder for price logic
                    bulk_updates.append(
                        UpdateOne(
                            {'_id': product['_id']},
                            {'$set': {'price': new_price}}
                        )
                    )
                    total_updates += 1

                if len(bulk_updates) >= 1000:
                    shopify_collection.bulk_write(bulk_updates, ordered=False)
                    bulk_updates.clear()

                pbar.update(len(batch))

        if bulk_updates:
            shopify_collection.bulk_write(bulk_updates, ordered=False)
        
        logger.info(f"Completed processing for {username}. Total updates: {total_updates}")

    except Exception as e:
        logger.error(f"Error in process_user for {config.get('username')}: {str(e)}")

def bulk_reprice():
    try:
        client = get_mongo_client()
        db = client['test']
        tcgplayer_key_collection = db['tcgplayerKey']
        autopricer_collection = db['autopricerShopify']

        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            logger.error("TCGPlayer key not found")
            return

        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        autopricer_configs = list(autopricer_collection.find({}))
        
        with ThreadPoolExecutor(max_workers=min(30, multiprocessing.cpu_count() * 2)) as executor:
            executor.map(lambda config: process_user(config, tcgplayer_api_key), autopricer_configs)

    except Exception as e:
        logger.error(f"Error in bulk_reprice: {str(e)}")

def run_scheduled_task():
    logger.info("Starting scheduled repricing task")
    bulk_reprice()

def main():
    schedule.every(30).minutes.do(run_scheduled_task)
    run_scheduled_task()

    while True:
        time.sleep(schedule.idle_seconds())
        schedule.run_pending()

if __name__ == "__main__":
    main()
