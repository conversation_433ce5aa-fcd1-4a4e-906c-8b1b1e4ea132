{% extends "base.html" %}

{% block title %}Wallet{% endblock %}

{% block content %}
<style>
    .table td {
        vertical-align: middle;
    }
    .badge {
        font-size: 0.85em;
    }
    .table-dark {
        background-color: #343a40;
    }
    .card.bg-dark {
        background-color: #343a40 !important;
    }
    .display-4 {
        font-size: 2.5rem;
        font-weight: 600;
    }
    .nav-tabs {
        border-bottom: 1px solid #495057;
    }
    .nav-tabs .nav-link {
        color: #fff;
        border: none;
    }
    .nav-tabs .nav-link:hover {
        border: none;
        color: #fff;
        background-color: #495057;
    }
    .nav-tabs .nav-link.active {
        color: #fff;
        background-color: #495057;
        border: none;
    }
    .bank-details p {
        margin-bottom: 0.5rem;
    }
    .bank-details strong {
        color: #9ca3af;
    }
</style>
<div class="container mt-4">
    <h1 class="text-white">Wallet</h1>
    <div class="row">
        <!-- Balance Card -->
        <div class="col-md-4 mb-4">
            <div class="card bg-dark h-100">
                <div class="card-body">
                    <h5 class="card-title text-white">Current Balance</h5>
                    <div class="d-flex align-items-center justify-content-between">
                        <h2 class="display-4 text-white mb-0">${{ "%.2f"|format(wallet.get('balance', 0)) }}</h2>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#topUpModal">
                            <i class="fas fa-plus-circle me-2"></i>Top Up
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Info Card -->
        <div class="col-md-8 mb-4">
            <div class="card bg-dark h-100">
                <div class="card-body">
                    <h5 class="card-title text-white">Account Information</h5>
                    <p class="text-white mb-1">Username: {{ wallet.get('username', '') }}</p>
                    <p class="text-white mb-1">Account Created: {{ wallet.get('created_at', '')|string }}</p>
                    <p class="text-white">Last Updated: {{ wallet.get('updated_at', '')|string }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Wallet Benefits -->
    <div class="card bg-dark mt-4">
        <div class="card-body">
            <h5 class="card-title text-white mb-3">Why Use TCG Sync Wallet?</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <h6 class="text-white"><i class="fas fa-piggy-bank me-2"></i>Save on Transaction Fees</h6>
                        <p class="text-white-50">By using bank transfers to fund your wallet instead of credit cards, you avoid payment processing fees. This means you'll pay the lowest possible price when purchasing from third-party sites.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-white"><i class="fas fa-shield-alt me-2"></i>Secure Transactions</h6>
                        <p class="text-white-50">Your wallet balance is safely stored and can be used instantly across all supported platforms without sharing your payment details multiple times.</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <h6 class="text-white"><i class="fas fa-bolt me-2"></i>Faster Purchases</h6>
                        <p class="text-white-50">Once your wallet is funded, you can make instant purchases without waiting for payment processing or bank transfers.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-white"><i class="fas fa-chart-line me-2"></i>Better Budget Control</h6>
                        <p class="text-white-50">Pre-fund your wallet with your desired budget and keep track of all your transactions in one place.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="card bg-dark mt-4">
        <div class="card-body">
            <h5 class="card-title text-white mb-4">Transaction History</h5>
            {% if wallet.get('transactions') %}
                <div class="table-responsive">
                    <table class="table table-hover table-dark">
                        <thead>
                            <tr>
                                <th class="text-white">Date</th>
                                <th class="text-white">Description</th>
                                <th class="text-white">Amount</th>
                                <th class="text-white">Type</th>
                                <th class="text-white">Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in wallet.get('transactions', []) %}
                                <tr>
                                    <td class="text-white">{{ transaction.get('date', '')|string }}</td>
                                    <td class="text-white">{{ transaction.get('description', '') }}</td>
                                    <td class="text-white">
                                        {% if transaction.get('type') == 'credit' %}
                                            <span class="text-success">+${{ "%.2f"|format(transaction.get('amount', 0)) }}</span>
                                        {% else %}
                                            <span class="text-danger">-${{ "%.2f"|format(transaction.get('amount', 0)) }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.get('type') == 'credit' %}
                                            <span class="badge bg-success">Credit</span>
                                        {% else %}
                                            <span class="badge bg-danger">Debit</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-white">${{ "%.2f"|format(transaction.get('balance_after', 0)) }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No transactions found in your wallet.
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Top Up Modal -->
    <div class="modal fade" id="topUpModal" tabindex="-1" aria-labelledby="topUpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark">
                <div class="modal-header">
                    <h5 class="modal-title text-white" id="topUpModalLabel">Top Up Your Wallet</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="currencyTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="gbp-tab" data-bs-toggle="tab" data-bs-target="#gbp" type="button" role="tab">GBP</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="usd-tab" data-bs-toggle="tab" data-bs-target="#usd" type="button" role="tab">USD</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="eur-tab" data-bs-toggle="tab" data-bs-target="#eur" type="button" role="tab">EUR</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="cad-tab" data-bs-toggle="tab" data-bs-target="#cad" type="button" role="tab">CAD</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="aud-tab" data-bs-toggle="tab" data-bs-target="#aud" type="button" role="tab">AUD</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="nzd-tab" data-bs-toggle="tab" data-bs-target="#nzd" type="button" role="tab">NZD</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="currencyTabsContent">
                        <!-- GBP Details -->
                        <div class="tab-pane fade show active" id="gbp" role="tabpanel">
                            <div class="bank-details text-white">
                                <h6 class="mb-3">GBP Account Details</h6>
                                <p><strong>Account holder:</strong> TCG Sync</p>
                                <p><strong>Sort code:</strong> 23-08-01</p>
                                <p><strong>Account number:</strong> ********</p>
                                <p><strong>IBAN:</strong> GB64 TRWI 2308 0111 3206 61</p>
                                <p><strong>Bank name and address:</strong><br>
                                Wise Payments Limited<br>
                                56 Shoreditch High Street<br>
                                London<br>
                                E1 6JJ<br>
                                United Kingdom</p>
                            </div>
                        </div>
                        <!-- USD Details -->
                        <div class="tab-pane fade" id="usd" role="tabpanel">
                            <div class="bank-details text-white">
                                <h6 class="mb-3">USD Account Details</h6>
                                <p><strong>Account holder:</strong> TCG Sync</p>
                                <p><strong>ACH and Wire routing number:</strong> *********</p>
                                <p><strong>Account number:</strong> **********</p>
                                <p><strong>Account type:</strong> Checking</p>
                                <p><strong>Bank name and address:</strong><br>
                                Community Federal Savings Bank<br>
                                89-16 Jamaica Ave<br>
                                Woodhaven NY 11421<br>
                                United States</p>
                            </div>
                        </div>
                        <!-- EUR Details -->
                        <div class="tab-pane fade" id="eur" role="tabpanel">
                            <div class="bank-details text-white">
                                <h6 class="mb-3">EUR Account Details</h6>
                                <p><strong>Account holder:</strong> TCG Sync</p>
                                <p><strong>BIC:</strong> TRWIBEB1XXX</p>
                                <p><strong>IBAN:</strong> BE32 9676 7345 5202</p>
                                <p><strong>Bank name and address:</strong><br>
                                Wise<br>
                                Rue du Trône 100, 3rd floor<br>
                                Brussels<br>
                                1050<br>
                                Belgium</p>
                            </div>
                        </div>
                        <!-- CAD Details -->
                        <div class="tab-pane fade" id="cad" role="tabpanel">
                            <div class="bank-details text-white">
                                <h6 class="mb-3">CAD Account Details</h6>
                                <p><strong>Account holder:</strong> TCG Sync</p>
                                <p><strong>Institution number:</strong> 621</p>
                                <p><strong>Account number:</strong> ************</p>
                                <p><strong>Transit number:</strong> 16001</p>
                                <p><strong>Bank name and address:</strong><br>
                                Peoples Trust<br>
                                595 Burrard Street<br>
                                Vancouver BC V7X 1L7<br>
                                Canada</p>
                            </div>
                        </div>
                        <!-- AUD Details -->
                        <div class="tab-pane fade" id="aud" role="tabpanel">
                            <div class="bank-details text-white">
                                <h6 class="mb-3">AUD Account Details</h6>
                                <p><strong>Account holder:</strong> TCG Sync</p>
                                <p><strong>BSB code:</strong> 774001</p>
                                <p><strong>Account number:</strong> *********</p>
                            </div>
                        </div>
                        <!-- NZD Details -->
                        <div class="tab-pane fade" id="nzd" role="tabpanel">
                            <div class="bank-details text-white">
                                <h6 class="mb-3">NZD Account Details</h6>
                                <p><strong>Account holder:</strong> TCG Sync</p>
                                <p><strong>Account number:</strong> 04-2021-0209523-55</p>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Important:</strong>
                        <ul class="mb-0">
                            <li>Please allow up to 3 business days for the funds to be credited to your account.</li>
                            <li>Use your username "<strong>{{ wallet.get('username', '') }}</strong>" as the payment reference.</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="requestBankDetails()">
                        <i class="fas fa-envelope me-2"></i>Email Bank Details
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function requestBankDetails() {
    const activeTab = document.querySelector('.tab-pane.active');
    const currency = activeTab.id.toUpperCase();
    
    fetch('/wallet/request-bank-details', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            currency: currency
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message using Bootstrap toast or alert
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed top-0 end-0 m-3';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                Bank details have been sent to your email address.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        } else {
            throw new Error(data.message || 'Failed to send bank details');
        }
    })
    .catch(error => {
        // Show error message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed top-0 end-0 m-3';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>
            ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.body.appendChild(alertDiv);
        
        // Auto dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    });
}
</script>
{% endblock %}
