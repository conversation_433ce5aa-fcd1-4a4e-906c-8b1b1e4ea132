from config.config import Config
import logging
from automation_email_sender import send_automation_settings_change_email
from config import Config
import pymongo

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_automation_settings_change_email():
    """Test the automation settings change email function"""
    try:
        # Connect to MongoDB directly
        client = pymongo.MongoClient(Config.MONGO_URI)
        db = client[Config.MONGO_DBNAME]
        
        # Get a test user from the database
        user = db.user.find_one({})
        if not user or 'username' not in user or 'email' not in user:
            logger.error("No suitable test user found in the database")
            return False
            
        username = user['username']
        email = user['email']
        
        logger.info(f"Testing automation settings change email for user: {username}, email: {email}")
        
        # Create a sample changes dictionary
        changes = {
            'Game Updates': {
                'Selected Games': {
                    'before': 'Magic: The Gathering, Pokemon',
                    'after': 'Magic: The Gathering, Pokemon, Yu-Gi-<PERSON>!'
                }
            },
            'Autopricing Settings': {
                'Selected Product Types': {
                    'before': 'Magic Single, Pokemon Single',
                    'after': 'Magic Single, Pokemon Single, Yu-Gi-Oh Single'
                }
            },
            'Straight To Shopify': {
                'Enabled': {
                    'before': 'No',
                    'after': 'Yes'
                }
            }
        }
        
        # Send the test email
        result = send_automation_settings_change_email(username, email, changes)
        
        if result:
            logger.info(f"Test email sent successfully to {email}")
            return True
        else:
            logger.error(f"Failed to send test email to {email}")
            return False
            
    except Exception as e:
        logger.error(f"Error in test_automation_settings_change_email: {str(e)}")
        return False

if __name__ == "__main__":
    if test_automation_settings_change_email():
        print("Test completed successfully")
    else:
        print("Test failed")

