from config.config import Config
from pymongo import MongoClient
import requests
import logging
import sys
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
db = client['test']

def fetch_pricing_data(product_id, tcgplayer_api_key):
    headers = {
        'Authorization': f'Bearer {tcgplayer_api_key}',
        'Accept': 'application/json',
    }
    
    try:
        url = f"https://api.tcgplayer.com/pricing/product/{product_id}"
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json().get('results', [])
    except Exception as e:
        logger.error(f"Error fetching pricing data: {str(e)}")
        return []

def get_game_minimum_price(user_profile, game, print_type, rarity):
    game_settings = user_profile.get('game_minimum_prices', {}).get(game, {})
    
    # Check print type specific minimum
    print_type_min = game_settings.get('print_types', {}).get(print_type)
    if print_type_min:
        return print_type_min
        
    # Check rarity specific minimum
    rarity_min = game_settings.get('rarities', {}).get(rarity)
    if rarity_min:
        return rarity_min
        
    # Fall back to game default
    game_default = game_settings.get('default_min_price')
    if game_default:
        return game_default
        
    # Finally, fall back to global minimum
    return user_profile.get('minPrice', 0.2)

def round_price(price, thresholds):
    """Round price to nearest threshold decimal"""
    if not thresholds:
        return price
        
    decimal_part = price % 1
    decimal_cents = int(decimal_part * 100)
    
    # Find the closest threshold
    closest_threshold = min(thresholds, key=lambda x: abs(x - decimal_cents))
    
    # Round to the closest threshold
    return int(price) + (closest_threshold / 100)

def calculate_price(pricing_data, condition, user_profile, product):
    logger.info(f"\nCalculating price for {product.get('title')} ({condition.upper()})")
    logger.info(f"Product ID: {product.get('productId')}")
    logger.info(f"Print Type: {product.get('printType', 'Normal')}")
    logger.info(f"Rarity: {product.get('rarity', 'Unknown')}")
    
    # Get base price based on user's price preference order
    price_preferences = user_profile.get('price_preference_order', ['midPrice', 'highPrice', 'marketPrice', 'lowPrice'])
    base_price = None
    used_price_type = None
    
    for price_type in price_preferences:
        if price_type in pricing_data and pricing_data[price_type]:
            base_price = float(pricing_data[price_type])
            used_price_type = price_type
            logger.info(f"Using {price_type}: {base_price:.2f}")
            break
    
    if base_price is None:
        logger.error("No valid price found")
        return None
        
    # Apply price modifier if exists
    modifiers = user_profile.get('price_modifiers', {})
    if used_price_type in modifiers:
        modifier = modifiers[used_price_type]
        modified_price = base_price * (1 + modifier/100)
        logger.info(f"After {modifier}% modifier: {modified_price:.2f}")
        base_price = modified_price
    
    # Apply condition stepping
    stepping = user_profile.get('customStepping', {}).get(condition, 100)
    stepped_price = base_price * (stepping / 100)
    logger.info(f"After {condition} stepping ({stepping:.2f}%): {stepped_price:.2f}")
    
    # Convert to user's currency
    currency = user_profile.get('currency', 'USD')
    if currency != 'USD':
        exchange_rate = 20.4917  # Hardcoded for testing MXN
        price_in_currency = stepped_price * exchange_rate
        logger.info(f"After currency conversion to {currency} (rate: {exchange_rate:.2f}): {price_in_currency:.2f}")
    else:
        price_in_currency = stepped_price
        
    # Apply game-specific minimum price
    game_min = get_game_minimum_price(
        user_profile,
        product.get('vendor', 'Unknown'),
        product.get('printType', 'Normal'),
        product.get('rarity', 'Unknown')
    )
    logger.info(f"Game-specific minimum price: {game_min} {currency}")
    
    final_price = max(price_in_currency, game_min)
    logger.info(f"After minimum price check: {final_price:.2f}")
    
    # Apply price rounding if enabled
    if user_profile.get('price_rounding_enabled', False):
        thresholds = user_profile.get('price_rounding_thresholds', [49, 99])
        rounded_price = round_price(final_price, thresholds)
        logger.info(f"After price rounding to thresholds {thresholds}: {rounded_price:.2f}")
        final_price = rounded_price
    
    return round(final_price, 2)

def test_pricing():
    # Get TCGPlayer key
    tcgplayer_key_doc = db.tcgplayerKey.find_one({})
    if not tcgplayer_key_doc:
        logger.error("TCGPlayer key not found")
        return
    tcgplayer_api_key = tcgplayer_key_doc['latestKey']
    
    # Get user profile
    user_profile = db.user.find_one({'username': 'Khaoz'})
    if not user_profile:
        logger.error("User profile not found")
        return
    
    # Get a sample MTG product
    product = db.shProducts.find_one({
        'username': 'Khaoz',
        'product_type': 'MTG Single',
        'productId': {'$exists': True}
    })
    
    if not product:
        logger.error("No MTG product found")
        return
        
    logger.info(f"Testing product: {product.get('title')}")
    logger.info(f"TCGPlayer Product ID: {product.get('productId')}")
    logger.info(f"Shopify Product ID: {product.get('id')}")
    logger.info(f"Vendor: {product.get('vendor')}")
    logger.info(f"Expansion: {product.get('expansionName')}")
    logger.info(f"Print Type: {product.get('printType', 'Normal')}")
    logger.info(f"Rarity: {product.get('rarity', 'Unknown')}")
    
    # Get pricing data
    pricing_data = fetch_pricing_data(product['productId'], tcgplayer_api_key)
    if not pricing_data:
        logger.error("No pricing data found")
        return
        
    logger.info("\nRaw pricing data:")
    for price_data in pricing_data:
        logger.info(f"  SubType: {price_data.get('subTypeName')}")
        logger.info(f"  Low: ${price_data.get('lowPrice', 'N/A')}")
        logger.info(f"  Market: ${price_data.get('marketPrice', 'N/A')}")
        logger.info(f"  Mid: ${price_data.get('midPrice', 'N/A')}")
        logger.info(f"  High: ${price_data.get('highPrice', 'N/A')}")
    
    # Test price calculation for all conditions
    conditions = ['nm', 'lp', 'mp', 'hp', 'dm']
    for condition in conditions:
        logger.info(f"\nTesting {condition.upper()} condition:")
        price = calculate_price(pricing_data[0], condition, user_profile, product)
        if price:
            logger.info(f"Final {condition.upper()} price: {price:.2f} {user_profile.get('currency', 'USD')}")

if __name__ == "__main__":
    test_pricing()
