#!/usr/bin/env python3
"""
MongoDB Connection Test Script

This script tests the connection to the MongoDB Atlas cluster.
"""

import logging
import sys
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mongodb_connection_test")

# MongoDB URI
MONGODB_URI = 'mongodb+srv://admin:<EMAIL>/admin?replicaSet=replicaset&tls=true'

def test_connection():
    """Test connection to MongoDB Atlas."""
    logger.info("Testing connection to MongoDB Atlas...")
    
    try:
        # Connect to MongoDB with a timeout
        client = MongoClient(MONGODB_URI, serverSelectionTimeoutMS=5000)
        
        # The ismaster command is cheap and does not require auth
        client.admin.command('ismaster')
        
        logger.info("MongoDB connection successful!")
        
        # List all database names
        logger.info("Available databases:")
        for db_name in client.list_database_names():
            logger.info(f"- {db_name}")
        
        return True
    
    except (ConnectionFailure, ServerSelectionTimeoutError) as e:
        logger.error(f"MongoDB connection failed: {e}")
        return False
    finally:
        if 'client' in locals():
            client.close()
            logger.info("MongoDB connection closed")

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
