from config.config import Config
import requests
from config import Config
import logging

logger = logging.getLogger(__name__)

def send_shopify_catalog_email():
    """Send email about Shopify catalog import feature"""
    try:
        logger.info("Starting send_shopify_catalog_email")
        
        MAILGUN_API_KEY = Config.MAILGUN_API_KEY
        MAILGUN_DOMAIN = Config.MAILGUN_DOMAIN

        if not MAILGUN_API_KEY or not MAILGUN_DOMAIN:
            logger.error("Mailgun API key or domain not set in config")
            raise ValueError("Mailgun API key or domain not set in config")

        # HTML content for the email
        html_content = """<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Simplify Your Trading Card Business with TCG Sync!</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
</head>
<body style="margin:0; padding:0; font-family: Arial, sans-serif; background-color:#f4f4f4;">
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td align="center" style="padding:20px 0;">
        <!-- Main Container -->
        <table border="0" cellpadding="0" cellspacing="0" width="600" style="background-color:#ffffff; border:1px solid #dddddd; border-radius:8px; box-shadow:0 2px 4px rgba(0,0,0,0.1);">
          
          <!-- Header Section -->
          <tr>
            <td align="center" style="padding:30px 20px; background-color:#f8f9fa; border-radius:8px 8px 0 0;">
              <h1 style="color:#2d3748; margin:0; font-size:28px; font-weight:700;">
                Free Trading Card Tools - No Registration Required!
              </h1>
            </td>
          </tr>
          
          <!-- Body Section -->
          <tr>
            <td style="padding:30px; color:#4a5568; line-height:1.6; font-size:16px;">
              <p style="margin-bottom:20px;">Hello,</p>
              <p style="margin-bottom:25px;">
                We're thrilled to introduce our <strong>completely free</strong> inventory management tools at 
                <a href="https://tcgsync.com" style="color:#3182ce; text-decoration:none; font-weight:600;">TCGSync.com</a>. 
                Get started instantly - <strong>no registration needed!</strong>
              </p>
              <div style="background-color:#ebf8ff; padding:20px; border-radius:6px; margin-bottom:25px;">
                <p style="margin:0; color:#2c5282;">
                  <strong>🆕 New Feature Highlight:</strong> Get ready-to-use catalog CSVs for all trading card games! 
                  Create Shopify products instantly with our pre-formatted data, complete with automatic pricing to save you countless hours of work!
                </p>
              </div>
              <p style="margin-bottom:20px;">
                Here's what makes TCG Sync the perfect tool for your business:
              </p>
              <ul style="padding-left:20px; margin-bottom:25px;">
                <li style="margin-bottom:10px;">Download complete catalog CSVs for all trading card games</li>
                <li style="margin-bottom:10px;">Create Shopify products instantly with our pre-formatted data</li>
                <li style="margin-bottom:10px;">Smart automatic pricing system that keeps your inventory competitive</li>
              </ul>
              <p style="margin-bottom:25px;">
                Ready to transform your inventory management? Visit 
                <a href="https://tcgsync.com" style="color:#3182ce; text-decoration:none; font-weight:600;">TCGSync.com</a> 
                and experience the power of automated card management - completely free!
              </p>
              <div style="background-color:#f7fafc; padding:20px; border-radius:6px; margin-bottom:25px;">
                <p style="margin:0; color:#4a5568;">
                  <strong>Already a subscriber?</strong> Help fellow traders discover these free tools! Share TCGSync.com with 
                  anyone who could benefit from our no-registration-required inventory management solutions.
                </p>
              </div>
              <p style="margin-bottom:10px;">Best regards,</p>
              <p style="margin-bottom:25px;"><strong>The TCG Sync Team</strong></p>
            </td>
          </tr>
          
          <!-- Call-to-Action Button -->
          <tr>
            <td align="center" style="padding:0 20px 30px;">
              <a href="https://tcgsync.com" 
                 style="display:inline-block; background-color:#3182ce; color:#ffffff; padding:14px 28px; text-decoration:none; border-radius:6px; font-weight:600; font-size:16px;">
                Try It Now - 100% Free!
              </a>
            </td>
          </tr>
          
          <!-- Footer Section -->
          <tr>
            <td style="padding:20px; font-size:12px; color:#718096; text-align:center; background-color:#f8f9fa; border-radius:0 0 8px 8px;">
              <p style="margin:0 0 10px;">&copy; 2025 TCG Sync. All rights reserved.</p>
              <p style="margin:0;">
                You're receiving this email because you subscribed to updates from TCG Sync.
              </p>
            </td>
          </tr>
          
        </table>
      </td>
    </tr>
  </table>
</body>
</html>"""

        # Mailgun API endpoint
        url = f"https://api.eu.mailgun.net/v3/{MAILGUN_DOMAIN}/messages"
        logger.info(f"Using Mailgun endpoint: {url}")

        # Email data
        data = {
            "from": f"TCG Sync <admin@{MAILGUN_DOMAIN}>",
            "to": "<EMAIL>",
            "subject": "🆓 Free Trading Card Tools - No Registration Required!",
            "html": html_content
        }
        logger.info("Prepared email data")

        # Send the email
        logger.info("Sending email via Mailgun...")
        response = requests.post(
            url,
            auth=("api", MAILGUN_API_KEY),
            data=data,
            timeout=30  # 30 second timeout
        )

        if response.status_code == 200:
            logger.info("Shopify catalog email sent successfully")
            print("Email sent successfully!")
            return True
        else:
            logger.error(f"Failed to send email. Status code: {response.status_code}")
            logger.error(f"Response: {response.text}")
            print(f"Failed to send email. Status code: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"Error sending Shopify catalog email: {str(e)}")
        print(f"Error sending email: {str(e)}")
        return False

if __name__ == "__main__":
    send_shopify_catalog_email()

