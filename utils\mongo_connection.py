"""
Robust MongoDB Connection Utility

This module provides a robust MongoDB connection utility with retry logic
and proper error handling to prevent application crashes during startup.
"""

import logging
import time
from flask import current_app
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
from config.config import Config

# Configure logging
logger = logging.getLogger(__name__)

def get_mongo_connection(max_retries=3, retry_delay=2, timeout_ms=5000):
    """
    Get MongoDB connection with retry logic
    
    Args:
        max_retries (int): Maximum number of connection attempts
        retry_delay (int): Delay between retries in seconds
        timeout_ms (int): Connection timeout in milliseconds
        
    Returns:
        MongoClient: MongoDB client instance
        
    Raises:
        ConnectionFailure: If all connection attempts fail
    """
    for attempt in range(max_retries):
        try:
            # Use the application's MongoDB connection if available
            if hasattr(current_app, 'mongo_client'):
                logger.info("Using application's MongoDB connection")
                return current_app.mongo_client
            
            # Otherwise create a new connection
            logger.info(f"Creating new MongoDB connection (attempt {attempt+1}/{max_retries})")
            mongo_client = MongoClient(
                Config.MONGO_URI,
                serverSelectionTimeoutMS=timeout_ms,
                connectTimeoutMS=timeout_ms,
                retryWrites=True
            )
            
            # Test the connection
            mongo_client.admin.command('ping')
            logger.info("MongoDB connection successful")
            
            return mongo_client
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.warning(f"MongoDB connection attempt {attempt+1} failed: {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                logger.error("All MongoDB connection attempts failed")
                raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {str(e)}")
            raise

def get_db_collection(collection_name, db_name=None):
    """
    Get a MongoDB collection with connection retry logic
    
    Args:
        collection_name (str): Name of the collection
        db_name (str, optional): Database name. Defaults to Config.MONGO_DBNAME.
        
    Returns:
        Collection: MongoDB collection
        
    Raises:
        ConnectionFailure: If all connection attempts fail
    """
    if db_name is None:
        db_name = Config.MONGO_DBNAME
        
    try:
        client = get_mongo_connection()
        db = client[db_name]
        collection = db[collection_name]
        return collection
    except Exception as e:
        logger.error(f"Failed to get collection {collection_name}: {str(e)}")
        raise
