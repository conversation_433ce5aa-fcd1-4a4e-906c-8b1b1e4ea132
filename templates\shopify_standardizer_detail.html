{% extends "base.html" %}

{% block title %}Discrepancy Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Discrepancy Details</h1>
        <a href="{{ url_for('standardizer.list_discrepancies') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Product Information</h6>
                    <div>
                        {% if discrepancy.status == 'pending' %}
                        <span class="badge badge-warning">Pending</span>
                        {% elif discrepancy.status == 'approved' %}
                        <span class="badge badge-success">Approved</span>
                        {% elif discrepancy.status == 'rejected' %}
                        <span class="badge badge-danger">Rejected</span>
                        {% elif discrepancy.status == 'repaired' %}
                        <span class="badge badge-info">Repaired</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{{ discrepancy.title }}</h5>
                            <p>
                                <strong>Product ID:</strong> {{ discrepancy.product_id_int }}<br>
                                <strong>Shopify ID:</strong> {{ discrepancy.shopify_id }}<br>
                                <strong>Username:</strong> {{ discrepancy.username }}<br>
                                <strong>Identified:</strong> {{ discrepancy.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
                            </p>
                            
                            {% if discrepancy.status != 'pending' %}
                            <p>
                                <strong>Reviewed By:</strong> {{ discrepancy.reviewed_by }}<br>
                                <strong>Reviewed At:</strong> {{ discrepancy.review_timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
                            </p>
                            {% endif %}
                            
                            {% if discrepancy.status == 'repaired' %}
                            <p>
                                <strong>Repaired By:</strong> {{ discrepancy.repaired_by }}<br>
                                <strong>Repaired At:</strong> {{ discrepancy.repair_timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
                            </p>
                            {% endif %}
                        </div>
                        <div class="col-md-6 text-right">
                            {% if discrepancy.status == 'pending' %}
                            <form method="post" action="{{ url_for('standardizer.approve_discrepancy') }}" class="d-inline" id="approveForm">
                                <input type="hidden" name="discrepancy_id" value="{{ discrepancy._id }}">
                                <button type="submit" class="btn btn-success">Approve Changes</button>
                            </form>
                            <form method="post" action="{{ url_for('standardizer.reject_discrepancy') }}" class="d-inline" id="rejectForm">
                                <input type="hidden" name="discrepancy_id" value="{{ discrepancy._id }}">
                                <button type="submit" class="btn btn-danger">Reject Changes</button>
                            </form>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Discrepancies -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Discrepancies</h6>
                </div>
                <div class="card-body">
                    {% for field, values in discrepancy.discrepancies.items() %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">{{ field|title }}</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger">Existing Value:</h6>
                                    <div class="border p-3 mb-3" style="max-height: 300px; overflow-y: auto;">
                                        {% if field == 'body_html' %}
                                            <div class="html-preview">{{ values.existing|safe }}</div>
                                        {% else %}
                                            <pre>{{ values.existing }}</pre>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">Simulated Value:</h6>
                                    <div class="border p-3" style="max-height: 300px; overflow-y: auto;">
                                        {% if field == 'body_html' %}
                                            <div class="html-preview">{{ values.simulated|safe }}</div>
                                        {% else %}
                                            <pre>{{ values.simulated }}</pre>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ url_for('standardizer.list_discrepancies') }}" class="btn btn-secondary btn-block">Back to List</a>
                        </div>
                        <div class="col-md-6">
                            {% if discrepancy.status == 'pending' %}
                            <form method="post" action="{{ url_for('standardizer.approve_discrepancy') }}">
                                <input type="hidden" name="discrepancy_id" value="{{ discrepancy._id }}">
                                <button type="submit" class="btn btn-success btn-block">Approve Changes</button>
                            </form>
                            {% elif discrepancy.status == 'approved' %}
                            <a href="{{ url_for('standardizer.repair_discrepancies') }}" class="btn btn-primary btn-block">Apply Repairs</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.html-preview {
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-word;
    color: white;
}
pre {
    white-space: pre-wrap;
    word-break: break-word;
    color: white;
}
.card-body {
    color: white;
}
table {
    color: white !important;
}
table tbody tr {
    background-color: transparent !important;
}

/* Loading overlay styles */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading-spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: white;
    font-size: 18px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading overlay to the page
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing changes and updating Shopify...</div>
    `;
    document.body.appendChild(loadingOverlay);
    
    // Show loading overlay when approve form is submitted
    const approveForm = document.getElementById('approveForm');
    if (approveForm) {
        approveForm.addEventListener('submit', function() {
            loadingOverlay.style.display = 'flex';
        });
    }
    
    // Show loading overlay when the bottom approve form is submitted
    const bottomApproveForm = document.querySelector('.card-body form[action*="approve_discrepancy"]');
    if (bottomApproveForm) {
        bottomApproveForm.id = 'bottomApproveForm';
        bottomApproveForm.addEventListener('submit', function() {
            loadingOverlay.style.display = 'flex';
        });
    }
    
    // Show loading overlay when batch approve form is submitted
    const batchApproveForm = document.querySelector('form[action*="batch-approve"]');
    if (batchApproveForm) {
        batchApproveForm.addEventListener('submit', function() {
            loadingOverlay.style.display = 'flex';
        });
    }
});
</script>
{% endblock %}
