{% if webhook_jobs %}
<div class="table-responsive">
    <table class="table table-hover">
        <thead class="table-light">
            <tr>
                <th>Job ID</th>
                <th>Created</th>
                <th>Updated</th>
                <th>Status</th>
                <th>Type</th>
                <th>Details</th>
            </tr>
        </thead>
        <tbody>
            {% for job in webhook_jobs %}
            <tr data-bs-toggle="collapse" data-bs-target="#webhookDetails{{ loop.index }}" class="accordion-toggle clickable">
                <td>{{ job.job_id }}</td>
                <td>{{ job.created_at_human }}</td>
                <td>{{ job.updated_at_human }}</td>
                <td>
                    <span class="badge bg-{{ job.status_class }}">
                        {{ job.status }}
                    </span>
                </td>
                <td>{{ job.job_type }}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#webhookModal{{ loop.index }}">
                        View Details
                    </button>
                    
                    <!-- Modal for webhook job details -->
                    <div class="modal fade" id="webhookModal{{ loop.index }}" tabindex="-1" aria-labelledby="webhookModalLabel{{ loop.index }}" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="webhookModalLabel{{ loop.index }}">Bulk Job Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <h6>Job ID:</h6>
                                            <p>{{ job.job_id }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Status:</h6>
                                            <p><span class="badge bg-{{ job.status_class }}">{{ job.status }}</span></p>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <h6>Created At:</h6>
                                            <p>{{ job.created_at_human }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Updated At:</h6>
                                            <p>{{ job.updated_at_human }}</p>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <h6>Job Type:</h6>
                                            <p>{{ job.job_type }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Username:</h6>
                                            <p>{{ job.username }}</p>
                                        </div>
                                    </div>
                                    
                                    {% if job.result %}
                                    <div class="mb-3">
                                        <h6>Result:</h6>
                                        <div class="alert alert-info">
                                            {{ job.result }}
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    {% if job.error %}
                                    <div class="mb-3">
                                        <h6>Error:</h6>
                                        <div class="alert alert-danger">
                                            {{ job.error }}
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    {% if job.progress %}
                                    <div class="mb-3">
                                        <h6>Progress:</h6>
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar" style="width: {{ job.progress }}%;" aria-valuenow="{{ job.progress }}" aria-valuemin="0" aria-valuemax="100">{{ job.progress }}%</div>
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    {% if job.metadata %}
                                    <div>
                                        <h6>Metadata:</h6>
                                        <pre class="bg-light p-3 rounded"><code>{{ job.metadata|tojson(indent=2) }}</code></pre>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="6" class="p-0">
                    <div id="webhookDetails{{ loop.index }}" class="collapse">
                        <div class="p-3 bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Job Details:</h6>
                                    <ul class="list-group">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Status
                                            <span class="badge bg-{{ job.status_class }}">{{ job.status }}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Job Type
                                            <span>{{ job.job_type }}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Created
                                            <span>{{ job.created_at_human }}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Updated
                                            <span>{{ job.updated_at_human }}</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    {% if job.result %}
                                    <div class="mb-3">
                                        <h6>Result:</h6>
                                        <div class="alert alert-info">
                                            {{ job.result }}
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    {% if job.error %}
                                    <div class="mb-3">
                                        <h6>Error:</h6>
                                        <div class="alert alert-danger">
                                            {{ job.error }}
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    {% if job.progress %}
                                    <div class="mb-3">
                                        <h6>Progress:</h6>
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar" style="width: {{ job.progress }}%;" aria-valuenow="{{ job.progress }}" aria-valuemin="0" aria-valuemax="100">{{ job.progress }}%</div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="no-logs-message">
    <i class="fas fa-tasks fa-3x mb-3 text-muted"></i>
    <h3>No bulk jobs found</h3>
    <p class="text-muted">There are no bulk jobs available for your account.</p>
</div>
{% endif %}

<script>
    // Initialize clickable rows for dynamically loaded content
    document.addEventListener('DOMContentLoaded', function() {
        initializeClickableRows(document.getElementById('bulkJobsData'));
    });
</script>
