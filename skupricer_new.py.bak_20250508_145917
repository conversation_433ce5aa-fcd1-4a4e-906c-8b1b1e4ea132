from concurrent.futures import Thread<PERSON>oolExecutor
from pymongo import MongoClient, UpdateOne
from requests.adapters import HTTPAdapter 
from requests.packages.urllib3.util.retry import Retry
from datetime import datetime, timedelta, timezone
from tqdm import tqdm
from itertools import islice
import requests
import sys
import logging
import time
import schedule
from threading import Lock
import signal
import re

# Configure full logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants
EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"
BATCH_SIZE = 500
DEFAULT_MIN_PRICE_USD = 0.2
MONGO_POOL_SIZE = 200

# Enhanced session configuration
session = requests.Session()
retries = Retry(
    total=5,
    backoff_factor=0.5,
    status_forcelist=[500, 502, 503, 504],
    allowed_methods=frozenset(['GET', 'POST'])  # Added POST for TCGPlayer API
)
adapter = HTTPAdapter(
    max_retries=retries,
    pool_connections=MONGO_POOL_SIZE,
    pool_maxsize=MONGO_POOL_SIZE,
    pool_block=False
)
session.mount('https://', adapter)
session.mount('http://', adapter)

# MongoDB Configuration with connection pooling
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_client = MongoClient(
    mongo_uri,
    maxPoolSize=MONGO_POOL_SIZE,
    waitQueueTimeoutMS=5000,
    connectTimeoutMS=5000,
    serverSelectionTimeoutMS=5000,
    retryWrites=True,
    w='majority'
)
db = mongo_client['test']
shopify_collection = db['shProducts']
user_collection = db['user']
tcgplayer_key_collection = db['tcgplayerKey']
autopricer_collection = db['autopricerShopify']
price_cache_collection = db['tcgplayer_price_cache']
skus_collection = db['skus']  # Collection for mapping variants to TCGPlayer SKU IDs
catalog_collection = db['catalog']  # Collection for catalog data with SKU information

# Printing type mapping from config/routes/matching.py
printing_mapping = {
    "1": "NF",
    "2": "FO",
    "7": "UL",
    "8": "1E",
    "10": "NH",
    "11": ["NH", "HF"],
    "23": "LI",
    "25": "FO",
    "32": "FO",
    "33": "FO",
    "35": "FO",
    "37": "FO",
    "40": "FO",
    "50": "FO",
    "57": "FO",
    "60": "FO",
    "70": "FO",
    "72": "FO",
    "77": "RF",
    "78": "1E",
    "79": "1HF",
    "81": ["FO", "PF"],
    "82": "FO",
    "84": "FO",
    "86": "FO",
    "88": "FO",
    "90": "FO",
    "96": "FO",
    "100": "FO",
    "105": "1ENO",
    "106": "1ECF",
    "107": "1ERF",
    "110": "UNO",
    "111": "URF",
    "112": "CF",
    "113": "RF",
    "114": "FO",
    "116": "FO",
    "120": "1ENO",
    "121": "1EFO",
    "122": "NH",
    "123": "HF",
    "125": "FO",
    "127": "FO",
    "128": "NF",
    "129": "FO",
    "133": "HF",
    "135": "HF",
    "138": "FO",
    "140": "HF",
    "141": "CF",
    "143": "FO",
    "145": "FO",
    "147": "FO",
    "149": "HF",
    "151": "HF"
}

# Reverse mapping for printing types (code to ID)
printing_code_to_id = {
    "NF": 1,
    "FO": 2,
    "UL": 7,
    "1E": 8,
    "NH": 10,
    "HF": 11,
    "LI": 23,
    "RF": 77,
    "1HF": 79,
    "PF": 81,
    "CF": 112,
    "1ENO": 105,
    "1ECF": 106,
    "1ERF": 107,
    "UNO": 110,
    "URF": 111
}

condition_mapping = {
    "1": "NM",
    "2": "LP",
    "3": "MP",
    "4": "HP",
    "5": "DM"
}

# Reverse mapping for conditions (code to ID)
condition_code_to_id = {
    "NM": 1,
    "LP": 2,
    "MP": 3,
    "HP": 4,
    "DM": 5
}

# Language mapping
language_code_to_id = {
    "EN": 1,
    "JP": 2,
    "DE": 3,
    "FR": 4,
    "IT": 5,
    "ES": 6,
    "PT": 7,
    "RU": 8,
    "KO": 9,
    "CS": 10,
    "CT": 11
}

# Create indexes
shopify_collection.create_index([("username", 1), ("product_type", 1)])

# Currency cache implementation
class CurrencyCache:
    def __init__(self, ttl_hours=24):
        self.cache = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.lock = Lock()
        self.last_cleanup = datetime.now(timezone.utc)

    def get(self, currency):
        with self.lock:
            if currency in self.cache:
                rate_data = self.cache[currency]
                if datetime.now(timezone.utc) - rate_data['timestamp'] < self.ttl:
                    return rate_data['rate']
                else:
                    del self.cache[currency]
            return None

    def set(self, currency, rate):
        with self.lock:
            self.cache[currency] = {
                'rate': rate,
                'timestamp': datetime.now(timezone.utc)
            }

    def clear_expired(self):
        with self.lock:
            current_time = datetime.now(timezone.utc)
            expired = [currency for currency, data in self.cache.items() 
                      if current_time - data['timestamp'] >= self.ttl]
            for currency in expired:
                del self.cache[currency]
            self.last_cleanup = current_time

# Initialize cache
currency_cache = CurrencyCache()

def get_exchange_rate(target_currency):
    if target_currency == 'USD':
        return 1.0
    
    cached_rate = currency_cache.get(target_currency)
    if cached_rate is not None:
        return cached_rate
    
    try:
        url = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
        response = session.get(url, timeout=10)
        response.raise_for_status()
        rates = response.json().get('conversion_rates', {})
        rate = rates.get(target_currency)
        
        if rate is None:
            logger.error(f"Exchange rate not found for {target_currency}, using 1.0")
            return 1.0
            
        currency_cache.set(target_currency, rate)
        return rate
    except Exception as e:
        logger.error(f"Error fetching exchange rate for {target_currency}: {str(e)}")
        return 1.0

def apply_fallback_pricing(price_data):
    """
    Apply fallback pricing logic for missing price points:
    - If a price is missing, use 90% of the higher tier price
    - No price hierarchy enforcement (as per user request)
    """
    if not price_data:
        return price_data
        
    # Define the price hierarchy (from highest to lowest) - only used for fallback logic
    hierarchy = ['highPrice', 'midPrice', 'marketPrice', 'lowPrice']
    
    # Create a copy of the price data to modify
    adjusted_prices = price_data.copy()
    
    # Summary of rules applied
    rules_applied = []
    
    # Apply fallback logic for missing prices (90% of higher variant)
    for i in range(len(hierarchy) - 1):
        higher_price_type = hierarchy[i]
        lower_price_type = hierarchy[i + 1]
        
        higher_price = adjusted_prices.get(higher_price_type)
        lower_price = adjusted_prices.get(lower_price_type)
        
        # If higher price exists and lower price is missing or zero
        if higher_price and (lower_price is None or lower_price == 0):
            # Set lower price to 90% of higher price
            adjusted_prices[lower_price_type] = round(higher_price * 0.9, 2)
            rule = f"RULE: {lower_price_type} missing - set to 90% of {higher_price_type}: ${higher_price:.2f} -> ${adjusted_prices[lower_price_type]:.2f}"
            rules_applied.append(rule)
            logger.info(rule)
    
    # Print summary of all rules applied
    if rules_applied:
        logger.info("\n=== Fallback Pricing Rules Applied ===")
        for rule in rules_applied:
            logger.info(rule)
        logger.info("=====================================")
    
    return adjusted_prices

def get_tcgplayer_price(sku_ids, tcgplayer_api_key):
    if not sku_ids:
        return {}

    try:
        logger.info(f"Getting prices for SKUs: {sku_ids}")
        # Check cache first
        cached_prices = {}
        missing_skus = []
        now = datetime.now(timezone.utc)
        cache_ttl = timedelta(hours=24)

        for sku_id in sku_ids:
            cached_price = price_cache_collection.find_one({
                'sku_id': sku_id,
                'timestamp': {'$gte': now - cache_ttl}
            })
            if cached_price:
                logger.info(f"Found cached price for SKU {sku_id}:")
                for price_type, value in cached_price['price_data'].items():
                    logger.info(f"  - {price_type}: ${value:.2f}")
                cached_prices[str(sku_id)] = cached_price['price_data']
            else:
                missing_skus.append(sku_id)

        if not missing_skus:
            return cached_prices

        logger.info(f"Fetching prices for missing SKUs: {missing_skus}")
        
        # Call TCGPlayer API for prices
        new_prices = {}
        for sku_id in missing_skus:
            url = f"https://api.tcgplayer.com/pricing/sku/{sku_id}"
            headers = {
                "Accept": "application/json",
                "Authorization": f"Bearer {tcgplayer_api_key}"
            }

            logger.info(f"Making API request to: {url}")
            response = session.get(url, headers=headers)
            logger.info(f"Response Status Code: {response.status_code}")
            logger.info(f"Response Headers: {dict(response.headers)}")
            response.raise_for_status()
            price_data = response.json()
            logger.info(f"Raw API Response Text: {response.text}")
            logger.info(f"Parsed API Response: {price_data}")

            if price_data.get('success') and price_data.get('results'):
                result = price_data['results'][0]  # Get first result
                if result:
                    price_info = {
                        "price": result.get('lowestListingPrice') or 0,
                        "marketPrice": result.get('marketPrice') or 0,
                        "lowPrice": result.get('lowestListingPrice') or 0,
                        "midPrice": result.get('marketPrice') or 0,  # Using marketPrice as midPrice
                        "highPrice": result.get('directLowPrice') or 0  # Using directLowPrice as highPrice
                    }
                    logger.info(f"Got new price data for SKU {sku_id}:")
                    for price_type, value in price_info.items():
                        logger.info(f"  - {price_type}: ${value:.2f}")
                    new_prices[sku_id] = price_info

                    # Cache the price data
                    price_cache_collection.update_one(
                        {'sku_id': sku_id},
                        {
                            '$set': {
                                'price_data': price_info,
                                'timestamp': now
                            }
                        },
                        upsert=True
                    )

        # Combine cached and new prices
        all_prices = {**cached_prices, **new_prices}
        logger.info(f"Total prices found: {len(all_prices)}")
        return all_prices

    except Exception as e:
        logger.error(f"Error fetching TCGPlayer prices: {str(e)}")
        return {}

def calculate_price(variant, price_data, user_profile, min_price_local, exchange_rate, user_currency):
    if not price_data:
        return None, True

    try:
        logger.info("\n=== Price Calculation Start ===")
        logger.info(f"Calculating price for variant: {variant.get('title')}")
        logger.info("\nRaw price data:")
        for price_type, value in price_data.items():
            logger.info(f"  - {price_type}: ${value:.2f}")
            
        # Apply fallback pricing for missing price points
        price_data = apply_fallback_pricing(price_data)
        logger.info("\nPrice data after fallback logic:")
        for price_type, value in price_data.items():
            logger.info(f"  - {price_type}: ${value:.2f}")

        # Get price comparison settings from user profile
        use_highest_price = user_profile.get('use_highest_price', False)
        price_comparison_pairs = user_profile.get('price_comparison_pairs', [])
        price_modifiers = user_profile.get('price_modifiers', {})
        price_preferences = user_profile.get('price_preference_order', 
            ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])
        
        logger.info("\nUser settings:")
        logger.info(f"  - Use highest price: {use_highest_price}")
        logger.info(f"  - Price comparison pairs: {price_comparison_pairs}")
        logger.info(f"  - Price modifiers: {price_modifiers}")
        logger.info(f"  - Price preferences: {price_preferences}")
        
        # Try to get price using comparison pairs first
        base_price_usd = None
        price_type = None
        
        if price_comparison_pairs:
            logger.info("\nChecking price comparison pairs:")
            # Check each price pair
            for pair in price_comparison_pairs:
                if len(pair) != 2:
                    continue
                    
                price1_type, price2_type = pair
                price1 = price_data.get(price1_type)
                price2 = price_data.get(price2_type)
                
                logger.info(f"\nComparing pair: {price1_type} vs {price2_type}")
                logger.info(f"  - {price1_type}: ${price1:.2f}")
                logger.info(f"  - {price2_type}: ${price2:.2f}")
                
                # Skip if either price is missing
                if price1 is None or price2 is None:
                    logger.info("  Skipping pair - missing price")
                    continue
                    
                # Apply modifiers if they exist
                if price1_type in price_modifiers:
                    modifier = price_modifiers[price1_type]
                    original_price1 = price1
                    price1 = price1 * (1 + modifier/100)
                    logger.info(f"  Applied {modifier}% modifier to {price1_type}: ${original_price1:.2f} -> ${price1:.2f}")
                    
                if price2_type in price_modifiers:
                    modifier = price_modifiers[price2_type]
                    original_price2 = price2
                    price2 = price2 * (1 + modifier/100)
                    logger.info(f"  Applied {modifier}% modifier to {price2_type}: ${original_price2:.2f} -> ${price2:.2f}")
                    
                if use_highest_price:
                    # Use highest price from the pair
                    if price1 >= price2:
                        base_price_usd = price1
                        price_type = price1_type
                        logger.info(f"  Selected highest price: {price_type} = ${base_price_usd:.2f}")
                    else:
                        base_price_usd = price2
                        price_type = price2_type
                        logger.info(f"  Selected highest price: {price_type} = ${base_price_usd:.2f}")
                else:
                    # Use first valid price from the pair
                    if price1 is not None:
                        base_price_usd = price1
                        price_type = price1_type
                        logger.info(f"  Selected first valid price: {price_type} = ${base_price_usd:.2f}")
                    elif price2 is not None:
                        base_price_usd = price2
                        price_type = price2_type
                        logger.info(f"  Selected first valid price: {price_type} = ${base_price_usd:.2f}")
                
                # If we found a valid price, break out of the loop
                if base_price_usd is not None:
                    break
        
        # Fall back to price preferences if no valid price from pairs
        if base_price_usd is None:
            logger.info("\nNo price from pairs, checking price preferences:")
            for price_type in price_preferences:
                if price_type in price_data and price_data[price_type] is not None:
                    base_price_usd = price_data[price_type]
                    logger.info(f"  Selected {price_type} price: ${base_price_usd:.2f}")
                    # Apply modifier if it exists
                    if price_type in price_modifiers:
                        modifier = price_modifiers[price_type]
                        original_price = base_price_usd
                        base_price_usd = base_price_usd * (1 + modifier/100)
                        logger.info(f"  Applied {modifier}% modifier: ${original_price:.2f} -> ${base_price_usd:.2f}")
                    break

        if base_price_usd is None:
            logger.info("No valid price found")
            return None, True

        # Convert to user's currency
        price = round(base_price_usd * exchange_rate, 2)
        logger.info(f"\nConverted to user currency (rate {exchange_rate}): ${base_price_usd:.2f} USD -> ${price:.2f} {user_currency}")

        # Initialize minimum price in user's currency
        min_price = min_price_local
        logger.info(f"\nChecking minimum prices:")
        logger.info(f"  Base minimum price: ${min_price:.2f}")

        # Check game-specific minimums if available
        if 'game_minimum_prices' in user_profile:
            product_type = variant.get('product_type', '')
            game_name = next((game for game in user_profile['game_minimum_prices'].keys() 
                           if product_type.startswith(game)), None)
            if game_name:
                game_settings = user_profile['game_minimum_prices'][game_name]
                logger.info(f"\nFound game settings for {game_name}:")
                logger.info(f"  Settings: {game_settings}")
                
                # Check game default minimum
                game_default_min = game_settings.get('default_min_price')
                if game_default_min is not None:
                    old_min = min_price
                    min_price = max(min_price, game_default_min)
                    logger.info(f"  Applied game default minimum: ${old_min:.2f} -> ${min_price:.2f}")
                    
                # Check rarity minimum
                rarity = variant.get('rarity')
                if rarity:
                    rarity_min = game_settings.get('rarities', {}).get(rarity)
                    if rarity_min is not None:
                        old_min = min_price
                        min_price = max(min_price, rarity_min)
                        logger.info(f"  Applied {rarity} rarity minimum: ${old_min:.2f} -> ${min_price:.2f}")

        # Apply minimum price
        old_price = price
        price = round(max(price, min_price), 2)
        if price > old_price:
            logger.info(f"\nApplied minimum price: ${old_price:.2f} -> ${price:.2f}")

        logger.info(f"\nFinal price: ${price:.2f} {user_currency}")
        logger.info("=== Price Calculation End ===\n")

        return price, False
    except Exception as e:
        logger.error(f"Error calculating price: {str(e)}")
        return None, True

def process_user(config, tcgplayer_api_key):
    try:
        username = config['username']
        selected_product_types = [pt.lower() for pt in config['selectedProductTypes']]
        
        user_profile = user_collection.find_one({'username': username})
        if not user_profile:
            logger.error(f"User profile not found for {username}")
            return

        # Log user profile for debugging
        logger.info(f"User profile: {user_profile}")
        
        # Skip if user hasn't opted into SKU pricing
        if not user_profile.get('use_skuid_pricing', False):
            logger.info(f"Skipping user {username} - SKU pricing not enabled")
            return
        
        logger.info(f"\nProcessing user {username}:")
        logger.info(f"  Currency: {user_profile.get('currency', 'USD')}")
        logger.info(f"  Min price: ${user_profile.get('minPrice', DEFAULT_MIN_PRICE_USD):.2f}")
        logger.info(f"  Selected product types: {selected_product_types}")
        
        user_currency = user_profile.get('currency', 'USD')
        min_price_local = user_profile.get('minPrice', DEFAULT_MIN_PRICE_USD)
        exchange_rate = get_exchange_rate(user_currency)

        # Only process products with types that the user has selected
        if not selected_product_types:
            logger.info(f"Skipping user {username} - no product types selected")
            return
            
        # Process all products with selected product types where skusMatched is true
        query = {
            'username': username,
            'product_type': {
                '$regex': f'^({"|".join(map(re.escape, selected_product_types))})$',
                '$options': 'i'
            },
            'skusMatched': True
        }
        
        cursor = shopify_collection.find(query, batch_size=BATCH_SIZE)
        total_products = shopify_collection.count_documents(query)
        
        if total_products == 0:
            logger.info(f"No products found for {username} with selected product types and skusMatched=true")
            return
            
        logger.info(f"Found {total_products} products for {username} with selected product types and skusMatched=true")
        
        bulk_updates = []
        total_updates = 0
        total_price_changes = 0
        
        with tqdm(total=total_products, desc=f"Processing {username}", leave=True) as pbar:
            while True:
                batch = list(islice(cursor, BATCH_SIZE))
                if not batch:
                    break

                # Collect all SKUs from variants in the batch
                sku_ids = []
                products_to_process = []
                
                for product in batch:
                    # Add product to the list of products to process
                    products_to_process.append(product)
                    
                    # Collect SKU IDs for variants that have skuId
                    variants_with_skuid = []
                    for variant in product.get('variants', []):
                        if 'skuId' in variant:
                            sku_ids.append(variant['skuId'])
                            variants_with_skuid.append(variant)
                            logger.info(f"Using skuId {variant['skuId']} for variant {variant.get('id', 'unknown')}")
                        else:
                            logger.info(f"Variant {variant.get('id', 'unknown')} with SKU {variant.get('sku', 'unknown')} is missing skuId - will skip this variant only")

                logger.info(f"Processing batch with SKUs: {sku_ids}")
                # Get prices for all SKUs
                sku_prices = get_tcgplayer_price(sku_ids, tcgplayer_api_key)
                
                # Process only products where all variants have skuId
                for product in products_to_process:
                    try:
                        variants_changed = False
                        variants = product.get('variants', [])
                        logger.info(f"Processing product: {product.get('title')} with {len(variants)} variants")
                            
                        # Extract condition and edition from all variants first
                        variant_prices = []
                        for variant in variants:
                            old_price = float(variant.get('price', '0.00'))
                            
                            # Extract condition and edition from variant title or SKU
                            condition = None
                            edition = None
                            title = variant.get('title', '').lower()
                            
                            if 'near mint' in title or 'nm' in title:
                                condition = 'NM'
                            elif 'lightly played' in title or 'lp' in title:
                                condition = 'LP'
                            elif 'moderately played' in title or 'mp' in title:
                                condition = 'MP'
                            elif 'heavily played' in title or 'hp' in title:
                                condition = 'HP'
                            elif 'damaged' in title or 'dm' in title:
                                condition = 'DM'
                                
                            if '1st edition' in title or '1st ed' in title:
                                edition = '1E'
                            elif 'unlimited' in title or 'unl' in title:
                                edition = 'UL'
                                
                            # If we couldn't extract from title, try from SKU
                            if (not condition or not edition) and 'sku' in variant:
                                sku = variant.get('sku', '')
                                sku_parts = sku.split('-')
                                if len(sku_parts) == 5:
                                    # SKU format: SET-NUMBER-LANG-PRINTING-CONDITION
                                    condition_code = sku_parts[4]
                                    printing_code = sku_parts[3]
                                    
                                    # Map condition code to condition
                                    if condition_code == '1':
                                        condition = 'NM'
                                    elif condition_code == '2':
                                        condition = 'LP'
                                    elif condition_code == '3':
                                        condition = 'MP'
                                    elif condition_code == '4':
                                        condition = 'HP'
                                    elif condition_code == '5':
                                        condition = 'DM'
                                        
                                    # Map printing code to edition
                                    if printing_code.upper() == '1E':
                                        edition = '1E'
                                    elif printing_code.upper() == 'UL':
                                        edition = 'UL'
                            
                            variant_prices.append({
                                'variant': variant,
                                'old_price': old_price,
                                'condition': condition,
                                'edition': edition,
                                'new_price': None,
                                'is_missing': True
                            })
                        
                        # Define condition hierarchy (best to worst)
                        condition_hierarchy = {'NM': 0, 'LP': 1, 'MP': 2, 'HP': 3, 'DM': 4}
                        
                        # Group variants by edition
                        edition_groups = {}
                        for vp in variant_prices:
                            edition = vp['edition'] or 'unknown'
                            if edition not in edition_groups:
                                edition_groups[edition] = []
                            edition_groups[edition].append(vp)
                        
                        # Process each edition group separately
                        for edition, group in edition_groups.items():
                            # Sort by condition (best to worst)
                            sorted_group = sorted(group, key=lambda x: condition_hierarchy.get(x['condition'], 999))
                            
                            # Get custom stepping percentages from user profile
                            custom_stepping = user_profile.get('customStepping', {
                                'nm': 100.0,
                                'lp': 90.0,
                                'mp': 80.0,
                                'hp': 70.0,
                                'dm': 60.0
                            })
                            
                            logger.info("\n=== CUSTOM STEPPING PERCENTAGES ===")
                            for condition, percentage in custom_stepping.items():
                                logger.info(f"{condition.upper()}: {percentage}%")
                            logger.info("=================================")
                            
                            # Process variants sequentially, using previous condition's price as fallback
                            previous_price = None
                            previous_condition = None
                            
                            # Process variants in order (best condition to worst)
                            for vp in sorted_group:
                                variant = vp['variant']
                                old_price = vp['old_price']
                                condition = vp['condition']
                                
                                # Get skuId safely with a default value if not present
                                sku_id = variant.get('skuId', 'Not assigned')
                                logger.info(f"\nProcessing variant: {variant.get('title')} ({condition})")
                                logger.info(f"SKU ID: {sku_id}")
                                logger.info(f"Current price: ${old_price:.2f} {user_currency}")
                                
                                # Set price to 9999 if missing or zero
                                if not variant.get('price') or old_price == 0:
                                    vp['
