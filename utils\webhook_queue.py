from config.config import Config
import redis
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from functools import wraps
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Redis configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0
WEBHOOK_QUEUE_KEY = 'shopify_webhooks'
BATCH_SIZE = 50  # Reduced batch size for better throughput
RATE_LIMIT_KEY = 'webhook_rate_limit'
MAX_REQUESTS_PER_MINUTE = 40  # Reduced to be more conservative
PROCESSING_SET_KEY = 'webhooks_processing'
PROCESSING_TIMEOUT = 300  # 5 minutes timeout for processing

class WebhookQueue:
    def __init__(self):
        self.redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
    def rate_limit_decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            current_minute = int(time.time() / 60)
            rate_limit_key = f"{RATE_LIMIT_KEY}:{current_minute}"
            
            # Use pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            try:
                # Increment counter and set expiry atomically
                pipe.incr(rate_limit_key)
                pipe.expire(rate_limit_key, 60)
                result = pipe.execute()
                current_count = result[0]
                
                if current_count > MAX_REQUESTS_PER_MINUTE:
                    logger.warning(f"Rate limit exceeded: {current_count}/{MAX_REQUESTS_PER_MINUTE}")
                    return False
                    
                return func(self, *args, **kwargs)
            finally:
                pipe.reset()
                
        return wrapper
    
    @rate_limit_decorator
    def enqueue_webhook(self, webhook_data: Dict[str, Any]) -> bool:
        """
        Add a webhook to the processing queue with deduplication
        """
        try:
            # Add timestamp and generate unique key
            webhook_data['enqueued_at'] = datetime.utcnow().isoformat()
            unique_key = f"{webhook_data.get('shop_domain')}:{webhook_data.get('topic')}:{webhook_data.get('data', {}).get('id')}"
            
            # Use pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            try:
                # Check if webhook is already being processed
                if self.redis_client.sismember(PROCESSING_SET_KEY, unique_key):
                    logger.info(f"Webhook already being processed: {unique_key}")
                    return True
                
                # Add to processing set with timeout
                pipe.sadd(PROCESSING_SET_KEY, unique_key)
                pipe.expire(PROCESSING_SET_KEY, PROCESSING_TIMEOUT)
                
                # Add to queue
                pipe.rpush(WEBHOOK_QUEUE_KEY, json.dumps(webhook_data))
                pipe.execute()
                
                logger.info(f"Enqueued webhook: {webhook_data.get('topic')} for shop {webhook_data.get('shop_domain')}")
                return True
            finally:
                pipe.reset()
                
        except Exception as e:
            logger.error(f"Error enqueueing webhook: {str(e)}")
            return False
            
    def get_batch(self, batch_size: int = BATCH_SIZE) -> List[Dict[str, Any]]:
        """
        Get a batch of webhooks for processing with improved error handling
        """
        try:
            batch = []
            pipe = self.redis_client.pipeline()
            
            try:
                # Get multiple items atomically
                for _ in range(batch_size):
                    pipe.lpop(WEBHOOK_QUEUE_KEY)
                results = pipe.execute()
                
                # Process results
                for item in results:
                    if item:
                        try:
                            webhook = json.loads(item)
                            batch.append(webhook)
                        except json.JSONDecodeError:
                            logger.error(f"Invalid JSON in webhook: {item}")
                            continue
                            
                return batch
            finally:
                pipe.reset()
                
        except Exception as e:
            logger.error(f"Error getting webhook batch: {str(e)}")
            return []
            
    def get_queue_length(self) -> int:
        """
        Get current length of webhook queue with retry
        """
        retries = 3
        for attempt in range(retries):
            try:
                return self.redis_client.llen(WEBHOOK_QUEUE_KEY)
            except Exception as e:
                if attempt == retries - 1:
                    logger.error(f"Error getting queue length: {str(e)}")
                    return 0
                time.sleep(0.1 * (attempt + 1))
                
    def clear_queue(self) -> bool:
        """
        Clear the webhook queue and processing set
        """
        try:
            pipe = self.redis_client.pipeline()
            try:
                pipe.delete(WEBHOOK_QUEUE_KEY)
                pipe.delete(PROCESSING_SET_KEY)
                pipe.execute()
                return True
            finally:
                pipe.reset()
        except Exception as e:
            logger.error(f"Error clearing queue: {str(e)}")
            return False
            
    def remove_from_processing(self, webhook_data: Dict[str, Any]) -> None:
        """
        Remove a webhook from the processing set after completion
        """
        try:
            unique_key = f"{webhook_data.get('shop_domain')}:{webhook_data.get('topic')}:{webhook_data.get('data', {}).get('id')}"
            self.redis_client.srem(PROCESSING_SET_KEY, unique_key)
        except Exception as e:
            logger.error(f"Error removing webhook from processing set: {str(e)}")

# Singleton instance
webhook_queue = WebhookQueue()

