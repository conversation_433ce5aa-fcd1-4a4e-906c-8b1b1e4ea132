import jwt
from datetime import datetime, timedelta
from config.config import Config

def generate_token(user):
    payload = {
        'exp': datetime.utcnow() + timedelta(days=1),
        'iat': datetime.utcnow(),
        'sub': str(user.id),
        'roles': user.roles
    }
    return jwt.encode(payload, Config.SECRET_KEY, algorithm='HS256')

def decode_token(token):
    payload = jwt.decode(token, Config.SECRET_KEY, algorithms=['HS256'])
    return payload['sub']
