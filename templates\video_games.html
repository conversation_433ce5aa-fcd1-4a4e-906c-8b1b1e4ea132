{% extends "base.html" %}

{% block head %}
<!-- Add jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- Add DataTables -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.css">
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.js"></script>
<!-- Add Bootstrap -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Add Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
{% endblock %}

{% block content %}
<style>
.table {
    color: white;
}
.table thead th {
    color: white;
    background-color: #1e2124;
}
.form-label, .pagination, #totalCount, #currentPage, #totalPages {
    color: white;
}
.modal-content {
    color: black;
}
.card {
    background-color: #1e2124;
    border-color: #2c3034;
}
.card-body {
    color: white;
}
.table > tbody > tr {
    background-color: #1e2124;
    border-bottom: 4px solid #2c3034;
}
.table td, .table th {
    padding: 1rem;
    vertical-align: middle;
}
.table td img {
    border-radius: 4px;
}
.table > tbody > tr:hover {
    background-color: #2c3034;
}
.table > thead > tr {
    border-bottom: 4px solid #2c3034;
}
.dataTables_info {
    color: white !important;
}
.btn-outline-secondary {
    color: white;
    border-color: white;
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
}
.btn-outline-secondary:hover, .btn-outline-secondary.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}
.form-control-lg {
    height: 3.5rem;
    font-size: 1.25rem;
}
.btn-lg {
    height: 3.5rem;
    font-size: 1.25rem;
    padding-left: 2rem;
    padding-right: 2rem;
}
.rounded-pill {
    border-radius: 50rem !important;
}
.page-link {
    background-color: #1e2124;
    border-color: #2c3034;
    color: white;
}
.page-link:hover {
    background-color: #2c3034;
    border-color: #2c3034;
    color: white;
}
.page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}
.page-item.disabled .page-link {
    background-color: #1e2124;
    border-color: #2c3034;
    color: #6c757d;
}
.form-select {
    background-color: #2c3034;
    border-color: #2c3034;
    color: white;
}
.form-select:focus {
    background-color: #2c3034;
    border-color: #2c3034;
    color: white;
}
.form-control {
    background-color: #2c3034;
    border-color: #2c3034;
    color: white;
}
.form-control:focus {
    background-color: #2c3034;
    border-color: #2c3034;
    color: white;
}
.form-control::placeholder {
    color: #6c757d;
}
.dataTables_wrapper .dataTables_paginate .paginate_button {
    color: white !important;
}
.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    color: white !important;
    background: #0d6efd !important;
    border-color: #0d6efd !important;
}
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    color: white !important;
    background: #2c3034 !important;
    border-color: #2c3034 !important;
}
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    color: white !important;
}
</style>

<div class="container-fluid px-4">
    <h1 class="mt-4 text-white">Video Games Inventory</h1>

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <!-- Search Bar -->
                <div class="col-12 mb-4">
                    <div class="input-group">
                        <input type="text" id="gameSearch" class="form-control form-control-lg" placeholder="Search by game name, genre, developer, or publisher...">
                        <button class="btn btn-primary btn-lg" type="button" onclick="applyFilters()">Search</button>
                    </div>
                </div>

                <!-- Quick Filter Pills -->
                <div class="col-12 mb-4">
                    <div class="d-flex gap-2 flex-wrap">
                        <button class="btn btn-outline-secondary rounded-pill" onclick="setQuickFilter('popular')">Popular Games</button>
                        <button class="btn btn-outline-secondary rounded-pill" onclick="setQuickFilter('new')">New Releases</button>
                        <button class="btn btn-outline-secondary rounded-pill" onclick="setQuickFilter('top')">Top Rated</button>
                        <button class="btn btn-outline-secondary rounded-pill" onclick="setQuickFilter('multiplayer')">Multiplayer</button>
                    </div>
                </div>

                <!-- Filter Controls -->
                <div class="col-12 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="text-white mb-0">Filters</h5>
                        <button class="btn btn-primary" type="button" onclick="applyFilters()">Apply Filters</button>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label">Genres</label>
                    <select class="form-select" id="genreFilter">
                        <option value="">All Genres</option>
                        <option value="Action">Action</option>
                        <option value="Adventure">Adventure</option>
                        <option value="RPG">RPG</option>
                        <option value="Strategy">Strategy</option>
                        <option value="Sports">Sports</option>
                        <option value="Racing">Racing</option>
                        <option value="Simulation">Simulation</option>
                        <option value="Puzzle">Puzzle</option>
                    </select>
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label">Platform</label>
                    <select class="form-select" id="platformFilter">
                        <option value="">All Platforms</option>
                        <option value="PC">PC</option>
                        <option value="PS5">PS5</option>
                        <option value="PS4">PS4</option>
                        <option value="Xbox Series X">Xbox Series X</option>
                        <option value="Xbox One">Xbox One</option>
                        <option value="Nintendo Switch">Nintendo Switch</option>
                    </select>
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label">Players</label>
                    <select class="form-select" id="playerCountFilter">
                        <option value="">Any Players</option>
                        <option value="1">Single Player</option>
                        <option value="2+">Multiplayer</option>
                    </select>
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label">Age Rating</label>
                    <select class="form-select" id="ageFilter">
                        <option value="">Any Rating</option>
                        <option value="E">Everyone</option>
                        <option value="E10+">Everyone 10+</option>
                        <option value="T">Teen</option>
                        <option value="M">Mature</option>
                    </select>
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label">Year Released</label>
                    <select class="form-select" id="yearFilter">
                        <option value="">Any Year</option>
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                        <option value="2022">2022</option>
                        <option value="2021">2021</option>
                        <option value="older">2020 or earlier</option>
                    </select>
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label">Rating</label>
                    <select class="form-select" id="ratingFilter">
                        <option value="">Any Rating</option>
                        <option value="9+">9+ Rating</option>
                        <option value="8+">8+ Rating</option>
                        <option value="7+">7+ Rating</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table" id="videoGamesTable">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Year</th>
                            <th>Platform</th>
                            <th>Genre</th>
                            <th>Age</th>
                            <th>Rating</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for game in games %}
                        <tr>
                            <td>
                                {% if game.image_url %}
                                <img src="{{ game.image_url }}" alt="{{ game.name }}" style="max-width: 50px; max-height: 50px;">
                                {% endif %}
                            </td>
                            <td>{{ game.name }}</td>
                            <td>{{ game.release_year }}</td>
                            <td>
                                {% for platform in game.platforms[:2] %}
                                <span class="badge bg-secondary text-white">{{ platform }}</span>
                                {% endfor %}
                                {% if game.platforms|length > 2 %}
                                <span class="badge bg-secondary text-white">+{{ game.platforms|length - 2 }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% for genre in game.genres[:2] %}
                                <span class="badge bg-secondary text-white">{{ genre }}</span>
                                {% endfor %}
                                {% if game.genres|length > 2 %}
                                <span class="badge bg-secondary text-white">+{{ game.genres|length - 2 }}</span>
                                {% endif %}
                            </td>
                            <td>{{ game.age_rating }}</td>
                            <td>{{ "%.1f"|format(game.rating) if game.rating else "N/A" }}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#gameModal{{ game.id }}">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </td>
                        </tr>

                        <!-- Modal for game details -->
                        <div class="modal fade" id="gameModal{{ game.id }}" tabindex="-1" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">{{ game.name }} ({{ game.release_year }})</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                {% if game.image_url %}
                                                <img src="{{ game.image_url }}" alt="{{ game.name }}" class="img-fluid">
                                                {% endif %}
                                            </div>
                                            <div class="col-md-8">
                                                <h6>Description</h6>
                                                <p>{{ game.description }}</p>
                                                
                                                <h6>Details</h6>
                                                <ul class="list-unstyled">
                                                    <li><strong>Developers:</strong> {{ game.developers|join(', ') }}</li>
                                                    <li><strong>Publishers:</strong> {{ game.publishers|join(', ') }}</li>
                                                    <li><strong>Genres:</strong> {{ game.genres|join(', ') }}</li>
                                                    <li><strong>Platforms:</strong> {{ game.platforms|join(', ') }}</li>
                                                    <li><strong>Age Rating:</strong> {{ game.age_rating }}</li>
                                                    <li><strong>Multiplayer:</strong> {{ 'Yes' if game.is_multiplayer else 'No' }}</li>
                                                </ul>

                                                <h6>Statistics</h6>
                                                <ul class="list-unstyled">
                                                    <li><strong>Overall Rank:</strong> {{ game.rank if game.rank else 'N/A' }}</li>
                                                    <li><strong>Rating:</strong> {{ "%.2f"|format(game.rating) if game.rating else 'N/A' }}</li>
                                                    <li><strong>Total Ratings:</strong> {{ game.total_ratings }}</li>
                                                    <li><strong>Metacritic Score:</strong> {{ game.metacritic_score if game.metacritic_score else 'N/A' }}</li>
                                                    <li><strong>User Score:</strong> {{ game.user_score if game.user_score else 'N/A' }}</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
let dataTable;

function setQuickFilter(filter) {
    // Remove active class from all quick filter buttons
    document.querySelectorAll('.btn-outline-secondary').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Add active class to clicked button
    const clickedButton = document.querySelector(`[onclick="setQuickFilter('${filter}')"]`);
    if (clickedButton) {
        clickedButton.classList.add('active');
    }
    
    // Reset other filters
    document.getElementById('genreFilter').value = '';
    document.getElementById('platformFilter').value = '';
    document.getElementById('playerCountFilter').value = '';
    document.getElementById('ageFilter').value = '';
    document.getElementById('yearFilter').value = '';
    document.getElementById('ratingFilter').value = '';
    document.getElementById('gameSearch').value = '';
    
    applyFilters();
}

function applyFilters() {
    if (dataTable) {
        dataTable.ajax.reload();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    dataTable = new DataTable('#videoGamesTable', {
        processing: true,
        serverSide: true,
        dom: 'rt<"bottom"ip>', // Hide default search box
        ajax: {
            url: '{{ url_for("video_games.search_video_games") }}',
            data: function(d) {
                d.query = document.getElementById('gameSearch').value;
                d.page = (d.start / d.length) + 1;
                d.genre = document.getElementById('genreFilter').value;
                d.platform = document.getElementById('platformFilter').value;
                d.players = document.getElementById('playerCountFilter').value;
                d.age = document.getElementById('ageFilter').value;
                d.year = document.getElementById('yearFilter').value;
                d.rating = document.getElementById('ratingFilter').value;
                d.quick = document.querySelector('.btn-outline-secondary.active')?.textContent.toLowerCase().split(' ')[0] || '';
            },
            dataSrc: function(json) {
                // Update pagination info
                document.getElementById('totalCount').textContent = json.total_count;
                document.getElementById('currentPage').textContent = json.current_page;
                document.getElementById('totalPages').textContent = json.total_pages;
                
                return json.games;
            }
        },
        pageLength: 25,
        columns: [
            { 
                data: 'image_url',
                render: function(data, type, row) {
                    return data ? `<img src="${data}" alt="${row.name}" style="max-width: 50px; max-height: 50px;">` : '';
                }
            },
            { data: 'name' },
            { data: 'release_year' },
            { 
                data: 'platforms',
                render: function(data) {
                    if (!data) return '';
                    let badges = data.slice(0, 2).map(platform => 
                        `<span class="badge bg-secondary text-white">${platform}</span>`
                    ).join(' ');
                    if (data.length > 2) {
                        badges += ` <span class="badge bg-secondary text-white">+${data.length - 2}</span>`;
                    }
                    return badges;
                }
            },
            { 
                data: 'genres',
                render: function(data) {
                    if (!data) return '';
                    let badges = data.slice(0, 2).map(genre => 
                        `<span class="badge bg-secondary text-white">${genre}</span>`
                    ).join(' ');
                    if (data.length > 2) {
                        badges += ` <span class="badge bg-secondary text-white">+${data.length - 2}</span>`;
                    }
                    return badges;
                }
            },
            { data: 'age_rating' },
            { 
                data: 'rating',
                render: function(data) {
                    return data ? data.toFixed(1) : 'N/A';
                }
            },
            { 
                data: null,
                render: function(data, type, row) {
                    return `<button type="button" class="btn btn-sm btn-primary" onclick="showGameDetails('${row.id}')">
                        <i class="fas fa-info-circle"></i>
                    </button>`;
                }
            }
        ],
        order: [[1, 'asc']],
        columnDefs: [
            { orderable: false, targets: [0, 7] }
        ]
    });

    // Handle search input
    document.getElementById('gameSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            applyFilters();
        }
    });
});

function showGameDetails(gameId) {
    // Get game data from DataTable
    const game = dataTable.rows().data().toArray().find(g => g.id === gameId);
    if (!game) return;

    // Create modal HTML
    const modalHtml = `
        <div class="modal fade" id="gameModal${game.id}" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${game.name} (${game.release_year})</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4">
                                ${game.image_url ? `<img src="${game.image_url}" alt="${game.name}" class="img-fluid">` : ''}
                            </div>
                            <div class="col-md-8">
                                <h6>Description</h6>
                                <p>${game.description || 'No description available.'}</p>
                                
                                <h6>Details</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Developers:</strong> ${(game.developers || []).join(', ') || 'N/A'}</li>
                                    <li><strong>Publishers:</strong> ${(game.publishers || []).join(', ') || 'N/A'}</li>
                                    <li><strong>Genres:</strong> ${(game.genres || []).join(', ') || 'N/A'}</li>
                                    <li><strong>Platforms:</strong> ${(game.platforms || []).join(', ') || 'N/A'}</li>
                                    <li><strong>Age Rating:</strong> ${game.age_rating || 'N/A'}</li>
                                    <li><strong>Multiplayer:</strong> ${game.is_multiplayer ? 'Yes' : 'No'}</li>
                                </ul>

                                <h6>Statistics</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Overall Rank:</strong> ${game.rank || 'N/A'}</li>
                                    <li><strong>Rating:</strong> ${game.rating ? game.rating.toFixed(2) : 'N/A'}</li>
                                    <li><strong>Total Ratings:</strong> ${game.total_ratings || 'N/A'}</li>
                                    <li><strong>Metacritic Score:</strong> ${game.metacritic_score || 'N/A'}</li>
                                    <li><strong>User Score:</strong> ${game.user_score || 'N/A'}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>`;

    // Remove existing modal if it exists
    const existingModal = document.getElementById(`gameModal${game.id}`);
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById(`gameModal${game.id}`));
    modal.show();
}
</script>

<!-- Pagination Info -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        Total Games: <span id="totalCount">{{ total_count }}</span>
    </div>
    <div>
        Page <span id="currentPage">{{ current_page }}</span> of <span id="totalPages">{{ total_pages }}</span>
    </div>
</div>
{% endblock %}
