from config.config import Config
import json
import requests
import os

# Shopify credentials should be set in environment variables
SHOPIFY_STORE_NAME = os.getenv('SHOPIFY_STORE_NAME')
SHOPIFY_ACCESS_TOKEN = os.getenv('SHOPIFY_ACCESS_TOKEN')

if not SHOPIFY_STORE_NAME or not SHOPIFY_ACCESS_TOKEN:
    print("Error: Please set SHOPIFY_STORE_NAME and SHOPIFY_ACCESS_TOKEN environment variables")
    exit(1)

# Board game data
data = {
    "id": 224517,
    "name": "Brass: Birmingham",
    "yearpublished": 2018,
    "rank": 1,
    "bayesaverage": 8.40836,
    "average": 8.58515,
    "usersrated": 49940,
    "is_expansion": 0,
    "strategygames_rank": 1,
    "artists": [
        "Gavan Brown",
        "<PERSON>a Cossette",
        "David Forest",
        "Gui Landgraf",
        "<PERSON>",
        "<PERSON>"
    ],
    "categories": [
        "Age of Reason",
        "Economic",
        "Industry / Manufacturing",
        "Post-Napoleonic",
        "Trains",
        "Transportation"
    ],
    "description": "Brass: Birmingham is an economic strategy game sequel to <PERSON> 2007 masterpiece, Brass. Birmingham tells the story of competing entrepreneurs in Birmingham during the industrial revolution between the years of 1770 and 1870.",
    "designers": [
        "<PERSON>avan <PERSON>",
        "Matt Tolman",
        "<PERSON> <PERSON>"
    ],
    "image_url": "https://cf.geekdo-images.com/x3zxjr-Vw5iU4yDPg70Jgw__original/img/FpyxH41Y6_ROoePAilPNEhXnzO8=/0x0/filters:format(jpeg)/pic3490053.jpg",
    "max_players": "4",
    "mechanics": [
        "Hand Management",
        "Income",
        "Loans",
        "Market",
        "Multi-Use Cards",
        "Network and Route Building",
        "Tags",
        "Tech Trees / Tech Tracks",
        "Turn Order: Stat-Based",
        "Variable Set-up"
    ],
    "min_age": "14",
    "min_players": "2",
    "playing_time": "120",
    "publishers": [
        "Roxley",
        "Arclight Games",
        "Board Game Rookie"
    ],
    "weight": 3.8706,
    "age": "14",
    "year_published": 2018,
    # Add Shopify credentials
    "shopifyStoreName": SHOPIFY_STORE_NAME,
    "shopifyAccessToken": SHOPIFY_ACCESS_TOKEN
}

def test_preview():
    """Test the preview endpoint"""
    print("1. Previewing product...")
    response = requests.post(
        "http://localhost:5011/api/board-games/preview",
        headers={"Content-Type": "application/json"},
        json=data
    )
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        print("Preview Response:")
        print(json.dumps(response.json(), indent=2))
        return True
    else:
        print("Error Response:")
        print(response.text)
        return False

def test_create():
    """Test the create endpoint"""
    print("\n2. Creating product in Shopify...")
    response = requests.post(
        "http://localhost:5011/api/board-games/create-product",
        headers={"Content-Type": "application/json"},
        json=data
    )
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        print("Create Response:")
        result = response.json()
        print(json.dumps(result, indent=2))
        print(f"\nProduct created successfully!")
        print(f"View in Shopify admin: {result['product']['url']}")
        return True
    else:
        print("Error Response:")
        print(response.text)
        return False

if __name__ == "__main__":
    # First test preview
    if test_preview():
        # If preview works, test create
        test_create()

