import logging
from datetime import datetime, timezone
from pymongo import MongoClient
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('shautopricer_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_client = MongoClient(mongo_uri)
db = mongo_client['test']

def setup_test_data():
    """Set up test data for user Khaoz"""
    logger.info("Setting up test data for user Khaoz")
    
    # Set up user profile with all pricing features
    user_data = {
        'username': 'Khaoz',
        'currency': 'GBP',  # Test currency conversion
        'minPrice': 0.25,  # Base minimum price
        'price_preference_order': ['marketPrice', 'lowPrice', 'midPrice', 'highPrice'],
        'use_highest_price': True,
        'price_comparison_pairs': [
            ['lowPrice', 'marketPrice'],
            ['midPrice', 'highPrice']
        ],
        'price_modifiers': {
            'lowPrice': 10,  # Add 10% to low price
            'marketPrice': 5  # Add 5% to market price
        },
        'price_rounding_enabled': True,
        'price_rounding_thresholds': [49, 99],
        'game_minimum_prices': {
            'Magic: The Gathering': {
                'default_min_price': 0.35,
                'print_types': {
                    'Foil': 0.75,
                    'Etched Foil': 1.00
                },
                'rarities': {
                    'Mythic Rare': 1.50,
                    'Rare': 0.50
                }
            }
        },
        'customStepping': {
            'nm': 100,
            'lp': 85,
            'mp': 70,
            'hp': 60,
            'dm': 50
        }
    }
    
    # Create test product
    test_product = {
        'username': 'Khaoz',
        'productId': '123456',
        'product_type': 'Magic: The Gathering Singles',
        'vendor': 'Magic: The Gathering',
        'expansionName': 'The Brothers War',
        'rarity': 'Mythic Rare',
        'variants': [
            {
                'option1': 'Near Mint',
                'title': 'Near Mint',
                'price': '5.00'
            },
            {
                'option1': 'Near Mint Foil',
                'title': 'Near Mint Foil',
                'price': '8.00'
            },
            {
                'option1': 'Lightly Played',
                'title': 'Lightly Played',
                'price': '4.00'
            },
            {
                'option1': 'Moderately Played',
                'title': 'Moderately Played',
                'price': '3.00'
            }
        ],
        'manualOverride': False
    }
    
    # Set up mock TCGPlayer pricing data
    tcgplayer_pricing = {
        '123456': [
            {
                'subTypeName': 'Normal',
                'lowPrice': 4.00,
                'marketPrice': 5.00,
                'midPrice': 5.50,
                'highPrice': 7.00
            },
            {
                'subTypeName': 'Foil',
                'lowPrice': 7.00,
                'marketPrice': 8.00,
                'midPrice': 9.00,
                'highPrice': 12.00
            }
        ]
    }
    
    # Clear existing test data
    db.user.delete_many({'username': 'Khaoz'})
    db.shProducts.delete_many({'username': 'Khaoz'})
    db.tcgplayerCache.delete_many({'_id': '123456'})
    
    # Insert test data
    db.user.insert_one(user_data)
    db.shProducts.insert_one(test_product)
    db.tcgplayerCache.insert_one({
        '_id': '123456',
        'pricing': tcgplayer_pricing['123456'],
        'timestamp': datetime.now(timezone.utc)
    })
    
    logger.info("Test data setup complete")

def run_test():
    """Run the test with detailed logging"""
    from routes.shautopricer import process_user, get_exchange_rate
    
    logger.info("Starting shautopricer test")
    
    # Set up test data
    setup_test_data()
    
    # Get TCGPlayer API key
    tcgplayer_key = db.tcgplayerKey.find_one({})['latestKey']
    
    # Get test config
    test_config = {
        'username': 'Khaoz',
        'selectedProductTypes': ['Magic: The Gathering Singles']
    }
    
    # Enable detailed logging for price calculations
    logging.getLogger().setLevel(logging.DEBUG)
    
    # Log initial state
    user_profile = db.user.find_one({'username': 'Khaoz'})
    logger.info(f"User Profile Settings:")
    logger.info(f"Currency: {user_profile['currency']}")
    logger.info(f"Price Preferences: {user_profile['price_preference_order']}")
    logger.info(f"Use Highest Price: {user_profile['use_highest_price']}")
    logger.info(f"Price Comparison Pairs: {user_profile['price_comparison_pairs']}")
    logger.info(f"Price Modifiers: {user_profile['price_modifiers']}")
    logger.info(f"Game Minimum Prices: {user_profile['game_minimum_prices']}")
    logger.info(f"Condition Stepping: {user_profile['customStepping']}")
    
    # Log exchange rate
    exchange_rate = get_exchange_rate('GBP')
    logger.info(f"Exchange Rate (USD to GBP): {exchange_rate}")
    
    # Process the test user
    process_user(test_config, tcgplayer_key)
    
    # Get updated product
    updated_product = db.shProducts.find_one({
        'username': 'Khaoz',
        'productId': '123456'
    })
    
    # Log results
    logger.info("\nTest Results:")
    logger.info("Original vs Updated Prices:")
    for variant in updated_product['variants']:
        logger.info(f"{variant['title']}: £{variant['price']}")

if __name__ == "__main__":
    try:
        run_test()
    finally:
        mongo_client.close()
