{% extends "base.html" %}

{% block title %}Sports Card Recognition{% endblock %}

{% block content %}
<style>
    /* Add your custom styles here, similar to card_scanning.html */
    #matchedCardsTable, #matchedCardsTable th, #matchedCardsTable td {
        color: white !important;
    }
    .image-upload-preview {
        max-width: 100px;
        max-height: 100px;
        margin: 5px;
        border: 1px solid #6c757d;
        border-radius: 4px;
        background-color: #343a40;
    }
    
    /* Dark theme for custom file input */
    .custom-file-label::after {
        background-color: #343a40;
        color: white;
        border-left: 1px solid #6c757d;
    }
    
    /* Dark theme for modal */
    .modal-content {
        box-shadow: 0 0 20px rgba(0,0,0,0.5);
    }
    
    /* Dark theme for form controls focus state */
    .form-control:focus {
        background-color: #2b3035;
        color: white;
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
    
    /* Editable cell styling */
    .editable {
        cursor: text;
        border: 1px solid transparent;
        position: relative;
        padding-right: 24px !important;
        transition: all 0.2s ease;
    }

    /* Add tooltip */
    .editable[title] {
        cursor: help;
    }

    .editable[title]:hover:before {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        padding: 4px 8px;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
    }
    
    .editable::after {
        content: '\f044';
        font-family: 'Font Awesome 5 Free';
        font-weight: 400;
        position: absolute;
        right: 6px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 0.8em;
        opacity: 0.6;
        color: #6c757d;
    }
    
    .editable:hover::after {
        opacity: 1;
        color: var(--primary-color);
    }
    
    .editable:hover {
        border-color: #6c757d;
        background-color: #2b3035 !important;
        padding: 2px 4px;
        cursor: text;
        color: white !important;
    }

    /* Ensure hover state is visible in both odd and even rows */
    .table-striped tbody tr:nth-of-type(odd) .editable:hover,
    .table-striped tbody tr:nth-of-type(even) .editable:hover {
        background-color: #2b3035 !important;
    }
    
    .editable:focus {
        outline: none;
        border-color: #80bdff;
        background-color: #2b3035 !important;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        color: white !important;
        padding: 2px 4px;
    }

    /* Ensure table stripes don't interfere with editing */
    .table-striped tbody tr:nth-of-type(odd) .editable:focus,
    .table-striped tbody tr:nth-of-type(even) .editable:focus {
        background-color: #2b3035 !important;
    }
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1 class="h2">Sports Card Recognition</h1>
            <div class="small text-success">
                Total QTY: <span id="cardCount">0</span> | 
                Total Price: <span id="totalPrice">0.00</span>
            </div>
        </div>
        <div class="col-md-12">
            <div class="card" id="uploadCardsSection">
                <div class="card-body">
                    <h5 class="card-title">Upload Sports Cards</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="fileInput">Choose files:</label>
                                <div class="custom-file">
                                    <input type="file" id="fileInput" accept="image/*" multiple class="custom-file-input">
                                    <label class="custom-file-label" for="fileInput">Select files</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action buttons -->
    <div class="row mb-4">
        <div class="col-md-12">
            <button id="selectAllBtn" class="btn btn-primary mr-2">Select All</button>
            <button id="exportCsvBtn" class="btn btn-secondary mr-2">Export CSV</button>
            <button id="clearAllBtn" class="btn btn-danger mr-2">Clear All</button>
            <button id="createShopifyBtn" class="btn btn-success">Create Shopify Products</button>
        </div>
    </div>

    <!-- Matched cards table -->
    <div class="row">
        <div class="col-md-12">
            <table id="matchedCardsTable" class="table table-striped table-bordered text-white" style="color: white;">
                <thead>
                    <tr>
                        <th>Select</th>
                        <th>Name</th>
                        <th>Team</th>
                        <th>Year</th>
                        <th>Card Number</th>
                        <th>Set Name</th>
                        <th>Card Type</th>
                        <th>Subcategory</th>
                        <th>Company</th>
                        <th>Full Name</th>
                        <th>Quantity <i class="fas fa-edit" style="font-size: 0.8em;" title="Click to edit"></i></th>
                        <th>Average Price <i class="fas fa-edit" style="font-size: 0.8em;" title="Click to edit"></i></th>
                        <th>Links</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Matched cards will be dynamically added here -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Image preview container -->
    <div id="imagePreview" style="display: none; position: fixed; z-index: 1000; pointer-events: none;">
        <img id="previewImage" src="" alt="Card Preview" style="max-width: 300px; max-height: 300px;">
    </div>

    <!-- Loading screen -->
    <div id="loadingScreen" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 2000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white;">
            <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-2" id="loadingText">Processing...</p>
        </div>
    </div>

    <!-- Shopify Product Modal -->
    <div class="modal fade" id="shopifyModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title">Create Shopify Product</h5>
                    <button type="button" class="close text-white" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="shopifyProductForm">
                        <div class="form-group">
                            <label>Product Title</label>
                            <input type="text" class="form-control bg-dark text-white border-secondary" id="shopifyTitle" required>
                        </div>
                        <div class="form-group">
                            <label>Price</label>
                            <input type="number" step="0.01" class="form-control bg-dark text-white border-secondary" id="shopifyPrice" required>
                        </div>
                        <div class="form-group">
                            <label>Vendor</label>
                            <input type="text" class="form-control bg-dark text-white border-secondary" id="shopifyVendor" required>
                        </div>
                        <div class="form-group">
                            <label>Product Type</label>
                            <input type="text" class="form-control bg-dark text-white border-secondary" id="shopifyProductType" required>
                        </div>
                        <div class="form-group">
                            <label>Additional Images</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="additionalImages" multiple accept="image/*">
                                <label class="custom-file-label bg-dark text-white border-secondary" for="additionalImages">Choose files</label>
                            </div>
                            <div id="imagePreviewContainer" class="mt-2 d-flex flex-wrap">
                                <!-- Image previews will be added here -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <textarea class="form-control bg-dark text-white border-secondary" id="shopifyDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" id="saveShopifyProduct">Create Product</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js"></script>
<script src="{{ url_for('static', filename='js/sports_card_scanning.js') }}"></script>

{% endblock %}
