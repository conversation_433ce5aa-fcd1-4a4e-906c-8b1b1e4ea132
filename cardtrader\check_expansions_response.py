import requests
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

def create_session():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=0.1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["GET"]
    )
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=100,
        pool_maxsize=100,
        pool_block=False
    )
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    return session

API_BASE_URL = "https://api.cardtrader.com/api/v2"
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

def main():
    session = create_session()
    
    # Get the first game ID
    url = f"{API_BASE_URL}/games"
    response = session.get(url, headers=HEADERS, timeout=10)
    response_data = response.json()
    games = response_data.get('array', [])
    
    if not games:
        print("No games found")
        return
    
    game_id = games[0]['id']
    print(f"Using game ID: {game_id}")
    
    # Fetch expansions for the game
    url = f"{API_BASE_URL}/expansions"
    params = {"game_id": game_id}
    response = session.get(url, headers=HEADERS, params=params, timeout=10)
    expansions = response.json()
    
    print(f"Expansions response type: {type(expansions)}")
    print(f"Expansions response content (first 3 items): {expansions[:3] if isinstance(expansions, list) else expansions}")
    
    if isinstance(expansions, dict) and 'array' in expansions:
        print(f"Expansions array type: {type(expansions['array'])}")
        print(f"Expansions array content (first 3 items): {expansions['array'][:3]}")
    
if __name__ == "__main__":
    main()
