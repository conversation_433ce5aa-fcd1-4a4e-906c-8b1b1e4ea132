from config.config import Config
#!/usr/bin/env python3
"""
Test script for the Repricing Service.

This script tests the connection to the repricing service and verifies that
the basic functionality is working correctly.

Usage:
    python test_reprice_service.py [--url URL] [--api-key API_KEY] [--username USERNAME]

Options:
    --url URL          The URL of the repricing service [default: https://webhooks.tcgsync.com]
    --api-key API_KEY  The API key for the repricing service [default: IPBRMIwlm5a7DWoZqPnxyamQSIPMyG19]
    --username USERNAME The username to test repricing with [default: test_user]
"""

import argparse
import sys
import time
import json
from datetime import datetime

try:
    import requests
    from colorama import init, Fore, Style
    init()  # Initialize colorama
except ImportError:
    print("Required packages not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "requests", "colorama"])
    import requests
    from colorama import init, Fore, Style
    init()  # Initialize colorama

def print_success(message):
    """Print a success message in green."""
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

def print_error(message):
    """Print an error message in red."""
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

def print_info(message):
    """Print an info message in blue."""
    print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")

def print_warning(message):
    """Print a warning message in yellow."""
    print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

def print_header(message):
    """Print a header message in cyan."""
    print(f"\n{Fore.CYAN}=== {message} ==={Style.RESET_ALL}")

def print_json(data):
    """Print JSON data in a formatted way."""
    print(json.dumps(data, indent=2))

class RepriceServiceTester:
    """Test the Repricing Service API."""

    def __init__(self, base_url, api_key):
        """Initialize the tester with the base URL and API key."""
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            'Content-Type': 'application/json',
            'X-API-Key': api_key
        }
        self.job_id = None

    def test_health(self):
        """Test the health endpoint."""
        print_header("Testing Health Endpoint")
        try:
            url = f"{self.base_url}/api/health"
            print_info(f"GET {url}")
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                print_success(f"Health check successful (Status: {response.status_code})")
                print_json(response.json())
                return True
            else:
                print_error(f"Health check failed (Status: {response.status_code})")
                print(response.text)
                return False
        except Exception as e:
            print_error(f"Error testing health endpoint: {str(e)}")
            return False

    def test_start_repricing(self, username):
        """Test starting a repricing job."""
        print_header("Testing Start Repricing")
        try:
            url = f"{self.base_url}/api/reprice"
            data = {'username': username}
            print_info(f"POST {url}")
            print_info(f"Data: {data}")
            
            response = requests.post(url, headers=self.headers, json=data)
            
            if response.status_code in [200, 201]:
                print_success(f"Start repricing successful (Status: {response.status_code})")
                result = response.json()
                print_json(result)
                
                if 'job_id' in result:
                    self.job_id = result['job_id']
                    print_success(f"Job ID: {self.job_id}")
                    return True
                else:
                    print_error("No job ID returned")
                    return False
            else:
                print_error(f"Start repricing failed (Status: {response.status_code})")
                print(response.text)
                return False
        except Exception as e:
            print_error(f"Error testing start repricing: {str(e)}")
            return False

    def test_check_status(self):
        """Test checking the status of a repricing job."""
        if not self.job_id:
            print_error("No job ID available. Start a repricing job first.")
            return False
            
        print_header("Testing Check Status")
        try:
            url = f"{self.base_url}/api/status/{self.job_id}"
            print_info(f"GET {url}")
            
            # Check status multiple times to see progress
            for i in range(5):
                response = requests.get(url, headers=self.headers)
                
                if response.status_code == 200:
                    result = response.json()
                    status = result.get('status', 'unknown')
                    print_success(f"Check #{i+1}: Status is '{status}' (Status code: {response.status_code})")
                    print_json(result)
                    
                    # If the job is complete or has an error, stop checking
                    if status in ['complete', 'error']:
                        break
                else:
                    print_error(f"Check status failed (Status: {response.status_code})")
                    print(response.text)
                    return False
                    
                # Wait before checking again
                if i < 4:  # Don't wait after the last check
                    print_info("Waiting 2 seconds before checking again...")
                    time.sleep(2)
            
            return True
        except Exception as e:
            print_error(f"Error testing check status: {str(e)}")
            return False

    def test_list_jobs(self):
        """Test listing all jobs."""
        print_header("Testing List Jobs")
        try:
            url = f"{self.base_url}/api/jobs"
            print_info(f"GET {url}")
            
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                print_success(f"List jobs successful (Status: {response.status_code})")
                print_json(response.json())
                return True
            else:
                print_error(f"List jobs failed (Status: {response.status_code})")
                print(response.text)
                return False
        except Exception as e:
            print_error(f"Error testing list jobs: {str(e)}")
            return False

    def test_cancel_job(self):
        """Test cancelling a repricing job."""
        if not self.job_id:
            print_error("No job ID available. Start a repricing job first.")
            return False
            
        print_header("Testing Cancel Job")
        try:
            url = f"{self.base_url}/api/cancel/{self.job_id}"
            print_info(f"POST {url}")
            
            response = requests.post(url, headers=self.headers)
            
            if response.status_code == 200:
                print_success(f"Cancel job successful (Status: {response.status_code})")
                print_json(response.json())
                return True
            else:
                print_error(f"Cancel job failed (Status: {response.status_code})")
                print(response.text)
                return False
        except Exception as e:
            print_error(f"Error testing cancel job: {str(e)}")
            return False

    def run_all_tests(self, username):
        """Run all tests."""
        print_header("Starting Repricing Service Tests")
        print_info(f"Base URL: {self.base_url}")
        print_info(f"API Key: {'*' * len(self.api_key)}")
        print_info(f"Username: {username}")
        print_info(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Run tests
        health_result = self.test_health()
        if not health_result:
            print_error("Health check failed. Aborting remaining tests.")
            return False
            
        start_result = self.test_start_repricing(username)
        if not start_result:
            print_error("Start repricing failed. Aborting remaining tests.")
            return False
            
        status_result = self.test_check_status()
        list_result = self.test_list_jobs()
        cancel_result = self.test_cancel_job()
        
        # Print summary
        print_header("Test Summary")
        print(f"Health Check: {'✓' if health_result else '✗'}")
        print(f"Start Repricing: {'✓' if start_result else '✗'}")
        print(f"Check Status: {'✓' if status_result else '✗'}")
        print(f"List Jobs: {'✓' if list_result else '✗'}")
        print(f"Cancel Job: {'✓' if cancel_result else '✗'}")
        
        if all([health_result, start_result, status_result, list_result, cancel_result]):
            print_success("\nAll tests passed!")
            return True
        else:
            print_error("\nSome tests failed.")
            return False

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test the Repricing Service API.')
    parser.add_argument('--url', default='https://webhooks.tcgsync.com',
                        help='The URL of the repricing service')
    parser.add_argument('--api-key', default='IPBRMIwlm5a7DWoZqPnxyamQSIPMyG19',
                        help='The API key for the repricing service')
    parser.add_argument('--username', default='admintcg',
                        help='The username to test repricing with')
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    tester = RepriceServiceTester(args.url, args.api_key)
    tester.run_all_tests(args.username)

if __name__ == '__main__':
    main()

