# Script to pull all blueprints from CardTrader API, save them to MongoDB, and add to matchedIds collection
import requests
import time
import random
from pymongo import MongoClient
from datetime import datetime
import logging
import sys
import threading
import queue
import argparse
from concurrent.futures import ThreadPoolExecutor
from requests.adapters import HTT<PERSON><PERSON>pter
from urllib3.util.retry import Retry
from pymongo.operations import ReplaceOne
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"pull_and_match_blueprints_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']
test_db = client['test']

# CardTrader API configuration
API_BASE_URL = "https://api.cardtrader.com/api/v2"
# CardTrader API token
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

# Create an optimized session with retry capabilities and connection pooling
def create_session():
    """Create an optimized requests session with retry capabilities and connection pooling"""
    session = requests.Session()
    retry_strategy = Retry(
        total=2,  # Reduced number of retries for speed
        backoff_factor=0.05,  # Further reduced backoff factor for faster retries
        status_forcelist=[500, 502, 503, 504],  # HTTP status codes to retry on (removed 429 since no rate limits)
        allowed_methods=["GET"]  # Only retry on GET requests
    )
    # Maximize connections for better parallelism
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=500,  # Greatly increase connection pool size
        pool_maxsize=500,  # Greatly increase max connections per host
        pool_block=False  # Don't block when pool is full
    )
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    return session

# No rate limiter needed as there are no API rate limits

def fetch_games(session=None):
    """Fetch all games from CardTrader API"""
    url = f"{API_BASE_URL}/games"
    
    # Use the provided session or create a new one
    if session is None:
        session = create_session()
    
    try:
        logger.info("Fetching games from CardTrader API")
        response = session.get(url, headers=HEADERS, timeout=5)  # Reduced timeout for faster response
        
        if response.status_code == 200:
            response_data = response.json()
            # The games are in the 'array' key of the response
            games = response_data.get('array', [])
            logger.info(f"Found {len(games)} games")
            return games
        else:
            logger.error(f"API request failed with status code {response.status_code}: {response.text}")
            return []
    except Exception as e:
        logger.error(f"Error fetching games: {str(e)}")
        return []

def fetch_expansions(game_id, session=None):
    """Fetch all expansions for a specific game from CardTrader API"""
    url = f"{API_BASE_URL}/expansions"
    params = {}
    if game_id is not None:
        params["game_id"] = game_id
    
    # Use the provided session or create a new one
    if session is None:
        session = create_session()
    
    try:
        logger.info(f"Fetching expansions for game ID: {game_id}")
        response = session.get(url, headers=HEADERS, params=params, timeout=5)  # Reduced timeout for faster response
        
        if response.status_code == 200:
            expansions = response.json()
            logger.info(f"Found {len(expansions)} expansions for game ID: {game_id}")
            return expansions
        else:
            logger.error(f"API request failed with status code {response.status_code}: {response.text}")
            return []
    except Exception as e:
        logger.error(f"Error fetching expansions for game ID {game_id}: {str(e)}")
        return []

def fetch_blueprints(expansion_id, session=None):
    """Fetch all blueprints for a specific expansion from CardTrader API"""
    url = f"{API_BASE_URL}/blueprints/export"
    params = {"expansion_id": expansion_id}
    
    # Use the provided session or create a new one
    if session is None:
        session = create_session()
    
    try:
        logger.info(f"Fetching blueprints for expansion ID: {expansion_id}")
        response = session.get(url, headers=HEADERS, params=params, timeout=5)  # Reduced timeout for faster response
        
        if response.status_code == 200:
            blueprints = response.json()
            logger.info(f"Found {len(blueprints)} blueprints for expansion ID: {expansion_id}")
            return blueprints
        elif response.status_code == 404:
            logger.warning(f"No blueprints found for expansion ID: {expansion_id}")
            return []
        else:
            logger.error(f"API request failed with status code {response.status_code}: {response.text}")
            return []
    except Exception as e:
        logger.error(f"Error fetching blueprints for expansion ID {expansion_id}: {str(e)}")
        return []

def save_games_to_mongodb(games):
    """Save games to MongoDB"""
    if not games:
        logger.info("No games to save")
        return 0
    
    try:
        # Create games collection if it doesn't exist
        if 'games' not in cardtrader_db.list_collection_names():
            logger.info("Creating games collection")
            cardtrader_db.create_collection('games')
        
        # Add fetched_at timestamp to each game
        for game in games:
            game['fetched_at'] = datetime.now()
        
        # Use bulk operations for better performance
        game_bulk_operations = []
        
        for game in games:
            # Use upsert to update if exists or insert if not for games collection
            game_bulk_operations.append(
                ReplaceOne(
                    {'id': game['id']},
                    game,
                    upsert=True
                )
            )
        
        # Execute bulk operations for games collection
        game_result = None
        if game_bulk_operations:
            game_result = cardtrader_db.games.bulk_write(game_bulk_operations)
            logger.info(f"Saved {len(games)} games")
            logger.info(f"Games - Inserted: {game_result.upserted_count}, Modified: {game_result.modified_count}")
        
        return len(games)
    except Exception as e:
        logger.error(f"Error saving games to MongoDB: {str(e)}")
        return 0

def save_expansions_to_mongodb(expansions, game_id=None):
    """Save expansions to MongoDB"""
    if not expansions:
        logger.info(f"No expansions to save for game ID: {game_id}")
        return 0
    
    try:
        # Create expansions collection if it doesn't exist
        if 'expansions' not in cardtrader_db.list_collection_names():
            logger.info("Creating expansions collection")
            cardtrader_db.create_collection('expansions')
        
        # Add fetched_at timestamp to each expansion
        for expansion in expansions:
            expansion['fetched_at'] = datetime.now()
        
        # Use bulk operations for better performance
        expansion_bulk_operations = []
        
        for expansion in expansions:
            # Use upsert to update if exists or insert if not for expansions collection
            expansion_bulk_operations.append(
                ReplaceOne(
                    {'id': expansion['id']},
                    expansion,
                    upsert=True
                )
            )
        
        # Execute bulk operations for expansions collection
        expansion_result = None
        if expansion_bulk_operations:
            expansion_result = cardtrader_db.expansions.bulk_write(expansion_bulk_operations)
            logger.info(f"Saved {len(expansions)} expansions for game ID: {game_id}")
            logger.info(f"Expansions - Inserted: {expansion_result.upserted_count}, Modified: {expansion_result.modified_count}")
        
        return len(expansions)
    except Exception as e:
        logger.error(f"Error saving expansions to MongoDB: {str(e)}")
        return 0

def save_blueprints_to_mongodb(blueprints, expansion_id):
    """Save blueprints to MongoDB and add to matchedIds collection"""
    if not blueprints:
        logger.info(f"No blueprints to save for expansion ID: {expansion_id}")
        return 0, 0
    
    try:
        # Create blueprints collection if it doesn't exist
        if 'blueprints' not in cardtrader_db.list_collection_names():
            logger.info("Creating blueprints collection")
            cardtrader_db.create_collection('blueprints')
        
        # Create matchedIds collection if it doesn't exist in test database
        if 'matchedIds' not in test_db.list_collection_names():
            logger.info("Creating matchedIds collection in test database")
            test_db.create_collection('matchedIds')
        
        # Add fetched_at timestamp to each blueprint
        for blueprint in blueprints:
            blueprint['fetched_at'] = datetime.now()
        
        # Use bulk operations for better performance
        blueprint_bulk_operations = []
        matchedIds_bulk_operations = []
        
        for blueprint in blueprints:
            # Use upsert to update if exists or insert if not for blueprints collection
            blueprint_bulk_operations.append(
                ReplaceOne(
                    {'id': blueprint['id']},
                    blueprint,
                    upsert=True
                )
            )
            
            # Extract only the required fields for matchedIds collection
            matched_record = {
                "blueprint_id": blueprint.get("id"),
                "card_market_id": blueprint.get("card_market_id"),
                "scryfall_id": blueprint.get("scryfall_id"),
                "tcg_player_id": blueprint.get("tcg_player_id")
            }
            
            # Add all records, even if they only have blueprint_id
            # Use upsert to update if exists or insert if not for matchedIds collection
            matchedIds_bulk_operations.append(
                ReplaceOne(
                    {'blueprint_id': matched_record['blueprint_id']},
                    matched_record,
                    upsert=True
                )
            )
        
        # Execute bulk operations for blueprints collection
        blueprint_result = None
        if blueprint_bulk_operations:
            blueprint_result = cardtrader_db.blueprints.bulk_write(blueprint_bulk_operations)
            logger.info(f"Saved {len(blueprints)} blueprints for expansion ID: {expansion_id}")
            logger.info(f"Blueprints - Inserted: {blueprint_result.upserted_count}, Modified: {blueprint_result.modified_count}")
        
        # Execute bulk operations for matchedIds collection in test database
        matchedIds_result = None
        if matchedIds_bulk_operations:
            matchedIds_result = test_db.matchedIds.bulk_write(matchedIds_bulk_operations)
            logger.info(f"Added {len(matchedIds_bulk_operations)} records to matchedIds collection in test database for expansion ID: {expansion_id}")
            logger.info(f"MatchedIds - Inserted: {matchedIds_result.upserted_count}, Modified: {matchedIds_result.modified_count}")
        
        return len(blueprints), len(matchedIds_bulk_operations)
    except Exception as e:
        logger.error(f"Error saving blueprints to MongoDB: {str(e)}")
        return 0, 0

def process_expansion(expansion, session, result_queue):
    """Process a single expansion in a thread"""
    expansion_id = expansion.get('id')
    expansion_name = expansion.get('name', 'Unknown')
    
    logger.info(f"Processing expansion: {expansion_name} (ID: {expansion_id})")
    
    # Save expansion to MongoDB
    save_expansions_to_mongodb([expansion], expansion.get('game_id'))
    
    # Fetch blueprints for this expansion
    blueprints = fetch_blueprints(expansion_id, session)
    
    # Save blueprints to MongoDB and add to matchedIds collection
    blueprints_saved, matchedIds_added = save_blueprints_to_mongodb(blueprints, expansion_id)
    
    # Add result to queue
    result_queue.put((expansion_id, len(blueprints), blueprints_saved, matchedIds_added))
    
    logger.info(f"Completed expansion: {expansion_name} (ID: {expansion_id})")

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Pull all blueprints from CardTrader API, save them to MongoDB, and add to matchedIds collection')
    parser.add_argument('--game', type=int, default=None,
                        help='Filter by game_id (default: None, fetch all games)')
    parser.add_argument('--expansion', type=int, default=None,
                        help='Filter by expansion_id (default: None, fetch all expansions)')
    parser.add_argument('--threads', type=int, default=500,
                        help='Number of threads to use (default: 500)')
    parser.add_argument('--analyze', action='store_true',
                        help='Run analysis on matchedIds collection after completion (default: False)')
    return parser.parse_args()

def analyze_matched_ids():
    """Analyze the matchedIds collection and show statistics"""
    # Check if matchedIds collection exists in test database
    if 'matchedIds' not in test_db.list_collection_names():
        logger.error("matchedIds collection not found in test database")
        return
    
    # Count total records
    total_records = test_db.matchedIds.count_documents({})
    logger.info(f"Total records in matchedIds collection: {total_records}")
    
    # Count records with valid blueprint_id
    blueprint_records = test_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None}
    })
    logger.info(f"Records with valid blueprint_id: {blueprint_records}")
    
    # Count records with valid tcg_player_id
    tcgplayer_records = test_db.matchedIds.count_documents({
        "tcg_player_id": {"$exists": True, "$ne": None}
    })
    logger.info(f"Records with valid tcg_player_id: {tcgplayer_records}")
    
    # Count records with valid card_market_id (not null)
    cardmarket_records = test_db.matchedIds.count_documents({
        "card_market_id": {"$exists": True, "$ne": None, "$ne": "null"}
    })
    logger.info(f"Records with valid card_market_id (not null): {cardmarket_records}")
    
    # Count records with card_market_id field (including null)
    cardmarket_field_records = test_db.matchedIds.count_documents({
        "card_market_id": {"$exists": True}
    })
    logger.info(f"Records with card_market_id field (including null): {cardmarket_field_records}")
    
    # Count records with valid scryfall_id
    scryfall_records = test_db.matchedIds.count_documents({
        "scryfall_id": {"$exists": True, "$ne": None}
    })
    logger.info(f"Records with valid scryfall_id: {scryfall_records}")
    
    # Count records with all three IDs (not null)
    all_ids_records = test_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "tcg_player_id": {"$exists": True, "$ne": None},
        "card_market_id": {"$exists": True, "$ne": None, "$ne": "null"}
    })
    logger.info(f"Records with valid blueprint_id, tcg_player_id, and card_market_id: {all_ids_records}")
    
    # Count records with blueprint_id and tcg_player_id only (no valid card_market_id)
    blueprint_tcgplayer_only = test_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "tcg_player_id": {"$exists": True, "$ne": None},
        "$or": [
            {"card_market_id": {"$exists": False}},
            {"card_market_id": None}
        ]
    })
    logger.info(f"Records with blueprint_id and tcg_player_id only (no card_market_id): {blueprint_tcgplayer_only}")
    
    # Count records with blueprint_id and card_market_id only (no valid tcg_player_id)
    blueprint_cardmarket_only = test_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "card_market_id": {"$exists": True, "$ne": None, "$ne": "null"},
        "$or": [
            {"tcg_player_id": {"$exists": False}},
            {"tcg_player_id": None}
        ]
    })
    logger.info(f"Records with blueprint_id and card_market_id only (no tcg_player_id): {blueprint_cardmarket_only}")
    
    # Count records with blueprint_id and scryfall_id only (no valid tcg_player_id or card_market_id)
    blueprint_scryfall_only = test_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "scryfall_id": {"$exists": True, "$ne": None},
        "$and": [
            {"$or": [
                {"tcg_player_id": {"$exists": False}},
                {"tcg_player_id": None}
            ]},
            {"$or": [
                {"card_market_id": {"$exists": False}},
                {"card_market_id": None}
            ]}
        ]
    })
    logger.info(f"Records with blueprint_id and scryfall_id only (no tcg_player_id or card_market_id): {blueprint_scryfall_only}")
    
    # Count records with blueprint_id only (no other valid IDs)
    blueprint_only = test_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "$and": [
            {"$or": [
                {"tcg_player_id": {"$exists": False}},
                {"tcg_player_id": None}
            ]},
            {"$or": [
                {"card_market_id": {"$exists": False}},
                {"card_market_id": None}
            ]},
            {"$or": [
                {"scryfall_id": {"$exists": False}},
                {"scryfall_id": None}
            ]}
        ]
    })
    logger.info(f"Records with blueprint_id only (no other valid IDs): {blueprint_only}")
    
    # Print summary
    print("\n=== SUMMARY ===")
    print(f"Total records: {total_records}")
    print(f"Records with valid blueprint_id: {blueprint_records}")
    print(f"Records with valid tcg_player_id: {tcgplayer_records}")
    print(f"Records with valid card_market_id (not null): {cardmarket_records}")
    print(f"Records with card_market_id field (including null): {cardmarket_field_records}")
    print(f"Records with valid scryfall_id: {scryfall_records}")
    print("\n=== COMBINATIONS ===")
    print(f"Records with all three IDs (blueprint, TCGPlayer, CardMarket): {all_ids_records}")
    print(f"Records with blueprint_id and tcg_player_id only (no card_market_id): {blueprint_tcgplayer_only}")
    print(f"Records with blueprint_id and card_market_id only (no tcg_player_id): {blueprint_cardmarket_only}")
    print(f"Records with blueprint_id and scryfall_id only (no tcg_player_id or card_market_id): {blueprint_scryfall_only}")
    print(f"Records with blueprint_id only (no other valid IDs): {blueprint_only}")

def main():
    # Parse command line arguments
    args = parse_arguments()
    
    start_time = time.time()
    logger.info(f"Starting CardTrader blueprints collection and matchedIds population")
    logger.info(f"Configuration: game_id: {args.game}, expansion_id: {args.expansion}, {args.threads} threads")
    
    # Create a session for API requests
    session = create_session()
    
    # If expansion_id is provided, only fetch blueprints for that expansion
    if args.expansion is not None:
        # First, try to fetch the expansion details to save to MongoDB
        # We'll need to find which game this expansion belongs to
        logger.info(f"Looking for expansion ID: {args.expansion} in all games")
        
        # Fetch all games to find the one containing this expansion
        games = fetch_games(session)
        if games:
            # Save games to MongoDB
            games_saved = save_games_to_mongodb(games)
            logger.info(f"Saved {games_saved} games to MongoDB")
            
            # Find the expansion in all games
            expansion_found = False
            for game in games:
                game_id = game.get('id')
                expansions = fetch_expansions(game_id, session)
                
                # Save expansions to MongoDB
                if expansions:
                    expansions_saved = save_expansions_to_mongodb(expansions, game_id)
                    logger.info(f"Saved {expansions_saved} expansions to MongoDB for game ID: {game_id}")
                
                # Check if our target expansion is in this game
                for expansion in expansions:
                    if expansion.get('id') == args.expansion:
                        logger.info(f"Found expansion ID: {args.expansion} in game ID: {game_id}")
                        expansion_found = True
                        break
                
                if expansion_found:
                    break
        
        # Now fetch and save blueprints for this expansion
        logger.info(f"Fetching blueprints for expansion ID: {args.expansion}")
        blueprints = fetch_blueprints(args.expansion, session)
        blueprints_saved, matchedIds_added = save_blueprints_to_mongodb(blueprints, args.expansion)
        logger.info(f"Saved {blueprints_saved} blueprints and added {matchedIds_added} records to matchedIds for expansion ID: {args.expansion}")
        end_time = time.time()
        logger.info(f"Completed in {end_time - start_time:.2f} seconds")
        
        # Run analysis if requested
        if args.analyze:
            logger.info("Running analysis on matchedIds collection")
            analyze_matched_ids()
        
        return
    
    # If game_id is provided, only fetch expansions for that game
    if args.game is not None:
        logger.info(f"Fetching expansions for game ID: {args.game}")
        expansions = fetch_expansions(args.game, session)
        
        # Save expansions to MongoDB
        expansions_saved = save_expansions_to_mongodb(expansions, args.game)
        logger.info(f"Saved {expansions_saved} expansions to MongoDB for game ID: {args.game}")
    else:
        # Fetch all games
        games = fetch_games(session)
        if not games:
            logger.error("No games found. Exiting.")
            return
        
        # Save games to MongoDB
        games_saved = save_games_to_mongodb(games)
        logger.info(f"Saved {games_saved} games to MongoDB")
        
        # Fetch expansions for all games in parallel
        expansions = []
        logger.info(f"Fetching expansions for {len(games)} games in parallel")
        
        def fetch_game_expansions(game):
            game_id = game.get('id')
            return fetch_expansions(game_id, session)
        
        # Use ThreadPoolExecutor to fetch expansions in parallel
        with ThreadPoolExecutor(max_workers=min(200, len(games))) as executor:  # Increased max_workers for more parallelism
            game_expansions_list = list(executor.map(fetch_game_expansions, games))
        
        # Combine all expansions and save them to MongoDB
        for i, game_expansions in enumerate(game_expansions_list):
            game_id = games[i].get('id') if i < len(games) else None
            if game_expansions:
                # Save expansions to MongoDB
                expansions_saved = save_expansions_to_mongodb(game_expansions, game_id)
                logger.info(f"Saved {expansions_saved} expansions to MongoDB for game ID: {game_id}")
            expansions.extend(game_expansions)
    
    if not expansions:
        logger.error("No expansions found. Exiting.")
        return
    
    # Create a queue for results
    result_queue = queue.Queue()
    
    # Determine number of threads (adjust based on your system capabilities)
    num_threads = min(args.threads, len(expansions))
    logger.info(f"Using {num_threads} threads to process {len(expansions)} expansions")
    
    # Process expansions using thread pool
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # Submit all tasks
        futures = []
        for expansion in expansions:
            futures.append(executor.submit(process_expansion, expansion, session, result_queue))
    
    # Process all results from the queue
    processed_count = 0
    blueprints_count = 0
    blueprints_saved_count = 0
    matchedIds_added_count = 0
    
    while not result_queue.empty():
        expansion_id, blueprint_count, blueprints_saved, matchedIds_added = result_queue.get()
        blueprints_count += blueprint_count
        blueprints_saved_count += blueprints_saved
        matchedIds_added_count += matchedIds_added
        processed_count += 1
        
        # Print progress less frequently to reduce logging overhead
        if processed_count % 50 == 0 or processed_count == len(expansions):
            logger.info(f"Progress: {processed_count}/{len(expansions)} expansions processed")
    
    end_time = time.time()
    duration = end_time - start_time
    logger.info(f"Completed in {duration:.2f} seconds")
    logger.info(f"Processed {processed_count} expansions")
    logger.info(f"Found {blueprints_count} blueprints")
    logger.info(f"Saved {blueprints_saved_count} blueprints to MongoDB")
    logger.info(f"Added {matchedIds_added_count} records to matchedIds collection")
    logger.info(f"Average processing time: {duration/processed_count:.2f} seconds per expansion")
    
    # Count documents in blueprints collection
    blueprints_count = cardtrader_db.blueprints.count_documents({})
    logger.info(f"Total documents in blueprints collection: {blueprints_count}")
    
    # Count documents in matchedIds collection in test database
    matchedIds_count = test_db.matchedIds.count_documents({})
    logger.info(f"Total documents in matchedIds collection in test database: {matchedIds_count}")
    
    # Run analysis if requested
    if args.analyze:
        logger.info("Running analysis on matchedIds collection")
        analyze_matched_ids()

if __name__ == "__main__":
    main()
