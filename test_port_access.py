from config.config import Config
#!/usr/bin/env python3
"""
Script to test if port 5001 is now accessible on the server.

This script performs a series of tests to check if port 5001 is accessible
and the repricing service is responding correctly.

Usage:
    python test_port_access.py [--host HOST] [--port PORT] [--api-key API_KEY]

Options:
    --host HOST          The hostname or IP address of the server [default: **************]
    --port PORT          The port to test [default: 5001]
    --api-key API_KEY    The API key for the repricing service [default: savyQYfxgyzdzZNUvk59PAajTKTSUbzu]
"""

import argparse
import sys
import socket
import time
import json
import requests
from datetime import datetime

def print_step(message):
    """Print a step message."""
    print(f"\n\033[1;34m=== {message} ===\033[0m")

def print_success(message):
    """Print a success message."""
    print(f"\033[1;32m✓ {message}\033[0m")

def print_error(message):
    """Print an error message."""
    print(f"\033[1;31m✗ {message}\033[0m")

def print_info(message):
    """Print an info message."""
    print(f"\033[1;36mℹ {message}\033[0m")

def test_socket_connection(host, port, timeout=5):
    """Test a socket connection to the host and port."""
    print_step(f"Testing socket connection to {host}:{port}")
    try:
        start_time = time.time()
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        end_time = time.time()
        
        if result == 0:
            print_success(f"Socket connection successful (took {end_time - start_time:.2f} seconds)")
            return True
        else:
            print_error(f"Socket connection failed with error code {result} (took {end_time - start_time:.2f} seconds)")
            return False
    except socket.gaierror:
        print_error(f"Hostname could not be resolved: {host}")
        return False
    except socket.error as e:
        print_error(f"Socket error: {e}")
        return False
    finally:
        if 'sock' in locals():
            sock.close()

def test_health_endpoint(host, port, api_key, timeout=5):
    """Test the health endpoint of the repricing service."""
    # Use HTTPS for port 443, HTTP for other ports
    protocol = "https" if port == 443 else "http"
    url = f"{protocol}://{host}:{port}/api/health"
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    print_step(f"Testing health endpoint at {url}")
    try:
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=timeout, verify=False)  # verify=False to ignore SSL certificate validation
        end_time = time.time()
        
        print_info(f"Response status code: {response.status_code}")
        print_info(f"Response time: {end_time - start_time:.2f} seconds")
        print_info(f"Response content: {response.text}")
        
        if response.status_code == 200:
            print_success("Health endpoint test successful")
            return True
        else:
            print_error(f"Health endpoint test failed with status code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_error(f"Health endpoint test failed: {e}")
        return False

def test_start_repricing(host, port, api_key, username="admintcg", product_id=87, timeout=5):
    """Test starting a repricing job."""
    # Use HTTPS for port 443, HTTP for other ports
    protocol = "https" if port == 443 else "http"
    url = f"{protocol}://{host}:{port}/api/reprice"
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    data = {
        'username': username,
        'product_id': product_id
    }
    
    print_step(f"Testing starting a repricing job for user {username} and product {product_id}")
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data, timeout=timeout)
        end_time = time.time()
        
        print_info(f"Response status code: {response.status_code}")
        print_info(f"Response time: {end_time - start_time:.2f} seconds")
        print_info(f"Response content: {response.text}")
        
        if response.status_code in [200, 201]:
            print_success("Start repricing test successful")
            
            try:
                result = response.json()
                if 'job_id' in result:
                    job_id = result['job_id']
                    print_success(f"Job ID: {job_id}")
                    return job_id
                else:
                    print_error("No job ID returned")
                    return None
            except json.JSONDecodeError:
                print_error("Failed to parse JSON response")
                return None
        else:
            print_error(f"Start repricing test failed with status code {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print_error(f"Start repricing test failed: {e}")
        return None

def test_check_status(host, port, api_key, job_id, timeout=5):
    """Test checking the status of a repricing job."""
    # Use HTTPS for port 443, HTTP for other ports
    protocol = "https" if port == 443 else "http"
    url = f"{protocol}://{host}:{port}/api/status/{job_id}"
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    print_step(f"Testing checking the status of job {job_id}")
    try:
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=timeout)
        end_time = time.time()
        
        print_info(f"Response status code: {response.status_code}")
        print_info(f"Response time: {end_time - start_time:.2f} seconds")
        print_info(f"Response content: {response.text}")
        
        if response.status_code == 200:
            print_success("Check status test successful")
            return True
        else:
            print_error(f"Check status test failed with status code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_error(f"Check status test failed: {e}")
        return False

def test_list_jobs(host, port, api_key, timeout=5):
    """Test listing all jobs."""
    # Use HTTPS for port 443, HTTP for other ports
    protocol = "https" if port == 443 else "http"
    url = f"{protocol}://{host}:{port}/api/jobs"
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    print_step(f"Testing listing all jobs")
    try:
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=timeout)
        end_time = time.time()
        
        print_info(f"Response status code: {response.status_code}")
        print_info(f"Response time: {end_time - start_time:.2f} seconds")
        print_info(f"Response content: {response.text}")
        
        if response.status_code == 200:
            print_success("List jobs test successful")
            return True
        else:
            print_error(f"List jobs test failed with status code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_error(f"List jobs test failed: {e}")
        return False

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test if port 5001 is now accessible on the server.')
    parser.add_argument('--host', default='**************',
                        help='The hostname or IP address of the server')
    parser.add_argument('--port', type=int, default=5001,
                        help='The port to test')
    parser.add_argument('--api-key', default='savyQYfxgyzdzZNUvk59PAajTKTSUbzu',
                        help='The API key for the repricing service')
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    print_step("Starting port access tests")
    print_info(f"Host: {args.host}")
    print_info(f"Port: {args.port}")
    print_info(f"API Key: {'*' * len(args.api_key)}")
    print_info(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test socket connection
    socket_result = test_socket_connection(args.host, args.port)
    if not socket_result:
        print_error("Socket connection failed. The port may still be blocked.")
        print_info("Please check that you've added the rule to allow traffic on port 5001 and that it has been applied.")
        return 1
    
    # Test health endpoint
    health_result = test_health_endpoint(args.host, args.port, args.api_key)
    if not health_result:
        print_error("Health endpoint test failed. The service may not be running correctly.")
        return 1
    
    # Test starting a repricing job
    job_id = test_start_repricing(args.host, args.port, args.api_key)
    if not job_id:
        print_error("Start repricing test failed. The service may not be running correctly.")
        return 1
    
    # Test checking the status of a repricing job
    status_result = test_check_status(args.host, args.port, args.api_key, job_id)
    if not status_result:
        print_error("Check status test failed. The service may not be running correctly.")
        return 1
    
    # Test listing all jobs
    list_result = test_list_jobs(args.host, args.port, args.api_key)
    if not list_result:
        print_error("List jobs test failed. The service may not be running correctly.")
        return 1
    
    print_step("Port access tests complete")
    print_success("All tests passed! The port is now accessible and the service is running correctly.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

