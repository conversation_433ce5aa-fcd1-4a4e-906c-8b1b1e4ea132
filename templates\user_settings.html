{% extends "base.html" %}

{% block content %}
<div class="container mt-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Buylist Settings</h1>
        <a href="https://buylist.tcgsync.com/{{ current_user.username }}" target="_blank" class="btn btn-primary">Personal Link</a>
    </div>

    {# Flash messages have been removed to prevent multiple notifications #}

    <form method="POST">
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">General Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-4">
                            <label class="font-weight-bold">Conditions</label>
                            <div class="d-flex flex-wrap">
                                {% for condition in ['NM', 'LP', 'MP', 'HP', 'DM'] %}
                                    <div class="custom-control custom-checkbox mr-4 mb-2">
                                        <input type="checkbox" class="custom-control-input" id="{{ condition }}" name="conditions" value="{{ condition }}"
                                               {% if condition in user_settings.conditions %}checked{% endif %}>
                                        <label class="custom-control-label" for="{{ condition }}">
                                            <span class="badge badge-pill badge-primary p-2">{{ condition }}</span>
                                        </label>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="form-row mb-4">
                            <div class="col-md-4">
                                <label for="defaultCashPercentage" class="font-weight-bold">Default Cash Percentage</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="defaultCashPercentage" name="defaultCashPercentage" value="{{ user_settings.defaultCashPercentage }}" min="0" max="100" step="0.01">
                                    <div class="input-group-append">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="defaultCreditPercentage" class="font-weight-bold">Default Credit Percentage</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="defaultCreditPercentage" name="defaultCreditPercentage" value="{{ user_settings.defaultCreditPercentage }}" min="0" max="100" step="0.01">
                                    <div class="input-group-append">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="currency" class="font-weight-bold">Currency</label>
                                <select class="form-control" id="currency" name="currency">
                                    {% set currencies = ['USD', 'CAD', 'GBP', 'NZD', 'AUD', 'EUR', 'MXN', 'CLP', 'JPY', 'CHF', 'CNY', 'HKD', 'SGD', 'BRL', 'INR', 'ZAR', 'AED', 'SAR', 'QAR', 'KWD', 'THB', 'SEK', 'NOK', 'DKK', 'PLN', 'TRY', 'RUB', 'KRW'] %}
                                    {% for curr in currencies %}
                                        <option value="{{ curr }}" {% if curr == user_settings.currency %}selected{% endif %}>{{ curr }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Enabled Games</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label for="gameSearch" class="font-weight-bold">Search Games</label>
                            <input type="text" class="form-control" id="gameSearch" placeholder="Type to search games...">
                        </div>
                        <div class="form-group mb-3">
                            <label for="enabledGames" class="font-weight-bold">Select Games</label>
                            <select multiple class="form-control" id="enabledGames" name="enabledGames" size="10">
                                {% for game in unique_games|sort %}
                                    <option value="{{ game }}" {% if game in user_settings.enabledGames %}selected{% endif %}>
                                        {{ game }}{% if game in user_settings.enabledGames %} (Enabled){% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="font-weight-bold">Selected Games:</label>
                            <div id="selectedGamesContainer" class="border p-2 mt-2" style="min-height: 50px; max-height: 200px; overflow-y: auto;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Pricing Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-4">
                            <label for="floorPrice" class="font-weight-bold">Floor Price</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="number" class="form-control" id="floorPrice" name="floorPrice" value="{{ user_settings.floorPrice }}" min="0" step="0.01">
                            </div>
                            <small class="form-text text-muted mt-2">The floor price is applied after your buylist percentage. Items will be classed as bulk if their price after applying your buylist percentage falls below this floor price. For example, if your floor price is $3 and you offer 50% on a $5 card, it would be classed as bulk because $5 * 50% = $2.50, which is below the $3 floor price. Bulk items will be listed as not buying on the normal buylist unless a custom rule exists.</small>
                        </div>

                        <div class="form-group mb-4">
                            <label class="font-weight-bold">Price Point Preference Order</label>
                            <div class="card">
                                <div class="card-body">
                                    <p class="text-muted mb-2">Drag to reorder price points. The system will try to use the first price point, then fall back to the next ones in order if data is unavailable.</p>
                                    <div class="d-flex justify-content-end mb-2">
                                        <button type="button" id="savePricePreferenceOrderBtn" class="btn btn-sm btn-success">
                                            <i class="fas fa-save mr-1"></i> Save Order
                                        </button>
                                    </div>
                                    <ul id="pricePointOrderList" class="list-group">
                                        {% set all_price_points = ['Low Price', 'Mid Price', 'Market Price', 'High Price', 'Lowest Listed Price'] %}
                                        
                                        {% if user_settings.price_preference_order is defined and user_settings.price_preference_order %}
                                            {% for point_key in user_settings.price_preference_order %}
                                                {% set point_name = {
                                                    'lowPrice': 'Low Price',
                                                    'midPrice': 'Mid Price', 
                                                    'marketPrice': 'Market Price',
                                                    'highPrice': 'High Price',
                                                    'lowestListedPrice': 'Lowest Listed Price'
                                                }.get(point_key, point_key) %}
                                                <li class="list-group-item d-flex justify-content-between align-items-center" data-value="{{ point_key }}">
                                                    <span>{{ point_name }}</span>
                                                    <div>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary move-up"><i class="fas fa-arrow-up"></i></button>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary move-down"><i class="fas fa-arrow-down"></i></button>
                                                    </div>
                                                </li>
                                            {% endfor %}
                                        {% else %}
                                            {% set default_order = ['lowPrice', 'marketPrice', 'midPrice', 'highPrice', 'lowestListedPrice'] %}
                                            {% set name_map = {
                                                'lowPrice': 'Low Price',
                                                'midPrice': 'Mid Price', 
                                                'marketPrice': 'Market Price',
                                                'highPrice': 'High Price',
                                                'lowestListedPrice': 'Lowest Listed Price'
                                            } %}
                                            {% for point_key in default_order %}
                                                <li class="list-group-item d-flex justify-content-between align-items-center" data-value="{{ point_key }}">
                                                    <span>{{ name_map.get(point_key, point_key) }}</span>
                                                    <div>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary move-up"><i class="fas fa-arrow-up"></i></button>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary move-down"><i class="fas fa-arrow-down"></i></button>
                                                    </div>
                                                </li>
                                            {% endfor %}
                                        {% endif %}
                                    </ul>
                                    <!-- Hidden inputs to store the order -->
                                    <div id="pricePointOrderInputs"></div>
                                </div>
                            </div>
                            <small class="form-text text-muted mt-2">Set the order of price points to use for your buylist calculations.</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="use_own_buylist_prices" name="use_own_buylist_prices" 
                                    {% if user_settings is defined and user_settings is not none %}
                                        {% if user_settings.use_own_buylist_prices is defined and user_settings.use_own_buylist_prices %}checked{% endif %}
                                    {% endif %}>
                                <label class="custom-control-label font-weight-bold" for="use_own_buylist_prices">Use My Own Inventory Prices</label>
                            </div>
                            <small class="form-text text-muted mt-2">When enabled, your buylist will use your own inventory prices instead of TCGPlayer prices. If a card is not found in your inventory, TCGPlayer prices will be used as a fallback.</small>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="use_v2_template" name="use_v2_template" 
                                    {% if user_settings is defined and user_settings is not none %}
                                        {% if user_settings.use_v2_template is defined and user_settings.use_v2_template %}checked{% endif %}
                                    {% endif %}>
                                <label class="custom-control-label font-weight-bold" for="use_v2_template">Use V2 Template</label>
                            </div>
                            <small class="form-text text-muted mt-2">Enable the V2 Template for your buylist.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0">Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-4">
                            <label for="buylistEmail" class="font-weight-bold">Buylist Email</label>
                            <input type="email" class="form-control" id="buylistEmail" name="buylistEmail" value="{{ user_settings.buylistEmail }}">
                        </div>
                        <div class="form-group">
                            <label for="sellerShippingAddress" class="font-weight-bold">Seller Shipping Address</label>
                            <textarea class="form-control" id="sellerShippingAddress" name="sellerShippingAddress" rows="4">{{ user_settings.sellerShippingAddress }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Language Settings</h5>
            </div>
            <div class="card-body">
                <p class="mb-3">Select languages you want to activate and set a percentage for each.</p>
                <div class="row">
                    {% set languages = [
                        {'code': 'JP', 'name': 'Japanese'},
                        {'code': 'CS', 'name': 'Czech'},
                        {'code': 'DE', 'name': 'German'},
                        {'code': 'FR', 'name': 'French'},
                        {'code': 'IT', 'name': 'Italian'},
                        {'code': 'PT', 'name': 'Portuguese'},
                        {'code': 'RU', 'name': 'Russian'},
                        {'code': 'KO', 'name': 'Korean'},
                        {'code': 'PL', 'name': 'Polish'},
                        {'code': 'ZH', 'name': 'Chinese'},
                        {'code': 'JA', 'name': 'Japanese (Alt)'}
                    ] %}
                    
                    {% for lang in languages %}
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="custom-control custom-checkbox mb-2">
                                        <input type="checkbox" class="custom-control-input" id="lang_{{ lang.code }}" name="lang_{{ lang.code }}" 
                                            {% if user_settings.langPercent and lang.code in user_settings.langPercent %}checked{% endif %}
                                            onchange="toggleLanguagePercentage('{{ lang.code }}')">
                                        <label class="custom-control-label font-weight-bold" for="lang_{{ lang.code }}">
                                            {{ lang.name }} ({{ lang.code }})
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label for="lang_percent_{{ lang.code }}">Percentage</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="lang_percent_{{ lang.code }}" 
                                                name="lang_percent_{{ lang.code }}" 
                                                value="{{ user_settings.langPercent[lang.code] if user_settings.langPercent and lang.code in user_settings.langPercent else 0 }}" 
                                                min="0" max="100" step="1"
                                                {% if not user_settings.langPercent or lang.code not in user_settings.langPercent %}disabled{% endif %}>
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-warning">
                <h5 class="mb-0">Advanced Pricing Rules</h5>
            </div>
            <div class="card-body">
                <p>Manage your advanced pricing rules and excluded cards on separate pages.</p>
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('advanced_rules.view_edit_advanced_rules') }}" class="btn btn-primary flex-grow-1 me-2">Go to Advanced Rules</a>
                    <a href="{{ url_for('excluded_cards.view_edit_excluded_cards') }}" class="btn btn-secondary flex-grow-1">Manage Excluded Cards</a>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">Custom Buylist Items</h5>
            </div>
            <div class="card-body">
                <p>Create custom buylist items for bulk purchases or special items.</p>
                
                <div class="form-group mb-4">
                    <label for="custom_buylist_button_label" class="font-weight-bold">Custom Buylist Button Label</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="custom_buylist_button_label" name="custom_buylist_button_label" 
                               value="{{ user_settings.custom_buylist_button_label|default('Add Custom Item') }}" 
                               placeholder="Add Custom Item">
                        <div class="input-group-append">
                            <button type="button" id="saveButtonLabelBtn" class="btn btn-success">
                                <i class="fas fa-save mr-1"></i> Save Label
                            </button>
                        </div>
                    </div>
                    <small class="form-text text-muted">This label will be displayed on the button for adding custom buylist items.</small>
                    <div id="buttonLabelSaveResult" class="mt-2"></div>
                </div>
                
                <div id="customBuylistItems" class="mb-4">
                    <!-- Custom buylist items will be loaded here -->
                    <div class="text-center py-3" id="loadingCustomItems">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <div id="noCustomItemsMessage" class="alert alert-info" style="display: none;">
                        No custom buylist items found. Create your first item below.
                    </div>
                    <div id="customItemsList"></div>
                </div>
                
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomItemModal">
                    {{ user_settings.custom_buylist_button_label|default('Add Custom Item') }}
                </button>
            </div>
        </div>
        <div class="text-center mt-4 mb-5">
            <button type="submit" class="btn btn-primary btn-lg">Save All Settings</button>
        </div>
    </form>
</div>

<!-- Modal for adding/editing custom buylist items -->
<div class="modal fade" id="addCustomItemModal" tabindex="-1" aria-labelledby="addCustomItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomItemModalLabel">{{ user_settings.custom_buylist_button_label|default('Add Custom Item') }}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="customItemForm">
                    <input type="hidden" id="customItemId">
                    <div class="mb-3">
                        <label for="itemTitle" class="form-label">Title</label>
                        <input type="text" class="form-control bg-dark text-white" id="itemTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="itemDescription" class="form-label">Description</label>
                        <textarea class="form-control bg-dark text-white" id="itemDescription" rows="3" required></textarea>
                    </div>
                    <div class="row mb-3">
                        <div class="col">
                            <label for="itemCashPrice" class="form-label">Cash Price</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark text-white">$</span>
                                <input type="number" class="form-control bg-dark text-white" id="itemCashPrice" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col">
                            <label for="itemCreditPrice" class="form-label">Credit Price</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark text-white">$</span>
                                <input type="number" class="form-control bg-dark text-white" id="itemCreditPrice" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="itemIsActive" checked>
                        <label class="form-check-label" for="itemIsActive">
                            Active
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="deleteCustomItemBtn" style="display: none;">Delete</button>
                <button type="button" class="btn btn-primary" id="saveCustomItemBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Function to toggle language percentage input fields
    function toggleLanguagePercentage(langCode) {
        const checkbox = document.getElementById(`lang_${langCode}`);
        const percentInput = document.getElementById(`lang_percent_${langCode}`);
        
        if (checkbox && percentInput) {
            percentInput.disabled = !checkbox.checked;
            
            // If enabling the language, set a default value if it's currently 0
            if (checkbox.checked && parseFloat(percentInput.value) === 0) {
                percentInput.value = 100; // Default to 100%
            }
        }
    }

    // Custom Buylist Items functionality
    let customItems = [];
    let isEditMode = false;

    function loadCustomBuylistItems() {
        fetch('/api/custom-buylist')
            .then(response => response.json())
            .then(data => {
                document.getElementById('loadingCustomItems').style.display = 'none';
                
                if (data.success && data.items && data.items.length > 0) {
                    customItems = data.items;
                    document.getElementById('noCustomItemsMessage').style.display = 'none';
                    renderCustomItems();
                } else {
                    document.getElementById('noCustomItemsMessage').style.display = 'block';
                    document.getElementById('customItemsList').innerHTML = '';
                }
            })
            .catch(error => {
                console.error('Error loading custom buylist items:', error);
                document.getElementById('loadingCustomItems').style.display = 'none';
                document.getElementById('noCustomItemsMessage').style.display = 'block';
                document.getElementById('noCustomItemsMessage').textContent = 'Error loading custom buylist items. Please try again.';
            });
    }

    function renderCustomItems() {
        const container = document.getElementById('customItemsList');
        container.innerHTML = '';
        
        customItems.forEach(item => {
            const card = document.createElement('div');
            card.className = 'card mb-3 bg-dark text-white';
            
            const statusBadge = item.is_active 
                ? '<span class="badge bg-success">Active</span>' 
                : '<span class="badge bg-danger">Inactive</span>';
            
            card.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">${item.title}</h6>
                    ${statusBadge}
                </div>
                <div class="card-body">
                    <p class="card-text">${item.description}</p>
                    <div class="d-flex justify-content-between">
                        <div>
                            <strong>Cash:</strong> $${item.cash_price.toFixed(2)}
                        </div>
                        <div>
                            <strong>Credit:</strong> $${item.credit_price.toFixed(2)}
                        </div>
                    </div>
                </div>
                <div class="card-footer text-end">
                    <button class="btn btn-sm btn-primary edit-item" data-id="${item.id}">Edit</button>
                </div>
            `;
            
            container.appendChild(card);
            
            // Add event listener to edit button
            const editButton = card.querySelector('.edit-item');
            if (editButton) {
                editButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    openEditModal(item);
                });
            }
        });
    }

    function openEditModal(item = null) {
        const modal = document.getElementById('addCustomItemModal');
        const modalTitle = document.getElementById('addCustomItemModalLabel');
        const form = document.getElementById('customItemForm');
        const deleteBtn = document.getElementById('deleteCustomItemBtn');
        
        // Initialize the modal
        const bsModal = new bootstrap.Modal(modal);
        
        // Use requestAnimationFrame to ensure DOM updates are complete
        requestAnimationFrame(() => {
            if (item) {
                // Edit mode
                isEditMode = true;
                // Use the button label from the input field, or fall back to default
                const buttonLabel = document.getElementById('custom_buylist_button_label').value || 'Add Custom Item';
                modalTitle.textContent = 'Edit ' + buttonLabel;
                document.getElementById('customItemId').value = item.id;
                document.getElementById('itemTitle').value = item.title;
                document.getElementById('itemDescription').value = item.description;
                document.getElementById('itemCashPrice').value = item.cash_price;
                document.getElementById('itemCreditPrice').value = item.credit_price;
                document.getElementById('itemIsActive').checked = item.is_active;
                deleteBtn.style.display = 'block';
            } else {
                // Add mode
                isEditMode = false;
                const buttonLabel = document.getElementById('custom_buylist_button_label').value || 'Add Custom Item';
                modalTitle.textContent = buttonLabel;
                form.reset();
                document.getElementById('customItemId').value = '';
                deleteBtn.style.display = 'none';
            }
            
            // Show the modal after setting values
            bsModal.show();
        });
    }

    function saveCustomItem() {
        const form = document.getElementById('customItemForm');
        
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const itemId = document.getElementById('customItemId').value;
        const itemData = {
            title: document.getElementById('itemTitle').value,
            description: document.getElementById('itemDescription').value,
            cash_price: parseFloat(document.getElementById('itemCashPrice').value),
            credit_price: parseFloat(document.getElementById('itemCreditPrice').value),
            is_active: document.getElementById('itemIsActive').checked
        };
        
        const url = isEditMode ? `/api/custom-buylist/${itemId}` : '/api/custom-buylist';
        const method = isEditMode ? 'PUT' : 'POST';
        
        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(itemData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close the modal
                bootstrap.Modal.getInstance(document.getElementById('addCustomItemModal')).hide();
                
                // Reload the items
                loadCustomBuylistItems();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error saving custom buylist item:', error);
            alert('An error occurred while saving the item. Please try again.');
        });
    }

    function deleteCustomItem() {
        const itemId = document.getElementById('customItemId').value;
        
        if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
            return;
        }
        
        fetch(`/api/custom-buylist/${itemId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close the modal
                bootstrap.Modal.getInstance(document.getElementById('addCustomItemModal')).hide();
                
                // Reload the items
                loadCustomBuylistItems();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error deleting custom buylist item:', error);
            alert('An error occurred while deleting the item. Please try again.');
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Price Point Order functionality
        const pricePointOrderList = document.getElementById('pricePointOrderList');
        const pricePointOrderInputs = document.getElementById('pricePointOrderInputs');
        
        // Initialize the price point order functionality
        initializePricePointOrder();
        
        // Add event listener to the Save Button Label button
        const saveButtonLabelBtn = document.getElementById('saveButtonLabelBtn');
        if (saveButtonLabelBtn) {
            saveButtonLabelBtn.addEventListener('click', function() {
                saveButtonLabel();
            });
        }
        
        // Add event listener to the Save Price Preference Order button
        const savePricePreferenceOrderBtn = document.getElementById('savePricePreferenceOrderBtn');
        if (savePricePreferenceOrderBtn) {
            savePricePreferenceOrderBtn.addEventListener('click', function() {
                savePricePreferenceOrder();
            });
        }
        
        // Function to save the custom buylist button label
        function saveButtonLabel() {
            const buttonLabel = document.getElementById('custom_buylist_button_label').value || 'Add Custom Item';
            
            // Create form data
            const formData = new FormData();
            formData.append('custom_buylist_button_label', buttonLabel);
            
            // Send the button label to the server
            fetch('/api/user/save-custom-buylist-button-label', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const resultDiv = document.getElementById('buttonLabelSaveResult');
                    resultDiv.innerHTML = '';
                    
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show';
                    alertDiv.role = 'alert';
                    alertDiv.innerHTML = `
                        <strong>Success!</strong> Button label saved successfully.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    
                    resultDiv.appendChild(alertDiv);
                    
                    // Update the button text
                    const addButton = document.querySelector('[data-bs-target="#addCustomItemModal"]');
                    if (addButton) {
                        addButton.textContent = buttonLabel;
                    }
                    
                    // Auto-dismiss the alert after 3 seconds
                    setTimeout(() => {
                        const bsAlert = new bootstrap.Alert(alertDiv);
                        bsAlert.close();
                    }, 3000);
                } else {
                    // Show error message
                    const resultDiv = document.getElementById('buttonLabelSaveResult');
                    resultDiv.innerHTML = '';
                    
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                    alertDiv.role = 'alert';
                    alertDiv.innerHTML = `
                        <strong>Error!</strong> ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    
                    resultDiv.appendChild(alertDiv);
                }
            })
            .catch(error => {
                console.error('Error saving button label:', error);
                
                // Show error message
                const resultDiv = document.getElementById('buttonLabelSaveResult');
                resultDiv.innerHTML = '';
                
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                alertDiv.role = 'alert';
                alertDiv.innerHTML = `
                    <strong>Error!</strong> An error occurred while saving the button label. Please try again.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                
                resultDiv.appendChild(alertDiv);
            });
        }
        
        function savePricePreferenceOrder() {
            if (!pricePointOrderList) return;
            
            // Get the current order of price points
            const listItems = pricePointOrderList.querySelectorAll('li');
            const pricePreferenceOrder = Array.from(listItems).map(item => item.getAttribute('data-value'));
            
            // Log the data being sent
            console.log('Sending price preference order:', pricePreferenceOrder);
            
            // Create form data
            const formData = new FormData();
            pricePreferenceOrder.forEach(value => {
                formData.append('price_preference_order[]', value);
            });
            
            // Send the order to the server
            fetch('/api/user/save-price-preference-order', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show mt-2';
                    alertDiv.role = 'alert';
                    alertDiv.innerHTML = `
                        <strong>Success!</strong> Price preference order saved successfully.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    
                    // Insert the alert after the save button
                    const cardBody = savePricePreferenceOrderBtn.closest('.card-body');
                    if (cardBody) {
                        const alertContainer = cardBody.querySelector('.alert-container') || document.createElement('div');
                        alertContainer.className = 'alert-container';
                        alertContainer.innerHTML = '';
                        alertContainer.appendChild(alertDiv);
                        
                        // Insert after the button container
                        const buttonContainer = savePricePreferenceOrderBtn.closest('.d-flex');
                        if (buttonContainer && buttonContainer.nextElementSibling) {
                            cardBody.insertBefore(alertContainer, buttonContainer.nextElementSibling);
                        } else {
                            cardBody.insertBefore(alertContainer, pricePointOrderList);
                        }
                    }
                    
                    // Auto-dismiss the alert after 3 seconds
                    setTimeout(() => {
                        const bsAlert = new bootstrap.Alert(alertDiv);
                        bsAlert.close();
                    }, 3000);
                } else {
                    // Show error message
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error saving price preference order:', error);
                alert('An error occurred while saving the price preference order. Please try again.');
            });
        }
        
        function initializePricePointOrder() {
            // Add event listeners to the move up/down buttons
            if (pricePointOrderList) {
                pricePointOrderList.addEventListener('click', function(e) {
                    const button = e.target.closest('button');
                    if (!button) return;
                    
                    const listItem = button.closest('li');
                    if (!listItem) return;
                    
                    if (button.classList.contains('move-up')) {
                        const prevItem = listItem.previousElementSibling;
                        if (prevItem) {
                            pricePointOrderList.insertBefore(listItem, prevItem);
                        }
                    } else if (button.classList.contains('move-down')) {
                        const nextItem = listItem.nextElementSibling;
                        if (nextItem) {
                            pricePointOrderList.insertBefore(nextItem, listItem);
                        }
                    }
                    
                    // Update the hidden inputs
                    updatePricePointOrderInputs();
                });
                
                // Initial update of hidden inputs
                updatePricePointOrderInputs();
            }
        }
        
        function updatePricePointOrderInputs() {
            if (!pricePointOrderInputs || !pricePointOrderList) return;
            
            // Clear existing inputs
            pricePointOrderInputs.innerHTML = '';
            
            // Create hidden inputs for each price point in the current order
            const listItems = pricePointOrderList.querySelectorAll('li');
            listItems.forEach((item, index) => {
                const value = item.getAttribute('data-value');
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'price_preference_order[]';
                input.value = value;
                pricePointOrderInputs.appendChild(input);
            });
        }
        
        // Initialize Bootstrap modal
        const modal = document.getElementById('addCustomItemModal');
        if (modal) {
            modal.addEventListener('hidden.bs.modal', function () {
                // Reset form when modal is closed
                document.getElementById('customItemForm').reset();
            });
        }
        
        // Load custom buylist items
        loadCustomBuylistItems();
        
        // Add event listener to the "Add Custom Buylist Item" button
        const addButton = document.querySelector('[data-bs-target="#addCustomItemModal"]');
        if (addButton) {
            addButton.addEventListener('click', (e) => {
                e.preventDefault(); // Prevent default button behavior
                e.stopPropagation(); // Stop event bubbling
                openEditModal();
            });
        }
        
        // Add event listener to the Save button in the modal
        document.getElementById('saveCustomItemBtn').addEventListener('click', saveCustomItem);
        
        // Add event listener to the Delete button in the modal
        document.getElementById('deleteCustomItemBtn').addEventListener('click', deleteCustomItem);
        const gameSearch = document.getElementById('gameSearch');
        const enabledGames = document.getElementById('enabledGames');
        const selectedGamesContainer = document.getElementById('selectedGamesContainer');

        // Initialize language percentage fields
        const languages = ['JP', 'CS', 'DE', 'FR', 'IT', 'PT', 'RU', 'KO', 'PL', 'ZH', 'JA'];
        languages.forEach(lang => {
            const checkbox = document.getElementById(`lang_${lang}`);
            if (checkbox) {
                // Set initial state of percentage input based on checkbox
                toggleLanguagePercentage(lang);
                
                // Add event listener for future changes
                checkbox.addEventListener('change', function() {
                    toggleLanguagePercentage(lang);
                });
            }
        });

        // Initialize selected games
        updateSelectedGames();

        if (gameSearch) {
            gameSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                Array.from(enabledGames.options).forEach(option => {
                    const gameNameMatches = option.text.toLowerCase().includes(searchTerm);
                    option.style.display = gameNameMatches ? '' : 'none';
                });
            });
        }

        if (enabledGames) {
            enabledGames.addEventListener('mousedown', function(e) {
                e.preventDefault();
                const option = e.target.closest('option');
                if (option) {
                    option.selected = !option.selected;
                    updateSelectedGames();
                }
            });
        }

        function updateSelectedGames() {
            if (!enabledGames || !selectedGamesContainer) return;

            // Get selected options from the dropdown
            const selectedOptions = Array.from(enabledGames.selectedOptions).map(option => option.value);
            
            // Get already enabled games (those with "(Enabled)" in their text)
            const alreadyEnabledGames = Array.from(enabledGames.options)
                .filter(option => option.text.includes('(Enabled)'))
                .map(option => option.value);
            
            // Combine both lists and remove duplicates using Set
            const allSelectedGames = [...new Set([...selectedOptions, ...alreadyEnabledGames])];
            
            // Clear the container before adding new elements
            selectedGamesContainer.innerHTML = '';
            
            // Add each game to the visual selection container
            allSelectedGames.forEach(game => {
                const gameElement = document.createElement('div');
                gameElement.className = 'badge badge-primary mr-2 mb-2 p-2';
                gameElement.style.fontSize = '14px';
                gameElement.innerHTML = `
                    ${game}
                    <button type="button" class="close ml-2" aria-label="Remove" style="font-size: 18px;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                `;
                gameElement.querySelector('button').addEventListener('click', function() {
                    removeGame(game);
                });
                selectedGamesContainer.appendChild(gameElement);
            });

            // Show a message if no games are selected
            if (allSelectedGames.length === 0) {
                selectedGamesContainer.innerHTML = '<p class="text-muted mb-0">No games selected</p>';
            }
        }

        function removeGame(game) {
            if (!enabledGames) return;

            // Find the option in the select element
            const option = Array.from(enabledGames.options).find(opt => opt.value === game);
            if (option) {
                // Deselect the option
                option.selected = false;
                
                // Remove the "(Enabled)" text from the option
                option.text = option.text.replace(' (Enabled)', '');
                
                console.log(`Removed game: ${game}`);
            } else {
                console.warn(`Could not find option for game: ${game}`);
            }
            
            // Update the visual representation of selected games
            updateSelectedGames();
        }

        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();

            if (enabledGames) {
                // Get all selected games
                const selectedOptions = Array.from(enabledGames.selectedOptions).map(option => option.value);
                const alreadyEnabledGames = Array.from(enabledGames.options)
                    .filter(option => option.text.includes('(Enabled)'))
                    .map(option => option.value);
                
                // Use Set to remove duplicates
                const allSelectedGames = [...new Set([...selectedOptions, ...alreadyEnabledGames])];
                
                // Log for debugging
                console.log('Selected games:', selectedOptions);
                console.log('Already enabled games:', alreadyEnabledGames);
                console.log('All selected games (after removing duplicates):', allSelectedGames);

                // Remove existing hidden inputs for enabled games
                this.querySelectorAll('input[name="enabledGames"]').forEach(el => el.remove());

                // Create hidden inputs for each selected game
                allSelectedGames.forEach(game => {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'enabledGames';
                    hiddenInput.value = game;
                    this.appendChild(hiddenInput);
                });
            }

            // Submit the form
            this.submit();
        });
    });
</script>
{% endblock %}
