{% extends "base.html" %}

{% block title %}CSV to Shopify{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-4">CSV to Shopify</h1>
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Step 1: Import your CSV</h5>
                    <p class="card-text">Use the CSV import form below to upload your file.</p>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Step 2: On the Warehouse Page</h5>
                    <p class="card-text">Once on the warehouse page, drag and drop all items from your staged inventory queue to the Shopify drop zone.</p>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Step 3: Process Queue</h5>
                    <p class="card-text">Press the "Process Queue" button. You will be informed once the process is complete.</p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body p-0">
                    <iframe src="{{ url_for('csv.csv') }}" 
                            style="width: 100%; height: 800px; border: none;" 
                            title="CSV Import">
                    </iframe>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
