{% extends "base.html" %}

{% block title %}Event Calendar{% endblock %}

{% block styles %}
{{ super() }}
{% include "events/event_styles.html" %}
<!-- FullCalendar CSS -->
<link href="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.css" rel="stylesheet" />

<style>
    .fc-event {
        cursor: pointer;
    }
    .event-icon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        vertical-align: middle;
    }
    .fc-list-item-title a {
        color: inherit;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid event-page-content">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Event Calendar</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('event.events') }}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> List View
                        </a>
                        <a href="{{ url_for('event.create_event') }}" class="btn btn-primary ml-2">
                            <i class="fas fa-plus"></i> Create Event
                        </a>
                        <a href="{{ url_for('event.analytics') }}" class="btn btn-success ml-2">
                            <i class="fas fa-chart-bar"></i> Analytics
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <form method="GET" action="{{ url_for('event.calendar') }}" class="form-inline">
                                <div class="form-group mr-2">
                                    <label for="game" class="mr-2">Game:</label>
                                    <select name="game" id="game" class="form-control">
                                        <option value="">All Games</option>
                                        {% for game in games %}
                                        <option value="{{ game }}" {% if selected_game == game %}selected{% endif %}>{{ game }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <label for="event_type" class="mr-2">Type:</label>
                                    <select name="event_type" id="event_type" class="form-control">
                                        <option value="">All Types</option>
                                        {% for type in event_types %}
                                        <option value="{{ type }}" {% if selected_event_type == type %}selected{% endif %}>
                                            {{ type|title }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <label for="format" class="mr-2">Format:</label>
                                    <select name="format" id="format" class="form-control">
                                        <option value="">All Formats</option>
                                        {% for format in formats %}
                                        <option value="{{ format }}" {% if selected_format == format %}selected{% endif %}>
                                            {{ format }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{{ url_for('event.calendar') }}" class="btn btn-secondary ml-2">Reset</a>
                            </form>
                        </div>
                    </div>

                    <!-- Calendar -->
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- FullCalendar JS -->
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@4.4.0/main.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get calendar element
        var calendarEl = document.getElementById('calendar');
        
        // Get events data from template
        var eventsData = {{ calendar_events|safe }};
        
        // Initialize calendar
        var calendar = new FullCalendar.Calendar(calendarEl, {
            plugins: ['dayGrid', 'timeGrid', 'list', 'interaction'],
            header: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
            },
            navLinks: true,
            editable: false,
            eventLimit: true,
            events: eventsData,
            eventRender: function(info) {
                var event = info.event;
                var tooltip = event.title;
                
                // Handle custom icon if available
                if (event.extendedProps && event.extendedProps.imageUrl) {
                    var imageEl = document.createElement('img');
                    imageEl.src = event.extendedProps.imageUrl;
                    imageEl.className = 'event-icon';
                    
                    if (info.el.classList.contains('fc-list-item')) {
                        // For list view
                        var titleEl = info.el.querySelector('.fc-list-item-title');
                        if (titleEl) {
                            titleEl.prepend(imageEl);
                        }
                    } else {
                        // For month/week/day views
                        var titleEl = info.el.querySelector('.fc-title');
                        if (titleEl) {
                            titleEl.prepend(imageEl);
                        }
                    }
                }
                
                // Add tooltip
                $(info.el).tooltip({
                    title: tooltip,
                    placement: 'top',
                    trigger: 'hover',
                    container: 'body'
                });
            }
        });
        
        // Render the calendar
        calendar.render();
        
        // Auto-submit form when filters change
        $('#game, #event_type, #format').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
