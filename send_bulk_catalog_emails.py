from config.config import Config
import json
import time
import logging
import requests
from config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bulk_email_send.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

html_content = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCGSync: Automated eBay Integration for Trading Cards</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333333;">
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <tr>
            <td style="padding: 40px 30px; background-color: #1a237e; text-align: center;">
                <h1 style="color: #ffffff; margin: 0; font-size: 28px;">Automated eBay Integration That Actually Works for Trading Cards</h1>
            </td>
        </tr>
        
        <tr>
            <td style="padding: 30px;">
                <p style="font-size: 18px; margin-bottom: 20px;">
                    Stop wrestling with incorrect eBay categories and manual price adjustments. Our custom-built integration ensures your cards list correctly every time - graded or ungraded - with automated pricing that covers your fees.
                </p>
                
                <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <h2 style="color: #1a237e; margin-top: 0;">Two Steps to Automated Success</h2>
                    <p style="font-size: 16px; margin-bottom: 20px;">
                        Set it once, and we handle the rest:
                    </p>
                    <div style="background-color: #ffffff; padding: 20px; border-radius: 5px;">
                        <p style="font-size: 18px; margin: 0;">
                            1. Choose your minimum price (e.g., $10)<br>
                            2. Set your premium (e.g., 18% for fees)<br>
                            <br>
                            That's it. We'll automatically sync and list everything else.
                        </p>
                    </div>
                </div>

                <div style="background-color: #e8eaf6; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <h2 style="color: #1a237e; margin-top: 0;">Built Specifically for Card Sellers</h2>
                    <p style="margin-bottom: 0;">
                        ✓ Correct eBay categories every time - graded or ungraded<br>
                        ✓ Automated price adjustments with your premium<br>
                        ✓ Real-time inventory sync to prevent overselling<br>
                        ✓ Custom-built for trading cards - no generic marketplace tools<br>
                        ✓ Zero additional fees
                    </p>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="https://tcgsync.com" style="background-color: #1a237e; color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Automate Your eBay Listings Today</a>
                </div>
            </td>
        </tr>
        
        <tr>
            <td style="padding: 30px; background-color: #f5f5f5; text-align: center;">
                <p style="margin: 0; font-size: 14px; color: #666666;">
                    TCGSync.com - Automated eBay Integration for Trading Cards<br>
                    <a href="https://tcgsync.com" style="color: #1a237e;">Visit Our Website</a>
                </p>
            </td>
        </tr>
    </table>
</body>
</html>"""

def send_single_email(recipient_email):
    """Send a single email using the Mailgun API"""
    try:
        logger.info(f"Sending email to: {recipient_email}")
        
        MAILGUN_API_KEY = Config.MAILGUN_API_KEY
        MAILGUN_DOMAIN = Config.MAILGUN_DOMAIN

        if not MAILGUN_API_KEY or not MAILGUN_DOMAIN:
            logger.error("Mailgun API key or domain not set in config")
            raise ValueError("Mailgun API key or domain not set in config")

        # Mailgun API endpoint
        url = f"https://api.eu.mailgun.net/v3/{MAILGUN_DOMAIN}/messages"

        # Email data
        data = {
            "from": f"TCG Sync <admin@{MAILGUN_DOMAIN}>",
            "to": recipient_email,
            "subject": "Automated eBay Integration for Trading Cards",
            "html": html_content
        }

        # Send the email
        response = requests.post(
            url,
            auth=("api", MAILGUN_API_KEY),
            data=data,
            timeout=30
        )

        if response.status_code == 200:
            logger.info(f"Successfully sent email to {recipient_email}")
            return True
        else:
            logger.error(f"Failed to send email to {recipient_email}. Status code: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error sending email to {recipient_email}: {str(e)}")
        return False

def send_bulk_emails(emails):
    """Send emails to all recipients with a delay between each"""
    total_emails = len(emails)
    successful = 0
    failed = 0

    logger.info(f"Starting bulk email send to {total_emails} recipients")
    
    for i, email_obj in enumerate(emails, 1):
        email = email_obj.get('email')
        if not email:
            logger.error(f"Invalid email object: {email_obj}")
            failed += 1
            continue

        if send_single_email(email):
            successful += 1
        else:
            failed += 1

        logger.info(f"Progress: {i}/{total_emails} ({successful} successful, {failed} failed)")
        
        # Sleep for 60 seconds between emails to stay under rate limit
        if i < total_emails:  # Don't sleep after the last email
            logger.info("Waiting 60 seconds before sending next email...")
            time.sleep(60)

    logger.info(f"Bulk email send completed. Total: {total_emails}, Successful: {successful}, Failed: {failed}")
    return successful, failed

if __name__ == "__main__":
    try:
        # Load email list from JSON
        with open('email_list.json', 'r') as file:
            emails = json.load(file)
        
        # Send the emails
        successful, failed = send_bulk_emails(emails)
        
        print(f"\nEmail send completed:")
        print(f"Total emails: {len(emails)}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        
    except Exception as e:
        logger.error(f"Error in main execution: {str(e)}")
        print(f"An error occurred: {str(e)}")

