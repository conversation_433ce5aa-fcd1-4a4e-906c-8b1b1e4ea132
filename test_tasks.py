from config.config import Config
import os
import base64
import logging
from datetime import datetime
from dramatiq_config import broker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_card_image_processing():
    """Test the card image processing task"""
    try:
        from tasks import process_card_image
        
        # Use a small test image
        test_image_path = os.path.join('static', 'test_card.jpg')
        if not os.path.exists(test_image_path):
            logger.error(f"Test image not found at {test_image_path}")
            return
        
        with open(test_image_path, 'rb') as f:
            image_data = base64.b64encode(f.read()).decode()
        
        logger.info("Sending card image processing task...")
        message = process_card_image.send(
            image_data,
            game_name="Magic: The Gathering",
            expansion_name="Dominaria United",
            card_type="Normal",
            condition="NM"
        )
        
        # Wait for result
        result = message.get_result(block=True, timeout=10000)
        logger.info(f"Card processing result: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Error in card image processing test: {str(e)}")
        return None

def test_tcgplayer_prices():
    """Test TCGPlayer price fetching task"""
    try:
        from tasks import fetch_tcgplayer_prices_batch
        
        # Test with a small batch of known SKUs
        test_skus = [
            512334,  # Example SKU
            512335   # Example SKU
        ]
        
        logger.info("Sending TCGPlayer price fetch task...")
        message = fetch_tcgplayer_prices_batch.send(test_skus)
        
        # Wait for result
        result = message.get_result(block=True, timeout=10000)
        logger.info(f"TCGPlayer price fetch result: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Error in TCGPlayer price fetch test: {str(e)}")
        return None

def test_shopify_sync():
    """Test Shopify synchronization tasks"""
    try:
        from tasks import (
            fetch_all_orders,
            fetch_all_customers,
            fetch_all_products
        )
        
        # Test credentials (replace with your test store details)
        test_headers = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json"
        }
        test_url = "https://test-store.myshopify.com/admin/api/2023-01"
        test_username = "test_user"
        
        # Test orders sync
        logger.info("Testing Shopify orders sync...")
        orders_message = fetch_all_orders.send(
            f"{test_url}/orders.json",
            test_headers,
            test_username
        )
        orders_result = orders_message.get_result(block=True, timeout=10000)
        logger.info(f"Orders sync result: {orders_result}")
        
        # Test customers sync
        logger.info("Testing Shopify customers sync...")
        customers_message = fetch_all_customers.send(
            f"{test_url}/customers.json",
            test_headers,
            test_username
        )
        customers_result = customers_message.get_result(block=True, timeout=10000)
        logger.info(f"Customers sync result: {customers_result}")
        
        # Test products sync
        logger.info("Testing Shopify products sync...")
        products_message = fetch_all_products.send(
            f"{test_url}/products.json",
            test_headers,
            test_username
        )
        products_result = products_message.get_result(block=True, timeout=10000)
        logger.info(f"Products sync result: {products_result}")
        
        return {
            "orders": orders_result,
            "customers": customers_result,
            "products": products_result
        }
        
    except Exception as e:
        logger.error(f"Error in Shopify sync test: {str(e)}")
        return None

def run_all_tests():
    """Run all background task tests"""
    logger.info("Starting background task tests...")
    
    results = {
        "card_processing": test_card_image_processing(),
        "tcgplayer_prices": test_tcgplayer_prices(),
        "shopify_sync": test_shopify_sync()
    }
    
    logger.info("All tests completed!")
    logger.info("Results summary:")
    for test_name, result in results.items():
        status = "✓ Success" if result is not None else "✗ Failed"
        logger.info(f"{test_name}: {status}")
    
    return results

if __name__ == "__main__":
    run_all_tests()

