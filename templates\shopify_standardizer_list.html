{% extends "base.html" %}

{% block title %}Shopify Product Discrepancies{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Shopify Product Discrepancies</h1>
        <a href="{{ url_for('standardizer.dashboard') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('standardizer.list_discrepancies') }}" class="form-inline">
                <div class="form-group mb-2 mr-2">
                    <label for="status" class="mr-2">Status:</label>
                    <select class="form-control" id="status" name="status">
                        <option value="pending" {% if status == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="approved" {% if status == 'approved' %}selected{% endif %}>Approved</option>
                        <option value="rejected" {% if status == 'rejected' %}selected{% endif %}>Rejected</option>
                        <option value="repaired" {% if status == 'repaired' %}selected{% endif %}>Repaired</option>
                    </select>
                </div>
                <div class="form-group mb-2 mr-2">
                    <label for="username" class="mr-2">Username:</label>
                    <select class="form-control" id="username" name="username">
                        <option value="">All Users</option>
                        {% for user in users %}
                        <option value="{{ user }}" {% if username == user %}selected{% endif %}>{{ user }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group mb-2 mr-2">
                    <label for="issue_type" class="mr-2">Issue Type:</label>
                    <select class="form-control" id="issue_type" name="issue_type">
                        <option value="">All Issues</option>
                        <option value="title" {% if issue_type == 'title' %}selected{% endif %}>Title</option>
                        <option value="body_html" {% if issue_type == 'body_html' %}selected{% endif %}>Body HTML</option>
                        <option value="tags" {% if issue_type == 'tags' %}selected{% endif %}>Tags</option>
                        <option value="vendor" {% if issue_type == 'vendor' %}selected{% endif %}>Vendor</option>
                        <option value="product_type" {% if issue_type == 'product_type' %}selected{% endif %}>Product Type</option>
                        <option value="images" {% if issue_type == 'images' %}selected{% endif %}>Images</option>
                        <option value="metafields" {% if issue_type == 'metafields' %}selected{% endif %}>Metafields</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary mb-2">Apply Filters</button>
            </form>
        </div>
    </div>

    <!-- Batch Actions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Batch Actions</h6>
        </div>
        <div class="card-body">
            <form id="batchForm" method="post" action="{{ url_for('standardizer.batch_approve') }}">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success" onclick="submitBatchForm('approve')">Approve Selected</button>
                    <button type="button" class="btn btn-danger" onclick="submitBatchForm('reject')">Reject Selected</button>
                    <button type="button" class="btn btn-secondary" onclick="toggleAllCheckboxes()">Select All</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Discrepancies Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Discrepancies ({{ total }} total)</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered text-white" id="dataTable" width="100%" cellspacing="0">
                <style>
                    #dataTable tbody tr {
                        background-color: transparent !important;
                    }
                    #dataTable {
                        color: white !important;
                    }
                </style>
                    <thead>
                        <tr>
                            <th width="30px"><input type="checkbox" id="selectAll"></th>
                            <th>Title</th>
                            <th>Username</th>
                            <th>Product ID</th>
                            <th>Issues</th>
                            <th>Identified</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for disc in discrepancies %}
                        <tr>
                            <td>
                                <input type="checkbox" name="discrepancy_ids" value="{{ disc._id }}" form="batchForm">
                            </td>
                            <td>{{ disc.title }}</td>
                            <td>{{ disc.username }}</td>
                            <td>{{ disc.product_id_int }}</td>
                            <td>
                                {% for field in disc.discrepancies %}
                                <span class="badge badge-info">{{ field }}</span>
                                {% endfor %}
                            </td>
                            <td>{{ disc.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <a href="{{ url_for('standardizer.view_discrepancy', discrepancy_id=disc._id) }}" class="btn btn-info btn-sm">View</a>
                                {% if status == 'pending' %}
                                <form method="post" action="{{ url_for('standardizer.approve_discrepancy') }}" class="d-inline">
                                    <input type="hidden" name="discrepancy_id" value="{{ disc._id }}">
                                    <button type="submit" class="btn btn-success btn-sm">Approve</button>
                                </form>
                                <form method="post" action="{{ url_for('standardizer.reject_discrepancy') }}" class="d-inline">
                                    <input type="hidden" name="discrepancy_id" value="{{ disc._id }}">
                                    <button type="submit" class="btn btn-danger btn-sm">Reject</button>
                                </form>
                                {% endif %}
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center">No discrepancies found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pages > 1 %}
            <div class="d-flex justify-content-center">
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        {% if page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('standardizer.list_discrepancies', page=page-1, status=status, username=username, issue_type=issue_type) }}">Previous</a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">Previous</span>
                        </li>
                        {% endif %}

                        {% for p in range(1, pages + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('standardizer.list_discrepancies', page=p, status=status, username=username, issue_type=issue_type) }}">{{ p }}</a>
                        </li>
                        {% endfor %}

                        {% if page < pages %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('standardizer.list_discrepancies', page=page+1, status=status, username=username, issue_type=issue_type) }}">Next</a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">Next</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
/* Loading overlay styles */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading-spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: white;
    font-size: 18px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading overlay to the page
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing changes and updating Shopify...</div>
    `;
    document.body.appendChild(loadingOverlay);
    
    // Select all checkbox functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        var checkboxes = document.querySelectorAll('input[name="discrepancy_ids"]');
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = this.checked;
        }
    });
    
    // Add event listeners to all approve buttons
    const approveButtons = document.querySelectorAll('form[action*="approve_discrepancy"] button');
    approveButtons.forEach(button => {
        button.addEventListener('click', function() {
            loadingOverlay.style.display = 'flex';
        });
    });
});

function toggleAllCheckboxes() {
    var selectAll = document.getElementById('selectAll');
    selectAll.checked = !selectAll.checked;
    selectAll.dispatchEvent(new Event('change'));
}

function submitBatchForm(action) {
    var form = document.getElementById('batchForm');
    var checkedBoxes = document.querySelectorAll('input[name="discrepancy_ids"]:checked');
    
    if (checkedBoxes.length === 0) {
        alert('Please select at least one discrepancy');
        return;
    }
    
    if (action === 'approve') {
        form.action = "{{ url_for('standardizer.batch_approve') }}";
        // Show loading overlay for batch approve
        document.querySelector('.loading-overlay').style.display = 'flex';
    } else if (action === 'reject') {
        form.action = "{{ url_for('standardizer.batch_reject') }}";
    }
    
    form.submit();
}

</script>
{% endblock %}
