{% extends "base.html" %}

{% block title %}Manage Waitlist - {{ event.title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/events.css') }}">
<style>
    .waitlist-table {
        max-height: 600px;
        overflow-y: auto;
    }
    
    .status-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-waiting {
        background-color: rgba(52, 152, 219, 0.2);
        color: #3498db;
    }
    
    .status-notified {
        background-color: rgba(241, 196, 15, 0.2);
        color: #f1c40f;
    }
    
    .status-registered {
        background-color: rgba(46, 204, 113, 0.2);
        color: #2ecc71;
    }
    
    .status-expired {
        background-color: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="{{ url_for('events.event_details', event_id=event.id) }}" class="btn btn-outline-secondary mb-2">
                        <i class="fas fa-arrow-left"></i> Back to Event
                    </a>
                    <h1 class="h3 mb-0">Manage Waitlist</h1>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-title mb-0">Event Details</h4>
                </div>
                <div class="card-body">
                    <h5>{{ event.title }}</h5>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Date:</span>
                        <span>{{ event.date.strftime('%A, %B %d, %Y') }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Time:</span>
                        <span>{{ event.start_time }} - {{ event.end_time }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Location:</span>
                        <span>{{ event.location }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Capacity:</span>
                        <span>
                            {% if event.max_tickets == 0 %}
                                Unlimited
                            {% else %}
                                {{ event.max_tickets }}
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Tickets Sold:</span>
                        <span>{{ event.tickets_sold }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Waitlist Count:</span>
                        <span>{{ event.waitlist_count }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Waitlist Limit:</span>
                        <span>
                            {% if event.waitlist_limit == 0 %}
                                Unlimited
                            {% else %}
                                {{ event.waitlist_limit }}
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Waitlist Stats</h4>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Waiting:</span>
                        <span>{{ waitlist_entries.filter(status='waiting').count() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Notified:</span>
                        <span>{{ waitlist_entries.filter(status='notified').count() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Registered:</span>
                        <span>{{ waitlist_entries.filter(status='registered').count() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Expired:</span>
                        <span>{{ waitlist_entries.filter(status='expired').count() }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Waitlist Entries</h4>
                </div>
                <div class="card-body">
                    {% if waitlist_entries %}
                        <div class="waitlist-table">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Joined</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in waitlist_entries %}
                                        <tr>
                                            <td>{{ entry.position }}</td>
                                            <td>{{ entry.customer_name }}</td>
                                            <td>{{ entry.customer_email }}</td>
                                            <td>{{ entry.customer_phone or '-' }}</td>
                                            <td>{{ entry.joined_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>
                                                <span class="status-badge status-{{ entry.status }}">
                                                    {{ entry.status|capitalize }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if entry.status == 'waiting' %}
                                                    <form action="{{ url_for('events.notify_waitlist_entry', event_id=event.id, entry_id=entry.id) }}" method="post" class="d-inline">
                                                        <button type="submit" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-bell"></i> Notify
                                                        </button>
                                                    </form>
                                                {% elif entry.status == 'notified' %}
                                                    <button type="button" class="btn btn-sm btn-success mark-registered" data-entry-id="{{ entry.id }}">
                                                        <i class="fas fa-check"></i> Mark Registered
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger mark-expired" data-entry-id="{{ entry.id }}">
                                                        <i class="fas fa-times"></i> Mark Expired
                                                    </button>
                                                {% endif %}
                                                
                                                {% if entry.notes %}
                                                    <button type="button" class="btn btn-sm btn-info view-notes" data-bs-toggle="modal" data-bs-target="#notesModal" data-notes="{{ entry.notes }}" data-name="{{ entry.customer_name }}">
                                                        <i class="fas fa-sticky-note"></i>
                                                    </button>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No one has joined the waitlist yet.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1" aria-labelledby="notesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notesModalLabel">Notes from <span id="customerName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="notesContent"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // View notes
        $('.view-notes').click(function() {
            const notes = $(this).data('notes');
            const name = $(this).data('name');
            
            $('#customerName').text(name);
            $('#notesContent').text(notes);
        });
        
        // Mark as registered
        $('.mark-registered').click(function() {
            const entryId = $(this).data('entry-id');
            
            if (confirm('Are you sure you want to mark this entry as registered?')) {
                // TODO: Implement AJAX call to update status
                $.ajax({
                    url: `/events/waitlist/${event.id}/update/${entryId}`,
                    type: 'POST',
                    data: { status: 'registered' },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error updating status: ' + response.error);
                        }
                    },
                    error: function() {
                        alert('Error updating status');
                    }
                });
            }
        });
        
        // Mark as expired
        $('.mark-expired').click(function() {
            const entryId = $(this).data('entry-id');
            
            if (confirm('Are you sure you want to mark this entry as expired?')) {
                // TODO: Implement AJAX call to update status
                $.ajax({
                    url: `/events/waitlist/${event.id}/update/${entryId}`,
                    type: 'POST',
                    data: { status: 'expired' },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error updating status: ' + response.error);
                        }
                    },
                    error: function() {
                        alert('Error updating status');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
