# Script to analyze the matchedIds collection and show statistics
from pymongo import MongoClient
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"analyze_matched_ids_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

def analyze_matched_ids():
    """Analyze the matchedIds collection and show statistics"""
    # Check if matchedIds collection exists
    if 'matchedIds' not in cardtrader_db.list_collection_names():
        logger.error("matchedIds collection not found in cardtrader database")
        return
    
    # Count total records
    total_records = cardtrader_db.matchedIds.count_documents({})
    logger.info(f"Total records in matchedIds collection: {total_records}")
    
    # Count records with valid blueprint_id
    blueprint_records = cardtrader_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None}
    })
    logger.info(f"Records with valid blueprint_id: {blueprint_records}")
    
    # Count records with valid tcg_player_id
    tcgplayer_records = cardtrader_db.matchedIds.count_documents({
        "tcg_player_id": {"$exists": True, "$ne": None}
    })
    logger.info(f"Records with valid tcg_player_id: {tcgplayer_records}")
    
    # Count records with valid card_market_id (not null)
    cardmarket_records = cardtrader_db.matchedIds.count_documents({
        "card_market_id": {"$exists": True, "$ne": None, "$ne": "null"}
    })
    logger.info(f"Records with valid card_market_id (not null): {cardmarket_records}")
    
    # Count records with card_market_id field (including null)
    cardmarket_field_records = cardtrader_db.matchedIds.count_documents({
        "card_market_id": {"$exists": True}
    })
    logger.info(f"Records with card_market_id field (including null): {cardmarket_field_records}")
    
    # Count records with valid scryfall_id
    scryfall_records = cardtrader_db.matchedIds.count_documents({
        "scryfall_id": {"$exists": True, "$ne": None}
    })
    logger.info(f"Records with valid scryfall_id: {scryfall_records}")
    
    # Count records with all three IDs (not null)
    all_ids_records = cardtrader_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "tcg_player_id": {"$exists": True, "$ne": None},
        "card_market_id": {"$exists": True, "$ne": None, "$ne": "null"}
    })
    logger.info(f"Records with valid blueprint_id, tcg_player_id, and card_market_id: {all_ids_records}")
    
    # Count records with blueprint_id and tcg_player_id only (no valid card_market_id)
    blueprint_tcgplayer_only = cardtrader_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "tcg_player_id": {"$exists": True, "$ne": None},
        "$or": [
            {"card_market_id": {"$exists": False}},
            {"card_market_id": None}
        ]
    })
    logger.info(f"Records with blueprint_id and tcg_player_id only (no card_market_id): {blueprint_tcgplayer_only}")
    
    # Count records with blueprint_id and card_market_id only (no valid tcg_player_id)
    blueprint_cardmarket_only = cardtrader_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "card_market_id": {"$exists": True, "$ne": None, "$ne": "null"},
        "$or": [
            {"tcg_player_id": {"$exists": False}},
            {"tcg_player_id": None}
        ]
    })
    logger.info(f"Records with blueprint_id and card_market_id only (no tcg_player_id): {blueprint_cardmarket_only}")
    
    # Count records with blueprint_id and scryfall_id only (no valid tcg_player_id or card_market_id)
    blueprint_scryfall_only = cardtrader_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "scryfall_id": {"$exists": True, "$ne": None},
        "$and": [
            {"$or": [
                {"tcg_player_id": {"$exists": False}},
                {"tcg_player_id": None}
            ]},
            {"$or": [
                {"card_market_id": {"$exists": False}},
                {"card_market_id": None}
            ]}
        ]
    })
    logger.info(f"Records with blueprint_id and scryfall_id only (no tcg_player_id or card_market_id): {blueprint_scryfall_only}")
    
    # Count records with blueprint_id only (no other valid IDs)
    blueprint_only = cardtrader_db.matchedIds.count_documents({
        "blueprint_id": {"$exists": True, "$ne": None},
        "$and": [
            {"$or": [
                {"tcg_player_id": {"$exists": False}},
                {"tcg_player_id": None}
            ]},
            {"$or": [
                {"card_market_id": {"$exists": False}},
                {"card_market_id": None}
            ]},
            {"$or": [
                {"scryfall_id": {"$exists": False}},
                {"scryfall_id": None}
            ]}
        ]
    })
    logger.info(f"Records with blueprint_id only (no other valid IDs): {blueprint_only}")
    
    # Let's also check a sample record to understand the structure better
    sample_record = cardtrader_db.matchedIds.find_one()
    if sample_record:
        logger.info(f"Sample record structure: {sample_record}")
    
    # Print summary
    print("\n=== SUMMARY ===")
    print(f"Total records: {total_records}")
    print(f"Records with valid blueprint_id: {blueprint_records}")
    print(f"Records with valid tcg_player_id: {tcgplayer_records}")
    print(f"Records with valid card_market_id (not null): {cardmarket_records}")
    print(f"Records with card_market_id field (including null): {cardmarket_field_records}")
    print(f"Records with valid scryfall_id: {scryfall_records}")
    print("\n=== COMBINATIONS ===")
    print(f"Records with all three IDs (blueprint, TCGPlayer, CardMarket): {all_ids_records}")
    print(f"Records with blueprint_id and tcg_player_id only (no card_market_id): {blueprint_tcgplayer_only}")
    print(f"Records with blueprint_id and card_market_id only (no tcg_player_id): {blueprint_cardmarket_only}")
    print(f"Records with blueprint_id and scryfall_id only (no tcg_player_id or card_market_id): {blueprint_scryfall_only}")
    print(f"Records with blueprint_id only (no other valid IDs): {blueprint_only}")

if __name__ == "__main__":
    logger.info("Starting analysis of matchedIds collection")
    analyze_matched_ids()
    logger.info("Analysis complete")
