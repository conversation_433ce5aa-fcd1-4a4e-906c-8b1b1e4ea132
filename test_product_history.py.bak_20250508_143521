import requests
import json
import sys
import time
from datetime import datetime
from pymongo import MongoClient

# MongoDB connection
MONGO_URI = "*******************************************************************"
DB_NAME = "test"
WATCH_COLLECTION = "shProducts"
HISTORY_COLLECTION = "shProductHistory"

def connect_to_mongo():
    """Connect to MongoDB and return the client."""
    try:
        client = MongoClient(MONGO_URI)
        # Test the connection
        client.admin.command('ping')
        print("Successfully connected to MongoDB")
        return client
    except Exception as e:
        print(f"Failed to connect to MongoDB: {e}")
        sys.exit(1)

def test_history_api():
    """Test the history API endpoint."""
    print("\n=== Testing History API ===")
    
    # Connect to MongoDB
    client = connect_to_mongo()
    db = client[DB_NAME]
    collection = db[WATCH_COLLECTION]
    history_collection = db[HISTORY_COLLECTION]
    
    # Get a sample product ID from the shProducts collection
    sample_product = collection.find_one({"id": {"$exists": True}})
    if not sample_product:
        print("No products found in the collection")
        return
    
    item_id = sample_product.get("id")
    print(f"Testing with item ID: {item_id}")
    
    # Check if there are any history records for this item
    history_count = history_collection.count_documents({"itemId": item_id})
    print(f"Found {history_count} history records for this item")
    
    if history_count == 0:
        print("Creating a test history record...")
        # Create a test history record
        test_record = {
            "itemId": item_id,
            "username": "test_user",
            "timestamp": datetime.utcnow(),
            "fieldsChanged": {
                "price": {
                    "from": 10.99,
                    "to": 12.99
                },
                "inventory_quantity": {
                    "from": 5,
                    "to": 10
                }
            }
        }
        history_collection.insert_one(test_record)
        print("Test record created")
    
    # Test the API endpoint
    try:
        response = requests.get(f"http://localhost:5050/api/history?itemId={item_id}")
        if response.status_code == 200:
            data = response.json()
            print(f"API response: {json.dumps(data, indent=2)}")
            
            if data.get("history") and len(data["history"]) > 0:
                print("✅ API returned history records successfully")
            else:
                print("❌ API returned no history records")
        else:
            print(f"❌ API request failed with status code {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing API: {e}")
    
    # Test the proxy endpoint in the main application
    try:
        response = requests.get(f"http://localhost:8000/api/history?itemId={item_id}")
        if response.status_code == 200:
            data = response.json()
            print(f"Proxy API response: {json.dumps(data, indent=2)}")
            
            if data.get("history") and len(data["history"]) > 0:
                print("✅ Proxy API returned history records successfully")
            else:
                print("❌ Proxy API returned no history records")
        else:
            print(f"❌ Proxy API request failed with status code {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing proxy API: {e}")

def test_view_history_button():
    """Test the View History button functionality."""
    print("\n=== Testing View History Button ===")
    print("To test the View History button:")
    print("1. Open the Shopify Products page in your browser")
    print("2. Click 'View More' on any product")
    print("3. Click the 'View History' button in the modal")
    print("4. Verify that the history modal opens and displays history records")
    print("\nThis test requires manual verification.")

if __name__ == "__main__":
    print("=== Product History Tracker Test ===")
    test_history_api()
    test_view_history_button()
    print("\nTests completed.")
