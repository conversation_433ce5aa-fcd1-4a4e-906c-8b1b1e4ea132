import asyncio
import aiohttp
import json
import logging
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import UpdateOne
from pymongo.errors import BulkWriteError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sealed_update.log'),
        logging.StreamHandler()
    ]
)

# MongoDB connection details
mongo_host = 'mongodb://admin:Reggie2805!@*************:27017/?authSource=admin'
test_db = 'test'
collection_names = {
    'catalog': 'catalog',
    'tcgplayerKey': 'tcgplayerKey'
}

# Global variables
tcgplayer_api_key = None
semaphore = asyncio.Semaphore(10)  # Limit concurrent API calls

async def get_mongo_client():
    return AsyncIOMotorClient(mongo_host, maxPoolSize=100)

async def get_tcgplayer_api_key(db):
    key_doc = await db[collection_names['tcgplayerKey']].find_one({}, sort=[('_id', -1)])
    if key_doc and 'latestKey' in key_doc:
        return key_doc['latestKey']
    else:
        raise ValueError("No TCGplayer API key found in the database")

async def fetch_extended_data(session, product_ids):
    if not product_ids:
        return []
    
    url = f"https://api.tcgplayer.com/catalog/products/{','.join(map(str, product_ids))}?getExtendedFields=true"
    headers = {
        "accept": "application/json",
        "Authorization": f"Bearer {tcgplayer_api_key}"
    }

    async with semaphore:
        max_retries = 3
        retry_delay = 10

        for attempt in range(max_retries):
            try:
                async with session.get(url, headers=headers) as response:
                    response.raise_for_status()
                    data = await response.json()
                    return data.get('results', [])

            except aiohttp.ClientResponseError as e:
                if e.status == 429:  # Rate limit
                    logging.warning(f"Rate limit hit. Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    logging.error(f"HTTP error {e.status}: {e.message}")
                    if attempt == max_retries - 1:
                        raise
            except Exception as e:
                logging.error(f"An error occurred: {e}")
                if attempt == max_retries - 1:
                    raise

            await asyncio.sleep(retry_delay)

        logging.error(f"Failed to fetch data for product IDs {product_ids} after {max_retries} attempts.")
        return []

async def update_products_batch(client, products_data):
    if not products_data:
        return

    operations = []
    for product in products_data:
        product_id = product.get('productId')
        if not product_id:
            continue

        update_data = {
            'extendedData': product.get('extendedData', []),
            'lastupdated': datetime.utcnow().isoformat()
        }

        # Extract specific fields from extendedData
        for field in product.get('extendedData', []):
            if field['name'] == 'Number':
                update_data['number'] = field['value']
            elif field['name'] == 'Rarity':
                update_data['rarity'] = field['value']
            elif field['name'] in ['UPC', 'GTIN']:
                update_data['upc'] = field['value']
                update_data['barcode'] = field['value']

        operations.append(
            UpdateOne(
                {'productId': product_id},
                {'$set': update_data}
            )
        )

    if operations:
        try:
            result = await client[test_db][collection_names['catalog']].bulk_write(operations, ordered=False)
            logging.info(f"Updated {result.modified_count} documents")
        except BulkWriteError as bwe:
            logging.warning(f"Bulk write error: {bwe.details}")

async def process_sealed_products():
    client = await get_mongo_client()
    global tcgplayer_api_key

    try:
        tcgplayer_api_key = await get_tcgplayer_api_key(client[test_db])
        logging.info("Successfully retrieved TCGplayer API key")

        # Find specific product
        product_id = 578122
        doc = await client[test_db][collection_names['catalog']].find_one(
            {'productId': product_id}
        )
        
        if not doc:
            logging.info(f"Product {product_id} not found")
            return

        logging.info(f"Processing product {product_id}")

        async with aiohttp.ClientSession() as session:
            try:
                products_data = await fetch_extended_data(session, [product_id])
                await update_products_batch(client, products_data)
                logging.info(f"Completed processing product {product_id}")
            except Exception as e:
                logging.error(f"Error processing product: {e}")

    except Exception as e:
        logging.error(f"An error occurred: {e}")
    finally:
        client.close()

async def main():
    logging.info("Starting sealed products update process")
    start_time = datetime.now()
    
    await process_sealed_products()
    
    end_time = datetime.now()
    logging.info(f"Process completed. Duration: {end_time - start_time}")

if __name__ == "__main__":
    asyncio.run(main())
