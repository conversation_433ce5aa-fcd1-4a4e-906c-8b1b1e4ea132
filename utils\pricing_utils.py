from config.config import Config
"""
Shared pricing utilities for consistent pricing across different application paths.
This module centralizes pricing logic for both scheduled jobs (bulkcreate) and manual
UI-triggered exports (shopify_update_catalog).
"""
import logging
import sys
import os
from typing import Dict, Any, Tuple, Optional, List, TYPE_CHECKING

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define types to handle optional imports
if TYPE_CHECKING:
    from typing import Type
    PricingCalculatorType = Type['PricingCalculator']
else:
    PricingCalculatorType = Any

# Try to import the saautopricing module
try:
    from saautopricing import (
        prepare_shopify_settings,
        PricingCalculator,
        fetch_pricing_data,
        get_exchange_rate,
        extract_condition,
        determine_printing_type
    )
    SAAUTOPRICING_AVAILABLE = True
    logger.info("saautopricing module loaded successfully")
except ImportError:
    SAAUTOPRICING_AVAILABLE = False
    logger.warning("saautopricing module not available, prices will default to catalog values")
    # Define placeholder for PricingCalculator
    class PricingCalculator:
        def __init__(self, *args, **kwargs):
            pass
        def calculate_final_price(self, *args, **kwargs):
            return None, True, None


def setup_pricing_calculator(user_profile: Dict[str, Any]) -> Tuple[Optional[Any], str, float, Optional[str]]:
    """
    Set up the pricing calculator with user-specific settings.

    Args:
        user_profile: User document with pricing settings

    Returns:
        Tuple containing:
        - pricing_calculator: PricingCalculator instance or None if not available
        - user_currency: User's preferred currency
        - exchange_rate: Exchange rate from USD to user's currency
        - tcgplayer_api_key: TCGPlayer API key if available, otherwise None
    """
    pricing_calculator = None
    user_currency = "USD"
    exchange_rate = 1.0
    tcgplayer_api_key = None

    if not SAAUTOPRICING_AVAILABLE:
        logger.warning("Cannot set up pricing calculator - saautopricing module not available")
        return pricing_calculator, user_currency, exchange_rate, tcgplayer_api_key

    try:
        # Get user currency and exchange rate
        user_currency = user_profile.get('currency', 'USD')
        exchange_rate = get_exchange_rate(user_currency)
        logger.info(f"User currency: {user_currency}, Exchange rate: {exchange_rate}")

        # Get pricing settings
        settings = prepare_shopify_settings(user_profile)
        pricing_calculator = PricingCalculator(settings, user_currency)
        logger.info(f"Created pricing calculator with settings: {settings}")

        return pricing_calculator, user_currency, exchange_rate, tcgplayer_api_key
    except Exception as e:
        logger.error(f"Error initializing pricing calculator: {str(e)}")
        return None, user_currency, exchange_rate, tcgplayer_api_key


def calculate_product_price(
    pricing_calculator: Optional[Any],
    sku: Dict[str, Any],
    doc: Dict[str, Any],
    exchange_rate: float = 1.0,
    tcgplayer_api_key: Optional[str] = None,
    pricing_data: Optional[Dict[str, List[Dict[str, Any]]]] = None
) -> str:
    """
    Calculate the price for a product variant using the pricing calculator.
    Falls back to the lowPrice if the calculator is not available or fails.

    Args:
        pricing_calculator: The pricing calculator instance
        sku: The SKU data dictionary
        doc: The product document
        exchange_rate: Exchange rate from USD to user's currency
        tcgplayer_api_key: TCGPlayer API key if available
        pricing_data: Cached pricing data keyed by product ID

    Returns:
        Formatted price string (e.g., "10.99")
    """
    if not pricing_calculator:
        # Fallback to lowPrice if no calculator
        low_price = sku.get("lowPrice")
        if low_price is None:
            logger.warning(f"Low price is None in SKU for product {doc.get('productId')} - {doc.get('name')}")
            return "0.00"
        try:
            return "{:.2f}".format(float(low_price))
        except (ValueError, TypeError) as e:
            logger.error(f"Error processing SKU price for product {doc.get('productId')} - {doc.get('name')}: {str(e)}")
            return "0.00"

    if not SAAUTOPRICING_AVAILABLE:
        logger.warning("saautopricing module not available, using default price")
        low_price = sku.get("lowPrice", 0.00)
        try:
            return "{:.2f}".format(float(low_price))
        except (ValueError, TypeError):
            return "0.00"

    try:
        # Get product ID
        product_id = str(doc.get('productId'))
        condition = sku.get('condName', '')
        printing = sku.get('printingName', '')
        variant_title = condition if printing.lower() == 'normal' else f"{condition} - {printing}"

        # Fetch pricing data if not provided
        current_pricing_data = pricing_data or {}
        if product_id not in current_pricing_data and tcgplayer_api_key:
            product_pricing = fetch_pricing_data([product_id], tcgplayer_api_key)
            current_pricing_data.update(product_pricing)

        # Get pricing data for this product
        product_pricing = current_pricing_data.get(product_id, [])

        # Only include subtypes that have valid prices
        valid_subtypes = [p.get('subTypeName') for p in product_pricing if p.get('subTypeName')]

        # Determine printing type
        printing_type = determine_printing_type(variant_title, valid_subtypes)

        # Find the price data for this printing type
        matched_price = None
        for p in product_pricing:
            if p.get('subTypeName', '').lower() == printing_type.lower():
                matched_price = p
                break

        if matched_price:
            # Extract pricing info for this printing type and convert from USD to user currency
            pricing_info = {}

            # Get market price
            market_price = matched_price.get('marketPrice')
            if market_price is not None:
                pricing_info['marketPrice'] = float(market_price) * exchange_rate

            # Get low price
            low_price = matched_price.get('lowPrice')
            if low_price is not None:
                pricing_info['lowPrice'] = float(low_price) * exchange_rate

            # Get mid price
            mid_price = matched_price.get('midPrice')
            if mid_price is not None:
                pricing_info['midPrice'] = float(mid_price) * exchange_rate

            # Get high price
            high_price = matched_price.get('highPrice')
            if high_price is not None:
                pricing_info['highPrice'] = float(high_price) * exchange_rate

            # Create sku_info for price calculation
            sku_info = {
                'pricingInfo': pricing_info,
                'condName': condition,
                'printingName': printing_type,
                'skuId': sku.get('skuId'),
                'variantTitle': variant_title
            }

            # Calculate price
            calculated_price, is_missing, price_history = pricing_calculator.calculate_final_price(sku_info, doc)

            if not is_missing and calculated_price is not None:
                price = "{:.2f}".format(calculated_price)
                logger.info(f"Calculated price for {variant_title}: {price} (using saautopricing)")
                return price

    except Exception as e:
        logger.error(f"Error calculating price: {str(e)}")

    # Fallback to low price if calculation fails or no matched price data
    low_price = sku.get("lowPrice")
    if low_price is None:
        return "0.00"
    try:
        return "{:.2f}".format(float(low_price))
    except (ValueError, TypeError):
        return "0.00"


def get_fallback_price_from_prices_collection(db, product_id, logger) -> str:
    """
    Attempt to get a fallback price from the 'prices' collection when other methods fail.

    Args:
        db: MongoDB database connection
        product_id: The product ID to look up
        logger: Logger instance for log messages

    Returns:
        Formatted price string (e.g., "10.99") or "0.00" if not found
    """
    try:
        product_id_int = int(product_id)
        prices_collection = db['prices']
        price_doc = prices_collection.find_one({'productId': product_id_int})

        if price_doc and 'low' in price_doc and price_doc['low'] is not None:
            fallback_price_val = price_doc['low']
            try:
                price = "{:.2f}".format(float(fallback_price_val))
                logger.info(f"Using fallback price from 'prices' collection: {price}")
                return price
            except (ValueError, TypeError):
                logger.warning(f"Invalid price format in 'prices' collection for productId {product_id_int}")
                return "0.00"
        else:
            logger.warning(f"No valid price found in 'prices' collection for productId {product_id_int}")
            return "0.00"

    except ValueError:
        logger.error(f"Could not convert productId {product_id} to int for prices collection lookup")
        return "0.00"
    except Exception as e:
        logger.error(f"Error querying 'prices' collection for productId {product_id}: {str(e)}")
        return "0.00"


def calculate_variant_price(variant, pricing_data, pricing_rules=None, min_price=0.50,
                          user_currency='USD', exchange_rate=1.0, price_preference_order=None,
                          game_minimum_prices=None, game_name=None, product_id=None,
                          catalog_collection=None, custom_stepping=None, is_test_user=False,
                          username=None, product=None, user_profile=None, valid_subtypes=None):
    """
    Calculate the price for a variant based on pricing data and rules.

    Args:
        variant: The variant data dictionary
        pricing_data: Pricing data from TCGPlayer or other source
        pricing_rules: Dictionary of pricing rules by condition
        min_price: Minimum price to use
        user_currency: User's preferred currency
        exchange_rate: Exchange rate from USD to user's currency
        price_preference_order: Order of price types to use (e.g., ['lowPrice', 'marketPrice'])
        game_minimum_prices: Dictionary of minimum prices by game
        game_name: Name of the game (e.g., 'Magic: The Gathering')
        product_id: Product ID for catalog lookup
        catalog_collection: MongoDB collection for catalog data
        custom_stepping: Custom stepping percentages by condition
        is_test_user: Whether this is a test user
        username: Username for logging
        product: Full product data
        user_profile: User profile data
        valid_subtypes: List of valid subtypes for this product

    Returns:
        Tuple of (calculated_price, is_missing)
    """
    # Use logger from module scope
    global logger

    # If no pricing data, return None and True for is_missing
    if not pricing_data:
        logger.warning("No pricing data provided for variant price calculation")
        return None, True

    # Set default price preference order if not provided
    if price_preference_order is None:
        price_preference_order = ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']

    # Use pricing_rules if provided, otherwise use custom_stepping
    condition_percentages = pricing_rules or custom_stepping or {
        'nm': 100, 'lp': 90, 'mp': 80, 'hp': 70, 'dm': 60
    }

    # Extract condition from variant title
    variant_title = variant.get('title', '').lower()
    condition = 'nm'  # Default to NM

    # Map of condition keywords to condition codes
    condition_map = {
        'near mint': 'nm',
        'nm': 'nm',
        'lightly played': 'lp',
        'lp': 'lp',
        'moderately played': 'mp',
        'mp': 'mp',
        'heavily played': 'hp',
        'hp': 'hp',
        'damaged': 'dm',
        'dm': 'dm'
    }

    # Determine condition from variant title
    for cond_key, cond_code in condition_map.items():
        if cond_key in variant_title:
            condition = cond_code
            break

    # Get condition percentage
    condition_percent = condition_percentages.get(condition, 100) / 100.0

    # Find the base price from pricing data using preference order
    base_price = None
    price_type_used = None

    for price_type in price_preference_order:
        if price_type in pricing_data and pricing_data[price_type] is not None:
            try:
                base_price = float(pricing_data[price_type])
                price_type_used = price_type
                break
            except (ValueError, TypeError):
                continue

    # If no valid price found, return None and True for is_missing
    if base_price is None:
        logger.warning(f"No valid price found in pricing data for variant {variant.get('title')}")
        return None, True

    # Apply condition percentage to base price
    calculated_price = base_price * condition_percent

    # Apply exchange rate
    calculated_price = calculated_price * exchange_rate

    # Apply game-specific minimum price if available
    if game_minimum_prices and game_name and game_name in game_minimum_prices:
        game_min_price = float(game_minimum_prices.get(game_name, min_price))
        calculated_price = max(calculated_price, game_min_price)
    else:
        # Apply global minimum price
        calculated_price = max(calculated_price, min_price)

    # Round to 2 decimal places
    calculated_price = round(calculated_price, 2)

    # Log the calculation details
    logger.info(f"Calculated price for {variant.get('title')}: ${calculated_price:.2f}")
    logger.info(f"  Base price (${base_price:.2f} from {price_type_used}) * {condition_percent:.2f} condition multiplier * {exchange_rate:.2f} exchange rate")

    return calculated_price, False

