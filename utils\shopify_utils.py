from config.config import Config
import time
import requests
import logging
import json
from typing import Dict, Any, Optional, List, Union
from functools import wraps
from datetime import datetime
from requests.exceptions import RequestException, HTTPError, ConnectionError, Timeout
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def normalize_condition(condition: str) -> List[str]:
    """
    Normalize condition names and return both full name and abbreviation
    Returns a list of possible condition strings to match against
    """
    condition_map = {
        "NM": ["Near Mint", "NM"],
        "EX": ["Near Mint", "NM", "EX"],  # Map EX to Near Mint
        "GD": ["Good", "GD"],
        "LP": ["Lightly Played", "LP"],
        "PL": ["Played", "PL"],
        "PO": ["Poor", "PO"],
        "MP": ["Moderately Played", "MP"],
        "HP": ["Heavily Played", "HP"],
        "DM": ["Damaged", "DM"]
    }

    # If it's a full name, find its abbreviation
    reverse_map = {
        "Near Mint": ["Near Mint", "NM"],
        "Good": ["Good", "GD"],
        "Lightly Played": ["Lightly Played", "LP"],
        "Played": ["Played", "PL"],
        "Poor": ["Poor", "PO"],
        "Moderately Played": ["Moderately Played", "MP"],
        "Heavily Played": ["Heavily Played", "HP"],
        "Damaged": ["Damaged", "DM"]
    }

    # Return all possible variations of the condition
    if condition in condition_map:
        return condition_map[condition]
    elif condition in reverse_map:
        return reverse_map[condition]
    return [condition]  # Return original if no mapping found

def check_foil_match(variant_title: str, is_foil: bool) -> bool:
    """Check if variant title matches foil status"""
    foil_keywords = ['foil', 'holofoil', 'holo foil']
    has_foil = any(keyword in variant_title.lower() for keyword in foil_keywords)
    return has_foil == is_foil

class ShopifyAPIError(Exception):
    """Base exception for Shopify API errors"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_body: Optional[str] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_body = response_body
        logger.error(f"ShopifyAPIError: {message} (Status: {status_code}, Body: {response_body})")

class ShopifyRateLimitError(ShopifyAPIError):
    """Exception for rate limit errors"""
    pass

class ShopifyAuthenticationError(ShopifyAPIError):
    """Exception for authentication errors"""
    pass

class ShopifyResourceNotFoundError(ShopifyAPIError):
    """Exception for 404 errors"""
    pass

class ShopifyRateLimiter:
    """Rate limiter for Shopify API calls with bucket implementation"""

    def __init__(self, calls_per_second: int = 2):
        self.calls_per_second = calls_per_second
        self.min_time_between_calls = 1.0 / calls_per_second
        self.last_call_time = 0
        self.bucket = []
        self.bucket_size = calls_per_second * 2  # Allow burst up to 2 seconds worth
        logger.info(f"Initialized rate limiter: {calls_per_second} calls/second")

    def wait_if_needed(self) -> None:
        """
        Wait if necessary to respect rate limit using token bucket algorithm
        """
        current_time = time.time()

        # Remove old timestamps from bucket
        self.bucket = [ts for ts in self.bucket if current_time - ts < 1.0]

        # If bucket is full, wait
        if len(self.bucket) >= self.bucket_size:
            wait_time = self.bucket[0] + 1.0 - current_time
            if wait_time > 0:
                logger.debug(f"Rate limit: waiting {wait_time:.2f} seconds")
                time.sleep(wait_time)
                current_time = time.time()

        # Add current timestamp to bucket
        self.bucket.append(current_time)
        self.last_call_time = current_time

class ShopifyAPI:
    """
    Shopify REST API client with rate limiting and error handling
    """

    def __init__(self, store_name: str, access_token: str, api_version: str = "2023-04"):
        """
        Initialize Shopify API client

        Args:
            store_name: Shopify store name (without .myshopify.com)
            access_token: Shopify admin API access token
            api_version: Shopify API version to use
        """
        self.store_name = store_name
        self.access_token = access_token
        self.api_version = api_version
        self.base_url = f"https://{store_name}.myshopify.com/admin/api/{api_version}"
        self.headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        self.rate_limiter = ShopifyRateLimiter()
        logger.info(f"Initialized Shopify API client for store: {store_name}")

    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Make a rate-limited request to Shopify API with error handling and retries

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without base URL)
            data: Request body data
            params: Query parameters

        Returns:
            API response as dictionary

        Raises:
            ShopifyAPIError: Base class for all Shopify API errors
            ShopifyRateLimitError: When rate limit is exceeded
            ShopifyAuthenticationError: When authentication fails
            ShopifyResourceNotFoundError: When resource is not found
        """
        url = f"{self.base_url}/{endpoint}"
        self.rate_limiter.wait_if_needed()

        try:
            response = requests.request(
                method=method,
                url=url,
                headers=self.headers,
                json=data if data else None,
                params=params if params else None,
                timeout=30
            )

            # Log rate limit headers
            calls_left = response.headers.get('X-Shopify-Shop-Api-Call-Limit', '').split('/')
            if calls_left and len(calls_left) == 2:
                logger.debug(f"API calls remaining: {calls_left[0]}/{calls_left[1]}")

            # Handle different status codes
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 10))
                raise ShopifyRateLimitError(
                    f"Rate limit exceeded. Retry after {retry_after} seconds",
                    status_code=429,
                    response_body=response.text
                )

            if response.status_code == 401:
                raise ShopifyAuthenticationError(
                    "Authentication failed. Check your access token.",
                    status_code=401,
                    response_body=response.text
                )

            if response.status_code == 404:
                raise ShopifyResourceNotFoundError(
                    f"Resource not found: {endpoint}",
                    status_code=404,
                    response_body=response.text
                )

            response.raise_for_status()
            return response.json()

        except (ConnectionError, Timeout) as e:
            logger.error(f"Network error during API request: {str(e)}")
            raise ShopifyAPIError(f"Network error: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in API response: {str(e)}")
            raise ShopifyAPIError(f"Invalid JSON response: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during API request: {str(e)}")
            raise

    @retry(
        retry=retry_if_exception_type((ConnectionError, Timeout, ShopifyRateLimitError)),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def get_variant(self, variant_id: Union[int, str]) -> Dict[str, Any]:
        """
        Get a variant by ID

        Args:
            variant_id: Shopify variant ID

        Returns:
            Variant data as dictionary
        """
        logger.info(f"Getting variant {variant_id}")
        return self._make_request('GET', f'variants/{variant_id}.json')

    @retry(
        retry=retry_if_exception_type((ConnectionError, Timeout, ShopifyRateLimitError)),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def update_variant_inventory(
        self,
        variant_id: Union[int, str],
        inventory_quantity: int,
        price: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Update a variant's inventory quantity and optionally price

        Args:
            variant_id: Shopify variant ID
            inventory_quantity: New inventory quantity
            price: Optional new price

        Returns:
            Updated variant data
        """
        logger.info(f"Updating variant {variant_id} inventory to {inventory_quantity}")
        data = {
            "variant": {
                "id": variant_id,
                "inventory_quantity": inventory_quantity
            }
        }

        if price is not None:
            data["variant"]["price"] = str(price)

        return self._make_request('PUT', f'variants/{variant_id}.json', data=data)

def find_matching_variant(
    variants: List[Dict[str, Any]],
    condition: str,
    is_foil: bool,
    is_reverse_holo: bool,
    printing_name: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Find matching variant based on condition and printing type

    Args:
        variants: List of variant dictionaries
        condition: Card condition (e.g., NM, LP, etc.)
        is_foil: Whether the card is foil
        is_reverse_holo: Whether the card is reverse holo
        printing_name: Optional specific printing name to match

    Returns:
        Matching variant dictionary or None if no match found
    """
    # Get all possible condition strings to match against
    condition_variations = normalize_condition(condition)
    logger.debug(f"Finding variant match: conditions={condition_variations}, foil={is_foil}, reverse_holo={is_reverse_holo}")

    for variant in variants:
        variant_title = variant.get('title', '').lower()

        # Skip if no title
        if not variant_title:
            continue

        # Check condition match (any variation)
        if not any(cond.lower() in variant_title for cond in condition_variations):
            continue

        # Check foil status
        if not check_foil_match(variant_title, is_foil):
            continue

        # Check reverse holo status
        if is_reverse_holo and 'reverse holo' not in variant_title:
            continue
        if not is_reverse_holo and 'reverse holo' in variant_title:
            continue

        # Check specific printing if provided
        if printing_name and printing_name.lower() not in variant_title:
            continue

        logger.info(f"Found matching variant: {variant.get('title')} (ID: {variant.get('id')})")
        return variant

    logger.warning(f"No matching variant found for conditions={condition_variations}, foil={is_foil}, reverse_holo={is_reverse_holo}")
    return None

def get_variant_match_score(variant: Dict[str, Any], cardmarket_data: Dict[str, Any]) -> float:
    """
    Calculate a match score between a Shopify variant and Cardmarket data

    Args:
        variant: Shopify variant dictionary
        cardmarket_data: Cardmarket product data

    Returns:
        Match score between 0 and 1
    """
    score = 0.0
    variant_title = variant.get('title', '').lower()

    # Check condition match (highest weight)
    condition_variations = normalize_condition(cardmarket_data.get('condition', ''))
    if any(cond.lower() in variant_title for cond in condition_variations):
        score += 0.4

    # Check foil status
    is_foil = cardmarket_data.get('isFoil', False)
    if check_foil_match(variant_title, is_foil):
        score += 0.3

    # Check reverse holo status
    is_reverse_holo = cardmarket_data.get('isReverseHolo', False)
    if is_reverse_holo and 'reverse holo' in variant_title:
        score += 0.3
    elif not is_reverse_holo and 'reverse holo' not in variant_title:
        score += 0.3

    # Check printing name if available
    printing_name = cardmarket_data.get('printingName', '').lower()
    if printing_name and printing_name in variant_title:
        score += 0.2

    logger.debug(f"Variant match score: {score} for {variant_title}")
    return min(score, 1.0)  # Cap at 1.0

def generate_json_files(username: str, catalog_products: List[Dict], selected_conditions: List[str], current_user: Any) -> Dict[str, Any]:
    """
    Generate JSON files for Shopify product import

    Args:
        username: Username of the current user
        catalog_products: List of catalog products to process
        selected_conditions: List of selected conditions to include
        current_user: Current user object with title format settings

    Returns:
        Dictionary with message and status
    """
    root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    output_dir = os.path.join(root_dir, "shopify_json_files", username)
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"Created output directory: {output_dir}")

    def get_condition_order(condition):
        order = {
            "Near Mint": 1,
            "Lightly Played": 2,
            "Moderately Played": 3,
            "Heavily Played": 4,
            "Damaged": 5
        }
        return order.get(condition, 6)

    def construct_title(doc, title_format):
        # Use debug level instead of info to reduce log volume
        logger.debug(f"Starting title construction for product {doc.get('productId')}")

        raw_name = doc.get('name', 'No Title')
        raw_number = str(doc.get('number', ''))
        raw_abbreviation = doc.get('abbreviation')

        components = {
            'name': raw_name,
            'number': f"({raw_number})" if raw_number else None,
            'abbreviation': f"({raw_abbreviation})" if raw_abbreviation else None
        }

        title_parts = []
        for part in title_format.get('order', ['name', 'number', 'abbreviation']):
            include_key = f'include{part.capitalize()}'
            should_include = title_format.get(include_key, True)
            has_value = components.get(part) is not None

            if should_include and has_value:
                title_parts.append(components[part])

        return ' '.join(title_parts)

    products_by_expansion = {}
    title_format = current_user.get_title_format()

    for doc in catalog_products:
        expansion_name = doc.get("expansionName", "Unknown Expansion")
        if expansion_name not in products_by_expansion:
            products_by_expansion[expansion_name] = []

        def clean_special_chars(text):
            """Remove special characters from text"""
            if not text:
                return ''
            import re
            # Keep only alphanumeric characters, spaces, and basic punctuation
            return re.sub(r'[^\w\s\-]', '', str(text)).strip()
        
        # Simplified tags - only the 5 required fields
        game_name = clean_special_chars(doc.get('gameName', ''))
        expansion_name = clean_special_chars(doc.get('expansionName', ''))
        number = clean_special_chars(doc.get('number', ''))
        abbreviation = clean_special_chars(doc.get('abbreviation', ''))
        product_id = clean_special_chars(str(doc.get('productId', '')))
        
        # Build simplified tags list
        tags = []
        if game_name:
            tags.append(game_name)
        if expansion_name:
            tags.append(expansion_name)
        if number:
            tags.append(number)
        if abbreviation:
            tags.append(abbreviation)
        if product_id:
            tags.append(product_id)
        
        # Remove empty tags and join
        tags_string = ', '.join(filter(None, tags))

        extended_data_list = doc.get("extendedData", [])
        body_html_parts = []
        for data in extended_data_list:
            display_name = data.get('displayName', '')
            value = data.get('value', '')
            body_html_parts.append(f"<li><strong>{display_name}:</strong> {value}</li>")
        body_html = "<ul>" + "".join(body_html_parts) + "</ul>"

        if doc.get("description"):
            body_html += f"<p>{doc['description']}</p>"

        # Add hidden card details
        product_id = doc.get("productId", "N/A")
        card_type = "mtg" if doc.get('gameName') == "Magic: The Gathering" else "other"
        body_html += f'''
        <div class="catalogMetaData" style="display:none;" data-cardtype="{card_type}" data-cardid="5" data-tcgid="{product_id}" data-lastupdated="{datetime.now().isoformat()}">
        </div>
        '''

        # Construct title using user's format settings
        title = construct_title(doc, title_format)

        variants = []
        for sku in doc.get("skus", []):
            if isinstance(sku, dict) and sku.get("langAbbr") == "EN":
                condition = sku.get('condName', '')

                # Special handling for user Dezmu - filter conditions first
                if username == "Dezmu":
                    if condition not in ["Near Mint", "Lightly Played"]:
                        continue
                # Special handling for user Xavier
                elif username == "Xavier":
                    if condition != "Near Mint":
                        continue

                # After user-specific filtering, check if condition is in selected conditions
                if condition not in selected_conditions:
                    continue

                printing = sku.get('printingName', '')
                # Special handling for Xavier's variant title
                if username == "Xavier":
                    variant_title = f"{printing} - LP - EN"
                else:
                    variant_title = f"{printing} - {condition} - EN"

                low_price = sku.get("lowPrice")
                if low_price is None:
                    logger.warning(f"Low price is None for product {doc.get('productId')} - {doc.get('name')}. Setting price to 0.00.")
                    price = "0.00"
                else:
                    try:
                        price = "{:.2f}".format(float(low_price))
                    except (ValueError, TypeError) as e:
                        logger.error(f"Error processing price for product {doc.get('productId')} - {doc.get('name')}: {str(e)}")
                        price = "0.00"  # Set a default price
                variants.append({
                    "title": variant_title,
                    "price": price,
                    "sku": str(sku.get("skuId", "")),
                    "barcode": str(sku.get("skuId", "")),
                    "weight": 0.3,
                    "weightUnit": "GRAMS",
                    "options": [variant_title],
                    "requiresShipping": True,
                    "inventoryManagement": "SHOPIFY",
                    "inventoryPolicy": "DENY",
                    "condition_order": get_condition_order(condition)
                })

        # Skip products with no variants after filtering
        if not variants:
            continue

        # Sort variants by printing (Normal first, then Foil) and then by condition order
        variants.sort(key=lambda x: (
            0 if x['title'].startswith("Normal") else 1,
            x['condition_order']
        ))

        # Remove the temporary 'condition_order' key
        for variant in variants:
            del variant['condition_order']

        product_status = "DRAFT" if any(variant["price"] == "0.00" for variant in variants) else "ACTIVE"

        if doc.get('gameName') == "Magic: The Gathering":
            product_type = "MTG Single" if doc.get('isSingle') else "MTG Sealed"
        else:
            product_type = f"{doc.get('gameName', 'Unknown')} {'Single' if doc.get('isSingle') else 'Sealed'}"

        # Create empty metafields list (removed all metafields as requested)
        metafields = []

        product_data = {
            "input": {
                "title": title,
                "published": True,
                "status": "ACTIVE",
                "publishedAt": datetime.now().strftime("%Y-%m-%d"),
                "tags": tags_string,
                "bodyHtml": body_html,
                "vendor": doc.get("gameName", "Unknown Vendor"),
                "productType": product_type,
                "variants": variants,
                "options": ["Title"],
                "metafields": metafields
            },
            "media": [
                {
                    "originalSource": doc.get("image", "No Image"),
                    "alt": f"Image for {title}",
                    "mediaContentType": "IMAGE"
                }
            ]
        }
        products_by_expansion[expansion_name].append(product_data)

    # Log only the total count of expansions, not each individual expansion
    expansion_count = len(products_by_expansion)
    total_products = sum(len(products) for products in products_by_expansion.values())
    logger.info(f"Processing {total_products} products across {expansion_count} expansions")

    # Save files without individual logging
    for expansion_name, products in products_by_expansion.items():
        save_json_file(products, output_dir, expansion_name)

    logger.info(f"All {expansion_count} expansions processed and JSON files saved.")
    return {"message": f"JSON files generated successfully. Check the directory: {output_dir}", "type": "success"}

def save_json_file(products: List[Dict], output_dir: str, expansion_name: str) -> None:
    """
    Save products to a JSON file

    Args:
        products: List of product dictionaries to save
        output_dir: Directory to save the file in
        expansion_name: Name of the expansion for the filename
    """
    safe_expansion_name = expansion_name.replace(" ", "_").replace("/", "_")
    base_filename = f"{safe_expansion_name}_products.json"
    file_path = os.path.join(output_dir, base_filename)

    file_counter = 0
    while os.path.exists(file_path):
        file_counter += 1
        base_filename = f"{safe_expansion_name}_products_{file_counter}.json"
        file_path = os.path.join(output_dir, base_filename)

    # No logging here - we log the summary in the calling function
    with open(file_path, 'w') as f:
        json.dump(products, f, indent=2)
