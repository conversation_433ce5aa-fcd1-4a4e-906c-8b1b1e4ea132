import sys
import logging
import time
import schedule
import signal
import re
import requests
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from pymongo import MongoClient, UpdateOne
from requests.adapters import HTTPAdapter 
from requests.packages.urllib3.util.retry import Retry
from datetime import datetime, timedelta, timezone
from tqdm import tqdm
from itertools import islice
from threading import Lock

# Configure minimal logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configure logging to show all messages
logger.setLevel(logging.INFO)

# Constants
EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"
BATCH_SIZE = 500
DEFAULT_MIN_PRICE_USD = 0.2
MONGO_POOL_SIZE = 200

# Enhanced session configuration
session = requests.Session()
retries = Retry(
    total=5,
    backoff_factor=0.5,
    status_forcelist=[500, 502, 503, 504],
    allowed_methods=frozenset(['GET', 'POST'])  # Added POST for TCGPlayer API
)
adapter = HTTPAdapter(
    max_retries=retries,
    pool_connections=MONGO_POOL_SIZE,
    pool_maxsize=MONGO_POOL_SIZE,
    pool_block=False
)
session.mount('https://', adapter)
session.mount('http://', adapter)

# MongoDB Configuration with connection pooling and replica set
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_client = MongoClient(
    mongo_uri,
    maxPoolSize=MONGO_POOL_SIZE,
    waitQueueTimeoutMS=5000,
    connectTimeoutMS=5000,
    serverSelectionTimeoutMS=5000,
    retryWrites=True,
    w='majority'
)
db = mongo_client['test']
shopify_collection = db['shProducts']
user_collection = db['user']
tcgplayer_key_collection = db['tcgplayerKey']
autopricer_collection = db['autopricerShopify']
price_cache_collection = db['tcgplayer_price_cache']
skus_collection = db['skus']  # Collection for mapping variants to TCGPlayer SKU IDs
catalog_collection = db['catalog']  # Collection for catalog data with SKU information

# Create indexes
shopify_collection.create_index([("username", 1), ("product_type", 1)])

# Currency cache implementation
class CurrencyCache:
    def __init__(self, ttl_hours=24):
        self.cache = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.lock = Lock()
        self.last_cleanup = datetime.now(timezone.utc)

    def get(self, currency):
        with self.lock:
            if currency in self.cache:
                rate_data = self.cache[currency]
                if datetime.now(timezone.utc) - rate_data['timestamp'] < self.ttl:
                    return rate_data['rate']
                else:
                    del self.cache[currency]
            return None

    def set(self, currency, rate):
        with self.lock:
            self.cache[currency] = {
                'rate': rate,
                'timestamp': datetime.now(timezone.utc)
            }

    def clear_expired(self):
        with self.lock:
            current_time = datetime.now(timezone.utc)
            expired = [currency for currency, data in self.cache.items() 
                      if current_time - data['timestamp'] >= self.ttl]
            for currency in expired:
                del self.cache[currency]
            self.last_cleanup = current_time

# Initialize cache
currency_cache = CurrencyCache()

def get_exchange_rate(target_currency):
    if target_currency == 'USD':
        return 1.0
    
    cached_rate = currency_cache.get(target_currency)
    if cached_rate is not None:
        return cached_rate
    
    try:
        url = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
        response = session.get(url, timeout=10)
        response.raise_for_status()
        rates = response.json().get('conversion_rates', {})
        rate = rates.get(target_currency)
        
        if rate is None:
            logger.error(f"Exchange rate not found for {target_currency}, using 1.0")
            return 1.0
            
        currency_cache.set(target_currency, rate)
        return rate
    except Exception as e:
        logger.error(f"Error fetching exchange rate for {target_currency}: {str(e)}")
        return 1.0

def apply_fallback_pricing(price_data):
    """
    Apply fallback pricing logic for missing price points:
    - If a price is missing, use 90% of the higher tier price
    - No price hierarchy enforcement (as per user request)
    """
    if not price_data:
        return price_data
        
    # Define the price hierarchy (from highest to lowest) - only used for fallback logic
    hierarchy = ['highPrice', 'midPrice', 'marketPrice', 'lowPrice']
    
    # Create a copy of the price data to modify
    adjusted_prices = price_data.copy()
    
    # Summary of rules applied
    rules_applied = []
    
    # Apply fallback logic for missing prices (90% of higher variant)
    for i in range(len(hierarchy) - 1):
        higher_price_type = hierarchy[i]
        lower_price_type = hierarchy[i + 1]
        
        higher_price = adjusted_prices.get(higher_price_type)
        lower_price = adjusted_prices.get(lower_price_type)
        
        # If higher price exists and lower price is missing or zero
        if higher_price and (lower_price is None or lower_price == 0):
            # Set lower price to 90% of higher price
            adjusted_prices[lower_price_type] = round(higher_price * 0.9, 2)
            rule = f"RULE: {lower_price_type} missing - set to 90% of {higher_price_type}: ${higher_price:.2f} -> ${adjusted_prices[lower_price_type]:.2f}"
            rules_applied.append(rule)
            logger.info(rule)
    
    # Print summary of all rules applied
    if rules_applied:
        logger.info("\n=== Fallback Pricing Rules Applied ===")
        for rule in rules_applied:
            logger.info(rule)
        logger.info("=====================================")
    
    return adjusted_prices

def get_tcgplayer_price(sku_ids, tcgplayer_api_key):
    if not sku_ids:
        return {}

    try:
        logger.info(f"Getting prices for SKUs: {sku_ids}")
        # Skip cache check, always use live prices
        now = datetime.now(timezone.utc)
        
        # Call TCGPlayer API for all prices
        new_prices = {}
        for sku_id in sku_ids:
            url = f"https://api.tcgplayer.com/pricing/sku/{sku_id}"
            headers = {
                "Accept": "application/json",
                "Authorization": f"Bearer {tcgplayer_api_key}"
            }

            logger.info(f"Making API request to: {url}")
            response = session.get(url, headers=headers)
            logger.info(f"Response Status Code: {response.status_code}")
            logger.info(f"Response Headers: {dict(response.headers)}")
            response.raise_for_status()
            price_data = response.json()
            logger.info(f"Raw API Response Text: {response.text}")
            logger.info(f"Parsed API Response: {price_data}")

            if price_data.get('success') and price_data.get('results'):
                result = price_data['results'][0]  # Get first result
                if result:
                    price_info = {
                        "price": result.get('lowestListingPrice') or 0,
                        "marketPrice": result.get('marketPrice') or 0,
                        "lowPrice": result.get('lowPrice') or 0,  # Using lowPrice directly
                        "midPrice": result.get('marketPrice') or 0,  # Using marketPrice as midPrice
                        "highPrice": result.get('highPrice') or 0,  # Using highPrice directly
                        "directLowPrice": result.get('directLowPrice') or 0  # Keeping directLowPrice as a separate price point
                    }
                    logger.info(f"Got live price data for SKU {sku_id}:")
                    for price_type, value in price_info.items():
                        logger.info(f"  - {price_type}: ${value:.2f}")
                    new_prices[str(sku_id)] = price_info

        logger.info(f"Total prices found: {len(new_prices)}")
        return new_prices

    except Exception as e:
        logger.error(f"Error fetching TCGPlayer prices: {str(e)}")
        return {}

def calculate_price(variant, price_data, user_profile, min_price_local, exchange_rate, user_currency):
    if not price_data:
        return None, True

    try:
        logger.info("\n=== Price Calculation Start ===")
        logger.info(f"Calculating price for variant: {variant.get('title')}")
        logger.info("\nRaw price data:")
        for price_type, value in price_data.items():
            logger.info(f"  - {price_type}: ${value:.2f}")
            
        # Apply fallback pricing for missing price points
        price_data = apply_fallback_pricing(price_data)
        logger.info("\nPrice data after fallback logic:")
        for price_type, value in price_data.items():
            logger.info(f"  - {price_type}: ${value:.2f}")

        # Get price comparison settings from user profile
        use_highest_price = user_profile.get('use_highest_price', False)
        price_comparison_pairs = user_profile.get('price_comparison_pairs', [])
        price_modifiers = user_profile.get('price_modifiers', {})
        price_preferences = user_profile.get('price_preference_order', 
            ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])
        
        logger.info("\nUser settings:")
        logger.info(f"  - Use highest price: {use_highest_price}")
        logger.info(f"  - Price comparison pairs: {price_comparison_pairs}")
        logger.info(f"  - Price modifiers: {price_modifiers}")
        logger.info(f"  - Price preferences: {price_preferences}")
        
        # Try to get price using comparison pairs first
        base_price_usd = None
        price_type = None
        
        if price_comparison_pairs:
            logger.info("\nChecking price comparison pairs:")
            # Check each price pair
            for pair in price_comparison_pairs:
                if len(pair) != 2:
                    continue
                    
                price1_type, price2_type = pair
                price1 = price_data.get(price1_type)
                price2 = price_data.get(price2_type)
                
                logger.info(f"\nComparing pair: {price1_type} vs {price2_type}")
                logger.info(f"  - {price1_type}: ${price1:.2f}")
                logger.info(f"  - {price2_type}: ${price2:.2f}")
                
                # Skip if either price is missing
                if price1 is None or price2 is None:
                    logger.info("  Skipping pair - missing price")
                    continue
                    
                # Apply modifiers if they exist
                if price1_type in price_modifiers:
                    modifier = price_modifiers[price1_type]
                    original_price1 = price1
                    price1 = price1 * (1 + modifier/100)
                    logger.info(f"  Applied {modifier}% modifier to {price1_type}: ${original_price1:.2f} -> ${price1:.2f}")
                    
                if price2_type in price_modifiers:
                    modifier = price_modifiers[price2_type]
                    original_price2 = price2
                    price2 = price2 * (1 + modifier/100)
                    logger.info(f"  Applied {modifier}% modifier to {price2_type}: ${original_price2:.2f} -> ${price2:.2f}")
                    
                if use_highest_price:
                    # Use highest price from the pair
                    if price1 >= price2:
                        base_price_usd = price1
                        price_type = price1_type
                        logger.info(f"  Selected highest price: {price_type} = ${base_price_usd:.2f}")
                    else:
                        base_price_usd = price2
                        price_type = price2_type
                        logger.info(f"  Selected highest price: {price_type} = ${base_price_usd:.2f}")
                else:
                    # Use first valid price from the pair
                    if price1 is not None:
                        base_price_usd = price1
                        price_type = price1_type
                        logger.info(f"  Selected first valid price: {price_type} = ${base_price_usd:.2f}")
                    elif price2 is not None:
                        base_price_usd = price2
                        price_type = price2_type
                        logger.info(f"  Selected first valid price: {price_type} = ${base_price_usd:.2f}")
                
                # If we found a valid price, break out of the loop
                if base_price_usd is not None:
                    break
        
        # Fall back to price preferences if no valid price from pairs
        if base_price_usd is None:
            logger.info("\nNo price from pairs, checking price preferences:")
            for price_type in price_preferences:
                if price_type in price_data and price_data[price_type] is not None:
                    base_price_usd = price_data[price_type]
                    logger.info(f"  Selected {price_type} price: ${base_price_usd:.2f}")
                    # Apply modifier if it exists
                    if price_type in price_modifiers:
                        modifier = price_modifiers[price_type]
                        original_price = base_price_usd
                        base_price_usd = base_price_usd * (1 + modifier/100)
                        logger.info(f"  Applied {modifier}% modifier: ${original_price:.2f} -> ${base_price_usd:.2f}")
                    break

        if base_price_usd is None:
            logger.info("No valid price found")
            return None, True

        # Convert to user's currency
        price = round(base_price_usd * exchange_rate, 2)
        logger.info(f"\nConverted to user currency (rate {exchange_rate}): ${base_price_usd:.2f} USD -> ${price:.2f} {user_currency}")

        # Initialize minimum price in user's currency
        min_price = min_price_local
        logger.info(f"\nChecking minimum prices:")
        logger.info(f"  Base minimum price: ${min_price:.2f}")

        # Check game-specific minimums if available
        if 'game_minimum_prices' in user_profile:
            product_type = variant.get('product_type', '')
            game_name = next((game for game in user_profile['game_minimum_prices'].keys() 
                           if product_type.startswith(game)), None)
            if game_name:
                game_settings = user_profile['game_minimum_prices'][game_name]
                logger.info(f"\nFound game settings for {game_name}:")
                logger.info(f"  Settings: {game_settings}")
                
                # Check game default minimum
                game_default_min = game_settings.get('default_min_price')
                if game_default_min is not None:
                    old_min = min_price
                    min_price = max(min_price, game_default_min)
                    logger.info(f"  Applied game default minimum: ${old_min:.2f} -> ${min_price:.2f}")
                    
                # Check rarity minimum
                rarity = variant.get('rarity')
                if rarity:
                    rarity_min = game_settings.get('rarities', {}).get(rarity)
                    if rarity_min is not None:
                        old_min = min_price
                        min_price = max(min_price, rarity_min)
                        logger.info(f"  Applied {rarity} rarity minimum: ${old_min:.2f} -> ${min_price:.2f}")

        # Apply minimum price
        old_price = price
        price = round(max(price, min_price), 2)
        if price > old_price:
            logger.info(f"\nApplied minimum price: ${old_price:.2f} -> ${price:.2f}")

        logger.info(f"\nFinal price: ${price:.2f} {user_currency}")
        logger.info("=== Price Calculation End ===\n")

        return price, False
    except Exception as e:
        logger.error(f"Error calculating price: {str(e)}")
        return None, True

def process_user(config, tcgplayer_api_key):
    try:
        username = config['username']
        selected_product_types = [pt.lower() for pt in config['selectedProductTypes']]
        
        user_profile = user_collection.find_one({'username': username})
        if not user_profile:
            logger.error(f"User profile not found for {username}")
            return

        # Log user profile for debugging
        logger.info(f"User profile: {user_profile}")
        
        # Skip if user hasn't opted into SKU pricing
        if not user_profile.get('use_skuid_pricing', False):
            logger.info(f"Skipping user {username} - SKU pricing not enabled")
            return
            
        logger.info(f"\nProcessing user {username}:")
        logger.info(f"  Currency: {user_profile.get('currency', 'USD')}")
        logger.info(f"  Min price: ${user_profile.get('minPrice', DEFAULT_MIN_PRICE_USD):.2f}")
        logger.info(f"  Selected product types: {selected_product_types}")
        
        user_currency = user_profile.get('currency', 'USD')
        min_price_local = user_profile.get('minPrice', DEFAULT_MIN_PRICE_USD)
        exchange_rate = get_exchange_rate(user_currency)

        # Only process products with types that the user has selected
        if not selected_product_types:
            logger.info(f"Skipping user {username} - no product types selected")
            return
            
        # Only process products that haven't been repriced in the last 6 hours
        six_hours_ago = datetime.now(timezone.utc) - timedelta(hours=6)
        query = {
            'username': username,
            'product_type': {
                '$regex': f'^({"|".join(map(re.escape, selected_product_types))})$',
                '$options': 'i'
            },
            'skusMatched': True,
            '$or': [
                {'last_repriced': {'$lt': six_hours_ago}},
                {'last_repriced': {'$exists': False}}
            ]
        }
        
        cursor = shopify_collection.find(query, batch_size=BATCH_SIZE)
        total_products = shopify_collection.count_documents(query)
        
        if not total_products:
            return
        
        bulk_updates = []
        total_updates = 0
        total_price_changes = 0
        
        with tqdm(total=total_products, desc=f"Processing {username}", leave=True) as pbar:
            while True:
                batch = list(islice(cursor, BATCH_SIZE))
                if not batch:
                    break

                # Process each product in the batch
                for product in batch:
                    # Get variants and verify all have skuId
                    variants = product.get('variants', [])
                    if not all('skuId' in variant for variant in variants):
                        logger.info(f"Skipping product {product.get('title')} - one or more variants missing skuId despite skusMatched=true")
                        try:
                            product_id = product['_id']
                            if isinstance(product_id, str):
                                from bson import ObjectId
                                product_id = ObjectId(product_id)
                            
                            # Update skusMatched to false since not all variants have skuId
                            result = shopify_collection.update_one(
                                {'_id': product_id},
                                {'$set': {'skusMatched': False}}
                            )
                            if result.modified_count > 0:
                                logger.info(f"Updated skusMatched to false for product {product.get('title')}")
                        except Exception as e:
                            logger.error(f"Error updating skusMatched status: {str(e)}")
                        continue

                    # Collect SKU IDs for all variants in this product
                    sku_ids = [variant['skuId'] for variant in variants]
                    logger.info(f"Processing product {product.get('title')} with {len(variants)} variants")
                    logger.info("SKU IDs for variants:")
                    for variant in variants:
                        logger.info(f"  - {variant.get('title')}: SKU ID {variant['skuId']}")

                    # Get prices for all SKUs in this product at once
                    logger.info(f"Processing product {product.get('title')} with SKUs: {sku_ids}")
                    sku_prices = get_tcgplayer_price(sku_ids, tcgplayer_api_key)
                    try:
                        variants_changed = False
                        variants = product.get('variants', [])
                        logger.info(f"Processing product: {product.get('title')} with {len(variants)} variants")
                            
                        # Process each variant
                        for variant in variants:
                            old_price = float(variant.get('price', '0.00'))
                                
                            # Get skuId safely with a default value if not present
                            sku_id = variant.get('skuId', 'Not assigned')
                            logger.info(f"\nProcessing variant: {variant.get('title')}")
                            logger.info(f"SKU ID: {sku_id}")
                            logger.info(f"Current price: ${old_price:.2f} {user_currency}")
                            
                            # Set price to 9999 if missing or zero
                            if not variant.get('price') or old_price == 0:
                                new_price = 9999.00
                                is_missing = False
                                logger.info(f"Set default price 9999.00")
                            else:
                                # Get skuId and price data
                                sku_id = str(variant['skuId'])
                                logger.info(f"SKU: {sku_id}")
                                price_data = sku_prices.get(sku_id, {})
                                
                                # Print variant details
                                logger.info("\n=== VARIANT DETAILS ===")
                                logger.info(f"Product: {product.get('title')}")
                                logger.info(f"Variant: {variant.get('title')}")
                                logger.info(f"SKU ID: {sku_id}")
                                logger.info(f"Current Price: ${old_price:.2f} {user_currency}")
                                logger.info("=====================")
                                
                                # Check if all price points are zero
                                all_prices_zero = True
                                if price_data:
                                    for price_type, value in price_data.items():
                                        if value > 0:
                                            all_prices_zero = False
                                            break
                                
                                # Get condition from variant title
                                condition = variant.get('title', '').split()[0].lower()
                                condition_code = {
                                    'near': 'nm',
                                    'lightly': 'lp',
                                    'moderately': 'mp',
                                    'heavily': 'hp',
                                    'damaged': 'dm'
                                }.get(condition, 'nm')

                                # Find the variant above this one
                                variant_index = variants.index(variant)
                                if variant_index > 0:
                                    prev_variant = variants[variant_index - 1]
                                    prev_price = float(prev_variant.get('price', '0.00'))
                                    max_allowed_price = prev_price  # Maximum allowed is 100% of previous variant
                                    logger.info(f"Maximum allowed price based on previous variant: ${prev_price:.2f}")

                                if price_data and not all_prices_zero:
                                    # Use TCGPlayer data since it has valid prices
                                    logger.info("TCGPlayer data has valid prices, using it")
                                    new_price, is_missing = calculate_price(
                                        variant=variant,
                                        price_data=price_data,
                                        user_profile=user_profile,
                                        min_price_local=min_price_local,
                                        exchange_rate=exchange_rate,
                                        user_currency=user_currency
                                    )

                                    if not is_missing and new_price is not None:
                                        # If there's a previous variant, ensure we don't exceed max allowed price
                                        if variant_index > 0 and new_price > max_allowed_price:
                                            logger.info(f"Price ${new_price:.2f} exceeds maximum allowed price ${max_allowed_price:.2f}")
                                            new_price = max_allowed_price
                                            logger.info(f"Adjusted price to ${new_price:.2f}")
                                    else:
                                        new_price = min_price_local
                                else:
                                    # No valid price data, cascade from previous variant if available
                                    if variant_index > 0 and prev_price > 0:
                                        # When cascading, apply stepping percentage
                                        stepping_percentage = user_profile.get('customStepping', {}).get(condition_code, 100.0)
                                        new_price = round(prev_price * (stepping_percentage / 100.0), 2)
                                        logger.info(f"No price data, cascading from previous variant ${prev_price:.2f}")
                                        logger.info(f"Applied {condition_code.upper()} condition stepping: {stepping_percentage}% of ${prev_price:.2f} = ${new_price:.2f}")
                                    else:
                                        logger.info(f"No valid price data available, using minimum price: ${min_price_local:.2f}")
                                        new_price = min_price_local
                                    is_missing = False

                            if not is_missing and new_price is not None:
                                logger.info(f"\n=== PRICE CHANGE SUMMARY ===")
                                logger.info(f"Variant: {variant.get('title')}")
                                logger.info(f"Old Price: ${old_price:.2f} {user_currency}")
                                logger.info(f"New Price: ${new_price:.2f} {user_currency}")
                                logger.info(f"Difference: ${new_price - old_price:.2f} ({((new_price/old_price)-1)*100:.2f}%)")
                                
                                # Always update the price regardless of the difference
                                variant['price'] = f"{new_price:.2f}"
                                variants_changed = True
                                total_price_changes += 1
                                logger.info(f"STATUS: Price updated - always updating regardless of difference")
                                logger.info("===========================")
                            else:
                                logger.info(f"\n=== PRICE CALCULATION FAILED ===")
                                logger.info(f"Variant: {variant.get('title')}")
                                logger.info(f"STATUS: Unable to calculate new price - using current price: ${old_price:.2f} {user_currency}")
                                logger.info("===============================")

                        # Log final price calculation summary for this product
                        logger.info("\n")
                        logger.info("*" * 100)
                        logger.info("FINAL PRICE CALCULATION SUMMARY")
                        logger.info("*" * 100)
                        logger.info(f"\nProduct: {product.get('title')}")
                        logger.info(f"User: {username}")
                        logger.info(f"Currency: {user_currency}")
                        logger.info(f"Exchange Rate: {exchange_rate}")
                        
                        # Show TCGPlayer price fetch details
                        logger.info(f"\nTCGPlayer Prices (fetched at {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')})")
                        for variant in variants:
                            sku_id = variant['skuId']
                            price_data = sku_prices.get(str(sku_id), {})
                            if any(value > 0 for value in price_data.values()):
                                logger.info(f"\n  {variant.get('title')} (SKU {sku_id}):")
                                for price_type, value in price_data.items():
                                    logger.info(f"    - {price_type}: ${value:.2f}")
                            else:
                                logger.info(f"\n  {variant.get('title')} (SKU {sku_id}): No valid prices")
                        
                        logger.info("\nSKU IDs:")
                        for variant in variants:
                            logger.info(f"  {variant.get('title')}: {variant['skuId']}")
                        
                        for variant in variants:
                            logger.info("\n" + "-" * 80)
                            logger.info(f"VARIANT: {variant.get('title')} (SKU ID: {variant['skuId']})")
                            logger.info("-" * 80)
                            
                            sku_id = variant.get('skuId', 'Not assigned')
                            old_price = float(variant.get('price', '0.00'))
                            new_price = variant.get('price')
                            
                            # Get the final price data for this variant
                            price_data = sku_prices.get(sku_id, {})
                            has_prices = any(value > 0 for value in price_data.values()) if price_data else False

                            logger.info("\n1. STARTING POINT")
                            logger.info(f"   Current price: ${old_price:.2f} {user_currency}")
                            
                            logger.info("\n2. TCGPLAYER PRICES")
                            for price_type, value in price_data.items():
                                logger.info(f"   {price_type}: ${value:.2f}")
                            
                            # Show price selection
                            if user_profile.get('use_highest_price'):
                                logger.info("\n3. PRICE SELECTION")
                                logger.info("   Using highest price rule")
                            else:
                                logger.info("\n3. PRICE SELECTION")
                                logger.info("   Using first valid price rule")
                            
                            # Show price modifiers
                            modifiers = user_profile.get('price_modifiers', {})
                            if modifiers:
                                logger.info("\n4. PRICE MODIFIERS")
                                for price_type, modifier in modifiers.items():
                                    logger.info(f"   {price_type}: {modifier}% modifier")
                            
                            # Show calculated base price
                            logger.info(f"\n5. BASE PRICE: ${float(new_price):.2f}")

                            # Get condition and stepping info
                            condition = variant.get('title', '').split()[0].lower()
                            condition_code = {
                                'near': 'nm',
                                'lightly': 'lp',
                                'moderately': 'mp',
                                'heavily': 'hp',
                                'damaged': 'dm'
                            }.get(condition, 'nm')

                            # Show condition stepping info
                            logger.info("\n6. CONDITION STEPPING")
                            logger.info(f"   Condition: {condition_code.upper()}")
                            
                            variant_index = variants.index(variant)
                            if variant_index > 0:
                                prev_variant = variants[variant_index - 1]
                                prev_price = float(prev_variant.get('price', '0.00'))
                                logger.info(f"   Previous variant ({prev_variant.get('title')}): ${prev_price:.2f}")
                                logger.info(f"   Maximum allowed price: ${prev_price:.2f}")
                                
                                if not has_prices:
                                    # When cascading due to no price data
                                    stepping_percentage = user_profile.get('customStepping', {}).get(condition_code, 100.0)
                                    cascade_price = round(prev_price * (stepping_percentage / 100.0), 2)
                                    logger.info(f"   No price data - cascading with {stepping_percentage}% stepping")
                                    logger.info(f"   Cascaded price: ${cascade_price:.2f}")
                                    new_price = cascade_price
                                elif float(new_price) > prev_price:
                                    # When price exceeds previous variant
                                    logger.info(f"   Price ${new_price:.2f} exceeds maximum allowed")
                                    logger.info(f"   Adjusted to: ${prev_price:.2f}")
                                    new_price = prev_price
                            
                            logger.info("\n7. FINAL RESULT")
                            logger.info(f"   Starting price: ${old_price:.2f} {user_currency}")
                            logger.info(f"   Final price: ${new_price} {user_currency}")
                            if old_price > 0:
                                change_pct = ((float(new_price)/old_price)-1)*100
                                logger.info(f"   Change: {change_pct:+.2f}%")
                        
                        logger.info("\n" + "*" * 100 + "\n")

                        # Capture the summary logs
                        summary_lines = []
                        summary_lines.append("*" * 100)
                        summary_lines.append("FINAL PRICE CALCULATION SUMMARY")
                        summary_lines.append("*" * 100)
                        summary_lines.append(f"\nProduct: {product.get('title')}")
                        summary_lines.append(f"User: {username}")
                        summary_lines.append(f"Currency: {user_currency}")
                        summary_lines.append(f"Exchange Rate: {exchange_rate}")
                        
                        # Add TCGPlayer price fetch details
                        summary_lines.append(f"\nTCGPlayer Prices (fetched at {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')})")
                        for variant in variants:
                            sku_id = variant['skuId']
                            price_data = sku_prices.get(str(sku_id), {})
                            if any(value > 0 for value in price_data.values()):
                                summary_lines.append(f"\n  {variant.get('title')} (SKU {sku_id}):")
                                for price_type, value in price_data.items():
                                    summary_lines.append(f"    - {price_type}: ${value:.2f}")
                            else:
                                summary_lines.append(f"\n  {variant.get('title')} (SKU {sku_id}): No valid prices")
                        
                        summary_lines.append("\nSKU IDs:")
                        for variant in variants:
                            summary_lines.append(f"  {variant.get('title')}: {variant['skuId']}")
                        
                        for variant in variants:
                            summary_lines.append("\n" + "-" * 80)
                            summary_lines.append(f"VARIANT: {variant.get('title')} (SKU ID: {variant['skuId']})")
                            summary_lines.append("-" * 80)
                            
                            sku_id = variant.get('skuId', 'Not assigned')
                            old_price = float(variant.get('price', '0.00'))
                            new_price = variant.get('price')
                            
                            # Get the final price data for this variant
                            price_data = sku_prices.get(sku_id, {})
                            has_prices = any(value > 0 for value in price_data.values()) if price_data else False

                            summary_lines.append("\n1. STARTING POINT")
                            summary_lines.append(f"   Current price: ${old_price:.2f} {user_currency}")
                            
                            summary_lines.append("\n2. TCGPLAYER PRICES")
                            for price_type, value in price_data.items():
                                summary_lines.append(f"   {price_type}: ${value:.2f}")
                            
                            # Show price selection
                            if user_profile.get('use_highest_price'):
                                summary_lines.append("\n3. PRICE SELECTION")
                                summary_lines.append("   Using highest price rule")
                            else:
                                summary_lines.append("\n3. PRICE SELECTION")
                                summary_lines.append("   Using first valid price rule")
                            
                            # Show price modifiers
                            modifiers = user_profile.get('price_modifiers', {})
                            if modifiers:
                                summary_lines.append("\n4. PRICE MODIFIERS")
                                for price_type, modifier in modifiers.items():
                                    summary_lines.append(f"   {price_type}: {modifier}% modifier")
                            
                            # Show calculated base price
                            summary_lines.append(f"\n5. BASE PRICE: ${float(new_price):.2f}")

                            # Show condition stepping info
                            summary_lines.append("\n6. CONDITION STEPPING")
                            summary_lines.append(f"   Condition: {condition_code.upper()}")
                            
                            if variant_index > 0:
                                prev_variant = variants[variant_index - 1]
                                prev_price = float(prev_variant.get('price', '0.00'))
                                summary_lines.append(f"   Previous variant ({prev_variant.get('title')}): ${prev_price:.2f}")
                                summary_lines.append(f"   Maximum allowed price: ${prev_price:.2f}")
                                
                                if not has_prices:
                                    stepping_percentage = user_profile.get('customStepping', {}).get(condition_code, 100.0)
                                    cascade_price = round(prev_price * (stepping_percentage / 100.0), 2)
                                    summary_lines.append(f"   No price data - cascading with {stepping_percentage}% stepping")
                                    summary_lines.append(f"   Cascaded price: ${cascade_price:.2f}")
                                elif float(new_price) > prev_price:
                                    summary_lines.append(f"   Price ${new_price:.2f} exceeds maximum allowed")
                                    summary_lines.append(f"   Adjusted to: ${prev_price:.2f}")
                            
                            summary_lines.append("\n7. FINAL RESULT")
                            summary_lines.append(f"   Starting price: ${old_price:.2f} {user_currency}")
                            summary_lines.append(f"   Final price: ${new_price} {user_currency}")
                            if old_price > 0:
                                change_pct = ((float(new_price)/old_price)-1)*100
                                summary_lines.append(f"   Change: {change_pct:+.2f}%")
                        
                        summary_lines.append("\n" + "*" * 100 + "\n")
                        
                        # Join all summary lines into a single string
                        summary_text = "\n".join(summary_lines)
                        
                        # Log the summary
                        logger.info(summary_text)
                        
                        # Always mark products as needing pushing and save the summary
                        # Create a more explicit update document that ensures variant prices are updated
                        update_doc = {
                            '$set': {
                                'needsPushing': True,
                                'last_repriced': datetime.now(timezone.utc),
                                'summary': summary_text
                            }
                        }
                        
                        # Explicitly set each variant's price in the update document
                        for i, variant in enumerate(variants):
                            update_doc['$set'][f'variants.{i}.price'] = variant['price']
                        
                        logger.info(f"Marking product {product.get('title')} as needsPushing=True")
                        logger.info(f"Updating variant prices with explicit field paths")
                        
                        try:
                            product_id = product['_id']
                            if isinstance(product_id, str):
                                from bson import ObjectId
                                product_id = ObjectId(product_id)
                            
                            # Execute the update with explicit variant price paths
                            result = shopify_collection.update_one(
                                {'_id': product_id},
                                update_doc
                            )
                            
                            if result.modified_count > 0:
                                total_updates += 1
                                logger.info(f"Updated product {product.get('title')} with {len(variants)} variant prices")
                                
                                # Verify the update by fetching the updated document
                                updated_product = shopify_collection.find_one({'_id': product_id})
                                if updated_product:
                                    logger.info(f"Verification - Updated variant prices:")
                                    for i, variant in enumerate(updated_product.get('variants', [])):
                                        logger.info(f"  - Variant {i} ({variant.get('title')}): ${variant.get('price')}")
                                else:
                                    logger.warning(f"Could not verify update for product {product.get('title')}")
                        except Exception as e:
                            logger.error(f"Error in immediate update: {str(e)}")
                            # Create a properly structured bulk update operation
                            # with explicit variant price paths
                            bulk_update_doc = {
                                '$set': {
                                    'needsPushing': True,
                                    'last_repriced': datetime.now(timezone.utc),
                                    'summary': summary_text
                                }
                            }
                            
                            # Explicitly set each variant's price in the bulk update document
                            for i, variant in enumerate(variants):
                                bulk_update_doc['$set'][f'variants.{i}.price'] = variant['price']
                                
                            logger.info(f"Adding product {product.get('title')} to bulk update queue")
                            bulk_updates.append(
                                UpdateOne(
                                    {'_id': product_id},
                                    bulk_update_doc
                                )
                            )

                    except Exception as e:
                        logger.error(f"Error processing product: {str(e)}")
                        continue

                # Perform bulk updates after each batch if there are any
                if bulk_updates:
                    try:
                        # Execute the bulk write operation
                        result = shopify_collection.bulk_write(bulk_updates, ordered=False)
                        logger.info(f"Bulk updated {len(bulk_updates)} products")
                        
                        # Verify bulk updates by fetching a sample of updated products
                        if bulk_updates:
                            sample_size = min(5, len(bulk_updates))
                            sample_updates = bulk_updates[:sample_size]
                            logger.info(f"Verifying {sample_size} sample updates from bulk operation")
                            
                            for update in sample_updates:
                                try:
                                    product_id = update._filter.get('_id')
                                    if product_id:
                                        updated_product = shopify_collection.find_one({'_id': product_id})
                                        if updated_product:
                                            logger.info(f"Verification - Product {updated_product.get('title')} variant prices:")
                                            for i, variant in enumerate(updated_product.get('variants', [])):
                                                logger.info(f"  - Variant {i} ({variant.get('title')}): ${variant.get('price')}")
                                except Exception as verify_e:
                                    logger.error(f"Error verifying bulk update: {str(verify_e)}")
                        
                        # Clear the bulk updates list
                        bulk_updates = []
                    except Exception as e:
                        logger.error(f"Error performing bulk write: {str(e)}")
                        # Fall back to individual updates
                        logger.info("Falling back to individual updates")
                        for update in bulk_updates:
                            try:
                                single_result = shopify_collection.update_one(
                                    update._filter,
                                    update._doc
                                )
                                if single_result.modified_count > 0:
                                    logger.info(f"Individual update successful for product ID: {update._filter.get('_id')}")
                            except Exception as inner_e:
                                logger.error(f"Error in single update: {str(inner_e)}")
                        bulk_updates = []
                   
                pbar.update(len(batch))

        # Log structured summary
        logger.info("\n=== Price Update Summary ===")
        logger.info(f"User: {username}")
        logger.info(f"Currency: {user_currency}")
        logger.info(f"Exchange Rate: {exchange_rate:.4f}")
        logger.info(f"Total Products Processed: {total_products}")
        logger.info(f"Total Updates: {total_updates}")
        logger.info(f"Total Price Changes: {total_price_changes}")
        logger.info("========================\n")

    except Exception as e:
        logger.error(f"Error in process_user for {config.get('username')}: {str(e)}")

def bulk_reprice(max_workers=30):
    try:
        currency_cache.clear_expired()
        
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            logger.error("TCGPlayer key not found")
            return

        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        
        # Get all autopricer configs (not just for admintcg)
        autopricer_configs = list(autopricer_collection.find())
        if not autopricer_configs:
            return
           
        # Filter out configs without selectedProductTypes
        autopricer_configs = [c for c in autopricer_configs if 'selectedProductTypes' in c]
        if not autopricer_configs:
            logger.error("No valid configs with selectedProductTypes found")
            return
        
        start_time = time.time()
        completed_users = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for config in autopricer_configs:
                future = executor.submit(process_user, config, tcgplayer_api_key)
                futures.append((config.get('username'), future))
            
            for username, future in futures:
                try:
                    future.result()
                    completed_users += 1
                except Exception as e:
                    logger.error(f"Failed to process user {username}: {str(e)}")

        execution_time = time.time() - start_time
        logger.info(f"Completed bulk reprice in {execution_time:.2f} seconds")

    except Exception as e:
        logger.error(f"Error in bulk_reprice: {str(e)}")
        raise

def run_scheduled_task():
    try:
        bulk_reprice()
    except Exception as e:
        logger.error(f"Error in scheduled task: {str(e)}")

def cleanup_resources():
    try:
        mongo_client.close()
        session.close()
    except Exception as e:
        logger.error(f"Error during resource cleanup: {str(e)}")

def handle_shutdown(signum, frame):
    cleanup_resources()
    sys.exit(0)

def main():
    try:
        signal.signal(signal.SIGTERM, handle_shutdown)
        signal.signal(signal.SIGINT, handle_shutdown)
        
        schedule.every(2).hours.do(run_scheduled_task)
        schedule.every(12).hours.do(currency_cache.clear_expired)
        
        run_scheduled_task()
        
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)
            except Exception as e:
                logger.error(f"Error in schedule loop: {str(e)}")
                time.sleep(60)
                
    except Exception as e:
        logger.error(f"Unhandled exception in main: {str(e)}")
    finally:
        cleanup_resources()

if __name__ == "__main__":
    main()
