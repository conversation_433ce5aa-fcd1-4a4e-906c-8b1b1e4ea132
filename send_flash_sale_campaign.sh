#!/bin/bash
# <PERSON><PERSON>t to send the Friday Flash Sale email campaign
# This script will send emails to users listed in the specified file
# with rate limiting (50 emails per hour)

# Default values
USERS_FILE="sample_users.txt"
TEST_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --test)
      TEST_MODE=true
      shift
      ;;
    --file)
      USERS_FILE="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--test] [--file FILENAME]"
      exit 1
      ;;
  esac
done

# Display banner
echo "=================================================="
echo "  TCGSync Friday Flash Sale Email Campaign"
echo "=================================================="
echo ""

# Run in test mode or with users file
if [ "$TEST_MODE" = true ]; then
  echo "Running in TEST mode - sending only to admintcg"
  python send_flash_sale_emails.py --test
else
  # Check if users file exists
  if [ ! -f "$USERS_FILE" ]; then
    echo "Error: Users file '$USERS_FILE' not found!"
    exit 1
  fi
  
  # Count users in file
  USER_COUNT=$(grep -c "." "$USERS_FILE")
  echo "Sending emails to $USER_COUNT users from file: $USERS_FILE"
  echo "Rate limit: 50 emails per hour"
  
  # Calculate estimated completion time
  HOURS=$(echo "scale=2; $USER_COUNT / 50" | bc)
  echo "Estimated completion time: $HOURS hours"
  
  # Confirm before proceeding
  read -p "Do you want to proceed? (y/n): " CONFIRM
  if [ "$CONFIRM" != "y" ]; then
    echo "Operation cancelled."
    exit 0
  fi
  
  # Run the script
  echo "Starting email campaign..."
  python send_flash_sale_emails.py --file "$USERS_FILE"
fi

echo ""
echo "Email campaign completed!"
echo "Check bulk_email_send.log for details."
