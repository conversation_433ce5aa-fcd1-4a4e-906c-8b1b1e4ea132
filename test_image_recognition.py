from config.config import Config
from flask import Flask, render_template, request, jsonify
import base64
import requests
import os
import json

app = Flask(__name__)
OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"

if not OPENROUTER_API_KEY:
    raise ValueError("OPENROUTER_API_KEY environment variable is not set")

print("Starting server with OpenRouter configuration...")
print(f"API URL: {OPENROUTER_URL}")

@app.route('/image-recognition-test')
def image_recognition_page():
    return render_template('image_recognition_test.html')

@app.route('/api/analyze-image', methods=['POST'])
def analyze_image():
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image uploaded'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image selected'}), 400

        # Read and encode the image
        image_data = file.read()
        
        # Convert to base64
        base64_image = base64.b64encode(image_data).decode('utf-8')
        
        # Call OpenRouter API
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "HTTP-Referer": "http://localhost:5001",
            "X-Title": "Image Recognition Test",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "anthropic/claude-3-opus-20240229",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "What's in this image? Please provide a detailed description."
                        },
                        {
                            "type": "image",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.7,
            "headers": {
                "HTTP-Referer": "http://localhost:5001",
                "X-Title": "Image Recognition Test"
            }
        }
        
        print("Sending request to OpenRouter...")
        response = requests.post(OPENROUTER_URL, headers=headers, json=payload)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        try:
            response_json = response.json()
            print(f"Response body: {json.dumps(response_json, indent=2)}")
        except Exception as e:
            print(f"Error parsing response: {str(e)}")
            print(f"Raw response: {response.text}")
            raise
        
        if response.status_code != 200:
            error_msg = response_json.get('error', {}).get('message', 'Unknown error')
            print(f"API Error: {error_msg}")
            raise Exception(f"API Error: {error_msg}")
        
        return jsonify({
            'success': True,
            'description': response_json['choices'][0]['message']['content']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5001)

