from config.config import Config
import paramiko

def test_write():
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect('*************', username='ubuntu', password='Reggie2805!')
    
    cmd = 'touch /var/www/html/cfc.tcgsync.com/test.txt && ls -l /var/www/html/cfc.tcgsync.com/test.txt'
    stdin, stdout, stderr = ssh.exec_command(cmd)
    
    print(stdout.read().decode())
    print(stderr.read().decode())
    
    ssh.close()

if __name__ == '__main__':
    test_write()

