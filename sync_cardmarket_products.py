from config.config import Config
from pymongo import MongoClient, UpdateOne
import logging
from datetime import datetime
import requests
from requests_oauthlib import OAuth1Session
import xml.etree.ElementTree as ET
import time
import gzip
import json
from io import BytesIO

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'sync_cardmarket_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = '*******************************************************************'
mongo_dbname = 'test'
client = MongoClient(mongo_uri)
db = client[mongo_dbname]

# Cardmarket API Configuration
BASE_URL = "https://api.cardmarket.com/ws/v2.0"
user_credentials = {
    'cardmarketAppToken': "57DgqEdCpGF8nJCP",
    'cardmarketAppSecret': "ZpAhuwOtJSS8DLNKSlyphBGB4QP3Dj9v",
    'cardmarketAccessToken': "OxaJQRhrhowHBwW6cFa3ljWyJBg4EE33",
    'cardmarketAccessTokenSecret': "kx3teyTAR0Vs7c2Kws5eM3oPj6J2MhzE"
}

def create_oauth_session(api_url):
    """Create an OAuth1 session"""
    logger.info("Creating OAuth session...")
    return OAuth1Session(
        client_key=user_credentials['cardmarketAppToken'],
        client_secret=user_credentials['cardmarketAppSecret'],
        resource_owner_key=user_credentials['cardmarketAccessToken'],
        resource_owner_secret=user_credentials['cardmarketAccessTokenSecret'],
        realm=api_url
    )

def api_call(url, method='GET', params=None, data=None, headers=None):
    """Make an API call"""
    logger.info(f"Making API call to: {url}")
    session = create_oauth_session(url)
    
    try:
        if method == 'GET':
            response = session.get(url, params=params)
        elif method == 'POST':
            response = session.post(url, params=params, data=data, headers=headers)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")

        logger.info(f"API call response status code: {response.status_code}")
        logger.info(f"Rate Limit Stats: {response.headers.get('X-Request-Limit-Count', 'Unknown')}/{response.headers.get('X-Request-Limit-Max', 'Unknown')}")
        
        if response.status_code == 404:
            logger.error(f"404 error encountered for URL: {url}")
            return None, 404
        
        return response.content, response.status_code
    except requests.RequestException as e:
        logger.error(f"API call failed: {e}")
        return None, getattr(e.response, 'status_code', None)

def find_user(username):
    """Find a user's ID based on their username."""
    logger.info(f"Searching for user: {username}")
    url = f"{BASE_URL}/users/find"
    params = {'search': username}
    response_data, status_code = api_call(url, method='GET', params=params)
    
    if status_code == 404:
        logger.error(f"User '{username}' not found (404).")
        return None
    
    if status_code == 200 and response_data:
        root = ET.fromstring(response_data)
        users = root.find('users')
        if users is not None:
            user_id = users.find('idUser').text if users.find('idUser') is not None else None
            username_found = users.find('username').text if users.find('username') is not None else None
            
            if user_id and username_found.lower() == username.lower():
                logger.info(f"Found user. ID: {user_id}")
                return user_id
    
    logger.error(f"User '{username}' not found.")
    return None

def request_user_offers_export(user_id):
    """Request an export of user offers."""
    logger.info(f"Requesting export for user ID: {user_id}")
    url = f"{BASE_URL}/exports/userOffers/{user_id}"
    response_data, status_code = api_call(url, method='POST')
    
    if status_code == 404:
        logger.error(f"404 error encountered when requesting export for user ID: {user_id}")
        return status_code
    
    if status_code in [202, 205]:
        if status_code == 202:
            logger.info("Export request accepted successfully.")
        else:
            logger.info("Export already in progress or recently completed.")
        return status_code
    else:
        logger.error(f"Export request failed. Status code: {status_code}")
        return status_code

def get_export_status(user_id):
    """Get the status of the export request."""
    logger.info(f"Checking export status for user ID: {user_id}")
    url = f"{BASE_URL}/exports/userOffers/{user_id}"
    response_data, status_code = api_call(url, method='GET')
    
    if status_code == 404:
        logger.error(f"404 error encountered when checking export status for user ID: {user_id}")
        return None
    
    if status_code == 200 and response_data:
        root = ET.fromstring(response_data)
        user_offers_requests = root.find('userOffersRequests')
        if user_offers_requests is not None:
            status = user_offers_requests.find('status').text if user_offers_requests.find('status') is not None else None
            url = user_offers_requests.find('url').text if user_offers_requests.find('url') is not None else None
            logger.info(f"Export status: {status}")
            return {'status': status, 'url': url}
    
    logger.error(f"Failed to get export status. Status code: {status_code}")
    return None

def download_and_process_export(url):
    """Download the export file, decompress if necessary, and return JSON data"""
    logger.info(f"Downloading export from URL: {url}")
    try:
        response = requests.get(url)
        if response.status_code == 404:
            logger.error("404 error encountered when downloading export")
            return None
            
        if response.status_code == 200:
            content = BytesIO(response.content)
            try:
                # Try to decompress as gzip
                with gzip.GzipFile(fileobj=content) as gz_file:
                    json_data = json.loads(gz_file.read().decode('utf-8'))
            except gzip.BadGzipFile:
                # If not gzipped, assume it's plain JSON
                json_data = json.loads(response.content.decode('utf-8'))
            
            logger.info("Export file successfully downloaded and processed")
            return json_data
        else:
            logger.error(f"Failed to download export. Status code: {response.status_code}")
            return None
    except requests.RequestException as e:
        logger.error(f"Failed to download export: {str(e)}")
        return None

def get_users_with_cardmarket_username():
    """Retrieve all users with a cardmarketUsername from the user collection, excluding vaultofcards."""
    try:
        user_collection = db["user"]
        users = user_collection.find({
            "$and": [
                {"cardmarketUsername": {"$exists": True}},
                {"cardmarketUsername": {"$ne": ""}},
                {"cardmarketUsername": {"$ne": "vaultofcards"}}
            ]
        })
        return list(users)
    except Exception as e:
        logger.error(f"Error retrieving users: {str(e)}")
        return []

def insert_into_mongodb_with_username(data, username):
    """Insert the exported data into MongoDB with the username and cmSingles data added to each record"""
    collection = db["exportedcm"]
    cm_singles = db["cmSingles"]
    
    try:
        logger.info(f"Removing any existing records for user {username}")
        delete_result = collection.delete_many({"username": username})
        logger.info(f"Deleted {delete_result.deleted_count} documents for user {username}")
        
        logger.info(f"Inserting data into MongoDB for user {username}")
        
        def enrich_item(item):
            """Helper function to enrich item with cmSingles data"""
            if 'idProduct' in item:
                try:
                    id_product = int(item['idProduct'])
                    matching_single = cm_singles.find_one(
                        {"idProduct": id_product},
                        {
                            "gameName": 1,
                            "enName": 1,
                            "expansionName": 1,
                            "rarity": 1,
                            "image": 1,
                            "number": 1,
                            "productId": 1
                        }
                    )
                    if matching_single:
                        item.update({
                            "gameName": matching_single.get("gameName"),
                            "enName": matching_single.get("enName"),
                            "expansionName": matching_single.get("expansionName"),
                            "rarity": matching_single.get("rarity"),
                            "image": matching_single.get("image"),
                            "number": matching_single.get("number")
                        })
                        
                        if "productId" in matching_single:
                            item["productId"] = matching_single["productId"]
                except (ValueError, TypeError) as e:
                    logger.error(f"Failed to convert idProduct to integer: {item['idProduct']}, Error: {str(e)}")
            item['username'] = username
            return item
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, list):
                    enriched_items = []
                    for item in value:
                        enriched_items.append(enrich_item(item))
                    logger.info(f"Inserting {len(enriched_items)} items for key '{key}' and user '{username}'")
                    if enriched_items:
                        try:
                            result = collection.insert_many(enriched_items)
                            logger.info(f"Inserted {len(result.inserted_ids)} documents for key '{key}' and user '{username}'")
                        except Exception as e:
                            logger.error(f"Failed to insert items for user {username}: {str(e)}")
                else:
                    document = {key: value, 'username': username}
                    try:
                        result = collection.insert_one(document)
                        logger.info(f"Inserted document with ID {result.inserted_id} for key '{key}' and user '{username}'")
                    except Exception as e:
                        logger.error(f"Failed to insert document for user {username}: {str(e)}")
        elif isinstance(data, list):
            enriched_items = []
            for item in data:
                enriched_items.append(enrich_item(item))
            logger.info(f"Inserting {len(enriched_items)} items for user '{username}'")
            if enriched_items:
                try:
                    result = collection.insert_many(enriched_items)
                    logger.info(f"Inserted {len(result.inserted_ids)} documents for user '{username}'")
                except Exception as e:
                    logger.error(f"Failed to insert items for user {username}: {str(e)}")
        else:
            logger.error(f"Unexpected data type for MongoDB insertion: {type(data)}")
    except Exception as e:
        logger.error(f"Error in insert_into_mongodb_with_username for user {username}: {str(e)}")

def process_user(user):
    """Process a single user's Cardmarket export"""
    username = user['cardmarketUsername']
    logger.info(f"Processing user: {username}")

    user_id = find_user(username)
    if not user_id:
        logger.error(f"User '{username}' not found on Cardmarket. Skipping.")
        return

    status_code = request_user_offers_export(user_id)
    if status_code == 404:
        logger.error(f"404 error encountered for user '{username}'. Skipping to next user.")
        return

    attempt = 1
    while True:
        logger.info(f"Attempt {attempt} to check export status for {username}")
        status = get_export_status(user_id)
        if not status:
            logger.error(f"Failed to get status for user '{username}'. Skipping to next user.")
            break
        if status['status'] == 'finished':
            logger.info(f"Export finished successfully for {username}.")
            export_data = download_and_process_export(status['url'])
            if export_data:
                insert_into_mongodb_with_username(export_data, username)
            break
        elif status['status'] == 'failed':
            logger.error(f"Export process failed for {username}.")
            break
        logger.info(f"Export still in progress for {username}. Waiting for 3 minutes before next check...")
        time.sleep(180)  # Wait 3 minutes
        attempt += 1

def refresh_cardmarket_data():
    """Refresh all Cardmarket data by pulling fresh exports for all users"""
    logger.info("Starting Cardmarket data refresh")
    try:
        users = get_users_with_cardmarket_username()
        logger.info(f"Found {len(users)} users with Cardmarket usernames")

        for user in users:
            try:
                process_user(user)
            except Exception as e:
                logger.error(f"Error processing user: {str(e)}")
                continue

        logger.info("Cardmarket data refresh completed")
    except Exception as e:
        logger.error(f"Error in refresh_cardmarket_data: {str(e)}")
        raise

def sync_products():
    try:
        logger.info("Starting product sync")
        start_time = datetime.now()

        # Use aggregation pipeline to get all necessary data in one query
        pipeline = [
            {
                "$lookup": {
                    "from": "cmSingles",
                    "localField": "idProduct",
                    "foreignField": "idProduct",
                    "as": "cardDetails"
                }
            },
            {
                "$lookup": {
                    "from": "priceguides",
                    "localField": "idProduct",
                    "foreignField": "idProduct",
                    "as": "priceGuide"
                }
            },
            {
                "$addFields": {
                    "cardDetails": {"$arrayElemAt": ["$cardDetails", 0]},
                    "priceGuide": {"$arrayElemAt": ["$priceGuide", 0]},
                    "currentPrice": {"$toDouble": "$price"}
                }
            },
            {
                "$addFields": {
                    "trendPrice": {
                        "$cond": [
                            {"$eq": ["$isFoil", True]},
                            "$priceGuide.trend-foil",
                            "$priceGuide.trend"
                        ]
                    },
                    "priceDifference": {
                        "$subtract": [
                            "$currentPrice",
                            {
                                "$cond": [
                                    {"$eq": ["$isFoil", True]},
                                    "$priceGuide.trend-foil",
                                    "$priceGuide.trend"
                                ]
                            }
                        ]
                    }
                }
            },
            {
                "$project": {
                    "_id": 1,
                    "needRepricing": {
                        "$and": [
                            {"$gt": ["$trendPrice", 0.20]},
                            {"$gt": ["$currentPrice", "$trendPrice"]}
                        ]
                    },
                    "updates": {
                        "enName": "$cardDetails.enName",
                        "gameName": "$cardDetails.gameName",
                        "expansionName": "$cardDetails.expansionName",
                        "rarity": "$cardDetails.rarity",
                        "image": "$cardDetails.image",
                        "priceDifference": "$priceDifference"
                    }
                }
            }
        ]

        # Execute pipeline
        logger.info("Executing aggregation pipeline")
        results = list(db.exportedcm.aggregate(pipeline))
        
        # Prepare bulk updates
        bulk_operations = []
        updated_count = 0
        repricing_count = 0

        for result in results:
            update_fields = {}
            
            # Add card details and price difference if they exist
            if result.get('updates'):
                for field, value in result['updates'].items():
                    if value is not None:  # Only update fields that have values
                        if field == 'priceDifference':
                            # Round price difference to 2 decimal places
                            update_fields[field] = round(float(value), 2) if value else None
                        else:
                            update_fields[field] = value

            # Set needRepricing flag
            if result.get('needRepricing') is not None:
                update_fields['needRepricing'] = result['needRepricing']
                if result['needRepricing']:
                    repricing_count += 1

            if update_fields:
                bulk_operations.append(
                    UpdateOne(
                        {'_id': result['_id']},
                        {'$set': update_fields}
                    )
                )
                updated_count += 1

        # Execute bulk update if there are operations
        if bulk_operations:
            logger.info(f"Executing bulk update for {len(bulk_operations)} products")
            result = db.exportedcm.bulk_write(bulk_operations)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"Sync completed in {duration:.2f} seconds")
            logger.info(f"Modified {result.modified_count} products")
            logger.info(f"Found {repricing_count} products that need repricing")
            
            return {
                'modified_count': result.modified_count,
                'repricing_count': repricing_count,
                'duration_seconds': duration
            }
        else:
            logger.info("No updates required")
            return {
                'modified_count': 0,
                'repricing_count': 0,
                'duration_seconds': (datetime.now() - start_time).total_seconds()
            }

    except Exception as e:
        logger.error(f"An error occurred during sync: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        logger.info("Starting sync script")
        
        # First, refresh the Cardmarket data
        logger.info("Step 1: Refreshing Cardmarket data")
        refresh_cardmarket_data()
        
        # Then, run the sync process
        logger.info("Step 2: Running sync process")
        result = sync_products()
        
        logger.info(f"Sync completed successfully:")
        logger.info(f"- Modified {result['modified_count']} products")
        logger.info(f"- Found {result['repricing_count']} products that need repricing")
        logger.info(f"- Duration: {result['duration_seconds']:.2f} seconds")
    except Exception as e:
        logger.error(f"Script failed: {str(e)}")
    finally:
        client.close()
