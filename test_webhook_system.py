from config.config import Config
import requests
import json
import time
import redis
import logging
from datetime import datetime
from typing import Dict, Any, Optional

class WebhookTester:
    def __init__(self, base_url: str = "http://localhost"):
        self.base_url = base_url
        self.logger = self._setup_logging()
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        
    def _setup_logging(self) -> logging.Logger:
        """Configure logging"""
        logger = logging.getLogger('WebhookTester')
        logger.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler
        file_handler = logging.FileHandler('webhook_tests.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger
        
    def test_redis_connection(self) -> bool:
        """Test Redis connectivity"""
        try:
            self.logger.info("Testing Redis connection...")
            result = self.redis_client.ping()
            if result:
                self.logger.info("Redis connection successful")
                return True
            else:
                self.logger.error("Redis ping failed")
                return False
        except Exception as e:
            self.logger.error(f"Redis connection failed: {str(e)}")
            return False
            
    def test_queue_operations(self) -> bool:
        """Test basic queue operations"""
        try:
            self.logger.info("Testing queue operations...")
            
            # Clear test queue
            test_queue = "test_webhook_queue"
            self.redis_client.delete(test_queue)
            
            # Test data
            test_data = {
                "test_id": "123",
                "timestamp": datetime.now().isoformat()
            }
            
            # Push to queue
            self.redis_client.lpush(test_queue, json.dumps(test_data))
            
            # Pop from queue
            result = self.redis_client.rpop(test_queue)
            if not result:
                self.logger.error("Failed to retrieve test data from queue")
                return False
                
            retrieved_data = json.loads(result)
            if retrieved_data["test_id"] != test_data["test_id"]:
                self.logger.error("Retrieved data doesn't match test data")
                return False
                
            self.logger.info("Queue operations test successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Queue operations test failed: {str(e)}")
            return False
            
    def test_webhook_endpoint(self) -> bool:
        """Test webhook endpoint with sample data"""
        try:
            self.logger.info("Testing webhook endpoint...")
            
            # Sample webhook data
            webhook_data = {
                'topic': 'products/create',
                'shop_domain': 'test-store.myshopify.com',
                'data': {
                    'id': 'test-123',
                    'title': 'Test Product',
                    'variants': [
                        {
                            'id': 'var-123',
                            'price': '10.00',
                            'inventory_quantity': 5
                        }
                    ]
                }
            }
            
            # Headers
            headers = {
                'X-Shopify-Topic': 'products/create',
                'X-Shopify-Shop-Domain': 'test-store.myshopify.com',
                'Content-Type': 'application/json'
            }
            
            # Send webhook
            response = requests.post(
                f"{self.base_url}/shopify/products/webhook",
                json=webhook_data,
                headers=headers
            )
            
            if response.status_code != 200:
                self.logger.error(f"Webhook endpoint test failed with status {response.status_code}")
                self.logger.error(f"Response: {response.text}")
                return False
                
            self.logger.info("Webhook endpoint test successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Webhook endpoint test failed: {str(e)}")
            return False
            
    def test_webhook_processing(self) -> bool:
        """Test webhook processing by monitoring queue and logs"""
        try:
            self.logger.info("Testing webhook processing...")
            
            # Send test webhook
            if not self.test_webhook_endpoint():
                return False
                
            # Wait for processing
            time.sleep(5)
            
            # Check Redis queue length
            queue_length = self.redis_client.llen('shopify_webhooks')
            self.logger.info(f"Current queue length: {queue_length}")
            
            # Read service logs
            import subprocess
            result = subprocess.run(
                ['journalctl', '-u', 'webhook_worker', '-n', '50', '--no-pager'],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                self.logger.error("Failed to read service logs")
                return False
                
            log_content = result.stdout
            
            # Check for error indicators in logs
            error_indicators = ['error', 'exception', 'failed', 'traceback']
            found_errors = [ind for ind in error_indicators if ind in log_content.lower()]
            
            if found_errors:
                self.logger.error(f"Found error indicators in logs: {found_errors}")
                self.logger.error("Recent log entries:")
                self.logger.error(log_content[-1000:])  # Last 1000 chars
                return False
                
            self.logger.info("Webhook processing test successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Webhook processing test failed: {str(e)}")
            return False
            
    def run_all_tests(self) -> bool:
        """Run all tests in sequence"""
        tests = [
            ("Redis Connection", self.test_redis_connection),
            ("Queue Operations", self.test_queue_operations),
            ("Webhook Endpoint", self.test_webhook_endpoint),
            ("Webhook Processing", self.test_webhook_processing)
        ]
        
        all_passed = True
        for test_name, test_func in tests:
            self.logger.info(f"\n=== Running {test_name} Test ===")
            if not test_func():
                self.logger.error(f"{test_name} test failed")
                all_passed = False
            else:
                self.logger.info(f"{test_name} test passed")
                
        if all_passed:
            self.logger.info("\n=== All tests passed successfully ===")
        else:
            self.logger.error("\n=== Some tests failed ===")
            
        return all_passed

def main():
    base_url = "http://localhost"  # Modify as needed
    tester = WebhookTester(base_url)
    success = tester.run_all_tests()
    exit(0 if success else 1)

if __name__ == "__main__":
    main()

