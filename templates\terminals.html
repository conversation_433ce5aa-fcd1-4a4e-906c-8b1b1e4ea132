{% extends "base.html" %}

{% block title %}In-Store Terminals{% endblock %}

{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">

<style>
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }

    /* Card styling to match dashboard */
    .dashboard-card {
        background-color: #1e293b;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .dashboard-card-header {
        padding: 15px 20px;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dashboard-card-body {
        padding: 20px;
        background-color: #1e293b;
    }

    /* Form controls styling */
    .form-control, .form-select {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
    }

    .form-control:focus, .form-select:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
    }

    /* Form label styling */
    .form-label {
        color: white;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Switch styling */
    .form-switch .form-check-input {
        width: 3em;
        height: 1.5em;
        margin-top: 0.25em;
    }

    .form-switch .form-check-input:checked {
        background-color: #e74c3c;
        border-color: #e74c3c;
    }

    .form-check-label {
        color: white;
        font-weight: 400;
    }

    /* Button styling */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Info box styling */
    .info-box {
        background-color: rgba(52, 152, 219, 0.1);
        border-left: 4px solid #3498db;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        color: white;
    }

    .info-box i {
        color: #3498db;
        margin-right: 10px;
    }

    /* Terminal preview */
    .terminal-preview {
        background-color: #000;
        border-radius: 16px;
        padding: 0;
        color: white;
        height: 400px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.1);
        perspective: 1000px;
    }

    .terminal-screen {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #111, #222);
        overflow: hidden;
    }

    .terminal-bezel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 12px solid #111;
        border-radius: 16px;
        pointer-events: none;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8);
        z-index: 10;
    }

    .terminal-reflection {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(to bottom,
            rgba(255, 255, 255, 0.05),
            rgba(255, 255, 255, 0.01),
            transparent);
        z-index: 3;
        pointer-events: none;
    }

    .terminal-content {
        position: relative;
        z-index: 2;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 30px;
    }

    .terminal-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 60px;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .terminal-logo {
        font-size: 18px;
        font-weight: 700;
        letter-spacing: 1px;
        color: white;
    }

    .terminal-offer {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 30px;
        color: #e74c3c;
        text-shadow: 0 0 15px rgba(231, 76, 60, 0.7);
        animation: pulse 2s infinite;
        max-width: 80%;
    }

    @keyframes pulse {
        0% { opacity: 0.8; transform: scale(0.98); }
        50% { opacity: 1; transform: scale(1); }
        100% { opacity: 0.8; transform: scale(0.98); }
    }

    .terminal-features {
        display: flex;
        gap: 20px;
        margin-top: 30px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .terminal-feature {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
        padding: 12px 20px;
        border-radius: 30px;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .terminal-feature:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        background: rgba(255, 255, 255, 0.15);
    }

    .terminal-feature i {
        color: #e74c3c;
        font-size: 18px;
    }

    .terminal-footer {
        position: absolute;
        bottom: 15px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
    }

    .terminal-custom-note {
        background-color: rgba(52, 152, 219, 0.1);
        border-left: 4px solid #3498db;
        padding: 15px;
        border-radius: 8px;
        margin-top: 20px;
        color: white;
        font-size: 14px;
    }
</style>

<div class="container-fluid mt-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <i class="fas fa-desktop me-2"></i> In-Store Terminal Configuration
                </div>
                <div class="dashboard-card-body">
                    <div class="info-box mb-4">
                        <i class="fas fa-info-circle"></i>
                        <span>Configure how your in-store terminals will look and function. These settings will be applied to all terminals in your store.</span>
                    </div>

                    <form id="terminalConfigForm">
                        <div class="mb-4">
                            <label for="currentOffer" class="form-label">Current Offer (Screensaver)</label>
                            <input type="text" class="form-control" id="currentOffer" name="currentOffer" placeholder="e.g., 20% OFF ALL SINGLES THIS WEEKEND!" required>
                            <small class="text-white-50">This text will be displayed as a screensaver when the terminal is idle.</small>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showBuylist" name="showBuylist" checked>
                                    <label class="form-check-label" for="showBuylist">Show Buylist</label>
                                </div>
                                <small class="text-white-50 d-block mt-1">Allow customers to view your buylist prices on the terminal.</small>
                            </div>

                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showInstock" name="showInstock" checked>
                                    <label class="form-check-label" for="showInstock">Show In-Stock Items</label>
                                </div>
                                <small class="text-white-50 d-block mt-1">Allow customers to browse your in-stock inventory.</small>
                            </div>

                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showEvents" name="showEvents" checked>
                                    <label class="form-check-label" for="showEvents">Show Events</label>
                                </div>
                                <small class="text-white-50 d-block mt-1">Display upcoming store events on the terminal.</small>
                            </div>

                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="allowBackorder" name="allowBackorder">
                                    <label class="form-check-label" for="allowBackorder">Allow Back Ordering</label>
                                </div>
                                <small class="text-white-50 d-block mt-1">Allow customers to back order items not in stock.</small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="alert alert-warning" style="background-color: rgba(255, 193, 7, 0.2); border-color: rgba(255, 193, 7, 0.3); color: #ffc107;">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Back Ordering:</strong> This feature allows customers to order items not currently in stock at your store. We'll check our other stores in the same country and have the items delivered to your store within 7 days.
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5 class="text-white mb-3">Branding Preferences</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="storeName" class="form-label">Store Name</label>
                                    <input type="text" class="form-control" id="storeName" name="storeName" placeholder="Your Store Name">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="logoUpload" class="form-label">Store Logo (Optional)</label>
                                    <input type="file" class="form-control" id="logoUpload" name="logoUpload">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="primaryColor" class="form-label">Primary Brand Color</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="primaryColor" name="primaryColor" value="#e74c3c" title="Choose your primary color">
                                        <input type="text" class="form-control" id="primaryColorHex" value="#e74c3c" placeholder="#RRGGBB">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="secondaryColor" class="form-label">Secondary Brand Color (Optional)</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="secondaryColor" name="secondaryColor" value="#3498db" title="Choose your secondary color">
                                        <input type="text" class="form-control" id="secondaryColorHex" value="#3498db" placeholder="#RRGGBB">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="fontStyle" class="form-label">Preferred Font Style</label>
                                <select class="form-select" id="fontStyle" name="fontStyle">
                                    <option value="modern" selected>Modern (Sans-serif)</option>
                                    <option value="classic">Classic (Serif)</option>
                                    <option value="playful">Playful/Casual</option>
                                    <option value="minimal">Minimal/Clean</option>
                                    <option value="bold">Bold/Impactful</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="detailedSpec" class="form-label">Additional Specifications</label>
                            <textarea class="form-control" id="detailedSpec" name="detailedSpec" rows="5" placeholder="Enter any additional specifications or requirements for your terminals..."></textarea>
                            <small class="text-white-50">Include any specific requirements or customizations you need for your terminals.</small>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn" style="background-color: #e74c3c; border-color: #e74c3c; color: white;">
                                <i class="fas fa-save me-2"></i> Save Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <i class="fas fa-eye me-2"></i> Terminal Preview
                </div>
                <div class="dashboard-card-body">
                    <div class="terminal-preview">
                        <div class="terminal-screen"></div>
                        <div class="terminal-header">
                            <div class="terminal-logo">YOUR STORE NAME</div>
                        </div>
                        <div class="terminal-reflection"></div>
                        <div class="terminal-content">
                            <div class="terminal-offer" id="previewOffer">20% OFF ALL SINGLES THIS WEEKEND!</div>
                            <div class="terminal-features">
                                <div class="terminal-feature" id="previewBuylist">
                                    <i class="fas fa-tags"></i> Buylist
                                </div>
                                <div class="terminal-feature" id="previewInstock">
                                    <i class="fas fa-boxes"></i> In-Stock
                                </div>
                                <div class="terminal-feature" id="previewEvents">
                                    <i class="fas fa-calendar-alt"></i> Events
                                </div>
                                <div class="terminal-feature" id="previewBackorder" style="display: none;">
                                    <i class="fas fa-truck"></i> Back Order
                                </div>
                            </div>
                            <div class="terminal-footer">
                                Tap a button to begin
                            </div>
                        </div>
                        <div class="terminal-bezel"></div>
                    </div>
                    <div class="terminal-custom-note mt-3">
                        <i class="fas fa-palette me-2"></i>
                        <span>Each terminal will be custom designed to match your store's branding, including your logo, colors, and fonts.</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-card mt-4">
                <div class="dashboard-card-header">
                    <i class="fas fa-question-circle me-2"></i> Need Help?
                </div>
                <div class="dashboard-card-body">
                    <p class="text-white">Not sure how to configure your terminals? Our team is here to help!</p>
                    <a href="{{ url_for('ticket.create_ticket') }}" class="btn w-100" style="background-color: #3498db; border-color: #3498db; color: white;">
                        <i class="fas fa-ticket-alt me-2"></i> Create Support Ticket
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form elements
        const currentOfferInput = document.getElementById('currentOffer');
        const showBuylistSwitch = document.getElementById('showBuylist');
        const showInstockSwitch = document.getElementById('showInstock');
        const showEventsSwitch = document.getElementById('showEvents');
        const allowBackorderSwitch = document.getElementById('allowBackorder');
        const storeNameInput = document.getElementById('storeName');
        const primaryColorInput = document.getElementById('primaryColor');
        const primaryColorHexInput = document.getElementById('primaryColorHex');
        const secondaryColorInput = document.getElementById('secondaryColor');
        const secondaryColorHexInput = document.getElementById('secondaryColorHex');
        const fontStyleSelect = document.getElementById('fontStyle');

        // Preview elements
        const terminalPreview = document.querySelector('.terminal-preview');
        const previewOffer = document.getElementById('previewOffer');
        const previewBuylist = document.getElementById('previewBuylist');
        const previewInstock = document.getElementById('previewInstock');
        const previewEvents = document.getElementById('previewEvents');
        const previewBackorder = document.getElementById('previewBackorder');
        const terminalLogo = document.querySelector('.terminal-logo');

        // Update preview when form values change
        currentOfferInput.addEventListener('input', function() {
            previewOffer.textContent = this.value || 'Enter your offer text';
        });

        showBuylistSwitch.addEventListener('change', function() {
            previewBuylist.style.display = this.checked ? 'flex' : 'none';
        });

        showInstockSwitch.addEventListener('change', function() {
            previewInstock.style.display = this.checked ? 'flex' : 'none';
        });

        showEventsSwitch.addEventListener('change', function() {
            previewEvents.style.display = this.checked ? 'flex' : 'none';
        });

        allowBackorderSwitch.addEventListener('change', function() {
            previewBackorder.style.display = this.checked ? 'flex' : 'none';
        });

        // Update store name in preview
        storeNameInput.addEventListener('input', function() {
            terminalLogo.textContent = this.value || 'YOUR STORE NAME';
        });

        // Update primary color in preview
        primaryColorInput.addEventListener('input', function() {
            updatePrimaryColor(this.value);
            primaryColorHexInput.value = this.value;
        });

        primaryColorHexInput.addEventListener('input', function() {
            if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                updatePrimaryColor(this.value);
                primaryColorInput.value = this.value;
            }
        });

        // Update secondary color in preview
        secondaryColorInput.addEventListener('input', function() {
            updateSecondaryColor(this.value);
            secondaryColorHexInput.value = this.value;
        });

        secondaryColorHexInput.addEventListener('input', function() {
            if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                updateSecondaryColor(this.value);
                secondaryColorInput.value = this.value;
            }
        });

        // Update font style in preview
        fontStyleSelect.addEventListener('change', function() {
            updateFontStyle(this.value);
        });

        // Helper functions for updating preview
        function updatePrimaryColor(color) {
            previewOffer.style.color = color;
            previewOffer.style.textShadow = `0 0 15px ${color}80`; // Add 50% opacity for shadow

            document.querySelectorAll('.terminal-feature i').forEach(icon => {
                icon.style.color = color;
            });
        }

        function updateSecondaryColor(color) {
            const terminalScreen = terminalPreview.querySelector('.terminal-screen');
            terminalScreen.style.background = `linear-gradient(135deg, #111, ${color}22)`; // Very transparent version of color
        }

        function updateFontStyle(style) {
            let fontFamily = '"Poppins", sans-serif'; // Default modern

            switch(style) {
                case 'classic':
                    fontFamily = '"Georgia", serif';
                    break;
                case 'playful':
                    fontFamily = '"Comic Sans MS", cursive';
                    break;
                case 'minimal':
                    fontFamily = '"Roboto", sans-serif';
                    break;
                case 'bold':
                    fontFamily = '"Montserrat", sans-serif';
                    terminalLogo.style.fontWeight = '800';
                    previewOffer.style.fontWeight = '800';
                    return;
            }

            terminalLogo.style.fontFamily = fontFamily;
            previewOffer.style.fontFamily = fontFamily;
            document.querySelectorAll('.terminal-feature').forEach(feature => {
                feature.style.fontFamily = fontFamily;
            });
        }

        // Logo upload preview
        const logoUpload = document.getElementById('logoUpload');
        logoUpload.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    // Create logo image if it doesn't exist
                    let logoImg = terminalLogo.querySelector('img');
                    if (!logoImg) {
                        logoImg = document.createElement('img');
                        logoImg.style.maxHeight = '40px';
                        logoImg.style.maxWidth = '200px';
                        terminalLogo.innerHTML = '';
                        terminalLogo.appendChild(logoImg);
                    }

                    logoImg.src = e.target.result;
                };

                reader.readAsDataURL(this.files[0]);
            }
        });

        // Form submission
        const terminalConfigForm = document.getElementById('terminalConfigForm');
        terminalConfigForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Collect form data
            const formData = new FormData(this);

            // Convert FormData to object for logging
            const formDataObj = {};
            formData.forEach((value, key) => {
                formDataObj[key] = value;
            });

            // Log form data (for development)
            console.log('Form data:', formDataObj);

            // Send data to server
            fetch('/terminals/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    currentOffer: currentOfferInput.value,
                    showBuylist: showBuylistSwitch.checked,
                    showInstock: showInstockSwitch.checked,
                    showEvents: showEventsSwitch.checked,
                    allowBackorder: allowBackorderSwitch.checked,
                    storeName: storeNameInput.value,
                    primaryColor: primaryColorInput.value,
                    secondaryColor: secondaryColorInput.value,
                    fontStyle: fontStyleSelect.value,
                    detailedSpec: document.getElementById('detailedSpec').value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-success';
                    successAlert.style.backgroundColor = 'rgba(46, 204, 113, 0.2)';
                    successAlert.style.borderColor = 'rgba(46, 204, 113, 0.3)';
                    successAlert.style.color = '#2ecc71';
                    successAlert.style.padding = '15px';
                    successAlert.style.borderRadius = '8px';
                    successAlert.style.marginTop = '20px';
                    successAlert.innerHTML = '<i class="fas fa-check-circle me-2"></i> Terminal configuration saved successfully! Our team will contact you shortly to discuss your custom terminal design.';

                    terminalConfigForm.appendChild(successAlert);

                    // Scroll to the success message
                    successAlert.scrollIntoView({ behavior: 'smooth' });

                    // Remove the alert after 5 seconds
                    setTimeout(() => {
                        successAlert.remove();
                    }, 5000);
                } else {
                    alert('Error saving configuration: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving your configuration. Please try again.');
            });
        });

        // Initialize preview with default values
        updatePrimaryColor(primaryColorInput.value);
        updateSecondaryColor(secondaryColorInput.value);
        updateFontStyle(fontStyleSelect.value);
    });
</script>
{% endblock %}
