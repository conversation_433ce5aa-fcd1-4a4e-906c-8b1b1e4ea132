from config.config import Config
#!/usr/bin/env python3
"""
Migration script to add price_preference_order field to all existing user_settings documents.
"""
import sys
import os
from mongoengine import connect
from models.user_settings_model import UserSettings
from config import Config

def update_user_settings():
    """Update all user_settings documents to add price_preference_order field."""
    # Get MongoDB connection details from the application config
    mongo_uri = Config.MONGO_URI
    mongo_db = Config.MONGO_DBNAME
    
    print(f"Connecting to MongoDB: {mongo_uri}, DB: {mongo_db}")
    
    # Connect to MongoDB
    connect(host=mongo_uri)
    
    # Print all collections in the database
    from mongoengine.connection import get_db
    db = get_db()
    print(f"Collections in database: {db.list_collection_names()}")
    
    # Check if user_settings collection exists
    if 'user_settings' not in db.list_collection_names():
        print("user_settings collection not found in database")
    
    # Default price preference order
    default_order = ['lowPrice', 'marketPrice', 'midPrice', 'highPrice', 'lowestListedPrice']
    
    # Get all user_settings documents
    user_settings_docs = UserSettings.objects()
    
    # Count of documents updated
    updated_count = 0
    
    # Update each document
    for user_settings in user_settings_docs:
        # Print document fields for debugging
        print(f"Document for {user_settings.username}:")
        print(f"  Fields: {dir(user_settings)}")
        print(f"  Has price_preference_order: {hasattr(user_settings, 'price_preference_order')}")
        if hasattr(user_settings, 'price_preference_order'):
            print(f"  Current price_preference_order: {user_settings.price_preference_order}")
        
        # Check if price_preference_order field exists
        if not hasattr(user_settings, 'price_preference_order') or not user_settings.price_preference_order:
            # Add price_preference_order field with default value
            user_settings.price_preference_order = default_order
            user_settings.save()
            updated_count += 1
            print(f"Updated user_settings for {user_settings.username}")
    
    print(f"Updated {updated_count} out of {len(user_settings_docs)} user_settings documents")

if __name__ == '__main__':
    update_user_settings()
    print("Migration completed successfully")

