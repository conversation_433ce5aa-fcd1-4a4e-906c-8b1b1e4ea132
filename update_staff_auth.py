from config.config import Config
"""
<PERSON><PERSON><PERSON> to update the main application to use the new staff authentication system.
This script will:
1. Register the new staff authentication routes
2. Update the templates to use the new login and set_password templates
3. Provide instructions for switching to the new system
"""

import os
import shutil
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def backup_file(file_path):
    """Create a backup of a file before modifying it"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup of {file_path} at {backup_path}")
        return True
    return False

def update_main_py():
    """Update main.py to register the new staff authentication routes"""
    try:
        main_py_path = "main.py"
        
        # Backup the file
        if not backup_file(main_py_path):
            logger.error(f"File {main_py_path} not found")
            return False
        
        # Read the current content
        with open(main_py_path, 'r') as f:
            content = f.read()
        
        # Check if the file already imports the new auth routes
        if "from staff.routes.new_auth_routes import register_staff_routes" in content:
            logger.info(f"File {main_py_path} already imports new_auth_routes")
            return True
        
        # Replace the import statement
        old_import = "from staff.routes.auth_routes import register_staff_routes"
        new_import = "# Old staff auth routes\n# from staff.routes.auth_routes import register_staff_routes\n\n# New staff auth routes using staff_members array\nfrom staff.routes.new_auth_routes import register_staff_routes"
        
        if old_import in content:
            content = content.replace(old_import, new_import)
            
            # Write the updated content
            with open(main_py_path, 'w') as f:
                f.write(content)
                
            logger.info(f"Updated {main_py_path} to use new_auth_routes")
            return True
        else:
            logger.warning(f"Could not find import statement in {main_py_path}")
            return False
    
    except Exception as e:
        logger.error(f"Error updating {main_py_path}: {str(e)}")
        return False

def update_templates():
    """Update templates to use the new login and set_password templates"""
    try:
        # Define the template paths
        old_login_path = "staff/templates/login.html"
        new_login_path = "staff/templates/new_login.html"
        old_set_password_path = "staff/templates/set_password.html"
        new_set_password_path = "staff/templates/new_set_password.html"
        
        # Backup the old templates
        backup_file(old_login_path)
        backup_file(old_set_password_path)
        
        # Replace the old templates with the new ones
        if os.path.exists(new_login_path) and os.path.exists(old_login_path):
            shutil.copy2(new_login_path, old_login_path)
            logger.info(f"Replaced {old_login_path} with {new_login_path}")
        else:
            logger.warning(f"Could not replace {old_login_path} with {new_login_path}")
        
        if os.path.exists(new_set_password_path) and os.path.exists(old_set_password_path):
            shutil.copy2(new_set_password_path, old_set_password_path)
            logger.info(f"Replaced {old_set_password_path} with {new_set_password_path}")
        else:
            logger.warning(f"Could not replace {old_set_password_path} with {new_set_password_path}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error updating templates: {str(e)}")
        return False

def print_instructions():
    """Print instructions for switching to the new system"""
    print("\n" + "="*80)
    print("INSTRUCTIONS FOR SWITCHING TO THE NEW STAFF AUTHENTICATION SYSTEM")
    print("="*80)
    print("\n1. Run the migration script to migrate existing staff records to the new system:")
    print("   python migrate_staff_to_array.py")
    print("\n2. Verify that the migration was successful by checking the logs.")
    print("\n3. If the migration was successful, run the migration script again with delete_old_records=True")
    print("   to delete the old staff records:")
    print("   - Edit migrate_staff_to_array.py")
    print("   - Uncomment the line: # delete_old_records = True")
    print("   - Run: python migrate_staff_to_array.py")
    print("\n4. Restart the application to use the new staff authentication system:")
    print("   - Stop the current application")
    print("   - Start the application again")
    print("\n5. Test the new staff authentication system by logging in as a staff member.")
    print("\n6. If everything works correctly, you can remove the old staff authentication routes")
    print("   and templates:")
    print("   - Delete staff/routes/auth_routes.py")
    print("   - Delete staff/templates/new_login.html")
    print("   - Delete staff/templates/new_set_password.html")
    print("   - Update main.py to remove the commented out old import")
    print("\n" + "="*80)

if __name__ == "__main__":
    print("Updating the application to use the new staff authentication system...")
    
    # Update main.py
    if update_main_py():
        print("✅ Successfully updated main.py")
    else:
        print("❌ Failed to update main.py")
    
    # Update templates
    if update_templates():
        print("✅ Successfully updated templates")
    else:
        print("❌ Failed to update templates")
    
    # Print instructions
    print_instructions()

