
{% extends "base.html" %}

{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<!-- Keep all existing styles -->
<style>
    /* Enterprise announcement styles */
    .alert-purple {
        background: linear-gradient(135deg, #8e44ad, #9b59b6);
        color: white;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .alert-purple .btn-outline-light {
        color: white;
        border-color: white;
    }

    .alert-purple .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .text-purple {
        color: #f8f9fa;
    }

    .status-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .status-icon i {
        font-size: 0.875rem;
    }
    /* Favorite icon styles */
    .favorite-icon {
        position: absolute;
        top: 8px;
        right: 8px;
        color: rgba(255, 255, 255, 0.5);
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        z-index: 10;
    }
    .favorite-icon:hover {
        color: rgba(255, 255, 255, 0.9);
        transform: scale(1.2);
    }
    .favorite-icon.active {
        color: #FFD700;
        text-shadow: 0 0 5px rgba(255, 215, 0, 0.7);
    }
    /* Collapsible section styles */
    .section-header {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;
    }
    .section-header:hover {
        opacity: 0.9;
    }
    .section-header .toggle-icon {
        transition: transform 0.3s ease;
        margin-left: 10px;
    }
    .section-header.collapsed .toggle-icon {
        transform: rotate(-90deg);
    }
    .section-content {
        overflow: hidden;
        transition: max-height 0.5s ease;
        max-height: 2000px; /* Large enough to contain content */
    }
    .section-content.collapsed {
        max-height: 0;
    }
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .quick-link-btn {
        animation: fadeInUp 0.5s ease backwards;
    }

    .quick-link-btn:nth-child(n) {
        animation-delay: calc(0.1s * var(--animation-order, 0));
    }

    /* Quick link button styles - improved for accessibility and visual appeal */
    .quick-link-btn {
        height: 80px; /* Reduced from 100px */
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: all 0.3s ease;
        border-radius: 8px; /* Smaller radius */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Lighter shadow */
        padding: 12px 10px; /* Reduced padding */
        margin: 3px; /* Reduced margin */
        border: 1px solid rgba(255, 255, 255, 0.15);
        position: relative;
        overflow: visible;
        width: 100%;
        backdrop-filter: blur(5px);
    }

    .quick-link-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 8px; /* Smaller radius */
    }

    .quick-link-btn:hover::before {
        opacity: 1;
    }

    .quick-link-btn:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.25);
    }

    .quick-link-btn:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.25), 0 8px 16px rgba(0, 0, 0, 0.2);
    }

    .quick-link-btn i {
        margin-bottom: 8px; /* Reduced margin */
        font-size: 1.5em; /* Smaller icon */
        color: rgba(255, 255, 255, 0.95);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease;
    }

    .quick-link-btn:hover i {
        transform: scale(1.1);
    }

    .quick-link-btn span {
        font-size: 12px; /* Smaller text */
        font-weight: 600;
        letter-spacing: 0.3px;
        color: rgba(255, 255, 255, 0.95);
        font-family: 'Inter', 'Roboto', sans-serif;
        text-align: center;
        line-height: 1.3;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
        height: 30px; /* Reduced height */
    }

    /* Accessibility improvements */
    .quick-link-btn:focus-visible {
        outline: 3px solid #fff;
        outline-offset: 2px;
    }

    /* Hover effect for touch devices */
    @media (hover: none) {
        .quick-link-btn:active {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
    }

    /* Maintenance mode styles */
    .quick-link-btn.maintenance-mode {
        position: relative;
        cursor: not-allowed;
        opacity: 0.7;
    }

    .quick-link-btn.maintenance-mode::after {
        content: 'Under Maintenance';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .quick-link-btn.maintenance-mode:hover {
        transform: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .quick-link-btn.maintenance-mode i,
    .quick-link-btn.maintenance-mode span {
        opacity: 0.5;
    }

    /* Under Development mode styles */
    .quick-link-btn.under-development {
        position: relative;
        cursor: not-allowed;
        opacity: 0.7;
    }

    .quick-link-btn.under-development::after {
        content: 'Under Development';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .quick-link-btn.under-development:hover {
        transform: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .quick-link-btn.under-development i,
    .quick-link-btn.under-development span {
        opacity: 0.5;
    }

    /* Highlight style for important links */
    .quick-link-btn[data-highlight="true"] {
        animation: highlight-pulse 2s infinite;
        box-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
        border: 2px solid #ffd700;
    }

    @keyframes highlight-pulse {
        0% {
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
        }
        50% {
            box-shadow: 0 0 25px rgba(255, 215, 0, 1);
        }
        100% {
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
        }
    }

    /* Enhanced styles for pending orders badge and animation */
    .quick-link-btn.has-pending {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        50% {
            box-shadow: 0 0 50px rgba(255, 0, 85, 0.9);
        }
        100% {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    }

    .new-features-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 50%, #4CAF50 100%);
        color: white;
        border-radius: 12px;
        padding: 3px 8px;
        font-size: 10px;
        font-weight: bold;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        z-index: 10;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .connection-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        font-size: 10px;
        padding: 3px 8px;
        border-radius: 12px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        z-index: 10;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .connection-badge.connected {
        background: linear-gradient(135deg, #28a745 0%, #218838 50%, #28a745 100%);
        color: white;
    }

    .connection-badge.disconnected {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 50%, #dc3545 100%);
        color: white;
    }

    .corner-badge-left {
        position: absolute;
        top: -10px;
        left: -10px;
        font-size: 10px;
        padding: 3px 8px;
        border-radius: 12px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        z-index: 10;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .pending-badge {
        position: absolute;
        top: -14px;
        right: -14px;
        background: linear-gradient(135deg, #ff0055 0%, #ff2d70 50%, #ff0055 100%);
        background-size: 200% 200%;
        color: white;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 900;
        box-shadow: 0 4px 12px rgba(255, 0, 85, 0.7),
                    0 0 24px rgba(255, 0, 85, 0.5),
                    0 0 36px rgba(255, 0, 85, 0.3);
        animation: badgePulse 2s infinite;
        z-index: 10;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.95);
        letter-spacing: 0px;
    }

    @keyframes badgePulse {
        0%, 100% {
            transform: scale(1);
            background-position: 0% 50%;
        }
        50% {
            transform: scale(1.15);
            background-position: 100% 50%;
        }
    }

    /* Category-specific colors */
    .quick-link-btn[data-category="Purchasing"] {
        background: linear-gradient(135deg, #4a90e2, #357abd);
    }
    .quick-link-btn[data-category="Scanning"] {
        background: linear-gradient(135deg, #2193b0, #6dd5ed);
    }
    .quick-link-btn[data-category="Buylist"] {
        background: linear-gradient(135deg, #ee0979, #ff6a00);
    }
    .quick-link-btn[data-category="Price Research"] {
        background: linear-gradient(135deg, #11998e, #38ef7d);
    }
    .quick-link-btn[data-category="Other"] {
        background: linear-gradient(135deg, #834d9b, #d04ed6);
    }
    .quick-link-btn[data-category="Help"] {
        background: linear-gradient(135deg, #2ecc71, #27ae60);
    }
    .quick-link-btn[data-category="Inventory"] {
        background: linear-gradient(135deg, #f1c40f, #f39c12);
    }
    .quick-link-btn[data-category="Shopify"] {
        background: linear-gradient(135deg, #96c93d, #00b09b);
    }
    .quick-link-btn[data-category="Workflows"] {
        background: linear-gradient(135deg, #6366f1, #4f46e5);
    }
    .card {
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .card:hover {
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    .card-body {
        padding: 1.75rem;
        flex: 1 1 auto;
    }
    .card-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    .display-4 {
        font-size: 2.5rem;
        font-weight: 700;
    }
    #drop-area {
        border: 2px dashed #007bff;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }
    #drop-area:hover, #drop-area.highlight {
        background-color: #f1f8ff;
        border-color: #0056b3;
    }
    #drop-area p {
        margin-bottom: 0;
        color: #6c757d;
    }
    #uploadForm {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    .mover-list {
        max-height: 300px;
        overflow-y: auto;
    }
    .mover-item {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        color: #000;
    }
    .mover-item:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .mover-title {
        font-weight: bold;
        margin-bottom: 5px;
        color: #000;
    }
    .mover-details {
        font-size: 0.9em;
        color: #000;
    }
    .mover-change {
        font-weight: bold;
    }
    .mover-change.positive {
        color: #28a745;
    }
    .mover-change.negative {
        color: #dc3545;
    }

    /* Search popup styles */
    .search-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 1.75rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        max-width: 90%;
        width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        z-index: 1000;
    }
    .search-popup:hover {
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    .search-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }
    .search-result-item {
        display: flex;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        background-color: #ffffff;
        align-items: center;
        transition: all 0.3s ease;
        border: 1px solid #eee;
    }
    .search-result-item:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    .search-result-image {
        width: 80px;
        height: 80px;
        object-fit: contain;
        margin-right: 15px;
        border-radius: 4px;
    }
    .search-result-details {
        flex-grow: 1;
        color: #000;
    }
    .search-result-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #000;
    }
    .search-result-info {
        font-size: 0.9em;
        color: #000;
    }
    .search-result-info > div {
        margin-bottom: 0.25rem;
    }
    .search-result-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 20px;
        background: #007bff;
        color: white;
        text-decoration: none;
        margin-left: 15px;
        transition: all 0.3s ease;
    }
    .search-result-link:hover {
        background: #0056b3;
        transform: scale(1.1);
    }
    .close-popup {
        position: absolute;
        top: 15px;
        right: 15px;
        background: none;
        border: none;
        font-size: 1.5em;
        cursor: pointer;
        color: #000;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 15px;
        transition: all 0.3s ease;
    }
    .close-popup:hover {
        background-color: #f8f9fa;
    }
    .search-results-container {
        margin-top: 20px;
    }
    .search-popup .alert {
        margin-top: 0;
        margin-bottom: 0;
        border-radius: 8px;
    }

    /* Trend prices styles */
    .trend-prices {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #eee;
    }
    .trend-price-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        color: #000;
    }
    .trend-price-subtype {
        font-weight: 600;
        color: #000;
    }
    .trend-price-value {
        color: #28a745;
        font-weight: 600;
    }

    /* User inventory styles */
    .user-inventory {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #eee;
    }
    .inventory-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        color: #000;
    }
    .inventory-details {
        display: flex;
        gap: 15px;
    }
    .inventory-sku {
        font-weight: 600;
        color: #000;
    }
    .inventory-price {
        color: #28a745;
        font-weight: 600;
    }
    .inventory-quantity {
        color: #007bff;
        font-weight: 600;
    }

    /* Sales history styles */
    .sales-history {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #eee;
    }
    .last-sale {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        color: #000;
    }
    .last-sale-details {
        font-weight: 600;
        color: #000;
    }
    .last-sale-price {
        color: #28a745;
        font-weight: 600;
    }
    .sales-count {
        color: #007bff;
        font-weight: 600;
        margin-top: 5px;
    }

    /* Search filters styles */
    .search-filters {
        margin-bottom: 15px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #eee;
    }
    .search-filters select {
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
        margin-right: 15px;
        min-width: 150px;
        color: #000;
        background-color: #fff;
    }
    .search-filters select option {
        color: #000;
        background-color: #fff;
    }
    .search-filters label {
        margin-right: 15px;
        user-select: none;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        color: #000;
    }
    .search-filters input[type="checkbox"] {
        margin-right: 5px;
        cursor: pointer;
    }
    .search-filters-title {
        font-weight: 600;
        margin-bottom: 10px;
        color: #000;
    }

    /* Add new Shopify modal styles */
    .shopify-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .shopify-modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 800px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .shopify-form {
        margin-top: 20px;
    }

    .shopify-form input {
        width: 100%;
        padding: 8px;
        margin: 10px 0;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .shopify-form button {
        background-color: #4F46E5;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .shopify-form button:hover {
        background-color: #4338CA;
    }

    .close-shopify-modal {
        float: right;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
    }

    .close-shopify-modal:hover {
        color: #666;
    }

    /* Support availability bar and Help Center styles - improved */
    .support-availability-bar {
        background-color: rgba(255, 193, 7, 0.1);
        border-left: 4px solid #ffc107;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .support-availability-bar.available {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.1);
    }

    .support-availability-bar.unavailable {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.1);
    }

    .support-availability-bar .status-indicator {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(255, 193, 7, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-size: 1.2rem;
    }

    .support-availability-bar.available .status-indicator {
        background-color: rgba(40, 167, 69, 0.2);
    }

    .support-availability-bar.unavailable .status-indicator {
        background-color: rgba(220, 53, 69, 0.2);
    }

    /* Pulse animation for status indicator */
    .pulse-animation {
        position: relative;
    }

    .pulse-animation::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        animation: pulse 2s infinite;
        z-index: -1;
    }

    .support-availability-bar.available .pulse-animation::after {
        box-shadow: 0 0 0 rgba(40, 167, 69, 0.4);
        animation: pulse-green 2s infinite;
    }

    .support-availability-bar.unavailable .pulse-animation::after {
        box-shadow: 0 0 0 rgba(220, 53, 69, 0.4);
        animation: pulse-red 2s infinite;
    }

    @keyframes pulse-green {
        0% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }

    @keyframes pulse-red {
        0% {
            box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
        }
    }

    /* Help Center Panel Styles */
    .help-center-panel {
        transition: all 0.3s ease;
        max-height: 0;
        overflow: hidden;
        opacity: 0;
        transform: translateY(-10px);
    }

    .help-center-panel.show {
        max-height: 1000px;
        opacity: 1;
        transform: translateY(0);
    }

    .help-options .list-group-item {
        transition: all 0.2s ease;
        border-radius: 6px;
        margin-bottom: 8px;
    }

    .help-options .list-group-item:hover {
        transform: translateX(5px);
        background-color: #343a40 !important;
    }

    /* Accordion Styles for FAQ */
    .accordion-button:not(.collapsed) {
        color: #fff;
        background-color: #343a40;
        box-shadow: none;
    }

    .accordion-button:focus {
        box-shadow: none;
        border-color: rgba(255, 255, 255, 0.15);
    }

    .accordion-button::after {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    }

    /* Form Styles */
    #quickSupportForm .form-control:focus,
    #quickSupportForm .form-select:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        border-color: #86b7fe;
    }

    #quickSupportForm .btn-primary {
        transition: all 0.3s ease;
    }

    #quickSupportForm .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Live Chat Styles */
    .live-chat-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 350px;
        height: 450px;
        background-color: #212529;
        border-radius: 12px;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        z-index: 9999;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .live-chat-container.chat-hidden {
        opacity: 0;
        transform: translateY(20px);
        pointer-events: none;
    }

    .live-chat-container.chat-visible {
        opacity: 1;
        transform: translateY(0);
        pointer-events: auto;
    }

    .live-chat-container.chat-minimized {
        height: 50px;
    }

    .live-chat-container.chat-minimized .chat-body,
    .live-chat-container.chat-minimized .chat-footer {
        display: none;
    }

    .chat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 15px;
        background-color: #343a40;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px 12px 0 0;
    }

    .chat-title {
        display: flex;
        align-items: center;
        color: white;
        font-weight: 600;
    }

    .chat-actions {
        display: flex;
        gap: 8px;
    }

    .chat-minimize-btn, .chat-close-btn {
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .chat-minimize-btn:hover, .chat-close-btn:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .chat-body {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
    }

    .chat-messages {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .chat-message {
        max-width: 80%;
        padding: 10px 15px;
        border-radius: 12px;
        position: relative;
    }

    .system-message {
        align-self: flex-start;
        background-color: #343a40;
        border-bottom-left-radius: 4px;
    }

    .user-message {
        align-self: flex-end;
        background-color: #0d6efd;
        border-bottom-right-radius: 4px;
    }

    .system-error {
        align-self: flex-start;
        background-color: #dc3545;
        border-bottom-left-radius: 4px;
    }

    .message-content {
        color: white;
    }

    .message-content p {
        margin: 0 0 5px 0;
    }

    .message-time {
        display: block;
        font-size: 10px;
        color: rgba(255, 255, 255, 0.6);
        text-align: right;
    }

    .chat-footer {
        padding: 10px 15px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .chat-input-container {
        display: flex;
        gap: 10px;
    }

    .chat-input {
        flex: 1;
        padding: 10px 15px;
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background-color: #343a40;
        color: white;
        resize: none;
        outline: none;
        transition: all 0.2s ease;
    }

    .chat-input:focus {
        border-color: rgba(255, 255, 255, 0.3);
        box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
    }

    .chat-send-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #0d6efd;
        color: white;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .chat-send-btn:hover {
        background-color: #0b5ed7;
        transform: scale(1.05);
    }

    /* Button pulse animation */
    @keyframes button-pulse {
        0% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
        }
        50% {
            transform: scale(0.95);
            box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
        }
    }

    .btn-pulse {
        animation: button-pulse 0.5s cubic-bezier(0.66, 0, 0.33, 1) forwards;
    }

    /* Search styles */
    .search-container {
        width: 300px;
        max-width: 100%;
    }

    #dashboardSearch:focus {
        box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.15);
    }

    kbd {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        font-size: 0.85em;
    }

    #searchResults {
        background-color: #2c3034;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.15);
        padding: 1rem;
        transition: all 0.3s ease;
    }

    /* Section styles */
    .section-content {
        transition: all 0.3s ease;
    }

    .section-content.collapsed {
        max-height: 0;
        padding: 0 !important;
        overflow: hidden;
        border: none !important;
        margin: 0 !important;
    }

    .section-toggle .toggle-icon {
        transition: transform 0.3s ease;
    }

    .section-content.collapsed + .section-header-wrapper .section-toggle .toggle-icon {
        transform: rotate(-90deg);
    }

    .section-content.collapsed + .section-header-wrapper .section-toggle .toggle-text {
        content: "Expand";
    }

    /* Favorites section */
    .favorites-wrapper {
        transition: all 0.3s ease;
    }

    .favorites-wrapper:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    #editFavoritesBtn:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* Editing mode for favorites */
    .favorites-editing .quick-link-btn {
        border: 2px dashed rgba(255, 255, 255, 0.3);
        opacity: 0.8;
    }

    .favorites-editing .quick-link-btn:hover {
        opacity: 1;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .favorites-editing .remove-favorite {
        display: block !important;
        position: absolute;
        top: 8px;
        right: 8px;
        background-color: rgba(220, 53, 69, 0.8);
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        transition: all 0.2s ease;
    }

    .favorites-editing .remove-favorite:hover {
        background-color: #dc3545;
        transform: scale(1.1);
    }
    /* Compact section styling */
    .section-content {
        padding: 1.5rem !important; /* Reduced padding when not collapsed */
    }

    /* Tighter grid layout */
    .row.g-3 {
        --bs-gutter-y: 0.5rem; /* Reduced vertical gap */
    }
    
    /* More compact card styling */
    .card-body {
        padding: 1.25rem; /* Reduced padding */
    }
    
    /* Smaller section headers */
    .section-header-wrapper h5 {
        font-size: 1.1rem;
    }
</style>

<div class="container-fluid px-4 w-100">
    {# {% include 'components/enterprise_upgrade_announcement.html' %} #}

    {% if not current_user.subscription or current_user.subscription.name == 'Free' %}
    <div class="alert alert-primary mt-4 d-flex justify-content-between align-items-center" role="alert">
        <div class="text-center w-100">
            <i class="fas fa-star me-2"></i>
            <strong>Upgrade your plan to unlock all features!</strong> Get access to advanced scanning tools, analytics, and more.
        </div>
        <a href="{{ url_for('activation.activating') }}" class="btn btn-primary btn-sm">
            Upgrade Now
        </a>
    </div>
    {% endif %}


    <div class="container-fluid px-0">


        <!-- Add Shopify Connection Modal -->
        <div id="shopifyModal" class="shopify-modal">
            <div class="shopify-modal-content">
                <span class="close-shopify-modal">&times;</span>
                <h2>Connect Your Shopify Store</h2>

                <!-- SVG Infographic -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 680">
                    <!-- Background -->
                    <rect width="800" height="680" fill="#f3f4f6"/>

                    <!-- Main Title -->
                    <text x="400" y="50" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold" fill="#111827">
                        How to Find Your Shopify Store Information
                    </text>

                    <!-- Collaboration Code Section -->
                    <text x="400" y="90" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="#4F46E5">
                        Finding Your Collaboration Code
                    </text>

                    <!-- Step 1 -->
                    <circle cx="160" cy="150" r="30" fill="#4F46E5"/>
                    <text x="160" y="160" text-anchor="middle" font-family="Arial" font-size="20" fill="white">1</text>
                    <rect x="60" y="200" width="200" height="100" rx="10" fill="white" stroke="#E5E7EB"/>
                    <text x="160" y="235" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        Log into your
                    </text>
                    <text x="160" y="255" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        Shopify Admin
                    </text>
                    <text x="160" y="275" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        dashboard
                    </text>

                    <!-- Arrow 1 -->
                    <path d="M 270 250 L 320 250" stroke="#9CA3AF" stroke-width="2" marker-end="url(#arrow)"/>

                    <!-- Step 2 -->
                    <circle cx="400" cy="150" r="30" fill="#4F46E5"/>
                    <text x="400" y="160" text-anchor="middle" font-family="Arial" font-size="20" fill="white">2</text>
                    <rect x="300" y="200" width="200" height="100" rx="10" fill="white" stroke="#E5E7EB"/>
                    <text x="400" y="235" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        Click "Settings"
                    </text>
                    <text x="400" y="255" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        then select
                    </text>
                    <text x="400" y="275" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        "Users and permissions"
                    </text>

                    <!-- Arrow 2 -->
                    <path d="M 510 250 L 560 250" stroke="#9CA3AF" stroke-width="2" marker-end="url(#arrow)"/>

                    <!-- Step 3 -->
                    <circle cx="640" cy="150" r="30" fill="#4F46E5"/>
                    <text x="640" y="160" text-anchor="middle" font-family="Arial" font-size="20" fill="white">3</text>
                    <rect x="540" y="200" width="200" height="100" rx="10" fill="white" stroke="#E5E7EB"/>
                    <text x="640" y="235" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        Find your
                    </text>
                    <text x="640" y="255" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        Collaboration Code
                    </text>
                    <text x="640" y="275" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        in this section
                    </text>

                    <!-- Store Name Section -->
                    <text x="400" y="380" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="#4F46E5">
                        Finding Your Store Name
                    </text>

                    <!-- Step 1 Store Name -->
                    <circle cx="160" cy="440" r="30" fill="#4F46E5"/>
                    <text x="160" y="450" text-anchor="middle" font-family="Arial" font-size="20" fill="white">1</text>
                    <rect x="60" y="490" width="200" height="100" rx="10" fill="white" stroke="#E5E7EB"/>
                    <text x="160" y="525" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        Go to "Settings"
                    </text>
                    <text x="160" y="545" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        then click
                    </text>
                    <text x="160" y="565" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        "General"
                    </text>

                    <!-- Arrow 3 -->
                    <path d="M 270 540 L 320 540" stroke="#9CA3AF" stroke-width="2" marker-end="url(#arrow)"/>

                    <!-- Step 2 Store Name -->
                    <circle cx="400" cy="440" r="30" fill="#4F46E5"/>
                    <text x="400" y="450" text-anchor="middle" font-family="Arial" font-size="20" fill="white">2</text>
                    <rect x="300" y="490" width="200" height="100" rx="10" fill="white" stroke="#E5E7EB"/>
                    <text x="400" y="525" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        Look for
                    </text>
                    <text x="400" y="545" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        "Store details"
                    </text>
                    <text x="400" y="565" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        section
                    </text>

                    <!-- Arrow 4 -->
                    <path d="M 510 540 L 560 540" stroke="#9CA3AF" stroke-width="2" marker-end="url(#arrow)"/>

                    <!-- Step 3 Store Name -->
                    <circle cx="640" cy="440" r="30" fill="#4F46E5"/>
                    <text x="640" y="450" text-anchor="middle" font-family="Arial" font-size="20" fill="white">3</text>
                    <rect x="540" y="490" width="200" height="100" rx="10" fill="white" stroke="#E5E7EB"/>
                    <text x="640" y="525" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        Find your store name
                    </text>
                    <text x="640" y="545" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        (the part before
                    </text>
                    <text x="640" y="565" text-anchor="middle" font-family="Arial" font-size="16" fill="#111827">
                        .myshopify.com)
                    </text>

                    <!-- Note Box -->
                    <rect x="200" y="620" width="400" height="40" rx="10" fill="#EEF2FF" stroke="#C7D2FE"/>
                    <text x="400" y="645" text-anchor="middle" font-family="Arial" font-size="14" fill="#4F46E5">
                        💡 Your store name is your unique myshopify.com subdomain
                    </text>

                    <!-- Arrow Marker -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 L2,5 Z" fill="#9CA3AF"/>
                        </marker>
                    </defs>
                </svg>

            <!-- Form -->
            <form id="shopifyForm" class="shopify-form">
                <input type="text" id="storeName" name="storeName" placeholder="Your Shopify Store Name" required>
                <input type="text" id="collabCode" name="collabCode" placeholder="Collaboration Code" required>
                <button type="submit">Submit</button>
            </form>
        </div>
</div>

    <div class="row g-4">
        <div class="col-12">
            <div class="row g-4">
                <div class="col-12 d-flex">
                    <div class="card flex-grow-1">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="card-title mb-0">Quick Links</h4>
                                <div class="d-flex align-items-center">
                                    <!-- Search bar -->
                                    <div class="search-container me-3">
                                        <div class="input-group">
                                            <span class="input-group-text bg-dark border-secondary">
                                                <i class="fas fa-search text-light"></i>
                                            </span>
                                            <input type="text" id="dashboardSearch" class="form-control bg-dark text-light border-secondary" placeholder="Search features..." aria-label="Search features">
                                            <button class="btn btn-outline-secondary border-secondary text-light" type="button" id="clearSearch">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Keyboard shortcut indicator -->
                                    <div class="keyboard-shortcut ms-2 d-none d-md-flex align-items-center">
                                        <span class="text-muted small me-2">Press</span>
                                        <kbd class="bg-dark text-light border border-secondary rounded px-2 py-1 me-1">/</kbd>
                                        <span class="text-muted small">to search</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Help Center Section -->
                            <div class="help-center-container mb-4">
                                <!-- Support Availability Indicator -->
                                <div class="support-availability-bar p-3 rounded d-flex align-items-center justify-content-between mb-3" id="supportAvailabilityBar">
                                    <div class="d-flex align-items-center">
                                        <div class="status-indicator me-3 pulse-animation">
                                            <i class="fas fa-headset"></i>
                                        </div>
                                        <div>
                                            <strong class="d-block mb-1">Live Support Status</strong>
                                            <span id="supportStatus" class="d-block">Checking availability...</span>
                                        </div>
                                    </div>
                                    <div class="d-flex">
                                        <button class="btn btn-outline-light" id="helpCenterToggleBtn">
                                            <i class="fas fa-question-circle me-1"></i> Help Center
                                        </button>
                                    </div>
                                </div>

                                <!-- Collapsible Help Center Panel -->
                                <div class="help-center-panel p-3 rounded bg-dark border border-secondary mb-3 d-none" id="helpCenterPanel">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <h5 class="border-bottom border-secondary pb-2 mb-3">Quick Help</h5>
                                            <div class="list-group help-options">
                                                <a href="https://www.youtube.com/@TCGSync/videos" target="_blank" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
                                                    <i class="fas fa-video text-danger me-2"></i> Video Tutorials
                                                </a>
                                                <a href="#" onclick="$('#contactModal').modal('show'); return false;" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
                                                    <i class="fas fa-envelope text-primary me-2"></i> Email Support
                                                </a>
                                                <a href="https://discord.gg/7tJdxehgTm" target="_blank" class="list-group-item list-group-item-action bg-dark text-light border-secondary">
                                                    <i class="fab fa-discord text-info me-2"></i> Join Discord Community
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <h5 class="border-bottom border-secondary pb-2 mb-3">Common Issues</h5>
                                            <div class="accordion" id="accordionFAQ">
                                                <div class="accordion-item bg-dark border-secondary">
                                                    <h2 class="accordion-header" id="headingOne">
                                                        <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                                            Shopify Connection Issues
                                                        </button>
                                                    </h2>
                                                    <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionFAQ">
                                                        <div class="accordion-body text-light">
                                                            Check your Shopify connection settings and ensure your collaboration code is correct. Try reconnecting if issues persist.
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="accordion-item bg-dark border-secondary">
                                                    <h2 class="accordion-header" id="headingTwo">
                                                        <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                                            Scanning Not Working
                                                        </button>
                                                    </h2>
                                                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionFAQ">
                                                        <div class="accordion-body text-light">
                                                            Ensure your camera is properly connected and permissions are granted. Try refreshing the page or using a different browser.
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="accordion-item bg-dark border-secondary">
                                                    <h2 class="accordion-header" id="headingThree">
                                                        <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                                            Pricing Issues
                                                        </button>
                                                    </h2>
                                                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionFAQ">
                                                        <div class="accordion-body text-light">
                                                            Check your pricing rules and ensure your API connections are working. Verify that your subscription includes pricing features.
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <h5 class="border-bottom border-secondary pb-2 mb-3">Contact Support</h5>
                                            <form id="quickSupportForm" class="mb-3">
                                                <div class="mb-3">
                                                    <label for="supportSubject" class="form-label">Subject</label>
                                                    <select class="form-select bg-dark text-light border-secondary" id="supportSubject">
                                                        <option value="technical">Technical Issue</option>
                                                        <option value="account">Account Question</option>
                                                        <option value="billing">Billing Question</option>
                                                        <option value="feature">Feature Request</option>
                                                        <option value="other">Other</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="supportMessage" class="form-label">Message</label>
                                                    <textarea class="form-control bg-dark text-light border-secondary" id="supportMessage" rows="3" placeholder="Describe your issue..."></textarea>
                                                </div>
                                                <button type="submit" class="btn btn-primary w-100">
                                                    <i class="fas fa-paper-plane me-1"></i> Send Message
                                                </button>
                                            </form>
                                            <div class="text-center">
                                                <p class="mb-1">Current response time: <span class="badge bg-success">Within 24 hours</span></p>
                                                <small class="text-muted">For urgent issues, please use Discord or Live Chat</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Search results container (hidden by default) -->
                            <div id="searchResults" class="mb-4 d-none">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0">Search Results</h5>
                                    <button class="btn btn-sm btn-outline-secondary" id="closeSearchResults">
                                        <i class="fas fa-times"></i> Close
                                    </button>
                                </div>
                                <div id="searchResultsContent" class="row g-3">
                                    <!-- Search results will be populated here -->
                                </div>
                            </div>
                            <div class="row g-2">
                                {% set quick_link_groups = [
    ('Notifications', [
        ('buylist.buylist_orders', 'Buylist Orders', 'fa-clipboard-list'),
        ('reprice_logs.view_logs', 'Logs', 'fa-history', 'maintenance'),
        ('onboarding.onboarding', 'Onboarding', 'fa-user-graduate', 'highlight'),
        ('pending_files.view_pending_files', 'Pending Files', 'fa-file-alt'),
        ('shopify_link.link_shopify', 'Link Shopify', 'fa-link'),
        ('staff_management.staff_management', 'Staff Management', 'fa-users-cog'),
        ('shopify_store_credit.store_credit', 'Import Binder Store Credit', 'fa-file-import'),
        ('cardtrader_repricer.dashboard', 'CardTrader Repricer', 'fa-sync-alt'),
        ('binder_transfer.binder_transfer', 'Binder Transfer', 'fa-exchange-alt')
    ]),
    ('Workflows', [
        ('workflows.csv_to_shopify', 'CSV to Shopify', 'fa-file-import'),
        ('cardmarket.mobile_orders', 'Mobile Picking', 'fa-box'),
        ('cardmarket.cardmarket', 'Cardmarket', 'fa-credit-card')
    ]),
    ('Autopricing', [
        ('cardmarket_autopricing.cardmarket_autopricing', 'Cardmarket', 'fa-euro-sign'),
        ('dashboard.shopify_autopricing', 'Shopify', 'fa-tags')
    ]),
    ('Shopify', [
        ('shopify_customers.get_customers', 'Customers & Store Credit', 'fa-users'),
        ('shopify_orders.get_orders', 'Orders', 'fa-shopping-cart'),
        ('products.products', 'Products', 'fa-box'),
        ('dashboard.shopify_autopricing', 'Autopricing', 'fa-tags'),
        ('cardmarket_autopricing.cardmarket_autopricing', 'CardMarket Pricing', 'fa-euro-sign'),
        ('automations.automations', 'Automations', 'fa-robot')
    ]),
    ('Inventory', [
        ('manual_inventory.manual_inventory_ui.manual_inventory', 'Add Manually', 'fa-plus'),
    ('update_catalog.update_catalog', 'TCG\'s', 'fa-book'),
        ('board_games.board_games', 'Board Games', 'fa-dice'),
        ('other.other', 'Other', 'fa-box-open'),
        ('warhammer.warhammer', 'Warhammer', 'fa-hammer'),
        ('warehouse.warehouse', 'Warehouse', 'fa-warehouse')
    ]),
    ('Scanning', [
        ('card_scanning.card_scanning', 'Card Scanning', 'fa-camera'),
        ('mobile_card_scanning.mobile_card_scanning_page', 'Mobile Scanner', 'fa-mobile-alt'),
        ('dashboard.comics_scanning', 'Comics Scanning', 'fa-book-open'),
        ('dashboard.sports_card_scanning', 'Sports Card Scanning', 'fa-football-ball'),
        ('psa_checker.psa_checker', 'PSA Checker', 'fa-certificate'),
        ('card_grader.grade_card', 'Card Grader', 'fa-star')
    ]),
('Buylist', [
        ('', 'Instore Buylist', 'fa-clipboard-list'),
        ('user_settings.view_edit_settings', 'Buylist Settings', 'fa-user-cog'),
        ('advanced_rules.view_edit_advanced_rules', 'Advanced Pricing Rules', 'fa-cogs'),
        ('excluded_cards.view_edit_excluded_cards', 'Excluded Cards', 'fa-ban'),
('hotlist.hotlist_page', 'Hotlist', 'fa-fire')
    ]),
    ('Other', [
        ('pos.pos', 'POS', 'fa-cash-register'),
        ('dashboard.csv_route', 'CSV', 'fa-file-csv'),
        ('downloads.downloads', 'Downloads', 'fa-download'),
    ('advanced.advanced_dashboard', 'Advanced', 'fa-tools'),
    ('standardizer.dashboard', 'Shopify Standardizer', 'fa-sync-alt'),
    ('', 'Help Videos', 'fa-video'),
    ('', 'Request Help', 'fa-question-circle')
    ])
] %}
                                {% if is_free_user %}
                                    {# Available for You section #}
                                    <div class="col-12 mb-3">
                                        <h5 class="mb-2">Available for You</h5>
                                        <div class="row g-2">
                                            {% for group_name, links in quick_link_groups %}
                                                {% if group_name in ['Price Research', 'Inventory'] %}
                                                    <div class="col-12 mb-2">
                                                        <h6 class="text-muted">{{ group_name }}</h6>
                                                    </div>
                                                {% for link_data in links %}
                                                    {% set route = link_data[0] %}
                                                    {% set title = link_data[1] %}
                                                    {% set icon = link_data[2] %}
                                                    {% set highlight = link_data[3] if link_data|length > 3 else None %}
                                                        {% if title != 'Board Games' %}
                                                            <div class="col-xl-2 col-lg-2 col-md-3 col-sm-4 col-6">
                                                                <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}">
                                                                    <i class="fas {{ icon }}"></i>
                                                                    <span>{{ title }}</span>
                                                                </a>
                                                            </div>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% elif group_name == 'Other' and 'Downloads' in links|map(attribute=1)|list %}
                                                    <div class="col-12 mb-2">
                                                        <h6 class="text-muted">{{ group_name }}</h6>
                                                    </div>
                                                    {% for route, title, icon in links %}
                                                        {% if title == 'Downloads' %}
                                                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                                <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}">
                                                                    <i class="fas {{ icon }}"></i>
                                                                    <span>{{ title }}</span>
                                                                </a>
                                                            </div>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% elif group_name == 'Scanning' and 'Card Scanning' in links|map(attribute=1)|list %}
                                                    <div class="col-12 mb-2">
                                                        <h6 class="text-muted">{{ group_name }}</h6>
                                                    </div>
                                                    {% for route, title, icon in links %}
                                                        {% if title == 'Card Scanning' %}
                                                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                                <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}">
                                                                    <i class="fas {{ icon }}"></i>
                                                                    <span>{{ title }}</span>
                                                                </a>
                                                            </div>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% elif group_name == 'Notifications' %}
                                                    <div class="col-12 mb-2">
                                                        <h6 class="text-muted">{{ group_name }}</h6>
                                                    </div>
                                                    {% for route, title, icon in links %}
                                                        {% if title in ['Help Videos', 'CSV'] %}
                                                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                                {% if title == 'Help Videos' %}
                                                                    <a href="https://www.youtube.com/@TCGSync/videos" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" target="_blank">
                                                                        <i class="fas {{ icon }}"></i>
                                                                        <span>{{ title }}</span>
                                                                    </a>
                                                                {% else %}
                                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}">
                                                                        <i class="fas {{ icon }}"></i>
                                                                        <span>{{ title }}</span>
                                                                    </a>
                                                                {% endif %}
                                                            </div>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>

                                    {# Needs Upgrade section #}
                                    <div class="col-12 mb-3">
                                        <h5 class="mb-2">Needs Upgrade</h5>
                                        <div class="row g-2">
                                            {% for group_name, links in quick_link_groups %}
                                                {% if group_name not in ['Price Research', 'Inventory'] %}
                                                    <div class="col-12 mb-2">
                                                        <h6 class="text-muted">{{ group_name }}</h6>
                                                    </div>
                                                    {% for route, title, icon in links %}
                                                {% if title == 'Board Games' %}
                                                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                                <a href="{{ url_for('activation.activating') }}" class="btn btn-block quick-link-btn" data-category="Inventory">
                                                                    <i class="fas fa-dice"></i>
                                                                    <span>Board Games</span>
                                                                </a>
                                                            </div>
                                                {% elif group_name == 'Notifications' and title not in ['Help Videos', 'CSV'] %}
                                                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                                <a href="{{ url_for('activation.activating') }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}">
                                                                    <i class="fas {{ icon }}"></i>
                                                                    <span>{{ title }}</span>
                                                                </a>
                                                            </div>
                                                        {% elif group_name == 'Other' and title != 'Downloads' %}
                                                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                                <a href="{{ url_for('activation.activating') }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}">
                                                                    <i class="fas {{ icon }}"></i>
                                                                    <span>{{ title }}</span>
                                                                </a>
                                                            </div>
                                                        {% elif group_name == 'Scanning' and title != 'Card Scanning' %}
                                                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                                <a href="{{ url_for('activation.activating') }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}">
                                                                    <i class="fas {{ icon }}"></i>
                                                                    <span>{{ title }}</span>
                                                                </a>
                                                            </div>
                                                        {% elif group_name not in ['Other', 'Scanning', 'Notifications'] %}
                                                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                                <a href="{{ url_for('activation.activating') }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}">
                                                                    <i class="fas {{ icon }}"></i>
                                                                    <span>{{ title }}</span>
                                                                </a>
                                                            </div>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                {% else %}
                                    <!-- Favorites Section (always visible) -->
                                    <div class="col-12 mb-4">
                                        <div class="favorites-header d-flex justify-content-between align-items-center mb-3">
                                            <h5 class="mb-0 ps-2 border-start border-4 border-warning d-flex align-items-center">
                                                <div>
                                                    <i class="fas fa-star text-warning me-2"></i>
                                                    <span class="me-2">Favorites</span>
                                                    <small class="text-muted fs-6 fw-normal">(<span id="favorites-count">0</span> items)</small>
                                                </div>
                                            </h5>
                                            <div class="favorites-actions">
                                                <button class="btn btn-sm btn-outline-secondary me-2" id="editFavoritesBtn">
                                                    <i class="fas fa-edit me-1"></i> Edit
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary d-none" id="doneFavoritesBtn">
                                                    <i class="fas fa-check me-1"></i> Done
                                                </button>
                                            </div>
                                        </div>
                                        <div class="favorites-wrapper p-3 rounded bg-dark border border-secondary">
                                            <div class="row g-3" id="favorites-container">
                                                <!-- Favorites will be populated here via JavaScript -->
                                                <div class="col-12 text-muted text-center py-4" id="no-favorites-message">
                                                    <i class="fas fa-star-half-alt fa-2x mb-3 text-warning opacity-50"></i>
                                                    <p class="mb-2">No favorites yet</p>
                                                    <p class="small">Star items below to add them to your favorites for quick access</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>



                                    {% for group_name, links in quick_link_groups %}
                                        <div class="col-12 mb-4">
                                            <div class="section-header-wrapper d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0 ps-2 border-start border-4
                                                    {% if group_name == 'Inventory' %}border-warning
                                                    {% elif group_name == 'Scanning' %}border-info
                                                    {% elif group_name == 'Shopify' %}border-success
                                                    {% elif group_name == 'Buylist' %}border-danger
                                                    {% elif group_name == 'Workflows' %}border-primary
                                                    {% elif group_name == 'Autopricing' %}border-secondary
                                                    {% else %}border-primary{% endif %}
                                                    d-flex align-items-center">
                                                    <div>
                                                        <i class="fas
                                                        {% if group_name == 'Inventory' %}fa-boxes text-warning
                                                        {% elif group_name == 'Scanning' %}fa-camera text-info
                                                        {% elif group_name == 'Shopify' %}fa-shopping-bag text-success
                                                        {% elif group_name == 'Buylist' %}fa-clipboard-list text-danger
                                                        {% elif group_name == 'Workflows' %}fa-project-diagram text-primary
                                                        {% elif group_name == 'Autopricing' %}fa-tags text-secondary
                                                        {% elif group_name == 'Notifications' %}fa-bell text-warning
                                                        {% else %}fa-tools text-primary{% endif %} me-2"></i>
                                                        <span class="me-2">{{ group_name }}</span>
                                                        <small class="text-muted fs-6 fw-normal">({{ links|length }} items)</small>
                                                    </div>
                                                </h5>
                                                <div class="section-actions">
                                                    <button class="btn btn-sm btn-outline-secondary section-toggle">
                                                        <i class="fas fa-chevron-down toggle-icon"></i>
                                                        <span class="toggle-text">Collapse</span>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="section-content p-3 rounded bg-dark border border-secondary mb-2">
                                                <div class="row g-3">
                                                {% for link_data in links %}
                                                    {% set route = link_data[0] %}
                                                    {% set title = link_data[1] %}
                                                    {% set icon = link_data[2] %}
                                                    {% set highlight = link_data[3] if link_data|length > 3 else None %}
                                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                                {% if title == 'Instore Buylist' %}
                                                    <a href="https://kiosk.tcgsync.com/{{ current_user.username }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="kiosk.{{ current_user.username }}" data-title="{{ title }}" data-icon="{{ icon }}" target="_blank">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, 'kiosk.{{ current_user.username }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Buylist Orders' %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn{% if pending_buylist_orders > 0 %} has-pending{% endif %}" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        {% if pending_buylist_orders > 0 %}
                                                            <div class="pending-badge">{{ pending_buylist_orders }}</div>
                                                        {% endif %}
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Staged Inventory' %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn{% if staged_count > 0 %} has-pending{% endif %}" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        {% if staged_count > 0 %}
                                                            <div class="pending-badge">{{ staged_count }}</div>
                                                        {% endif %}
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Scanned Cards' %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn{% if mobile_uploads_count > 0 %} has-pending{% endif %}" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        {% if mobile_uploads_count > 0 %}
                                                            <div class="pending-badge">{{ mobile_uploads_count }}</div>
                                                        {% endif %}
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Help Videos' %}
                                                    <a href="https://www.youtube.com/@TCGSync/videos" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="youtube.TCGSync" data-title="{{ title }}" data-icon="{{ icon }}" target="_blank">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, 'youtube.TCGSync', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Request Help' %}
                                                    <a href="#" onclick="$('#contactModal').modal('show'); return false;" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="contact.modal" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, 'contact.modal', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Request Help' %}
                                                    <a href="#" onclick="toggleMessagePopup(); return false;" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="contact.modal" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, 'contact.modal', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Link Shopify' %}
    <a href="{{ url_for(route if route != 'rota.rota_page' else 'hotlist.hotlist_page') }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <div class="connection-badge {% if shopify_connected %}connected{% else %}disconnected{% endif %}">
                                                            {% if shopify_connected %}Connected{% else %}Not Connected{% endif %}
                                                        </div>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Video Games' %}
                                                    <div class="btn btn-block quick-link-btn maintenance-mode" data-category="{{ group_name }}" data-route="video_games.maintenance" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                    </div>
                                                {% elif title == 'Custom Products' %}
                                                    <div class="btn btn-block quick-link-btn under-development" data-category="{{ group_name }}" data-route="custom_products.development" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                    </div>
                                                {% elif title == 'Board Games' %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Card Scanning' %}
                                                    <a href="{{ url_for('card_scanning.card_scanning') }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Mobile Scanner' %}
                                                    <a href="#" onclick="window.open('{{ url_for(route) }}?standalone=true', 'Mobile Scanner', 'width=1200,height=800'); return false;" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Mobile Picking' %}
                                                    {% if current_user.username == 'admintcg' %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}" data-bypass-check="true">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                    {% endif %}
                                                {% elif title == 'Cardmarket' %}
                                                    {% if current_user.username == 'admintcg' %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}" data-bypass-check="true">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                    {% endif %}
                                                {% elif group_name in ['Scanning', 'Other'] %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% elif title == 'Kiosk' %}
                                                    <div class="btn btn-block quick-link-btn maintenance-mode" data-category="{{ group_name }}" data-route="kiosk.maintenance" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                    </div>
                                                {% elif title == 'Automations' %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% else %}
                                                    <a href="{{ url_for(route) }}" class="btn btn-block quick-link-btn" data-category="{{ group_name }}" data-route="{{ route }}" data-title="{{ title }}" data-icon="{{ icon }}">
                                                        <i class="fas {{ icon }}"></i>
                                                        <span>{{ title }}</span>
                                                        <i class="fas fa-star favorite-icon" onclick="toggleFavorite(event, '{{ route }}', '{{ title }}', '{{ icon }}', '{{ group_name }}')"></i>
                                                    </a>
                                                {% endif %}
                                                    </div>
                                                {% endfor %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enterprise Video Modal -->
<div class="modal fade" id="enterpriseVideoModal" tabindex="-1" aria-labelledby="enterpriseVideoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="enterpriseVideoModalLabel" style="color: #000;">Enterprise Plan Features</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="ratio ratio-16x9">
                    <video id="enterpriseVideo" controls>
                        <source src="{{ url_for('static', filename='video/Recording 2025-04-14 193412.mp4') }}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Free Upgrade Popup Modal -->
<div class="modal fade" id="freeUpgradeModal" tabindex="-1" aria-labelledby="freeUpgradeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="freeUpgradeModalLabel" style="color: #000;">Enterprise Upgrade Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="color: #000;">
                <p>The following email will be sent to our team:</p>
                <div class="card p-3 mb-3" style="background-color: #f8f9fa;">
                    <p style="color: #000;"><strong>To:</strong> <EMAIL></p>
                    <p style="color: #000;"><strong>Subject:</strong> Enterprise Upgrade Request</p>
                    <p style="color: #000;"><strong>Message:</strong></p>
                    <p style="color: #000;">Please upgrade my account ({{ current_user.username }}) to Enterprise level.</p>
                    <p style="color: #000;">Thank you!</p>
                </div>
                <p>Would you like to proceed with this request?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="sendFreeUpgradeRequest()">Confirm & Send</button>
            </div>
        </div>
    </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script>
    // Initialize Stripe
    const stripe = Stripe('pk_live_51Mk2mKIh9K9cw3GroxSDcv6lPVCGPieOKxtPL2wEOalMBIjSngKbgzpnD55eJCWolNeFWYgRFVPBCDzeLlKohfh8004bflPSSM');

    // Function to notify admin about lifetime upgrade
    function notifyLifetimeUpgrade(event) {
        // Don't prevent default so the link still works

        // Send notification email to admin using Mailgun
        fetch('/send_email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                to: '<EMAIL>',
                subject: 'Enterprise Upgrade Notification',
                text: `User {{ current_user.username }} has upgraded to Enterprise level with a payment of £1,000.`,
                html: `<p>User <strong>{{ current_user.username }}</strong> has upgraded to Enterprise level with a payment of £1,000.</p>`
            })
        }).catch(err => console.error('Error sending lifetime upgrade notification:', err));
    }

    // Function to show the free upgrade popup
    function showFreeUpgradePopup() {
        const modal = new bootstrap.Modal(document.getElementById('freeUpgradeModal'));
        modal.show();
    }

    // Function to send the free upgrade request email
    async function sendFreeUpgradeRequest() {
        try {
            const response = await fetch('/api/send-upgrade-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: '{{ current_user.username }}',
                    type: 'free'
                })
            });

            if (response.ok) {
                // Close the modal
                bootstrap.Modal.getInstance(document.getElementById('freeUpgradeModal')).hide();

                // Hide the enterprise upgrade announcement and save preference in localStorage
                const enterpriseAnnouncement = document.getElementById('enterprise-upgrade-announcement');
                if (enterpriseAnnouncement) {
                    enterpriseAnnouncement.style.display = 'none';
                    localStorage.setItem('hideEnterpriseAnnouncement', 'true');
                }

                // Show success message
                const successAlert = document.createElement('div');
                successAlert.className = 'alert alert-success alert-dismissible fade show mt-4';
                successAlert.role = 'alert';
                successAlert.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-2 text-success"></i>
                        <strong>Success!</strong>
                        <span class="ms-2">Your Enterprise upgrade request has been sent successfully! Your account will be upgraded within 1 working day.</span>
                        <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // Insert the success message at the top of the container
                const container = document.querySelector('.container-fluid.px-4.w-100');
                container.insertBefore(successAlert, container.firstChild);

                // Scroll to the top to show the message
                window.scrollTo(0, 0);
            } else {
                alert('There was an error sending your request. Please try again or contact support.');
            }
        } catch (error) {
            console.error('Error sending upgrade request:', error);
            alert('There was an error sending your request. Please try again or contact support.');
        }
    }

    // Function to handle donation checkout
    async function handleDonation(amount) {
        try {
            // Send email notification in the background
            fetch('/api/send-upgrade-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: '{{ current_user.username }}',
                    type: 'donation',
                    amount: amount
                })
            }).catch(err => console.error('Error sending donation notification:', err));

            // Create a checkout session
            const response = await fetch('/create-checkout-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    plan: 'EnterpriseDonation',
                    amount: amount * 100, // Convert to pence
                    upgrade: 'enterprise'
                })
            });

            const session = await response.json();

            // Redirect to Stripe checkout
            if (session.id) {
                await stripe.redirectToCheckout({ sessionId: session.id });
            } else {
                // Fallback to profile page if checkout session creation fails
                window.location.href = `{{ url_for('profile.profile') }}?upgrade=enterprise`;
            }
        } catch (error) {
            console.error('Error creating checkout session:', error);
            // Fallback to profile page
            window.location.href = `{{ url_for('profile.profile') }}?upgrade=enterprise`;
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Check if the compatibility notice should be hidden
        const hideNotice = localStorage.getItem('hideCompatibilityNotice') === 'true';
        const noticeElement = document.getElementById('systemCompatibilityNotice');
        const hideCheckbox = document.getElementById('hideCompatibilityNotice');

        // If user previously chose to hide the notice, hide it
        if (hideNotice && noticeElement) {
            noticeElement.style.display = 'none';
        }

        // Add event listener to the checkbox
        if (hideCheckbox) {
            hideCheckbox.addEventListener('change', function() {
                // Store the user's preference in localStorage
                localStorage.setItem('hideCompatibilityNotice', this.checked);

                // If checked, hide the notice
                if (this.checked && noticeElement) {
                    noticeElement.style.display = 'none';
                }
            });
        }

        // Apply highlight to onboarding link
        document.querySelectorAll('.quick-link-btn').forEach(function(link) {
            if (link.querySelector('span').textContent.trim() === 'Onboarding') {
                link.setAttribute('data-highlight', 'true');
            }
        });

        // Initialize all sections as collapsed except Notifications
        document.querySelectorAll('.section-header').forEach(function(header) {
            header.classList.add('collapsed');
            const content = header.nextElementSibling;
            if (content && content.classList.contains('section-content')) {
                content.classList.add('collapsed');
            }
        });

        // Load favorite quicklinks from user profile
        loadFavorites();


        // Keep all existing functions unchanged
        window.performGlobalSearch = function() {
            const searchTerm = document.getElementById('globalSearch').value.trim();

            if (!searchTerm) {
                return;
            }

            // Create overlay and popup
            const overlay = document.createElement('div');
            overlay.className = 'search-overlay';
            document.body.appendChild(overlay);

            const popup = document.createElement('div');
            popup.className = 'search-popup';
            popup.innerHTML = `
                <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                <div class="card-title mb-3">Search Results</div>
                <div class="text-center" style="color: #000;">
                    <i class="fas fa-spinner fa-spin"></i> Searching...
                    <p class="mt-2 small" style="color: #000;">This may take a moment if there are many results</p>
                </div>
            `;
            document.body.appendChild(popup);

            fetch('/dashboard/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ searchTerm: searchTerm })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    popup.innerHTML = `
                        <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                        <div class="card-title mb-3">Search Results</div>
                        <div class="alert alert-danger">${data.error}</div>
                    `;
                    return;
                }

                if (!data.products.length) {
                    popup.innerHTML = `
                        <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                        <div class="card-title mb-3">Search Results</div>
                        <div class="alert alert-info">No results found</div>
                    `;
                    return;
                }

                let resultsHtml = `
                    <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                    <div class="card-title mb-3">Search Results</div>
                    <div class="search-filters">
                        <div class="search-filters-title">Filter Results</div>
                        <select id="gameFilter" onchange="filterResults()">
                            <option value="">All Games</option>
                            ${data.games.map(game => `<option value="${game}">${game}</option>`).join('')}
                        </select>
                        <select id="expansionFilter" onchange="filterResults()">
                            <option value="">All Expansions</option>
                            ${[...new Set(data.products.map(p => p.expansion))].sort().map(exp => `<option value="${exp}">${exp}</option>`).join('')}
                        </select>
                        <label>
                            <input type="checkbox" id="singlesFilter" onchange="filterResults()">
                            Singles
                        </label>
                        <label>
                            <input type="checkbox" id="sealedFilter" onchange="filterResults()">
                            Sealed
                        </label>
                    </div>
                    <div class="search-results-container">
                `;

                // Store all results for filtering
                window.searchResults = data.products;

                // Initial display of all results
                resultsHtml += getFilteredResultsHtml(data.products);
                resultsHtml += '</div>';
                popup.innerHTML = resultsHtml;
            })
            .catch(error => {
                console.error('Search error:', error);
                popup.innerHTML = `
                    <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                    <div class="card-title mb-3">Search Results</div>
                    <div class="alert alert-danger">An error occurred while searching</div>
                `;
            });
        };

        // Filter results function
        window.filterResults = function() {
            const gameFilter = document.getElementById('gameFilter').value;
            const singlesFilter = document.getElementById('singlesFilter').checked;
            const sealedFilter = document.getElementById('sealedFilter').checked;

            let filteredResults = window.searchResults;

            // Filter by game
            if (gameFilter) {
                filteredResults = filteredResults.filter(product => product.game === gameFilter);
            }

            // Filter by expansion
            const expansionFilter = document.getElementById('expansionFilter').value;
            if (expansionFilter) {
                filteredResults = filteredResults.filter(product => product.expansion === expansionFilter);
            }

            // Filter by singles/sealed
            if (singlesFilter || sealedFilter) {
                filteredResults = filteredResults.filter(product =>
                    (singlesFilter && product.isSingle) || (sealedFilter && product.isSealed)
                );
            }

            // Update results display
            const container = document.querySelector('.search-results-container');
            container.innerHTML = getFilteredResultsHtml(filteredResults);
        };

        // Helper function to generate results HTML
        window.getFilteredResultsHtml = function(products) {
            return products.map(product => `
                <div class="search-result-item">
                    <img src="${product.imageUrl || '/static/placeholder.jpg'}"
                         alt="${product.title}"
                         class="search-result-image"
                         onerror="this.onerror=null; this.src='/static/placeholder.jpg';">
                    <div class="search-result-details">
                        <div class="search-result-title">${product.title}</div>
                        <div class="search-result-info">
                            <div><strong>Game:</strong> ${product.game}</div>
                            <div><strong>Expansion:</strong> ${product.expansion}</div>
                            <div><strong>Number:</strong> ${product.number}</div>
                            <div><strong>Product ID:</strong> ${product.productId}</div>
                        </div>
                        ${product.trendPrices && product.trendPrices.length > 0 ? `
                            <div class="trend-prices">
                                <div><strong>Market Prices:</strong></div>
                                ${product.trendPrices.map(trend => `
                                    <div class="trend-price-item">
                                        <span class="trend-price-subtype">${trend.subType}:</span>
                                        <span class="trend-price-value">${trend.lowPrice === null ? 'Unavailable' : '$' + trend.lowPrice.toFixed(2)}</span>
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                        <div class="user-inventory">
                            <div><strong>Your Inventory:</strong></div>
                            ${product.variants && product.variants.length > 0 ? `
                                ${product.variants.map(variant => `
                                    <div class="inventory-item">
                                        <span class="inventory-sku">${variant.title}</span>
                                        <div class="inventory-details">
                                            <span class="inventory-price">$${variant.price}</span>
                                            <span class="inventory-quantity">Qty: ${variant.inventory_quantity}</span>
                                        </div>
                                    </div>
                                `).join('')}
                            ` : `
                                <div class="inventory-item">
                                    <span class="inventory-sku">N/A</span>
                                    <div class="inventory-details">
                                        <span class="inventory-price">Unavailable</span>
                                        <span class="inventory-quantity">Qty: 0</span>
                                    </div>
                                </div>
                            `}
                        </div>
                        ${product.salesData ? `
                            <div class="sales-history">
                                <div><strong>Sales History:</strong></div>
                                ${product.salesData.last_sale ? `
                                    <div class="last-sale">
                                        <div class="last-sale-details">
                                            Last Sale (${new Date(product.salesData.last_sale.date).toLocaleString()}):
                                            ${product.salesData.last_sale.condition}
                                            ${product.salesData.last_sale.variant ? `- ${product.salesData.last_sale.variant}` : ''}
                                        </div>
                                        <span class="last-sale-price">$${product.salesData.last_sale.price.toFixed(2)}</span>
                                    </div>
                                ` : ''}
                                <div class="sales-count">
                                    Sales in last 24h: ${product.salesData.total_sold_24h}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    ${product.url ? `<a href="${product.url}" target="_blank" class="search-result-link" title="View Details">
                        <i class="fas fa-external-link-alt"></i>
                    </a>` : ''}
                </div>
            `).join('');
        };

        // Close popup function
        window.closeSearchPopup = function() {
            const overlay = document.querySelector('.search-overlay');
            const popup = document.querySelector('.search-popup');
            if (overlay) overlay.remove();
            if (popup) popup.remove();
        };

        function formatDate(dateString) {
            if (!dateString || dateString === 'N/A') return 'N/A';
            const date = new Date(dateString);
            return isNaN(date.getTime()) ? 'N/A' : date.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' });
        }

        function updateManipulatedList(manipulatedProducts) {
            const accordion = document.getElementById('manipulatedAccordion');
            if (!accordion) return;

            accordion.innerHTML = '';

            // Check if manipulatedProducts exists and is an array
            if (!manipulatedProducts || !Array.isArray(manipulatedProducts) || manipulatedProducts.length === 0) {
                accordion.innerHTML = '<p>No possible manipulated products found in the last 24 hours.</p>';
                return;
            }

            manipulatedProducts.forEach((game, gameIndex) => {
                // Skip if game or manipulated array is invalid
                if (!game || !game.manipulated || !Array.isArray(game.manipulated) || game.manipulated.length === 0) {
                    return;
                }

                const gameCard = document.createElement('div');
                gameCard.className = 'card mb-2';

                const gameHeader = document.createElement('div');
                gameHeader.className = 'card-header';
                gameHeader.id = `manipulatedGameHeading${gameIndex}`;

                const gameButton = document.createElement('button');
                gameButton.className = 'btn btn-link';
                gameButton.type = 'button';
                gameButton.setAttribute('data-bs-toggle', 'collapse');
                gameButton.setAttribute('data-bs-target', `#manipulatedGameCollapse${gameIndex}`);
                gameButton.setAttribute('aria-expanded', 'false');
                gameButton.setAttribute('aria-controls', `manipulatedGameCollapse${gameIndex}`);
                gameButton.textContent = game.gameName || 'Unknown Game';

                gameHeader.appendChild(gameButton);
                gameCard.appendChild(gameHeader);

                const gameCollapseDiv = document.createElement('div');
                gameCollapseDiv.id = `manipulatedGameCollapse${gameIndex}`;
                gameCollapseDiv.className = 'collapse';
                gameCollapseDiv.setAttribute('aria-labelledby', `manipulatedGameHeading${gameIndex}`);
                gameCollapseDiv.setAttribute('data-bs-parent', '#manipulatedAccordion');

                const gameCardBody = document.createElement('div');
                gameCardBody.className = 'card-body';

                game.manipulated.forEach((product) => {
                    // Skip if product is invalid
                    if (!product) {
                        return;
                    }

                    const productDiv = document.createElement('div');
                    productDiv.className = 'mover-item mb-3';

                    // Ensure all values have defaults to prevent undefined errors
                    const priceChange = typeof product.priceChange === 'number' ? product.priceChange : 0;
                    const firstPrice = product.firstPrice || 0;
                    const lastPrice = product.lastPrice || 0;
                    const salesCount = product.salesCount || 0;

                    productDiv.innerHTML = `
                        <div class="mover-title">${product.name || 'Unknown Product'}</div>
                        <div class="mover-details">
                            Expansion: ${product.expansionName || 'N/A'}
                        </div>
                        <div class="mover-details">
                            ${product.condition || 'N/A'} | ${product.language || 'N/A'}
                        </div>
                        <div class="mover-change positive">
                            +${priceChange.toFixed(2)}%
                        </div>
                        <div class="mover-details">
                            ${product.variant ? product.variant + ' | ' : ''}${product.condition || 'N/A'} | ${product.language || 'N/A'}
                        </div>
                        <div class="mover-details">
                            Initial: $${parseFloat(firstPrice).toFixed(2)}
                        </div>
                        <div class="mover-details">
                            Latest: $${parseFloat(lastPrice).toFixed(2)}
                        </div>
                        <div class="mover-details">
                            Sales Count: ${salesCount}
                        </div>
                    `;
                    gameCardBody.appendChild(productDiv);
                });

                gameCollapseDiv.appendChild(gameCardBody);
                gameCard.appendChild(gameCollapseDiv);
                accordion.appendChild(gameCard);

                // Add click event listener to toggle collapse
                gameButton.addEventListener('click', function() {
                    try {
                        const collapse = bootstrap.Collapse.getInstance(gameCollapseDiv) || new bootstrap.Collapse(gameCollapseDiv);
                        collapse.toggle();
                    } catch (error) {
                        console.error('Error toggling collapse:', error);
                    }
                });
            });
        }

    function checkFeatureAccess(event, featureName) {
        // Skip checks for Help Videos and bypassed features
        if (featureName === 'Help Videos' || event.currentTarget.getAttribute('data-bypass-check') === 'true') {
            return true;
        }

        // Allow access to all features
        return true;
    }

        function showUpgradePopup(featureName) {
            const popup = document.createElement('div');
            popup.style.position = 'fixed';
            popup.style.left = '50%';
            popup.style.top = '50%';
            popup.style.transform = 'translate(-50%, -50%)';
            popup.style.backgroundColor = '#2c3e50';
            popup.style.color = '#ffffff';
            popup.style.padding = '20px';
            popup.style.borderRadius = '5px';
            popup.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
            popup.style.zIndex = '1000';
            popup.innerHTML = `
                <h3>Subscription Upgrade Required</h3>
                <p>${featureName} is only available for subscribers.</p>
                <a href="{{ url_for('activation.activating') }}" style="display: inline-block; margin-top: 10px; padding: 5px 10px; background-color: #3498db; color: #ffffff; text-decoration: none; border-radius: 3px;">Upgrade Now</a>
                <button onclick="this.parentElement.remove()" style="display: block; margin-top: 10px; padding: 5px 10px; background-color: #95a5a6; color: #ffffff; border: none; border-radius: 3px; cursor: pointer;">Close</button>
            `;
            document.body.appendChild(popup);
        }

        // Close Shopify modal when clicking the X
        document.querySelector('.close-shopify-modal').onclick = function() {
            document.getElementById('shopifyModal').style.display = 'none';
        };

        // Close Shopify modal when clicking outside
        window.onclick = function(event) {
            if (event.target == document.getElementById('shopifyModal')) {
                document.getElementById('shopifyModal').style.display = 'none';
            }
            if (event.target.classList.contains('search-overlay')) {
                closeSearchPopup();
            }
        };

        // Initialize quick links
        initializeQuickLinks();

        // Handle Shopify form submission
        document.getElementById('shopifyForm').onsubmit = function(e) {
            e.preventDefault();

            const formData = {
                storeName: document.getElementById('storeName').value,
                collabCode: document.getElementById('collabCode').value
            };

            fetch('/submit-shopify-info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Information submitted successfully!');
                    document.getElementById('shopifyModal').style.display = 'none';
                } else {
                    alert('Error submitting information. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error submitting information. Please try again.');
            });
        };
    });

    function initializeQuickLinks() {
        const quickLinks = document.querySelectorAll('.quick-link-btn');
        quickLinks.forEach(function(link) {
            link.addEventListener('click', function(event) {
                // Allow all clicks to proceed
                return true;
            });
        });
    }

    function toggleSection(header) {
        header.classList.toggle('collapsed');
        const content = header.nextElementSibling;
        if (content && content.classList.contains('section-content')) {
            content.classList.toggle('collapsed');
        }
    }

        // Keep all existing functions unchanged
        window.performGlobalSearch = function() {
            const searchTerm = document.getElementById('globalSearch').value.trim();

            if (!searchTerm) {
                return;
            }

            // Create overlay and popup
            const overlay = document.createElement('div');
            overlay.className = 'search-overlay';
            document.body.appendChild(overlay);

            const popup = document.createElement('div');
            popup.className = 'search-popup';
            popup.innerHTML = `
                <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                <div class="card-title mb-3">Search Results</div>
                <div class="text-center" style="color: #000;">
                    <i class="fas fa-spinner fa-spin"></i> Searching...
                    <p class="mt-2 small" style="color: #000;">This may take a moment if there are many results</p>
                </div>
            `;
            document.body.appendChild(popup);

            fetch('/dashboard/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ searchTerm: searchTerm })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    popup.innerHTML = `
                        <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                        <div class="card-title mb-3">Search Results</div>
                        <div class="alert alert-danger">${data.error}</div>
                    `;
                    return;
                }

                if (!data.products.length) {
                    popup.innerHTML = `
                        <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                        <div class="card-title mb-3">Search Results</div>
                        <div class="alert alert-info">No results found</div>
                    `;
                    return;
                }

                let resultsHtml = `
                    <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                    <div class="card-title mb-3">Search Results</div>
                    <div class="search-filters">
                        <div class="search-filters-title">Filter Results</div>
                        <select id="gameFilter" onchange="filterResults()">
                            <option value="">All Games</option>
                            ${data.games.map(game => `<option value="${game}">${game}</option>`).join('')}
                        </select>
                        <select id="expansionFilter" onchange="filterResults()">
                            <option value="">All Expansions</option>
                            ${[...new Set(data.products.map(p => p.expansion))].sort().map(exp => `<option value="${exp}">${exp}</option>`).join('')}
                        </select>
                        <label>
                            <input type="checkbox" id="singlesFilter" onchange="filterResults()">
                            Singles
                        </label>
                        <label>
                            <input type="checkbox" id="sealedFilter" onchange="filterResults()">
                            Sealed
                        </label>
                    </div>
                    <div class="search-results-container">
                `;

                // Store all results for filtering
                window.searchResults = data.products;

                // Initial display of all results
                resultsHtml += getFilteredResultsHtml(data.products);
                resultsHtml += '</div>';
                popup.innerHTML = resultsHtml;
            })
            .catch(error => {
                console.error('Search error:', error);
                popup.innerHTML = `
                    <button class="close-popup" onclick="closeSearchPopup()">&times;</button>
                    <div class="card-title mb-3">Search Results</div>
                    <div class="alert alert-danger">An error occurred while searching</div>
                `;
            });
        };

        // Filter results function
        window.filterResults = function() {
            const gameFilter = document.getElementById('gameFilter').value;
            const singlesFilter = document.getElementById('singlesFilter').checked;
            const sealedFilter = document.getElementById('sealedFilter').checked;

            let filteredResults = window.searchResults;

            // Filter by game
            if (gameFilter) {
                filteredResults = filteredResults.filter(product => product.game === gameFilter);
            }

            // Filter by expansion
            const expansionFilter = document.getElementById('expansionFilter').value;
            if (expansionFilter) {
                filteredResults = filteredResults.filter(product => product.expansion === expansionFilter);
            }

            // Filter by singles/sealed
            if (singlesFilter || sealedFilter) {
                filteredResults = filteredResults.filter(product =>
                    (singlesFilter && product.isSingle) || (sealedFilter && product.isSealed)
                );
            }

            // Update results display
            const container = document.querySelector('.search-results-container');
            container.innerHTML = getFilteredResultsHtml(filteredResults);
        };

        // Helper function to generate results HTML
        window.getFilteredResultsHtml = function(products) {
            return products.map(product => `
                <div class="search-result-item">
                    <img src="${product.imageUrl || '/static/placeholder.jpg'}"
                         alt="${product.title}"
                         class="search-result-image"
                         onerror="this.onerror=null; this.src='/static/placeholder.jpg';">
                    <div class="search-result-details">
                        <div class="search-result-title">${product.title}</div>
                        <div class="search-result-info">
                            <div><strong>Game:</strong> ${product.game}</div>
                            <div><strong>Expansion:</strong> ${product.expansion}</div>
                            <div><strong>Number:</strong> ${product.number}</div>
                            <div><strong>Product ID:</strong> ${product.productId}</div>
                        </div>
                        ${product.trendPrices && product.trendPrices.length > 0 ? `
                            <div class="trend-prices">
                                <div><strong>Market Prices:</strong></div>
                                ${product.trendPrices.map(trend => `
                                    <div class="trend-price-item">
                                        <span class="trend-price-subtype">${trend.subType}:</span>
                                        <span class="trend-price-value">${trend.lowPrice === null ? 'Unavailable' : '$' + trend.lowPrice.toFixed(2)}</span>
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                        <div class="user-inventory">
                            <div><strong>Your Inventory:</strong></div>
                            ${product.variants && product.variants.length > 0 ? `
                                ${product.variants.map(variant => `
                                    <div class="inventory-item">
                                        <span class="inventory-sku">${variant.title}</span>
                                        <div class="inventory-details">
                                            <span class="inventory-price">$${variant.price}</span>
                                            <span class="inventory-quantity">Qty: ${variant.inventory_quantity}</span>
                                        </div>
                                    </div>
                                `).join('')}
                            ` : `
                                <div class="inventory-item">
                                    <span class="inventory-sku">N/A</span>
                                    <div class="inventory-details">
                                        <span class="inventory-price">Unavailable</span>
                                        <span class="inventory-quantity">Qty: 0</span>
                                    </div>
                                </div>
                            `}
                        </div>
                        ${product.salesData ? `
                            <div class="sales-history">
                                <div><strong>Sales History:</strong></div>
                                ${product.salesData.last_sale ? `
                                    <div class="last-sale">
                                        <div class="last-sale-details">
                                            Last Sale (${new Date(product.salesData.last_sale.date).toLocaleString()}):
                                            ${product.salesData.last_sale.condition}
                                            ${product.salesData.last_sale.variant ? `- ${product.salesData.last_sale.variant}` : ''}
                                        </div>
                                        <span class="last-sale-price">$${product.salesData.last_sale.price.toFixed(2)}</span>
                                    </div>
                                ` : ''}
                                <div class="sales-count">
                                    Sales in last 24h: ${product.salesData.total_sold_24h}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    ${product.url ? `<a href="${product.url}" target="_blank" class="search-result-link" title="View Details">
                        <i class="fas fa-external-link-alt"></i>
                    </a>` : ''}
                </div>
            `).join('');
        };

        // Close popup function
        window.closeSearchPopup = function() {
            const overlay = document.querySelector('.search-overlay');
            const popup = document.querySelector('.search-popup');
            if (overlay) overlay.remove();
            if (popup) popup.remove();
        };

        function formatDate(dateString) {
            if (!dateString || dateString === 'N/A') return 'N/A';
            const date = new Date(dateString);
            return isNaN(date.getTime()) ? 'N/A' : date.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' });
        }

        function updateManipulatedList(manipulatedProducts) {
            const accordion = document.getElementById('manipulatedAccordion');
            if (!accordion) return;

            accordion.innerHTML = '';

            // Check if manipulatedProducts exists and is an array
            if (!manipulatedProducts || !Array.isArray(manipulatedProducts) || manipulatedProducts.length === 0) {
                accordion.innerHTML = '<p>No possible manipulated products found in the last 24 hours.</p>';
                return;
            }

            manipulatedProducts.forEach((game, gameIndex) => {
                // Skip if game or manipulated array is invalid
                if (!game || !game.manipulated || !Array.isArray(game.manipulated) || game.manipulated.length === 0) {
                    return;
                }

                const gameCard = document.createElement('div');
                gameCard.className = 'card mb-2';

                const gameHeader = document.createElement('div');
                gameHeader.className = 'card-header';
                gameHeader.id = `manipulatedGameHeading${gameIndex}`;

                const gameButton = document.createElement('button');
                gameButton.className = 'btn btn-link';
                gameButton.type = 'button';
                gameButton.setAttribute('data-bs-toggle', 'collapse');
                gameButton.setAttribute('data-bs-target', `#manipulatedGameCollapse${gameIndex}`);
                gameButton.setAttribute('aria-expanded', 'false');
                gameButton.setAttribute('aria-controls', `manipulatedGameCollapse${gameIndex}`);
                gameButton.textContent = game.gameName || 'Unknown Game';

                gameHeader.appendChild(gameButton);
                gameCard.appendChild(gameHeader);

                const gameCollapseDiv = document.createElement('div');
                gameCollapseDiv.id = `manipulatedGameCollapse${gameIndex}`;
                gameCollapseDiv.className = 'collapse';
                gameCollapseDiv.setAttribute('aria-labelledby', `manipulatedGameHeading${gameIndex}`);
                gameCollapseDiv.setAttribute('data-bs-parent', '#manipulatedAccordion');

                const gameCardBody = document.createElement('div');
                gameCardBody.className = 'card-body';

                game.manipulated.forEach((product) => {
                    // Skip if product is invalid
                    if (!product) {
                        return;
                    }

                    const productDiv = document.createElement('div');
                    productDiv.className = 'mover-item mb-3';

                    // Ensure all values have defaults to prevent undefined errors
                    const priceChange = typeof product.priceChange === 'number' ? product.priceChange : 0;
                    const firstPrice = product.firstPrice || 0;
                    const lastPrice = product.lastPrice || 0;
                    const salesCount = product.salesCount || 0;

                    productDiv.innerHTML = `
                        <div class="mover-title">${product.name || 'Unknown Product'}</div>
                        <div class="mover-details">
                            Expansion: ${product.expansionName || 'N/A'}
                        </div>
                        <div class="mover-details">
                            ${product.condition || 'N/A'} | ${product.language || 'N/A'}
                        </div>
                        <div class="mover-change positive">
                            +${priceChange.toFixed(2)}%
                        </div>
                        <div class="mover-details">
                            ${product.variant ? product.variant + ' | ' : ''}${product.condition || 'N/A'} | ${product.language || 'N/A'}
                        </div>
                        <div class="mover-details">
                            Initial: $${parseFloat(firstPrice).toFixed(2)}
                        </div>
                        <div class="mover-details">
                            Latest: $${parseFloat(lastPrice).toFixed(2)}
                        </div>
                        <div class="mover-details">
                            Sales Count: ${salesCount}
                        </div>
                    `;
                    gameCardBody.appendChild(productDiv);
                });

                gameCollapseDiv.appendChild(gameCardBody);
                gameCard.appendChild(gameCollapseDiv);
                accordion.appendChild(gameCard);

                // Add click event listener to toggle collapse
                gameButton.addEventListener('click', function() {
                    try {
                        const collapse = bootstrap.Collapse.getInstance(gameCollapseDiv) || new bootstrap.Collapse(gameCollapseDiv);
                        collapse.toggle();
                    } catch (error) {
                        console.error('Error toggling collapse:', error);
                    }
                });
            });
        }

    function checkFeatureAccess(event, featureName) {
        // Skip checks for Help Videos and bypassed features
        if (featureName === 'Help Videos' || event.currentTarget.getAttribute('data-bypass-check') === 'true') {
            return true;
        }

        // Allow access to all features
        return true;
    }

        function showUpgradePopup(featureName) {
            const popup = document.createElement('div');
            popup.style.position = 'fixed';
            popup.style.left = '50%';
            popup.style.top = '50%';
            popup.style.transform = 'translate(-50%, -50%)';
            popup.style.backgroundColor = '#2c3e50';
            popup.style.color = '#ffffff';
            popup.style.padding = '20px';
            popup.style.borderRadius = '5px';
            popup.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
            popup.style.zIndex = '1000';
            popup.innerHTML = `
                <h3>Subscription Upgrade Required</h3>
                <p>${featureName} is only available for subscribers.</p>
                <a href="{{ url_for('activation.activating') }}" style="display: inline-block; margin-top: 10px; padding: 5px 10px; background-color: #3498db; color: #ffffff; text-decoration: none; border-radius: 3px;">Upgrade Now</a>
                <button onclick="this.parentElement.remove()" style="display: block; margin-top: 10px; padding: 5px 10px; background-color: #95a5a6; color: #ffffff; border: none; border-radius: 3px; cursor: pointer;">Close</button>
            `;
            document.body.appendChild(popup);
        }

</script>

<!-- Include favorites.js for handling favorites functionality -->
<script src="{{ url_for('static', filename='js/favorites.js') }}"></script>

<!-- Dashboard enhancements script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const dashboardSearch = document.getElementById('dashboardSearch');
        const clearSearchBtn = document.getElementById('clearSearch');
        const searchResults = document.getElementById('searchResults');
        const searchResultsContent = document.getElementById('searchResultsContent');
        const closeSearchResultsBtn = document.getElementById('closeSearchResults');
        // const requestSupportBtn = document.getElementById('requestSupportBtn'); // Button is removed

        // Get all quick links for searching
        const allQuickLinks = Array.from(document.querySelectorAll('.quick-link-btn')).map(link => {
            return {
                element: link,
                title: link.querySelector('span').textContent.trim().toLowerCase(),
                category: link.getAttribute('data-category')?.toLowerCase() || '',
                route: link.getAttribute('data-route') || '',
                icon: link.querySelector('i').className
            };
        });

        // Search function
        function performSearch(query) {
            if (!query || query.trim() === '') {
                searchResults.classList.add('d-none');
                return;
            }

            query = query.trim().toLowerCase();
            const results = allQuickLinks.filter(link =>
                link.title.includes(query) ||
                link.category.includes(query)
            );

            searchResultsContent.innerHTML = '';

            if (results.length === 0) {
                searchResultsContent.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <i class="fas fa-search fa-2x mb-3 text-muted"></i>
                        <p class="mb-0">No results found for "${query}"</p>
                    </div>
                `;
            } else {
                results.forEach(result => {
                    const col = document.createElement('div');
                    col.className = 'col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6';

                    // Clone the original button to preserve all attributes and event listeners
                    const clonedBtn = result.element.cloneNode(true);

                    // Add a highlight class to make it stand out
                    clonedBtn.classList.add('search-result-item');

                    col.appendChild(clonedBtn);
                    searchResultsContent.appendChild(col);
                });
            }

            searchResults.classList.remove('d-none');
        }

        // Search input event
        dashboardSearch.addEventListener('input', function() {
            performSearch(this.value);
        });

        // Clear search button
        clearSearchBtn.addEventListener('click', function() {
            dashboardSearch.value = '';
            searchResults.classList.add('d-none');
            dashboardSearch.focus();
        });

        // Close search results
        closeSearchResultsBtn.addEventListener('click', function() {
            searchResults.classList.add('d-none');
            dashboardSearch.value = '';
        });

        // Keyboard shortcut for search
        document.addEventListener('keydown', function(e) {
            // '/' key to focus search
            if (e.key === '/' && !e.ctrlKey && !e.metaKey &&
                !(e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA')) {
                e.preventDefault();
                dashboardSearch.focus();
            }

            // Escape key to close search results
            if (e.key === 'Escape' && !searchResults.classList.contains('d-none')) {
                searchResults.classList.add('d-none');
                dashboardSearch.value = '';
                dashboardSearch.blur();
            }
        });

        // Help Center functionality
        const helpCenterToggleBtn = document.getElementById('helpCenterToggleBtn');
        const helpCenterPanel = document.getElementById('helpCenterPanel');
        const quickSupportForm = document.getElementById('quickSupportForm');

        // Toggle Help Center panel
        if (helpCenterToggleBtn && helpCenterPanel) {
            helpCenterToggleBtn.addEventListener('click', function() {
                helpCenterPanel.classList.toggle('d-none');

                // Add show class after d-none is removed for animation
                if (!helpCenterPanel.classList.contains('d-none')) {
                    setTimeout(() => {
                        helpCenterPanel.classList.add('show');
                    }, 10);
                } else {
                    helpCenterPanel.classList.remove('show');
                }

                // Update button text
                if (helpCenterPanel.classList.contains('d-none')) {
                    helpCenterToggleBtn.innerHTML = '<i class="fas fa-question-circle me-1"></i> Help Center';
                } else {
                    helpCenterToggleBtn.innerHTML = '<i class="fas fa-times me-1"></i> Close';
                }
            });
        }

        // Request support button logic is removed as the button is removed.

        // Quick support form submission
        if (quickSupportForm) {
            quickSupportForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const subject = document.getElementById('supportSubject').value;
                const message = document.getElementById('supportMessage').value;

                if (!message.trim()) {
                    // Show error for empty message
                    const messageField = document.getElementById('supportMessage');
                    messageField.classList.add('is-invalid');

                    if (!messageField.nextElementSibling || !messageField.nextElementSibling.classList.contains('invalid-feedback')) {
                        const errorFeedback = document.createElement('div');
                        errorFeedback.className = 'invalid-feedback';
                        errorFeedback.textContent = 'Please enter a message';
                        messageField.parentNode.insertBefore(errorFeedback, messageField.nextSibling);
                    }

                    return;
                }

                // Submit the form (in a real implementation, this would send the data to the server)
                // For now, we'll just show a success message

                // Clear form
                document.getElementById('supportMessage').value = '';
                document.getElementById('supportSubject').selectedIndex = 0;

                // Remove any validation errors
                const messageField = document.getElementById('supportMessage');
                messageField.classList.remove('is-invalid');
                if (messageField.nextElementSibling && messageField.nextElementSibling.classList.contains('invalid-feedback')) {
                    messageField.nextElementSibling.remove();
                }

                // Show success message
                const successAlert = document.createElement('div');
                successAlert.className = 'alert alert-success mt-3';
                successAlert.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i> Your message has been sent! We'll get back to you as soon as possible.
                `;

                // Add the alert after the form
                quickSupportForm.appendChild(successAlert);

                // Remove the alert after 5 seconds
                setTimeout(() => {
                    successAlert.remove();
                }, 5000);
            });

            // Remove validation errors when typing in the message field
            document.getElementById('supportMessage').addEventListener('input', function() {
                this.classList.remove('is-invalid');
                if (this.nextElementSibling && this.nextElementSibling.classList.contains('invalid-feedback')) {
                    this.nextElementSibling.remove();
                }
            });
        }







        // Favorites editing functionality
        const editFavoritesBtn = document.getElementById('editFavoritesBtn');
        const doneFavoritesBtn = document.getElementById('doneFavoritesBtn');
        const favoritesContainer = document.getElementById('favorites-container');

        if (editFavoritesBtn && doneFavoritesBtn && favoritesContainer) {
            editFavoritesBtn.addEventListener('click', function() {
                favoritesContainer.classList.add('favorites-editing');
                editFavoritesBtn.classList.add('d-none');
                doneFavoritesBtn.classList.remove('d-none');

                // Add remove buttons to all favorites
                const favoriteItems = favoritesContainer.querySelectorAll('.quick-link-btn');
                favoriteItems.forEach(item => {
                    if (!item.querySelector('.remove-favorite')) {
                        const removeBtn = document.createElement('span');
                        removeBtn.className = 'remove-favorite';
                        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                        removeBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            const route = item.getAttribute('data-route');
                            toggleFavorite(e, route, '', '', '', true);
                            item.closest('.col-xl-2').remove();

                            // Update favorites count
                            const favoritesCount = document.getElementById('favorites-count');
                            if (favoritesCount) {
                                const currentCount = parseInt(favoritesCount.textContent);
                                favoritesCount.textContent = Math.max(0, currentCount - 1);
                            }

                            // Show no favorites message if needed
                            if (favoritesContainer.querySelectorAll('.quick-link-btn').length === 0) {
                                document.getElementById('no-favorites-message').style.display = 'block';
                            }
                        });
                        item.appendChild(removeBtn);
                    }
                });
            });

            doneFavoritesBtn.addEventListener('click', function() {
                favoritesContainer.classList.remove('favorites-editing');
                doneFavoritesBtn.classList.add('d-none');
                editFavoritesBtn.classList.remove('d-none');

                // Remove all remove buttons
                const removeButtons = favoritesContainer.querySelectorAll('.remove-favorite');
                removeButtons.forEach(btn => btn.remove());
            });
        }



        // END OF OLD Favorites editing functionality

        // NEW Quick Links Accordion Logic
        function updateToggleButtonUI(toggleBtn, isCollapsed) {
            if (!toggleBtn) return;
            const toggleIcon = toggleBtn.querySelector('.toggle-icon');
            const toggleText = toggleBtn.querySelector('.toggle-text');

            if (isCollapsed) {
                if (toggleIcon) toggleIcon.style.transform = 'rotate(-90deg)';
                if (toggleText) toggleText.textContent = 'Expand';
            } else {
                if (toggleIcon) toggleIcon.style.transform = 'rotate(0deg)';
                if (toggleText) toggleText.textContent = 'Collapse';
            }
        }

        function initializeQuickLinkAccordion() {
            const quickLinkSectionContainers = document.querySelectorAll('.card-body > .row.g-2 > .col-12.mb-4');
            if (quickLinkSectionContainers.length === 0) {
                return;
            }

            let openSectionId = localStorage.getItem('dashboardOpenQuickLinkSection');
            // Default to first section open if nothing in storage and you want one open by default
            // if (!openSectionId && quickLinkSectionContainers.length > 0) {
            //     const firstHeader = quickLinkSectionContainers[0].querySelector('.section-header-wrapper h5');
            //     if (firstHeader) {
            //         openSectionId = firstHeader.textContent.trim();
            //         // localStorage.setItem('dashboardOpenQuickLinkSection', openSectionId); // Uncomment to save default
            //     }
            // }


            quickLinkSectionContainers.forEach(container => {
                const headerWrapper = container.querySelector('.section-header-wrapper');
                const sectionContent = container.querySelector('.section-content');
                const sectionTitleElement = headerWrapper ? headerWrapper.querySelector('h5') : null;
                const sectionId = sectionTitleElement ? sectionTitleElement.textContent.trim() : null;
                const toggleButton = headerWrapper ? headerWrapper.querySelector('.section-toggle') : null;

                if (!headerWrapper || !sectionContent || !sectionId || !toggleButton) {
                    console.warn('Skipping a quick link section due to missing elements:', container);
                    return;
                }

                // Remove old onclick from button if it exists (it was removed from HTML, but good practice)
                toggleButton.removeAttribute('onclick');

                if (sectionId === openSectionId) {
                    sectionContent.classList.remove('collapsed');
                    updateToggleButtonUI(toggleButton, false);
                } else {
                    sectionContent.classList.add('collapsed');
                    updateToggleButtonUI(toggleButton, true);
                }

                headerWrapper.style.cursor = 'pointer'; // Add pointer cursor to header
                headerWrapper.addEventListener('click', function() {
                    const isCurrentlyCollapsed = sectionContent.classList.contains('collapsed');

                    // Close all other quick link sections
                    quickLinkSectionContainers.forEach(otherContainer => {
                        const otherHeader = otherContainer.querySelector('.section-header-wrapper');
                        const otherContent = otherContainer.querySelector('.section-content');
                        const otherTitleEl = otherHeader ? otherHeader.querySelector('h5') : null;
                        const otherId = otherTitleEl ? otherTitleEl.textContent.trim() : null;
                        const otherToggleBtn = otherHeader ? otherHeader.querySelector('.section-toggle') : null;

                        if (otherContent && otherId && otherId !== sectionId && otherToggleBtn) {
                            otherContent.classList.add('collapsed');
                            updateToggleButtonUI(otherToggleBtn, true);
                        }
                    });

                    // Toggle the clicked section
                    if (isCurrentlyCollapsed) { // If it was collapsed, open it
                        sectionContent.classList.remove('collapsed');
                        updateToggleButtonUI(toggleButton, false);
                        localStorage.setItem('dashboardOpenQuickLinkSection', sectionId);
                    } else { // If it was open, close it
                        sectionContent.classList.add('collapsed');
                        updateToggleButtonUI(toggleButton, true);
                        // If closing the currently open one, clear from storage so all are closed
                        if (localStorage.getItem('dashboardOpenQuickLinkSection') === sectionId) {
                            localStorage.removeItem('dashboardOpenQuickLinkSection');
                        }
                    }
                });
            });
        }

        // Call the new accordion initializer
        initializeQuickLinkAccordion();
    });
</script>

<!-- Support availability check script -->
<script>
    // Define the message popup functionality if it doesn't exist
    if (typeof window.toggleMessagePopup !== 'function') {
        window.toggleMessagePopup = function() {
            // Check if the contact modal exists
            const contactModal = document.getElementById('contactModal');
            if (contactModal) {
                // Use the contact modal as a fallback
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const bsModal = new bootstrap.Modal(contactModal);
                    bsModal.show();
                    return;
                } else if (typeof $ !== 'undefined' && typeof $(contactModal).modal === 'function') {
                    // Fall back to jQuery modal if available
                    $(contactModal).modal('show');
                    return;
                }
                // If we can't show the modal, continue to chat interface
            }

            // If no contact modal exists or it failed to show, create a chat-like interface
            let chatContainer = document.getElementById('liveChatContainer');

            if (chatContainer) {
                // Toggle existing chat container
                if (chatContainer.classList.contains('chat-hidden')) {
                    chatContainer.classList.remove('chat-hidden');
                    setTimeout(() => {
                        chatContainer.classList.add('chat-visible');
                    }, 10);
                } else {
                    chatContainer.classList.remove('chat-visible');
                    setTimeout(() => {
                        chatContainer.classList.add('chat-hidden');
                    }, 300);
                }
            } else {
                // Create new chat container
                const newChatContainer = document.createElement('div');
                newChatContainer.id = 'liveChatContainer';
                newChatContainer.className = 'live-chat-container chat-hidden';

                const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                newChatContainer.innerHTML = `
                    <div class="chat-header">
                        <div class="chat-title">
                            <i class="fas fa-headset me-2"></i>
                            <span>Live Support Chat</span>
                        </div>
                        <div class="chat-actions">
                            <button type="button" class="chat-minimize-btn" onclick="minimizeChat()">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="chat-close-btn" onclick="closeChat()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chat-body">
                        <div class="chat-messages">
                            <div class="chat-message system-message">
                                <div class="message-content">
                                    <p>Welcome to TCGSync support! How can we help you today?</p>
                                    <span class="message-time">${currentTime}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chat-footer">
                        <form id="chatForm">
                            <div class="chat-input-container">
                                <textarea id="chatInput" placeholder="Type your message here..." rows="1" class="chat-input"></textarea>
                                <button type="button" class="chat-send-btn" id="chatSendButton">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(newChatContainer);
                chatContainer = newChatContainer;

                // Add event listeners after the container is added to the DOM
                const chatForm = document.getElementById('chatForm');
                const chatSendButton = document.getElementById('chatSendButton');

                if (chatForm) {
                    chatForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        sendChatMessage();
                    });
                }

                if (chatSendButton) {
                    chatSendButton.addEventListener('click', function() {
                        sendChatMessage();
                    });
                }

                const chatInput = document.getElementById('chatInput');
                if (chatInput) {
                    chatInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            sendChatMessage();
                        }
                    });
                }

                // Show the chat with animation
                setTimeout(() => {
                    chatContainer.classList.remove('chat-hidden');
                    chatContainer.classList.add('chat-visible');

                    // Focus the input
                    if (chatInput) {
                        chatInput.focus();
                    }
                }, 10);
            }
        };

        // Function to show error message
        window.showErrorMessage = function(message) {
            const errorToast = document.createElement('div');
            errorToast.className = 'position-fixed bottom-0 end-0 p-3';
            errorToast.style.zIndex = '5';
            errorToast.innerHTML = `
                <div class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-exclamation-circle me-2"></i> ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;
            document.body.appendChild(errorToast);

            try {
                if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                    const toastEl = errorToast.querySelector('.toast');
                    const bsToast = new bootstrap.Toast(toastEl, { delay: 5000 });
                    bsToast.show();

                    // Remove toast after it's hidden
                    toastEl.addEventListener('hidden.bs.toast', function() {
                        errorToast.remove();
                    });
                } else {
                    // Fallback if Bootstrap Toast is not available
                    setTimeout(() => {
                        errorToast.remove();
                    }, 5000);
                }
            } catch (error) {
                console.error("Error showing toast:", error);
                // Fallback if showing toast fails
                setTimeout(() => {
                    errorToast.remove();
                }, 5000);
            }
        };

        // Function to close the chat
        window.closeChat = function() {
            const chatContainer = document.getElementById('liveChatContainer');
            if (chatContainer) {
                chatContainer.classList.remove('chat-visible');
                chatContainer.classList.add('chat-hidden');

                // Remove the chat container after animation completes
                setTimeout(() => {
                    if (chatContainer.parentNode) {
                        chatContainer.parentNode.removeChild(chatContainer);
                    }
                }, 300);
            }
        };

        // Function to minimize the chat
        window.minimizeChat = function() {
            const chatContainer = document.getElementById('liveChatContainer');
            if (chatContainer) {
                chatContainer.classList.toggle('chat-minimized');
            }
        };

        // Function to send a chat message
        window.sendChatMessage = function() {
            const chatInput = document.getElementById('chatInput');
            if (!chatInput) return;

            const message = chatInput.value.trim();
            if (!message) return;

            // Clear the input
            chatInput.value = '';

            // Add the user message to the chat
            addChatMessage(message, 'user');

            // Simulate a response after a short delay
            setTimeout(() => {
                const responses = [
                    "Thank you for your message. Our support team will get back to you shortly.",
                    "I've received your inquiry and will connect you with a support agent soon.",
                    "Thanks for reaching out! We're reviewing your question and will respond as soon as possible.",
                    "Your message has been received. A support representative will be with you shortly."
                ];

                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addChatMessage(randomResponse, 'system');
            }, 1000);
        };

        // Function to add a message to the chat
        window.addChatMessage = function(message, type) {
            const chatMessages = document.querySelector('.chat-messages');
            if (!chatMessages) return;

            const messageElement = document.createElement('div');
            messageElement.className = `chat-message ${type}-message`;

            const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            messageElement.innerHTML = `
                <div class="message-content">
                    <p>${message}</p>
                    <span class="message-time">${currentTime}</span>
                </div>
            `;

            chatMessages.appendChild(messageElement);

            // Scroll to the bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        };
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Function to check if current time is within business hours (9am-5pm Monday-Friday)
        function isBusinessHours() {
            const now = new Date();
            const day = now.getDay(); // 0 is Sunday, 1-5 is Monday-Friday, 6 is Saturday
            const hour = now.getHours();

            // Check if it's a weekday (Monday-Friday)
            if (day >= 1 && day <= 5) {
                // Check if it's between 9am and 5pm
                if (hour >= 9 && hour < 17) {
                    return true;
                }
            }

            return false;
        }

        // Function to update the support availability bar
        function updateSupportAvailability() {
            const supportBar = document.getElementById('supportAvailabilityBar');
            const supportStatus = document.getElementById('supportStatus');
            const statusIndicator = supportBar ? supportBar.querySelector('.status-indicator') : null;
            // const chatButton = document.getElementById('requestSupportBtn'); // Button is removed

            // Get current UK time
            const now = new Date();
            const ukTimeOptions = {
                timeZone: 'Europe/London',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            };
            const ukTime = now.toLocaleTimeString('en-GB', ukTimeOptions);

            // Format date for display
            const ukDateOptions = {
                timeZone: 'Europe/London',
                weekday: 'long',
                day: 'numeric',
                month: 'short'
            };
            const ukDate = now.toLocaleDateString('en-GB', ukDateOptions);

            if (isBusinessHours()) {
                if (supportBar) {
                    supportBar.classList.remove('unavailable');
                    supportBar.classList.add('available');
                }

                if (statusIndicator) {
                    statusIndicator.innerHTML = '<i class="fas fa-headset"></i>';
                }

                if (supportStatus) {
                    supportStatus.innerHTML = `
                        <span class="badge bg-success me-2">Available Now</span>
                        <span class="d-block mt-1 small">
                            <i class="far fa-clock me-1"></i> ${ukTime} (UK) on ${ukDate}
                        </span>
                        <span class="d-block mt-1 small">
                            <i class="fas fa-info-circle me-1"></i> Typical response time: <strong>Under 5 minutes</strong>
                        </span>
                    `;
                }

                // if (chatButton) { // Logic for chatButton removed
                //     chatButton.innerHTML = '<i class="fas fa-comment-dots me-1"></i> Chat Now';
                //     chatButton.classList.remove('btn-outline-light');
                //     chatButton.classList.add('btn-success');
                // }
            } else {
                if (supportBar) {
                    supportBar.classList.remove('available');
                    supportBar.classList.add('unavailable');
                }

                if (statusIndicator) {
                    statusIndicator.innerHTML = '<i class="fas fa-moon"></i>';
                }

                if (supportStatus) {
                    supportStatus.innerHTML = `
                        <span class="badge bg-warning text-dark me-2">Offline</span>
                        <span class="d-block mt-1 small">
                            <i class="far fa-clock me-1"></i> ${ukTime} (UK) on ${ukDate}
                        </span>
                        <span class="d-block mt-1 small">
                            <i class="fas fa-info-circle me-1"></i> Support hours: <strong>9am-5pm Monday-Friday (UK)</strong>
                        </span>
                        <span class="d-block mt-1 small">
                            <i class="fas fa-history me-1"></i> Response time: <strong>Within 24 hours</strong>
                        </span>
                    `;
                }

                // if (chatButton) { // Logic for chatButton removed
                //     chatButton.innerHTML = '<i class="fas fa-envelope me-1"></i> Leave Message';
                //     chatButton.classList.remove('btn-success');
                //     chatButton.classList.add('btn-outline-light');
                // }
            }

            // Update help center panel status if it exists
            const helpCenterResponseTime = document.querySelector('.help-center-panel .badge');
            if (helpCenterResponseTime) {
                if (isBusinessHours()) {
                    helpCenterResponseTime.className = 'badge bg-success';
                    helpCenterResponseTime.textContent = 'Under 5 minutes';
                } else {
                    helpCenterResponseTime.className = 'badge bg-warning text-dark';
                    helpCenterResponseTime.textContent = 'Within 24 hours';
                }
            }
        }

        // Update support availability when page loads
        updateSupportAvailability();

        // Update support availability every minute
        setInterval(updateSupportAvailability, 60000);

        // Enterprise Upgrade Announcement functionality
        const hideEnterpriseAnnouncement = localStorage.getItem('hideEnterpriseAnnouncement') === 'true';
        const enterpriseAnnouncementElement = document.getElementById('enterprise-upgrade-announcement');
        const hideEnterpriseCheckbox = document.getElementById('hideEnterpriseAnnouncement');


        // If user previously chose to hide the announcement, hide it
        if (hideEnterpriseAnnouncement && enterpriseAnnouncementElement) {
            enterpriseAnnouncementElement.style.display = 'none';
        }

        // Add event listener to the checkbox
        if (hideEnterpriseCheckbox) {
            hideEnterpriseCheckbox.addEventListener('change', function() {
                // Store the user's preference in localStorage
                localStorage.setItem('hideEnterpriseAnnouncement', this.checked);

                // If checked, hide the announcement
                if (this.checked && enterpriseAnnouncementElement) {
                    enterpriseAnnouncementElement.style.display = 'none';
                }
            });
        }

        // Also check on page load to ensure the announcement is hidden if previously selected
        document.addEventListener('DOMContentLoaded', function() {
            const shouldHide = localStorage.getItem('hideEnterpriseAnnouncement') === 'true';
            if (shouldHide && enterpriseAnnouncementElement) {
                enterpriseAnnouncementElement.style.display = 'none';
            }
        });
    });

    // Function to play the enterprise video in a modal
    function playEnterpriseVideo(event) {
        event.preventDefault();
        const videoModal = new bootstrap.Modal(document.getElementById('enterpriseVideoModal'));
        videoModal.show();

        // Auto-play the video when the modal is shown
        document.getElementById('enterpriseVideoModal').addEventListener('shown.bs.modal', function () {
            const video = document.getElementById('enterpriseVideo');
            video.play();
        });

        // Pause the video when the modal is closed
        document.getElementById('enterpriseVideoModal').addEventListener('hidden.bs.modal', function () {
            const video = document.getElementById('enterpriseVideo');
            video.pause();
        });
    }


    
    // Function to process donation and redirect to Stripe payment
    function processDonation() {
        const donationAmount = document.getElementById('donationAmount').value;

        // Validate donation amount
        if (!donationAmount || isNaN(donationAmount) || donationAmount < 0) {
            alert('Please enter a valid donation amount');
            return;
        }

        // Use the handleDonation function to create a checkout session and redirect
        handleDonation(donationAmount);
    }
</script>





{% endblock %}
