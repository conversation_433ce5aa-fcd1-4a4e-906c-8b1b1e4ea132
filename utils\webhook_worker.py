from config.config import Config
import logging
import time
from typing import Optional, List, Dict, Any
import dramatiq
from dramatiq.brokers.redis import RedisBroker
from dramatiq.middleware import CurrentMessage
from dramatiq.rate_limits import ConcurrentRateLimiter
from dramatiq.rate_limits.backends import RedisBackend

from utils.webhook_queue import webhook_queue
from utils.webhook_processor import WebhookProcessor
from utils.webhook_db import mongo

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure Redis broker for dramatiq
redis_broker = RedisBroker(host="localhost", port=6379)
dramatiq.set_broker(redis_broker)

# Configure rate limiter
backend = RedisBackend(url="redis://localhost:6379")
RATE_LIMITER = ConcurrentRateLimiter(backend, "webhook_worker", limit=3)

# Configure retry policy
MAX_RETRIES = 3
MIN_BACKOFF = 1000  # 1 second in milliseconds
MAX_BACKOFF = 300000  # 5 minutes in milliseconds

def process_webhooks_with_cleanup(webhooks: List[Dict[str, Any]], processor: WebhookProcessor) -> None:
    """
    Process webhooks and ensure cleanup of processing set
    """
    try:
        stats = processor.process_batch(webhooks)
        logger.info(f"Processed webhook batch: {stats}")
    finally:
        # Always clean up processing set
        for webhook in webhooks:
            webhook_queue.remove_from_processing(webhook)

@dramatiq.actor(
    queue_name="webhook_processing",
    max_retries=MAX_RETRIES,
    min_backoff=MIN_BACKOFF,
    max_backoff=MAX_BACKOFF,
    time_limit=300000  # 5 minutes timeout
)
def process_webhook_batch() -> None:
    """
    Process a batch of webhooks from the queue with rate limiting
    """
    try:
        # Acquire rate limit
        with RATE_LIMITER.acquire():
            # Initialize processor
            processor = WebhookProcessor(mongo.db)
            
            # Get batch of webhooks
            batch = webhook_queue.get_batch()
            if not batch:
                logger.debug("No webhooks to process")
                return
                
            # Process the batch with cleanup
            process_webhooks_with_cleanup(batch, processor)
            
            # Log remaining queue length
            remaining = webhook_queue.get_queue_length()
            logger.info(f"Remaining in queue: {remaining}")
            
            # If more items in queue, enqueue another task
            if remaining > 0:
                process_webhook_batch.send_with_options(delay=1000)  # 1 second delay
                
    except Exception as e:
        logger.error(f"Error processing webhook batch: {str(e)}")
        message: Optional[CurrentMessage] = CurrentMessage.get_current()
        if message and message.retries < MAX_RETRIES:
            # Retry with exponential backoff
            raise dramatiq.Retry(delay=MIN_BACKOFF * (2 ** message.retries))
        else:
            logger.error("Max retries exceeded, dropping batch")
            # Clean up processing set for failed webhooks
            if 'batch' in locals():
                for webhook in batch:
                    webhook_queue.remove_from_processing(webhook)

def start_worker():
    """
    Webhook worker is intentionally disabled to prevent webhook records from being deleted
    """
    logger.info("Webhook worker is disabled to preserve webhook records")
    
    # Just sleep indefinitely
    try:
        while True:
            time.sleep(3600)  # Sleep for an hour
    except KeyboardInterrupt:
        logger.info("Webhook worker stopped")

if __name__ == "__main__":
    start_worker()

