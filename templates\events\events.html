{% extends "base.html" %}

{% block title %}Events{% endblock %}

{% block styles %}
{{ super() }}
{% include "events/event_styles.html" %}
{% endblock %}

{% block content %}
<div class="container-fluid event-page-content">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Events</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('event.create_event') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Event
                        </a>
                        <a href="{{ url_for('event.calendar') }}" class="btn btn-info ml-2">
                            <i class="fas fa-calendar"></i> Calendar View
                        </a>
                        <a href="{{ url_for('event.analytics') }}" class="btn btn-success ml-2">
                            <i class="fas fa-chart-bar"></i> Analytics
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <form method="GET" action="{{ url_for('event.events') }}" class="form-inline">
                                <div class="form-group mr-2">
                                    <label for="game" class="mr-2">Game:</label>
                                    <select name="game" id="game" class="form-control">
                                        <option value="">All Games</option>
                                        {% for game in games %}
                                        <option value="{{ game }}" {% if selected_game == game %}selected{% endif %}>{{ game }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <label for="event_type" class="mr-2">Type:</label>
                                    <select name="event_type" id="event_type" class="form-control">
                                        <option value="">All Types</option>
                                        {% for type in event_types %}
                                        <option value="{{ type }}" {% if selected_event_type == type %}selected{% endif %}>
                                            {{ type|title }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <label for="format" class="mr-2">Format:</label>
                                    <select name="format" id="format" class="form-control">
                                        <option value="">All Formats</option>
                                        {% for format in formats %}
                                        <option value="{{ format }}" {% if selected_format == format %}selected{% endif %}>
                                            {{ format }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{{ url_for('event.events') }}" class="btn btn-secondary ml-2">Reset</a>
                            </form>
                        </div>
                    </div>

                    <!-- Events Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Game</th>
                                    <th>Type</th>
                                    <th>Format</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Location</th>
                                    <th>Price</th>
                                    <th>Attendees</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if events %}
                                    {% for event in events %}
                                    <tr>
                                        <td>
                                            <a href="{{ url_for('event.event_detail', event_id=event.id) }}">
                                                {{ event.title }}
                                            </a>
                                        </td>
                                        <td>{{ event.game }}</td>
                                        <td>
                                            <span class="badge 
                                                {% if event.event_type == 'competitive' %}badge-primary
                                                {% elif event.event_type == 'casual' %}badge-success
                                                {% else %}badge-warning{% endif %}">
                                                {{ event.event_type|title }}
                                            </span>
                                        </td>
                                        <td>{{ event.format }}</td>
                                        <td>{{ event.start_datetime.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ event.start_datetime.strftime('%H:%M') }} - {{ event.end_datetime.strftime('%H:%M') }}</td>
                                        <td>{{ event.location }}</td>
                                        <td>
                                            {% if event.is_free() %}
                                                <span class="badge badge-success">Free</span>
                                            {% else %}
                                                {{ "$%.2f"|format(event.ticket_price) }}
                                            {% endif %}
                                        </td>
                                        <td>
                                            {{ event.get_attendee_count() }}
                                            {% if event.max_attendees %}
                                                / {{ event.max_attendees }}
                                            {% else %}
                                                <span class="text-muted">(unlimited)</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if event.published %}
                                                <span class="badge badge-success">Published</span>
                                            {% else %}
                                                <span class="badge badge-secondary">Draft</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('event.event_detail', event_id=event.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('event.edit_event', event_id=event.id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ url_for('event.event_attendees', event_id=event.id) }}" class="btn btn-sm btn-success">
                                                    <i class="fas fa-users"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteModal{{ event.id }}">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>

                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal{{ event.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel{{ event.id }}" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel{{ event.id }}">Confirm Delete</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            Are you sure you want to delete the event "{{ event.title }}"? This action cannot be undone.
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                            <form action="{{ url_for('event.delete_event', event_id=event.id) }}" method="POST" style="display: inline;">
                                                                <button type="submit" class="btn btn-danger">Delete</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="11" class="text-center">No events found. <a href="{{ url_for('event.create_event') }}">Create your first event</a>.</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Auto-submit form when filters change
        $('#game, #event_type, #format').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
