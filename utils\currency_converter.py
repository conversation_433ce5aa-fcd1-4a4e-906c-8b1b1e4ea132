from config.config import Config
import requests
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)

API_KEY = "cur_live_Yl5Oa5Oa5Oa5Oa5Oa5Oa5Oa5Oa5Oa5Oa5"  # Replace with your actual API key

SUPPORTED_CURRENCIES = ['GBP', 'USD', 'CAD', 'AUD', 'NZD', 'EUR']

@lru_cache(maxsize=1)
def get_exchange_rates():
    try:
        currencies = ','.join(SUPPORTED_CURRENCIES)
        response = requests.get(f"https://api.currencyapi.com/v3/latest?apikey={API_KEY}&base_currency=GBP&currencies={currencies}")
        data = response.json()
        return data['data']
    except Exception as e:
        logger.error(f"Error fetching exchange rates: {str(e)}")
        # Fallback rates (you may want to update these periodically)
        return {
            'GBP': {'value': 1},
            'USD': {'value': 1.38},
            'CAD': {'value': 1.73},
            'AUD': {'value': 1.85},
            'NZD': {'value': 1.97},
            'EUR': {'value': 1.16},
        }

def convert_to_gbp(amount, from_currency):
    if from_currency == 'GBP':
        return amount
    
    rates = get_exchange_rates()
    if from_currency in rates:
        return amount / rates[from_currency]['value']
    else:
        logger.warning(f"Unsupported currency: {from_currency}. Using 1:1 conversion.")
        return amount

def convert_currency(amount, from_currency, to_currency):
    if from_currency == to_currency:
        return amount
    
    rates = get_exchange_rates()
    if from_currency in rates and to_currency in rates:
        gbp_amount = amount / rates[from_currency]['value']
        return gbp_amount * rates[to_currency]['value']
    else:
        logger.warning(f"Unsupported currency conversion: {from_currency} to {to_currency}. Using 1:1 conversion.")
        return amount

def get_currency_symbol(currency_code):
    symbols = {
        'GBP': '£',
        'USD': '$',
        'CAD': 'C$',
        'AUD': 'A$',
        'NZD': 'NZ$',
        'EUR': '€',
    }
    return symbols.get(currency_code, currency_code)

def is_supported_currency(currency_code):
    return currency_code in SUPPORTED_CURRENCIES

