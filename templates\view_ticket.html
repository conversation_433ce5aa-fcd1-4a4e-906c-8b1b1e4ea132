{% extends "base.html" %}

{% block title %}View Ticket{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1>{{ ticket.title }}</h1>
    <p>{{ ticket.description }}</p>
    <p>Priority: {{ ticket.priority }}</p>
    <p>Status: {{ ticket.status }}</p>
    <p>Created At: {{ ticket.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>

    <h2>Responses</h2>
    <div class="list-group mb-3">
        {% for response in responses %}
        <div class="list-group-item">
            <p>{{ response.message }}</p>
            <small>By: {{ response.username }} | At: {{ response.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
        </div>
        {% endfor %}
    </div>

    {% if 'Admin' in current_user.roles %}
    <form method="POST">
        <div class="form-group">
            <label for="message">Add Response</label>
            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Add Response</button>
    </form>
    {% endif %}
</div>
{% endblock %}
