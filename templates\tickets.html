{% extends "base.html" %}

{% block title %}Support Tickets{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1>Support Tickets</h1>
    <a href="{{ url_for('ticket.create_ticket') }}" class="btn btn-primary mb-3">Create Ticket</a>
    <div class="list-group">
        {% for ticket in tickets %}
        <a href="{{ url_for('ticket.view_ticket', ticket_id=ticket.id) }}" class="list-group-item list-group-item-action">
            <h5 class="mb-1">{{ ticket.title }}</h5>
            <p class="mb-1">{{ ticket.description }}</p>
            <small>Priority: {{ ticket.priority }} | Status: {{ ticket.status }} | Created At: {{ ticket.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
        </a>
        {% endfor %}
    </div>
</div>
{% endblock %}
