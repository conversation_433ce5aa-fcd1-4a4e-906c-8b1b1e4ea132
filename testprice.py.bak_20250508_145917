from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from pymongo import MongoClient, UpdateOne
from requests.adapters import HTT<PERSON><PERSON>pter 
from requests.packages.urllib3.util.retry import Retry
from datetime import datetime, timedelta, timezone
from itertools import islice
import requests
import sys
import logging
import time
import schedule
from threading import Lock
import signal
import re
import traceback
import random
import asyncio
import aiohttp

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('repricer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Pre-compile regex patterns
CONDITION_PATTERNS = {
    re.compile(r'near\s*mint|nm\b', re.I): 'nm',
    re.compile(r'lightly\s*played|lp\b', re.I): 'lp',
    re.compile(r'moderately\s*played|mp\b', re.I): 'mp',
    re.compile(r'heavily\s*played|hp\b', re.I): 'hp',
    re.compile(r'damaged|dm\b', re.I): 'dm'
}

SEALED_PATTERN = re.compile(r'.*seal.*', re.I)

# Constants
EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"
BATCH_SIZE = 1000  # Increased batch size for better throughput
DEFAULT_MIN_PRICE_USD = 0.2
TEST_USERNAME = "Khaoz"
MONGO_POOL_SIZE = 200

# Enhanced session configuration
session = requests.Session()
retries = Retry(
    total=5,
    backoff_factor=0.5,
    status_forcelist=[500, 502, 503, 504],
    allowed_methods=frozenset(['GET'])
)
adapter = HTTPAdapter(
    max_retries=retries,
    pool_connections=MONGO_POOL_SIZE,
    pool_maxsize=MONGO_POOL_SIZE,
    pool_block=False
)
session.mount('https://', adapter)
session.mount('http://', adapter)

# MongoDB Configuration with connection pooling
mongo_uri = '*******************************************************************'
mongo_client = MongoClient(
    mongo_uri,
    maxPoolSize=MONGO_POOL_SIZE,
    waitQueueTimeoutMS=5000,
    connectTimeoutMS=5000,
    serverSelectionTimeoutMS=5000,
    retryWrites=True,
    w='majority'
)
db = mongo_client['test']
shopify_collection = db['shProducts']
user_collection = db['user']
tcgplayer_key_collection = db['tcgplayerKey']
autopricer_collection = db['autopricerShopify']
reprice_logs_collection = db['repriceLogs']

# Create indexes
shopify_collection.create_index([("username", 1), ("product_type", 1)])

# Currency cache implementation
class CurrencyCache:
    def __init__(self, ttl_hours=24):
        self.cache = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.lock = Lock()
        self.last_cleanup = datetime.now(timezone.utc)

    def get(self, currency):
        with self.lock:
            if currency in self.cache:
                rate_data = self.cache[currency]
                if datetime.now(timezone.utc) - rate_data['timestamp'] < self.ttl:
                    return rate_data['rate']
                else:
                    del self.cache[currency]
            return None

    def set(self, currency, rate):
        with self.lock:
            self.cache[currency] = {
                'rate': rate,
                'timestamp': datetime.now(timezone.utc)
            }

    def clear_expired(self):
        with self.lock:
            current_time = datetime.now(timezone.utc)
            expired = [currency for currency, data in self.cache.items() 
                      if current_time - data['timestamp'] >= self.ttl]
            for currency in expired:
                del self.cache[currency]
            self.last_cleanup = current_time

# TCGPlayer price cache implementation
class TCGPriceCache:
    def __init__(self, ttl_minutes=15):  # Reduced TTL for more frequent updates
        self.cache = {}
        self.ttl = timedelta(minutes=ttl_minutes)
        self.lock = Lock()
    
    def get_batch(self, product_ids):
        now = datetime.now(timezone.utc)
        cached = {}
        missing = []
        
        with self.lock:
            for pid in product_ids:
                if pid in self.cache:
                    data = self.cache[pid]
                    if now - data['timestamp'] < self.ttl:
                        cached[pid] = data['pricing']
                    else:
                        missing.append(pid)
                        del self.cache[pid]
                else:
                    missing.append(pid)
        return cached, missing

    def update_batch(self, pricing_data):
        with self.lock:
            now = datetime.now(timezone.utc)
            for pid, data in pricing_data.items():
                self.cache[pid] = {
                    'pricing': data,
                    'timestamp': now
                }

# Initialize caches
currency_cache = CurrencyCache()
tcgplayer_cache = TCGPriceCache()

def get_exchange_rate(target_currency):
    if target_currency == 'USD':
        return 1.0
    
    cached_rate = currency_cache.get(target_currency)
    if cached_rate is not None:
        return cached_rate
    
    try:
        url = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
        response = session.get(url, timeout=10)
        response.raise_for_status()
        rates = response.json().get('conversion_rates', {})
        rate = rates.get(target_currency)
        
        if rate is None:
            return 1.0
            
        currency_cache.set(target_currency, rate)
        return rate
    except Exception as e:
        logger.error(f"Error fetching exchange rate: {str(e)}")
        return 1.0

def has_valid_price(price_data):
    """Check if a price data object has any valid prices."""
    return (price_data.get('marketPrice') is not None or 
            price_data.get('lowPrice') is not None or 
            price_data.get('directLowPrice') is not None)

def determine_printing_type(variant_title, valid_subtypes):
    """
    Optimized function to determine printing type from variant title and valid subtypes.
    """
    def try_match(title, subtypes_lower):
        if 'foil' in title:
            if 'cold foil' in title and 'cold foil' in subtypes_lower:
                return 'Cold Foil'
            if 'rainbow foil' in title and 'rainbow foil' in subtypes_lower:
                return 'Rainbow Foil'
            if 'reverse holofoil' in title or 'reverse holo' in title:
                if any('reverse holofoil' in s for s in subtypes_lower):
                    return 'Reverse Holofoil'
            if 'etched foil' in title and any('etched foil' in s for s in subtypes_lower):
                return 'Etched Foil'
            if 'holofoil' in title or 'holo foil' in title:
                if 'holofoil' in subtypes_lower:
                    return 'Holofoil'
            # Generic foil match
            if any('foil' in s for s in subtypes_lower):
                for subtype in valid_subtypes:
                    if 'foil' in subtype.lower():
                        return subtype
                return 'Foil'
        
        if 'etched' in title and 'etched' in subtypes_lower:
            return 'Etched'
        if 'gilded' in title and 'gilded' in subtypes_lower:
            return 'Gilded'
        if '1st edition' in title and '1st edition' in subtypes_lower:
            return '1st Edition'
        if 'unlimited' in title and 'unlimited' in subtypes_lower:
            return 'Unlimited'
        
        return None

    if not valid_subtypes:
        return 'Normal'
    
    # If only one subtype, return it
    if len(valid_subtypes) == 1:
        return valid_subtypes[0]

    # Clean and prepare variant title
    variant_title = variant_title.lower()
    variant_title = re.sub(r'\s+', ' ', variant_title)
    
    # Remove condition prefixes
    for cond in ['near mint', 'nm', 'lightly played', 'lp', 'moderately played', 'mp', 
                'heavily played', 'hp', 'damaged', 'dmg']:
        variant_title = variant_title.replace(cond, '').strip()
    variant_title = variant_title.strip('- ').strip()
    
    # Create lowercase set of subtypes for faster lookups
    subtypes_lower = {s.lower() for s in valid_subtypes if s}
    
    # Try exact match first
    for subtype in valid_subtypes:
        if subtype and variant_title == subtype.lower():
            return subtype
    
    # Try pattern matching
    match = try_match(variant_title, subtypes_lower)
    if match:
        return match
    
    # Try with extended art removed
    cleaned_title = variant_title.replace('extended art', '').strip()
    match = try_match(cleaned_title, subtypes_lower)
    if match:
        return match
    
    # Try with 1st edition removed
    final_title = cleaned_title.replace('1st edition', '').strip()
    match = try_match(final_title, subtypes_lower)
    if match:
        return match
    
    # If normal is in subtypes, use that
    if 'normal' in subtypes_lower:
        return 'Normal'
    
    # Default fallback
    return 'Normal'

def extract_condition(variant_title):
    title = variant_title.lower()
    for pattern, condition in CONDITION_PATTERNS.items():
        if pattern.search(title):
            return condition
    return 'nm'

# Initialize MongoDB prices collection
prices_collection = db['prices']

async def check_prices_collection(product_ids):
    """
    Check if product IDs exist in the prices collection and are recent enough
    Returns a tuple of (cached_data, missing_ids)
    """
    cached_data = {}
    missing_ids = []
    
    # Define the maximum age of cached data (24 hours)
    max_age = timedelta(hours=24)
    current_time = datetime.now(timezone.utc)
    
    try:
        # Query the prices collection for all product IDs at once
        cursor = prices_collection.find(
            {
                "productId": {"$in": [int(pid) for pid in product_ids if pid.isdigit()]},
                "last_updated": {"$gte": current_time - max_age}
            }
        )
        
        # Process the results
        for doc in cursor:
            product_id = str(doc.get('productId'))
            if product_id and 'prices' in doc:
                cached_data[product_id] = []
                
                # Convert the prices structure to match the expected format
                for subtype_name, price_data in doc.get('prices', {}).items():
                    if price_data:
                        cached_data[product_id].append({
                            'productId': price_data.get('productId'),
                            'subTypeName': subtype_name,
                            'directLowPrice': price_data.get('directLowPrice'),
                            'lowPrice': price_data.get('lowPrice'),
                            'midPrice': price_data.get('midPrice'),
                            'highPrice': price_data.get('highPrice'),
                            'marketPrice': price_data.get('marketPrice')
                        })
        
        # Determine which product IDs are missing from the cache
        missing_ids = [pid for pid in product_ids if pid not in cached_data]
                
        logger.info(f"Found {len(cached_data)} products in cache, {len(missing_ids)} products need API calls")
        return cached_data, missing_ids
        
    except Exception as e:
        logger.error(f"Error checking prices collection: {str(e)}")
        # If there's an error, return empty cached data and all product IDs as missing
        return {}, product_ids

async def fetch_pricing_data_async(product_ids, tcgplayer_api_key):
    """
    Fetch pricing data directly from TCGPlayer API using async calls
    """
    if not product_ids or not tcgplayer_api_key:
        return {}
    
    api_data = {}
    
    # Split product IDs into chunks of 10 (reduced from 250 to minimize API load)
    chunk_size = 10
    chunks = [product_ids[i:i + chunk_size] for i in range(0, len(product_ids), chunk_size)]
    
    logger.info(f"Calling TCGPlayer API for {len(product_ids)} products in {len(chunks)} batches of {chunk_size}")
    
    async def fetch_chunk(chunk):
        ids_param = ','.join(chunk)
        url = f"https://api.tcgplayer.com/pricing/product/{ids_param}"
        headers = {
            'Accept': 'application/json',
            'Authorization': f'Bearer {tcgplayer_api_key}'
        }
        
        chunk_data = {}
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Process the API response
                        if 'results' in data:
                            for result in data['results']:
                                product_id = str(result.get('productId'))
                                if product_id:
                                    if product_id not in chunk_data:
                                        chunk_data[product_id] = []
                                    
                                    chunk_data[product_id].append({
                                        'productId': result.get('productId'),
                                        'subTypeName': result.get('subTypeName'),
                                        'directLowPrice': result.get('directLowPrice'),
                                        'lowPrice': result.get('lowPrice'),
                                        'midPrice': result.get('midPrice'),
                                        'highPrice': result.get('highPrice'),
                                        'marketPrice': result.get('marketPrice')
                                    })
                                    
                                    # Store this data in MongoDB for future use in the new format
                                    try:
                                        product_id = result.get('productId')
                                        subtype_name = result.get('subTypeName')
                                        
                                        if product_id and subtype_name:
                                            # Update using the new format with nested prices object
                                            prices_collection.update_one(
                                                {'productId': product_id},
                                                {
                                                    '$set': {
                                                        'productId': product_id,
                                                        'last_updated': datetime.now(timezone.utc),
                                                        'timestamp': datetime.now(timezone.utc),
                                                        'lastUpdateStatus': 'success',
                                                        f'prices.{subtype_name}': {
                                                            'productId': product_id,
                                                            'directLowPrice': result.get('directLowPrice'),
                                                            'lowPrice': result.get('lowPrice'),
                                                            'midPrice': result.get('midPrice'),
                                                            'highPrice': result.get('highPrice'),
                                                            'marketPrice': result.get('marketPrice')
                                                        }
                                                    }
                                                },
                                                upsert=True
                                            )
                                    except Exception as e:
                                        logger.error(f"Error storing TCGPlayer API data in MongoDB: {str(e)}")
        except Exception as e:
            logger.error(f"Error fetching prices from TCGPlayer API for chunk: {str(e)}")
        
        return chunk_data
    
    # Create tasks for all chunks and run them concurrently
    tasks = [fetch_chunk(chunk) for chunk in chunks]
    chunk_results = await asyncio.gather(*tasks)
    
    # Merge all chunk results
    for result in chunk_results:
        for product_id, data in result.items():
            if product_id not in api_data:
                api_data[product_id] = []
            api_data[product_id].extend(data)
    
    return api_data

def fetch_pricing_data(product_ids, tcgplayer_api_key=None):
    """
    Fetch pricing data, checking the prices collection first before making API calls
    """
    if not product_ids or not tcgplayer_api_key:
        return {}
    
    try:
        # Run the async functions in an event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # First check the prices collection
        cached_data, missing_ids = loop.run_until_complete(check_prices_collection(product_ids))
        
        # If we have missing IDs, fetch them from the API
        api_data = {}
        if missing_ids:
            logger.info(f"Fetching {len(missing_ids)} products from TCGPlayer API")
            api_data = loop.run_until_complete(fetch_pricing_data_async(missing_ids, tcgplayer_api_key))
        
        loop.close()
        
        # Merge cached data with API data
        merged_data = cached_data.copy()
        for product_id, data in api_data.items():
            merged_data[product_id] = data
        
        return merged_data
    except Exception as e:
        logger.error(f"Error in fetch_pricing_data: {str(e)}")
        return {}

class PricingCalculator:
    def __init__(self, settings: dict, currency: str = 'USD'):
        """
        Initialize the pricing calculator with Shopify pricing settings.
        
        Args:
            settings (dict): Shopify pricing settings from ShopifySettings model
            currency (str): Currency code (e.g., 'USD', 'GBP')
        """
        self.settings = settings
        self.currency = currency
        
        # Set defaults for any missing settings
        required_settings = [
            'minPrice', 'price_point', 'price_rounding_enabled', 'price_rounding_thresholds',
            'use_highest_price', 'price_comparison_pairs', 'price_modifiers', 'price_preference_order',
            'game_minimum_prices', 'advancedPricingRules', 'customStepping',
            'tcg_trend_increasing', 'tcg_trend_decreasing'
        ]
        
        defaults = {
            'minPrice': 0.50,
            'price_point': 'Low Price',
            'price_rounding_enabled': False,
            'price_rounding_thresholds': [49, 99],
            'use_highest_price': False,
            'price_comparison_pairs': [],
            'price_modifiers': {},
            'price_preference_order': ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'],
            'game_minimum_prices': {},
            'advancedPricingRules': {},
            'customStepping': {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50},
            'tcg_trend_increasing': 0.0,
            'tcg_trend_decreasing': 0.0
        }
        
        for setting in required_settings:
            if setting not in self.settings:
                self.settings[setting] = defaults[setting]

    def apply_price_comparison(self, pricing_data: dict) -> tuple:
        """
        Apply price comparison rules to determine the base price.
        
        Args:
            pricing_data (Dict[str, float]): Dictionary of price types and their values
            
        Returns:
            Tuple[float, bool]: (calculated price, is_missing flag)
        """
        try:
            if not pricing_data:
                return None, True
                
            price_modifiers = self.settings.get('price_modifiers', {})
            
            # Apply modifiers to all prices upfront
            modified_prices = {}
            for price_type, price in pricing_data.items():
                if price is not None:
                    try:
                        modifier = price_modifiers.get(price_type, 0)
                        modified_price = round(float(price) * (1 + modifier/100), 2)
                        modified_prices[price_type] = modified_price
                    except (ValueError, TypeError):
                        # Skip this price but continue with others
                        continue
            
            if not modified_prices:
                return None, True
            
            if self.settings.get('use_highest_price', False):
                pairs = self.settings.get('price_comparison_pairs', [])
                
                highest_price = None
                
                for pair in pairs:
                    try:
                        price1 = modified_prices.get(pair[0])
                        price2 = modified_prices.get(pair[1])
                        
                        if price1 is not None and price2 is not None:
                            pair_max = max(price1, price2)
                            
                            if highest_price is None or pair_max > highest_price:
                                highest_price = pair_max
                    except Exception:
                        continue
                
                if highest_price is not None:
                    return highest_price, False
            
            # Use price preference order
            price_preferences = self.settings.get('price_preference_order', 
                ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])
                
            for price_type in price_preferences:
                if price_type in modified_prices:
                    return modified_prices[price_type], False
            
            # If we get here, try any available price as a fallback
            for price_type, price in modified_prices.items():
                return price, False
                    
            return None, True
            
        except Exception as e:
            logger.error(f"Error in apply_price_comparison: {str(e)}")
            return None, True

    # Map of rarity abbreviations to full names
    RARITY_MAP = {
        'C': 'Common',
        'U': 'Uncommon',
        'R': 'Rare',
        'SR': 'Super Rare',
        'SEC': 'Secret Rare',
        'L': 'Leader',
        'DON': 'DON!!',
        'P': 'Promo',
        'TR': 'Treasure Rare',
        'M': 'Mythic',
        'SP': 'Special Rare',
        'F': 'Fabled',
        'LEG': 'Legendary',
        'MAJ': 'Majestic',
        'MAR': 'Marvel',
        'TOK': 'Token'
    }

    def get_rarity_mapping(self, game_name, rarity_code):
        """
        Get the full rarity name from the rarities collection.
        
        Args:
            game_name (str): The game name to match
            rarity_code (str): The rarity code to look up
            
        Returns:
            str: The full rarity name if found, otherwise the original rarity code
        """
        try:
            # First check if the game name is valid
            if not game_name:
                return rarity_code
                
            # Try to find a matching game in the rarities collection
            rarities_collection = db['rarities']
            
            # Find a game that matches or starts with the provided game name
            game_doc = None
            for doc in rarities_collection.find({}):
                db_game_name = doc.get('gameName')
                if db_game_name and (game_name.startswith(db_game_name) or db_game_name.startswith(game_name)):
                    game_doc = doc
                    break
            
            if not game_doc:
                return rarity_code
                
            # Find the matching rarity
            for rarity_info in game_doc.get('rarities', []):
                if rarity_info.get('dbValue') == rarity_code:
                    return rarity_info.get('displayText', rarity_code)
            
            return rarity_code
        except Exception as e:
            logger.error(f"Error getting rarity mapping: {str(e)}")
            return rarity_code

    def apply_game_rules(self, price: float, game_name: str, product_type: str, rarity: str = None) -> float:
        if not game_name:
            return price
            
        game_settings = self.settings.get('game_minimum_prices', {}).get(game_name, {})
        
        game_default_min = game_settings.get('default_min_price')
        if game_default_min is not None:
            price = max(price, game_default_min)
                
        if rarity and 'rarities' in game_settings:
            # Try to map abbreviation to full name if needed
            full_rarity = self.RARITY_MAP.get(rarity, rarity)
            
            # If not found in the static map, try to get it from the database
            if full_rarity == rarity and rarity not in game_settings['rarities']:
                db_full_rarity = self.get_rarity_mapping(game_name, rarity)
                if db_full_rarity != rarity:
                    full_rarity = db_full_rarity
            
            # First try with the original rarity
            rarity_min = game_settings['rarities'].get(rarity)
            
            # If not found, try with the mapped full name
            if rarity_min is None and rarity != full_rarity:
                rarity_min = game_settings['rarities'].get(full_rarity)
                
            if rarity_min is not None:
                price = max(price, rarity_min)
                    
        return price

    def apply_condition_stepping(self, price: float, condition: str, vendor: str, 
                               product_type: str, expansion: str) -> float:
        """Apply condition-based price stepping rules"""
        # First check advanced rules
        key = f"{vendor}_{product_type}_{expansion}"
        stepping_rules = self.settings.get('advancedPricingRules', {}).get(key)
        
        # If no advanced rules, use custom stepping
        if not stepping_rules:
            stepping_rules = self.settings.get('customStepping')
            if not stepping_rules:
                stepping_rules = {
                    'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50
                }
        
        condition_map = {
            'near mint': 'nm', 'near-mint': 'nm', 'nearmint': 'nm', 'nm': 'nm',
            'lightly played': 'lp', 'lightly-played': 'lp', 'lightlyplayed': 'lp', 'lp': 'lp',
            'moderately played': 'mp', 'moderately-played': 'mp', 'moderatelyplayed': 'mp', 'mp': 'mp',
            'heavily played': 'hp', 'heavily-played': 'hp', 'heavilyplayed': 'hp', 'hp': 'hp',
            'damaged': 'dm', 'dm': 'dm'
        }
            
        condition_code = condition_map.get(condition.lower().strip(), 'nm')
        
        stepping_percentage = stepping_rules.get(condition_code, 100)
        
        # Convert percentage to decimal
        stepping_decimal = float(stepping_percentage) / 100
        
        return round(price * stepping_decimal, 2)

    def apply_price_rounding(self, price: float) -> float:
        if not self.settings.get('price_rounding_enabled', False):
            return round(price, 2)
            
        thresholds = sorted(self.settings.get('price_rounding_thresholds', [49, 99]))
        dollars = int(price)
        cents = round((price - dollars) * 100)
        
        for threshold in thresholds:
            if cents <= threshold:
                return dollars + (threshold / 100)
                
        return dollars + 1

    def calculate_final_price(self, sku_info: dict, catalog_item: dict) -> tuple:
        """
        Calculate the final price in local currency.
        All input prices in sku_info['pricingInfo'] should already be in local currency.
        """
        price_history = []
        try:
            # Get base price in local currency using price comparison rules
            base_price, is_missing = self.apply_price_comparison(sku_info.get('pricingInfo', {}))
            if is_missing:
                return None, True, price_history
                
            price_history.append({
                'step': 'Initial Price',
                'old_price': 0,
                'new_price': base_price,
                'details': 'Price after applying comparison rules'
            })

            # Apply TCG trend adjustments if enabled
            old_price = base_price
            try:
                tcg_trend_increasing = float(self.settings.get('tcg_trend_increasing', 0))
                tcg_trend_decreasing = float(self.settings.get('tcg_trend_decreasing', 0))
                
                if tcg_trend_increasing > 0 and base_price > old_price:
                    increase = tcg_trend_increasing / 100
                    base_price = round(base_price * (1 + increase), 2)
                    price_history.append({
                        'step': 'TCG Trend Increasing',
                        'old_price': old_price,
                        'new_price': base_price,
                        'details': f'Applied {tcg_trend_increasing}% increase'
                    })
                elif tcg_trend_decreasing > 0 and base_price < old_price:
                    decrease = tcg_trend_decreasing / 100
                    base_price = round(base_price * (1 - decrease), 2)
                    price_history.append({
                        'step': 'TCG Trend Decreasing',
                        'old_price': old_price,
                        'new_price': base_price,
                        'details': f'Applied {tcg_trend_decreasing}% decrease'
                    })
            except (ValueError, TypeError):
                # Continue with the base price
                pass

            # Apply game rules (min prices are already in local currency)
            old_price = base_price
            try:
                nm_price = self.apply_game_rules(
                    base_price,
                    catalog_item.get('gameName'),
                    catalog_item.get('product_type'),
                    catalog_item.get('rarity')
                )
                if nm_price != old_price:
                    price_history.append({
                        'step': 'Game Rules',
                        'old_price': old_price,
                        'new_price': nm_price,
                        'details': f'Applied game minimum price rules for {catalog_item.get("gameName")}'
                    })
            except Exception:
                nm_price = base_price  # Continue with the base price

            condition = sku_info.get('condName', 'Near Mint').lower().strip()
            username = catalog_item.get('username', '')
            
            # Special handling for specific users (Khaoz, FantasyTower, supercollectiblesmx)
            special_users = ['khaoz', 'fantasytower', 'supercollectiblesmx']
            is_special_user = username.lower() in special_users
            
            # For special users, apply minimum price only to NM condition before stepping
            if is_special_user:
                # Apply minimum price check to NM price only
                try:
                    min_price = float(self.settings.get('minPrice', 0))
                    old_price = nm_price
                    
                    # Only apply minimum price to NM condition
                    if condition == 'nm' and nm_price < min_price:
                        nm_price = min_price
                        price_history.append({
                            'step': 'Minimum Price (NM only)',
                            'old_price': old_price,
                            'new_price': nm_price,
                            'details': f'Applied minimum price of {min_price} to NM condition only'
                        })
                except (ValueError, TypeError):
                    # Continue with the previous price
                    pass
            
            # Apply condition stepping
            old_price = nm_price
            try:
                price = self.apply_condition_stepping(
                    nm_price,  # Use the min-price-adjusted NM price as base
                    condition,
                    catalog_item.get('vendor', ''),
                    catalog_item.get('product_type', ''),
                    catalog_item.get('expansionName', '')
                )
                if price != old_price:
                    price_history.append({
                        'step': 'Condition Stepping',
                        'old_price': old_price,
                        'new_price': price,
                        'details': f'Applied condition stepping for {condition}'
                    })
            except Exception:
                price = old_price  # Continue with the previous price

            # Apply minimum price check for non-special users or non-NM conditions
            if not is_special_user:
                try:
                    min_price = float(self.settings.get('minPrice', 0))
                    old_price = price
                    if price < min_price:
                        price = min_price
                        price_history.append({
                            'step': 'Minimum Price',
                            'old_price': old_price,
                            'new_price': price,
                            'details': f'Applied minimum price of {min_price}'
                        })
                except (ValueError, TypeError):
                    # Continue with the previous price
                    pass

            # Apply price rounding in local currency
            old_price = price
            try:
                if self.settings.get('price_rounding_enabled', False):
                    price = self.apply_price_rounding(price)
                    if price != old_price:
                        price_history.append({
                            'step': 'Price Rounding',
                            'old_price': old_price,
                            'new_price': price,
                            'details': f'Rounded to thresholds {self.settings.get("price_rounding_thresholds", [])}'
                        })
                else:
                    price = round(price, 2)
                    if price != old_price:
                        price_history.append({
                            'step': 'Standard Rounding',
                            'old_price': old_price,
                            'new_price': price,
                            'details': 'Rounded to 2 decimal places'
                        })
            except Exception:
                price = round(old_price, 2)  # Ensure we have a rounded price
            
            return price, False, price_history
            
        except Exception as e:
            logger.error(f"Error calculating final price: {str(e)}")
            logger.error(traceback.format_exc())
            price_history.append({
                'step': 'Error',
                'old_price': 0,
                'new_price': 0,
                'details': f'Error calculating price: {str(e)}'
            })
            return None, True, price_history

def prepare_shopify_settings(user_profile):
    """Convert user profile to standardized settings for PricingCalculator"""
    return {
        'use_highest_price': user_profile.get('use_highest_price', False),
        'price_comparison_pairs': user_profile.get('price_comparison_pairs', []),
        'price_modifiers': user_profile.get('price_modifiers', {}),
        'price_preference_order': user_profile.get('price_preference_order', ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']),
        'minPrice': float(user_profile.get('minPrice', 0.2)),
        'game_minimum_prices': user_profile.get('game_minimum_prices', {}),
        'advancedPricingRules': user_profile.get('advancedPricingRules', {}),
        'customStepping': user_profile.get('customStepping', {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50}),
        'price_rounding_enabled': user_profile.get('price_rounding_enabled', False),
        'price_rounding_thresholds': user_profile.get('price_rounding_thresholds', [49, 99]),
        'tcg_trend_increasing': user_profile.get('tcg_trend_increasing', 0.0),
        'tcg_trend_decreasing': user_profile.get('tcg_trend_decreasing', 0.0)
    }

def process_user(config, tcgplayer_api_key, job_id=None, check_cancelled=None, processed_count=0):
    try:
        username = config['username']
        selected_product_types = [pt.lower() for pt in config['selectedProductTypes']]
        user_profile = user_collection.find_one({'username': username})
        if not user_profile:
            return f"{username}: User profile not found"

        # Process all products regardless of when they were last repriced
        query = {
            'username': username,
            'product_type': {
                '$regex': f'^({"|".join(map(re.escape, selected_product_types))})$',
                '$options': 'i',
                '$not': {'$regex': r'.*seal.*', '$options': 'i'}
            },
            'rarity': {'$not': {'$regex': r'.*seal.*', '$options': 'i'}},
            '$or': [
                {'manualOverride': {'$exists': False}},
                {'manualOverride': False}
            ]
        }

        total_products = shopify_collection.count_documents(query)
        if not total_products:
            return f"{username}: No products found"

        is_test_user = username.lower() == TEST_USERNAME.lower()
        user_currency = user_profile.get('currency', 'USD')
        exchange_rate = get_exchange_rate(user_currency)
        
        # Create pricing calculator with user settings
        settings = prepare_shopify_settings(user_profile)
        calculator = PricingCalculator(settings, user_currency)

        # Only fetch fields we need to reduce data transfer
        projection = {
            'productId': 1,
            'variants': 1,
            'product_type': 1,
            'gameName': 1,
            'rarity': 1,
            'vendor': 1,
            'expansionName': 1,
            'manualOverride': 1
        }
        cursor = shopify_collection.find(query, projection, batch_size=BATCH_SIZE)
        bulk_updates = []
        total_updates = 0
        total_price_changes = 0
        
        # For logging a random changed record
        random_changed_record = None
        random_record_price_history = None

        while True:
            # Check if job has been cancelled
            if check_cancelled and check_cancelled():
                logger.info(f"Job {job_id} was cancelled, stopping processing")
                return f"Processed {processed_count} products (cancelled), Updated {total_updates} products, Changed {total_price_changes} prices"
                
            batch = list(islice(cursor, BATCH_SIZE))
            if not batch:
                break

            filtered_batch = [p for p in batch if p.get('product_type', '').lower() in selected_product_types]
            product_ids = [str(p.get('productId')) for p in filtered_batch if p.get('productId')]
            pricing_data = fetch_pricing_data(product_ids, tcgplayer_api_key)
            
            for product in filtered_batch:
                try:
                    if product.get('manualOverride', False):
                        continue

                    product_id = str(product.get('productId'))
                    if not product_id or product_id not in pricing_data:
                        continue

                    product_pricing = pricing_data[product_id]
                    
                    # Only include subtypes that have valid prices
                    valid_subtypes = [p.get('subTypeName') for p in product_pricing 
                                    if p.get('subTypeName') and has_valid_price(p)]
                    
                    variants_changed = False
                    variants = product['variants']
                    
                    for variant in variants:
                        try:
                            old_price = float(variant.get('price', 0))
                            printing_type = determine_printing_type(variant['title'], valid_subtypes)
                            original_printing_type = printing_type # Store original for logging

                            # --- Start: Pokemon 1st Edition/Unlimited Edge Case (Optimized) ---
                            # Only run this for Pokemon products to avoid unnecessary processing
                            game_name = product.get('gameName', '').lower()
                            if game_name == 'pokemon' and valid_subtypes and len(valid_subtypes) > 1:
                                try:
                                    # Quick check if we need to apply the edge case
                                    has_1st_ed = False
                                    has_unlimited = False
                                    unlimited_subtype_name = None
                                    
                                    # Check if this is a 1st Edition/Unlimited only case
                                    for subtype in valid_subtypes:
                                        st_lower = subtype.lower()
                                        if '1st edition' in st_lower:
                                            has_1st_ed = True
                                        elif 'unlimited' in st_lower or 'holo' in st_lower or 'foil' in st_lower or 'normal' in st_lower:
                                            has_unlimited = True
                                            # Store the first unlimited-type subtype we find
                                            if unlimited_subtype_name is None:
                                                unlimited_subtype_name = subtype
                                            # Prioritize explicit "Unlimited" variants
                                            if 'unlimited' in st_lower:
                                                unlimited_subtype_name = subtype
                                    
                                    # Only proceed if we have both 1st edition and unlimited options
                                    if has_1st_ed and has_unlimited and unlimited_subtype_name:
                                        # Check if current printing type is NOT 1st edition
                                        is_1st_edition = '1st edition' in printing_type.lower()
                                        
                                        # If not 1st edition and we have only these two options, use unlimited
                                        if not is_1st_edition:
                                            # Only log when we actually change the printing type
                                            if printing_type != unlimited_subtype_name:
                                                # Log at debug level to reduce I/O impact
                                                if logger.isEnabledFor(logging.DEBUG):
                                                    logger.debug(f"Pokemon edge case: Using '{unlimited_subtype_name}' for variant '{variant.get('title', 'N/A')}' (Product ID: {product_id})")
                                            printing_type = unlimited_subtype_name
                                except Exception:
                                    # Simplified exception handling - don't log full traceback for performance
                                    pass
                            # --- End: Pokemon 1st Edition/Unlimited Edge Case (Optimized) ---
                            
                            # Find the price data for this printing type - optimized matching logic
                            matched_price = None
                            printing_type_lower = printing_type.lower()
                            
                            # Create lookup dictionaries for faster matching
                            exact_matches = {}
                            holofoil_matches = {}
                            normal_matches = {}
                            valid_prices = {}
                            
                            # Single pass through product_pricing to categorize all matches
                            for p in product_pricing:
                                if not has_valid_price(p):
                                    continue
                                    
                                subtype = p.get('subTypeName', '').lower()
                                if not subtype:
                                    continue
                                    
                                # Store in appropriate category
                                if subtype == printing_type_lower:
                                    exact_matches[subtype] = p
                                elif 'holofoil' in subtype:
                                    holofoil_matches[subtype] = p
                                elif subtype == 'normal':
                                    normal_matches[subtype] = p
                                
                                # Store all valid prices
                                valid_prices[subtype] = p
                            
                            # Try matches in priority order
                            if exact_matches:
                                matched_price = next(iter(exact_matches.values()))
                            elif holofoil_matches:
                                matched_price = next(iter(holofoil_matches.values()))
                            elif normal_matches:
                                matched_price = next(iter(normal_matches.values()))
                            elif valid_prices:
                                matched_price = next(iter(valid_prices.values()))

                            if matched_price:
                                # Extract pricing info for this printing type and convert from USD to user currency
                                pricing_info = {}
                                
                                # Get market price first as it's often the most reliable
                                market_price = matched_price.get('marketPrice')
                                if market_price is not None:
                                    # Convert from USD to user currency
                                    pricing_info['marketPrice'] = float(market_price) * exchange_rate
                                
                                # Get low price
                                low_price = matched_price.get('lowPrice')
                                if low_price is not None:
                                    # Convert from USD to user currency
                                    pricing_info['lowPrice'] = float(low_price) * exchange_rate
                                
                                # Get mid price (fallback to market price if not available)
                                mid_price = matched_price.get('midPrice')
                                if mid_price is None and market_price is not None:
                                    mid_price = market_price
                                if mid_price is not None:
                                    # Convert from USD to user currency
                                    pricing_info['midPrice'] = float(mid_price) * exchange_rate
                                
                                # Get high price (fallback to market price if not available)
                                high_price = matched_price.get('highPrice')
                                if high_price is None and market_price is not None:
                                    high_price = market_price
                                if high_price is not None:
                                    # Convert from USD to user currency
                                    pricing_info['highPrice'] = float(high_price) * exchange_rate

                                # Update product info to include printing type and condition
                                product['printingType'] = printing_type
                                condition = extract_condition(variant.get('option1', ''))
                                
                                # Create sku_info for price calculation
                                sku_info = {
                                    'pricingInfo': pricing_info,
                                    'condName': condition,
                                    'printingName': printing_type,
                                    'skuId': variant.get('id'),
                                    'variantTitle': variant.get('title')
                                }
                                
                                # Calculate new price
                                new_price, is_missing, price_history = calculator.calculate_final_price(sku_info, product)
                                
                                if not is_missing and new_price is not None:
                                    if abs(old_price - new_price) > 0.01:
                                        variant['price'] = str(new_price)
                                        variants_changed = True
                                        total_price_changes += 1
                                        
                                        # Capture a random changed record for logging
                                        # We use a simple probability to select a random record
                                        # The more records we process, the lower the probability
                                        # to ensure we don't always pick the first one
                                        if random_changed_record is None or random.random() < 0.1:
                                            random_changed_record = {
                                                'product_id': product_id,
                                                'title': product.get('title', 'Unknown Product'),
                                                'variant_title': variant.get('title', 'Unknown Variant'),
                                                'old_price': old_price,
                                                'new_price': new_price,
                                                'condition': condition,
                                                'printing_type': printing_type,
                                                'game_name': product.get('gameName'),
                                                'product_type': product.get('product_type')
                                            }
                                            random_record_price_history = price_history
                        except Exception as e:
                            logger.error(f"Error processing variant {variant.get('title')}: {str(e)}")
                            continue

                    # Create a summary of the price calculation process
                    summary_lines = []
                    summary_lines.append("*" * 80)
                    summary_lines.append("PRICE CALCULATION SUMMARY")
                    summary_lines.append("*" * 80)
                    summary_lines.append(f"\nProduct: {product.get('title', 'Unknown Product')}")
                    summary_lines.append(f"Product ID: {product_id}")
                    summary_lines.append(f"User: {username}")
                    summary_lines.append(f"Currency: {user_currency}")
                    summary_lines.append(f"Exchange Rate: {exchange_rate}")
                    summary_lines.append(f"Timestamp: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
                    
                    # Add TCGPlayer price fetch details
                    summary_lines.append(f"\nTCGPlayer Prices:")
                    for variant in variants:
                        variant_title = variant.get('title', 'Unknown Variant')
                        summary_lines.append(f"\n  {variant_title}:")
                        
                        # Get the pricing data for this variant
                        printing_type = determine_printing_type(variant['title'], valid_subtypes)
                        printing_type_lower = printing_type.lower()
                        
                        # Find the matched price data
                        matched_price = None
                        for p in product_pricing:
                            if p.get('subTypeName', '').lower() == printing_type_lower:
                                matched_price = p
                                break
                        
                        if matched_price:
                            summary_lines.append(f"    Printing Type: {printing_type}")
                            summary_lines.append(f"    Market Price: ${matched_price.get('marketPrice', 'N/A')}")
                            summary_lines.append(f"    Low Price: ${matched_price.get('lowPrice', 'N/A')}")
                            summary_lines.append(f"    Direct Low Price: ${matched_price.get('directLowPrice', 'N/A')}")
                            summary_lines.append(f"    Mid Price: ${matched_price.get('midPrice', 'N/A')}")
                            summary_lines.append(f"    High Price: ${matched_price.get('highPrice', 'N/A')}")
                        else:
                            summary_lines.append(f"    No matching price data found for {printing_type}")
                    
                    # Add variant price changes
                    summary_lines.append("\nVariant Price Changes:")
                    for variant in variants:
                        old_price = float(variant.get('old_price', 0)) if hasattr(variant, 'old_price') else float(variant.get('price', 0))
                        new_price = float(variant.get('price', 0))
                        variant_title = variant.get('title', 'Unknown Variant')
                        
                        if abs(old_price - new_price) > 0.01:
                            change_pct = ((new_price/old_price)-1)*100 if old_price > 0 else 0
                            summary_lines.append(f"  {variant_title}: ${old_price:.2f} -> ${new_price:.2f} ({change_pct:+.2f}%)")
                        else:
                            summary_lines.append(f"  {variant_title}: ${new_price:.2f} (unchanged)")
                    
                    # Add user settings used for calculation
                    summary_lines.append("\nUser Settings:")
                    summary_lines.append(f"  Min Price: ${settings.get('minPrice', 0)}")
                    summary_lines.append(f"  Price Preference Order: {settings.get('price_preference_order', [])}")
                    summary_lines.append(f"  Use Highest Price: {settings.get('use_highest_price', False)}")
                    summary_lines.append(f"  Price Comparison Pairs: {settings.get('price_comparison_pairs', [])}")
                    summary_lines.append(f"  Custom Stepping: {settings.get('customStepping', {})}")
                    
                    # Join all summary lines into a single string
                    summary_text = "\n".join(summary_lines)
                    
                    # Always update the last_repriced timestamp, even if prices didn't change
                    # Only set needsPushing and update variants if prices changed
                    update_fields = {
                        'last_repriced': datetime.now(timezone.utc),
                        'summary': summary_text  # Add the summary to the document
                    }
                    
                    if variants_changed:
                        update_fields['variants'] = variants
                        update_fields['needsPushing'] = True
                        total_price_changes += 1
                    
                    bulk_updates.append(
                        UpdateOne(
                            {'_id': product['_id']},
                            {'$set': update_fields}
                        )
                    )
                    total_updates += 1

                except Exception as e:
                    logger.error(f"Error processing product {product.get('_id')}: {str(e)}")
                    continue

            if bulk_updates:
                try:
                    shopify_collection.bulk_write(bulk_updates, ordered=False)
                    bulk_updates = []
                except Exception as e:
                    logger.error(f"Error during bulk write: {str(e)}")
                    continue

        # Log a random changed record if one was captured
        if random_changed_record and random_record_price_history:
            # Get all variants for this product to show before/after for all of them
            product_id = random_changed_record.get('product_id')
            full_product = None
            if product_id:
                try:
                    full_product = shopify_collection.find_one({"productId": product_id, "username": username})
                except Exception as e:
                    logger.error(f"Error fetching full product details: {str(e)}")
            
            # Log to console
            logger.info("=== Random Changed Record Example ===")
            logger.info(f"Product: {random_changed_record.get('title', 'Unknown')} (ID: {product_id})")
            logger.info(f"Game: {random_changed_record.get('game_name', 'Unknown')}")
            logger.info(f"Product Type: {random_changed_record.get('product_type', 'Unknown')}")
            
            # Log all variants
            if full_product and 'variants' in full_product:
                logger.info("All Variants:")
                for variant in full_product['variants']:
                    variant_id = variant.get('id', 'Unknown')
                    variant_title = variant.get('title', 'Unknown')
                    variant_price = variant.get('price', '0.00')
                    
                    # Check if this is the variant that was changed
                    is_changed = (random_changed_record.get('variant_title') == variant_title)
                    
                    if is_changed:
                        logger.info(f"  * {variant_title} (ID: {variant_id}): ${random_changed_record.get('old_price', 0):.2f} -> ${random_changed_record.get('new_price', 0):.2f} [CHANGED]")
                    else:
                        logger.info(f"  - {variant_title} (ID: {variant_id}): ${float(variant_price):.2f}")
            else:
                # Fallback if we can't get all variants
                logger.info(f"Changed Variant: {random_changed_record.get('variant_title', 'Unknown')}")
                logger.info(f"Condition: {random_changed_record.get('condition', 'Unknown')}")
                logger.info(f"Printing Type: {random_changed_record.get('printing_type', 'Unknown')}")
                logger.info(f"Price Change: ${random_changed_record.get('old_price', 0):.2f} -> ${random_changed_record.get('new_price', 0):.2f}")
            
            # Log price calculation steps
            logger.info("Price Calculation Steps:")
            for step in random_record_price_history:
                logger.info(f"  {step['step']}: ${step['old_price']:.2f} -> ${step['new_price']:.2f} ({step['details']})")
            logger.info("===================================")
            
            # Store the random changed record details for MongoDB logging
            return_data = {
                "username": username,
                "total_products": total_products,
                "total_updates": total_updates,
                "total_price_changes": total_price_changes,
                "random_changed_record": {
                    "product_id": product_id,
                    "title": random_changed_record.get('title', 'Unknown'),
                    "game_name": random_changed_record.get('game_name', 'Unknown'),
                    "product_type": random_changed_record.get('product_type', 'Unknown'),
                    "changed_variant": {
                        "title": random_changed_record.get('variant_title', 'Unknown'),
                        "condition": random_changed_record.get('condition', 'Unknown'),
                        "printing_type": random_changed_record.get('printing_type', 'Unknown'),
                        "old_price": random_changed_record.get('old_price', 0),
                        "new_price": random_changed_record.get('new_price', 0)
                    },
                    "price_history": random_record_price_history
                }
            }
            
            # Add all variants if available
            if full_product and 'variants' in full_product:
                return_data["random_changed_record"]["all_variants"] = []
                for variant in full_product['variants']:
                    is_changed = (random_changed_record.get('variant_title') == variant.get('title'))
                    variant_data = {
                        "id": variant.get('id', 'Unknown'),
                        "title": variant.get('title', 'Unknown'),
                        "current_price": float(variant.get('price', 0)),
                        "is_changed": is_changed
                    }
                    if is_changed:
                        variant_data["old_price"] = random_changed_record.get('old_price', 0)
                    
                    return_data["random_changed_record"]["all_variants"].append(variant_data)
            
            return f"{username}: Processed {total_products} products, Updated {total_updates} products, Changed {total_price_changes} prices", return_data

        return f"{username}: Processed {total_products} products, Updated {total_updates} products, Changed {total_price_changes} prices"

    except Exception as e:
        logger.error(f"Error in process_user: {str(e)}")
        return f"{username}: Error - {str(e)}"

def bulk_reprice(max_workers=50):  # Increased worker threads for better parallelization
    try:
        start_time = time.time()
        currency_cache.clear_expired()
        
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            logger.error("TCGPlayer key not found")
            return

        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        # Process all users with autopricer configurations
        autopricer_configs = list(autopricer_collection.find())
        
        if not autopricer_configs:
            logger.info("No autopricer configurations found")
            return
            
        # Log the configurations for all users
        logger.info(f"Found {len(autopricer_configs)} users to process")
        for config in autopricer_configs:
            logger.info(f"Will process user: {config.get('username')}")
            logger.info(f"Selected product types: {config.get('selectedProductTypes', [])}")
            
            # Get user profile to log settings
            user_profile = user_collection.find_one({'username': config.get('username')})
            if user_profile:
                settings = prepare_shopify_settings(user_profile)
                logger.info(f"Pricing settings for {config.get('username')}:")
                logger.info(f"  Min Price: {settings.get('minPrice')}")
                logger.info(f"  Price Preference Order: {settings.get('price_preference_order')}")
                logger.info(f"  Price Modifiers: {settings.get('price_modifiers')}")
                logger.info(f"  Use Highest Price: {settings.get('use_highest_price')}")
                logger.info(f"  Price Comparison Pairs: {settings.get('price_comparison_pairs')}")
                logger.info(f"  Custom Stepping: {settings.get('customStepping')}")
                logger.info(f"  Price Rounding Enabled: {settings.get('price_rounding_enabled')}")
                logger.info(f"  Price Rounding Thresholds: {settings.get('price_rounding_thresholds')}")
                logger.info(f"  TCG Trend Increasing: {settings.get('tcg_trend_increasing')}%")
                logger.info(f"  TCG Trend Decreasing: {settings.get('tcg_trend_decreasing')}%")
                
                # Log game minimum prices if available
                game_min_prices = settings.get('game_minimum_prices', {})
                if game_min_prices:
                    logger.info(f"  Game Minimum Prices:")
                    for game, game_settings in game_min_prices.items():
                        logger.info(f"    {game}: Default Min: {game_settings.get('default_min_price')}")
                        if 'rarities' in game_settings:
                            for rarity, price in game_settings['rarities'].items():
                                logger.info(f"      {rarity}: {price}")

        # Track overall statistics
        total_processed = 0
        total_updated = 0
        total_price_changes = 0
        completed_users = 0
        all_results = []
        
        # Process users in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_user, config, tcgplayer_api_key): config 
                      for config in autopricer_configs}
            
            for future in as_completed(futures):
                user_config = futures[future]
                username = user_config.get('username')
                user_start_time = time.time()
                
                try:
                    result = future.result()
                    
                    # Process the result
                    if isinstance(result, tuple) and len(result) == 2:
                        # If process_user returned a tuple with result string and return_data
                        result_str, return_data = result
                        all_results.append(result_str)
                        
                        # Log the result
                        logger.info(result_str)
                        
                        # Parse the result string to extract statistics
                        parts = result_str.split(", ")
                        user_processed = 0
                        user_updated = 0
                        user_price_changes = 0
                        
                        if len(parts) >= 3:
                            processed_match = re.search(r'Processed (\d+)', parts[0])
                            updated_match = re.search(r'Updated (\d+)', parts[1])
                            changed_match = re.search(r'Changed (\d+)', parts[2])
                            
                            if processed_match:
                                user_processed = int(processed_match.group(1))
                                total_processed += user_processed
                            if updated_match:
                                user_updated = int(updated_match.group(1))
                                total_updated += user_updated
                            if changed_match:
                                user_price_changes = int(changed_match.group(1))
                                total_price_changes += user_price_changes
                        
                        # Get user settings
                        user_settings = {}
                        user_product_types = []
                        
                        user_profile = user_collection.find_one({'username': username})
                        if user_profile:
                            user_settings = prepare_shopify_settings(user_profile)
                            user_product_types = user_config.get('selectedProductTypes', [])
                        
                        # Create log entry for this user
                        user_execution_time = time.time() - user_start_time
                        
                        log_data = {
                            "timestamp": datetime.now(timezone.utc),
                            "username": username,
                            "execution_time_seconds": user_execution_time,
                            "results": [result_str],
                            "total_processed": user_processed,
                            "total_updated": user_updated,
                            "total_price_changes": user_price_changes,
                            "settings": user_settings,
                            "product_types": user_product_types
                        }
                        
                        # Add random changed records if available
                        if return_data:
                            log_data["random_changed_records"] = return_data
                        
                        # Immediately insert log into MongoDB
                        reprice_logs_collection.insert_one(log_data)
                        logger.info(f"Repricing log for {username} saved to database with timestamp {log_data['timestamp']}")
                    else:
                        # If process_user returned just a string (no random changed record)
                        all_results.append(result)
                        logger.info(result)
                    
                    completed_users += 1
                    logger.info(f"Completed {completed_users}/{len(autopricer_configs)} users")
                    
                except Exception as e:
                    logger.error(f"Error processing user {username}: {str(e)}")
                    logger.error(traceback.format_exc())

        execution_time = time.time() - start_time
        logger.info("=== Repricing Summary ===")
        for result in sorted(all_results):
            logger.info(result)
        logger.info(f"Total execution time: {execution_time:.2f} seconds")
        logger.info(f"Overall Summary: Processed {total_processed}, Updated {total_updated}, Changed {total_price_changes} prices across {completed_users} users")
        logger.info("========================")

    except Exception as e:
        logger.error(f"Error in bulk_reprice: {str(e)}")
        raise

def run_scheduled_task():
    try:
        start_time = time.time()
        # Count users to be processed
        user_count = autopricer_collection.count_documents({})
        logger.info(f"Starting scheduled repricing task for {user_count} users")
        bulk_reprice()
        execution_time = time.time() - start_time
        logger.info(f"Completed scheduled repricing task in {execution_time:.2f} seconds")
        
        # Schedule the next run to occur 30 minutes after completion
        logger.info("Scheduling next run in 30 minutes")
        schedule.clear('rerun_task')  # Clear any existing rerun tasks
        schedule.every(30).minutes.do(run_scheduled_task).tag('rerun_task')
    except Exception as e:
        logger.error(f"Error in scheduled task: {str(e)}")

def cleanup_resources():
    try:
        mongo_client.close()
        session.close()
        logger.info("Successfully cleaned up resources")
    except Exception as e:
        logger.error(f"Error during resource cleanup: {str(e)}")

def handle_shutdown(signum, frame):
    logger.info("Received shutdown signal, cleaning up...")
    cleanup_resources()
    sys.exit(0)

def main():
    try:
        logger.info("Initializing scheduled repricing script for all users")
        
        signal.signal(signal.SIGTERM, handle_shutdown)
        signal.signal(signal.SIGINT, handle_shutdown)
        
        # We no longer need the 2-hour schedule since we'll rerun every 30 minutes after completion
        # Instead, we'll just schedule the currency cache cleanup
        schedule.every(12).hours.do(currency_cache.clear_expired)
        
        # Count users to be processed
        user_count = autopricer_collection.count_documents({})
        logger.info(f"Found {user_count} users with autopricer configurations")
        
        # Run initial repricing task
        run_scheduled_task()
        
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)
            except Exception as e:
                logger.error(f"Error in schedule loop: {str(e)}")
                time.sleep(60)
                
    except Exception as e:
        logger.error(f"Unhandled exception in main: {str(e)}")
    finally:
        cleanup_resources()

def reprice_user_inventory(username, job_id=None, progress_callback=None, check_cancelled=None):
    """
    Reprice a specific user's inventory on demand.
    
    Args:
        username (str): The username of the user whose inventory should be repriced
        job_id (str, optional): The ID of the job for tracking progress
        progress_callback (callable, optional): A callback function to report progress
                                               with signature (job_id, processed_count)
        check_cancelled (callable, optional): A function that returns True if the job
                                             has been cancelled and should stop processing
        
    Returns:
        tuple: (result_message, detailed_data) where result_message is a string summary
               and detailed_data is a dictionary with detailed information (if available)
    """
    try:
        logger.info(f"Manual repricing triggered for user: {username}")
        
        # Get user's autopricer configuration
        user_config = autopricer_collection.find_one({"username": username})
        if not user_config:
            logger.warning(f"No autopricer configuration found for user: {username}")
            return f"Error: No autopricer configuration found for {username}", None
        
        # Get TCGPlayer API key
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            logger.error("TCGPlayer key not found")
            return "Error: TCGPlayer API key not found", None
            
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        
        # Modify process_user to track progress
        if job_id and progress_callback:
            # Create a wrapper for process_user that tracks progress
            def process_user_with_progress(config, tcgplayer_api_key):
                try:
                    username = config['username']
                    selected_product_types = [pt.lower() for pt in config['selectedProductTypes']]
                    user_profile = user_collection.find_one({'username': username})
                    if not user_profile:
                        return f"{username}: User profile not found"

                    query = {
                        'username': username,
                        'product_type': {
                            '$regex': f'^({"|".join(map(re.escape, selected_product_types))})$',
                            '$options': 'i',
                            '$not': {'$regex': r'.*seal.*', '$options': 'i'}
                        },
                        'rarity': {'$not': {'$regex': r'.*seal.*', '$options': 'i'}},
                        '$or': [
                            {'manualOverride': {'$exists': False}},
                            {'manualOverride': False}
                        ]
                    }

                    total_products = shopify_collection.count_documents(query)
                    if not total_products:
                        return f"{username}: No products found"

                    is_test_user = username.lower() == TEST_USERNAME.lower()
                    user_currency = user_profile.get('currency', 'USD')
                    exchange_rate = get_exchange_rate(user_currency)
                    
                    # Create pricing calculator with user settings
                    settings = prepare_shopify_settings(user_profile)
                    calculator = PricingCalculator(settings, user_currency)

                    # Only fetch fields we need to reduce data transfer
                    projection = {
                        'productId': 1,
                        'variants': 1,
                        'product_type': 1,
                        'gameName': 1,
                        'rarity': 1,
                        'vendor': 1,
                        'expansionName': 1,
                        'manualOverride': 1
                    }
                    cursor = shopify_collection.find(query, projection, batch_size=BATCH_SIZE)
                    bulk_updates = []
                    total_updates = 0
                    total_price_changes = 0
                    processed_count = 0
                    
                    # For logging a random changed record
                    random_changed_record = None
                    random_record_price_history = None

                    while True:
                        # Check if job has been cancelled
                        if check_cancelled and check_cancelled():
                            logger.info(f"Job {job_id} was cancelled, stopping processing")
                            return f"Processed {processed_count} products (cancelled), Updated {total_updates} products, Changed {total_price_changes} prices"
                            
                        batch = list(islice(cursor, BATCH_SIZE))
                        if not batch:
                            break

                        filtered_batch = [p for p in batch if p.get('product_type', '').lower() in selected_product_types]
                        product_ids = [str(p.get('productId')) for p in filtered_batch if p.get('productId')]
                        pricing_data = fetch_pricing_data(product_ids, tcgplayer_api_key)
                        
                        for product in filtered_batch:
                            processed_count += 1
                            
                            # Update progress every 10 products or at the end of each batch
                            if processed_count % 10 == 0 or processed_count == len(filtered_batch):
                                progress_callback(job_id, processed_count)
                                logger.info(f"Progress update: {processed_count}/{total_products} products processed")
                                
                            try:
                                if product.get('manualOverride', False):
                                    continue

                                product_id = str(product.get('productId'))
                                if not product_id or product_id not in pricing_data:
                                    continue

                                product_pricing = pricing_data[product_id]
                                
                                # Only include subtypes that have valid prices
                                valid_subtypes = [p.get('subTypeName') for p in product_pricing 
                                                if p.get('subTypeName') and has_valid_price(p)]
                                
                                variants_changed = False
                                variants = product['variants']
                                
                                for variant in variants:
                                    try:
                                        old_price = float(variant.get('price', 0))
                                        printing_type = determine_printing_type(variant['title'], valid_subtypes)
                                        
                                        # Find the price data for this printing type - optimized matching logic
                                        matched_price = None
                                        printing_type_lower = printing_type.lower()
                                        
                                        # Create lookup dictionaries for faster matching
                                        exact_matches = {}
                                        holofoil_matches = {}
                                        normal_matches = {}
                                        valid_prices = {}
                                        
                                        # Single pass through product_pricing to categorize all matches
                                        for p in product_pricing:
                                            if not has_valid_price(p):
                                                continue
                                                
                                            subtype = p.get('subTypeName', '').lower()
                                            if not subtype:
                                                continue
                                                
                                            # Store in appropriate category
                                            if subtype == printing_type_lower:
                                                exact_matches[subtype] = p
                                            elif 'holofoil' in subtype:
                                                holofoil_matches[subtype] = p
                                            elif subtype == 'normal':
                                                normal_matches[subtype] = p
                                            
                                            # Store all valid prices
                                            valid_prices[subtype] = p
                                        
                                        # Try matches in priority order
                                        if exact_matches:
                                            matched_price = next(iter(exact_matches.values()))
                                        elif holofoil_matches:
                                            matched_price = next(iter(holofoil_matches.values()))
                                        elif normal_matches:
                                            matched_price = next(iter(normal_matches.values()))
                                        elif valid_prices:
                                            matched_price = next(iter(valid_prices.values()))

                                        if matched_price:
                                            # Extract pricing info for this printing type and convert from USD to user currency
                                            pricing_info = {}
                                            
                                            # Get market price first as it's often the most reliable
                                            market_price = matched_price.get('marketPrice')
                                            if market_price is not None:
                                                # Convert from USD to user currency
                                                pricing_info['marketPrice'] = float(market_price) * exchange_rate
                                            
                                            # Get low price
                                            low_price = matched_price.get('lowPrice')
                                            if low_price is not None:
                                                # Convert from USD to user currency
                                                pricing_info['lowPrice'] = float(low_price) * exchange_rate
                                            
                                            # Get mid price (fallback to market price if not available)
                                            mid_price = matched_price.get('midPrice')
                                            if mid_price is None and market_price is not None:
                                                mid_price = market_price
                                            if mid_price is not None:
                                                # Convert from USD to user currency
                                                pricing_info['midPrice'] = float(mid_price) * exchange_rate
                                            
                                            # Get high price (fallback to market price if not available)
                                            high_price = matched_price.get('highPrice')
                                            if high_price is None and market_price is not None:
                                                high_price = market_price
                                            if high_price is not None:
                                                # Convert from USD to user currency
                                                pricing_info['highPrice'] = float(high_price) * exchange_rate

                                            # Update product info to include printing type and condition
                                            product['printingType'] = printing_type
                                            condition = extract_condition(variant.get('option1', ''))
                                            
                                            # Create sku_info for price calculation
                                            sku_info = {
                                                'pricingInfo': pricing_info,
                                                'condName': condition,
                                                'printingName': printing_type,
                                                'skuId': variant.get('id'),
                                                'variantTitle': variant.get('title')
                                            }
                                            
                                            # Calculate new price
                                            new_price, is_missing, price_history = calculator.calculate_final_price(sku_info, product)
                                            
                                            if not is_missing and new_price is not None:
                                                if abs(old_price - new_price) > 0.01:
                                                    variant['price'] = str(new_price)
                                                    variants_changed = True
                                                    total_price_changes += 1
                                                    
                                                    # Capture a random changed record for logging
                                                    # We use a simple probability to select a random record
                                                    # The more records we process, the lower the probability
                                                    # to ensure we don't always pick the first one
                                                    if random_changed_record is None or random.random() < 0.1:
                                                        random_changed_record = {
                                                            'product_id': product_id,
                                                            'title': product.get('title', 'Unknown Product'),
                                                            'variant_title': variant.get('title', 'Unknown Variant'),
                                                            'old_price': old_price,
                                                            'new_price': new_price,
                                                            'condition': condition,
                                                            'printing_type': printing_type,
                                                            'game_name': product.get('gameName'),
                                                            'product_type': product.get('product_type')
                                                        }
                                                        random_record_price_history = price_history
                                    except Exception as e:
                                        logger.error(f"Error processing variant {variant.get('title')}: {str(e)}")
                                        continue

                                # Always update the last_repriced timestamp, even if prices didn't change
                                # Only set needsPushing and update variants if prices changed
                                update_fields = {
                                    'last_repriced': datetime.now(timezone.utc)
                                }
                                
                                if variants_changed:
                                    update_fields['variants'] = variants
                                    update_fields['needsPushing'] = True
                                    total_price_changes += 1
                                
                                bulk_updates.append(
                                    UpdateOne(
                                        {'_id': product['_id']},
                                        {'$set': update_fields}
                                    )
                                )
                                total_updates += 1

                            except Exception as e:
                                logger.error(f"Error processing product {product.get('_id')}: {str(e)}")
                                continue

                        if bulk_updates:
                            try:
                                shopify_collection.bulk_write(bulk_updates, ordered=False)
                                bulk_updates = []
                            except Exception as e:
                                logger.error(f"Error during bulk write: {str(e)}")
                                continue

                    # Final progress update
                    progress_callback(job_id, processed_count)

                    # Log a random changed record if one was captured
                    if random_changed_record and random_record_price_history:
                        # Get all variants for this product to show before/after for all of them
                        product_id = random_changed_record.get('product_id')
                        full_product = None
                        if product_id:
                            try:
                                full_product = shopify_collection.find_one({"productId": product_id, "username": username})
                            except Exception as e:
                                logger.error(f"Error fetching full product details: {str(e)}")
                        
                        # Store the random changed record details for MongoDB logging
                        return_data = {
                            "username": username,
                            "total_products": total_products,
                            "total_updates": total_updates,
                            "total_price_changes": total_price_changes,
                            "random_changed_record": {
                                "product_id": product_id,
                                "title": random_changed_record.get('title', 'Unknown'),
                                "game_name": random_changed_record.get('game_name', 'Unknown'),
                                "product_type": random_changed_record.get('product_type', 'Unknown'),
                                "changed_variant": {
                                    "title": random_changed_record.get('variant_title', 'Unknown'),
                                    "condition": random_changed_record.get('condition', 'Unknown'),
                                    "printing_type": random_changed_record.get('printing_type', 'Unknown'),
                                    "old_price": random_changed_record.get('old_price', 0),
                                    "new_price": random_changed_record.get('new_price', 0)
                                },
                                "price_history": random_record_price_history
                            }
                        }
                        
                        # Add all variants if available
                        if full_product and 'variants' in full_product:
                            return_data["random_changed_record"]["all_variants"] = []
                            for variant in full_product['variants']:
                                is_changed = (random_changed_record.get('variant_title') == variant.get('title'))
                                variant_data = {
                                    "id": variant.get('id', 'Unknown'),
                                    "title": variant.get('title', 'Unknown'),
                                    "current_price": float(variant.get('price', 0)),
                                    "is_changed": is_changed
                                }
                                if is_changed:
                                    variant_data["old_price"] = random_changed_record.get('old_price', 0)
                                
                                return_data["random_changed_record"]["all_variants"].append(variant_data)
                        
                        return f"Processed {total_products} products, Updated {total_updates} products, Changed {total_price_changes} prices", return_data

                    return f"Processed {total_products} products, Updated {total_updates} products, Changed {total_price_changes} prices"

                except Exception as e:
                    logger.error(f"Error in process_user_with_progress: {str(e)}")
                    return f"{username}: Error - {str(e)}"
            
            # Use the progress-tracking version of process_user
            result = process_user_with_progress(user_config, tcgplayer_api_key)
        else:
            # Use the standard process_user function
            result = process_user(user_config, tcgplayer_api_key)
        
        # Log the result
        if isinstance(result, tuple) and len(result) == 2:
            logger.info(f"Manual repricing completed for {username}: {result[0]}")
        else:
            logger.info(f"Manual repricing completed for {username}: {result}")
            
        return result
        
    except Exception as e:
        error_msg = f"Error in manual repricing for {username}: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        return error_msg, None

if __name__ == "__main__":
    main()
