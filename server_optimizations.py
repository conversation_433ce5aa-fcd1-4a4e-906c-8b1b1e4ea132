from config.config import Config
import os
import subprocess
import sys
import psutil
import json

def optimize_gunicorn():
    """Optimize Gunicorn configuration"""
    gunicorn_config = {
        'workers': min(psutil.cpu_count() * 2 + 1, 6),  # Limit max workers
        'threads': 2,  # Enable threading
        'worker_class': 'gevent',  # Use gevent for async
        'worker_connections': 750,  # Limit connections per worker
        'max_requests': 1000,  # Restart workers after max requests
        'max_requests_jitter': 100,  # Add jitter to prevent all workers restarting at once
        'timeout': 30,  # Increase timeout for long-running requests
        'keepalive': 2,  # Keepalive timeout
        'preload_app': True,  # Preload app to share memory
    }
    
    config_content = f"""
import multiprocessing

# Server socket
bind = '127.0.0.1:8000'
backlog = 2048

# Worker processes
workers = {gunicorn_config['workers']}
worker_class = '{gunicorn_config['worker_class']}'
threads = {gunicorn_config['threads']}
worker_connections = {gunicorn_config['worker_connections']}
timeout = {gunicorn_config['timeout']}
keepalive = {gunicorn_config['keepalive']}
max_requests = {gunicorn_config['max_requests']}
max_requests_jitter = {gunicorn_config['max_requests_jitter']}
preload_app = {str(gunicorn_config['preload_app']).lower()}

# Process naming
proc_name = 'gunicorn_tcgsync'
default_proc_name = 'gunicorn_tcgsync'

# Logging
accesslog = '/var/log/gunicorn/access.log'
errorlog = '/var/log/gunicorn/error.log'
loglevel = 'info'
access_log_format = '%({X-Real-IP}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process management
daemon = False
pidfile = '/var/run/gunicorn/gunicorn.pid'
umask = 0
user = None
group = None
tmp_upload_dir = None

# SSL
keyfile = None
certfile = None

# Server mechanics
chdir = '/var/www/html/cfc.tcgsync.com'
worker_tmp_dir = '/dev/shm'  # Use tmpfs for worker tmp files
"""
    
    return config_content

def optimize_nginx():
    """Optimize Nginx configuration"""
    nginx_config = """
user www-data;
worker_processes auto;
worker_rlimit_nofile 65535;
pid /run/nginx.pid;

events {
    worker_connections 4096;
    multi_accept on;
    use epoll;
}

http {
    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    client_max_body_size 10M;

    # Buffer Settings
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # Compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_min_length 256;
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;

    # Proxy Settings
    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;
    proxy_temp_file_write_size 256k;
    proxy_connect_timeout 90;
    proxy_send_timeout 90;
    proxy_read_timeout 90;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=one:10m rate=10r/s;
    limit_conn_zone $binary_remote_addr zone=addr:10m;

    server {
        listen 80;
        server_name cfc.tcgsync.com;
        
        location / {
            proxy_pass http://127.0.0.1:8000;
            limit_req zone=one burst=20 nodelay;
            limit_conn addr 10;
        }
        
        location /static/ {
            expires 7d;
            add_header Cache-Control "public, no-transform";
            root /var/www/html/cfc.tcgsync.com;
        }
        
        location /webhook {
            proxy_pass http://127.0.0.1:8000;
            limit_req zone=one burst=50 nodelay;  # Higher burst for webhooks
            limit_conn addr 20;  # Higher concurrent connections
        }
    }
}
"""
    return nginx_config

def optimize_mongodb():
    """Optimize MongoDB configuration"""
    mongodb_config = """
# MongoDB Config
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

storage:
  dbPath: /var/lib/mongodb
  journal:
    enabled: true
  wiredTiger:
    engineConfig:
      cacheSizeGB: 2  # Adjust based on available RAM
      journalCompressor: snappy
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

operationProfiling:
  mode: slowOp
  slowOpThresholdMs: 100

net:
  port: 27017
  bindIp: 127.0.0.1
  maxIncomingConnections: 1000

security:
  authorization: enabled

replication:
  oplogSizeMB: 1024

processManagement:
  timeZoneInfo: /usr/share/zoneinfo

setParameter:
  maxTransactionLockRequestTimeoutMillis: 5000
  wiredTigerConcurrentReadTransactions: 128
  wiredTigerConcurrentWriteTransactions: 128
"""
    return mongodb_config

def optimize_system():
    """Optimize system settings"""
    sysctl_settings = """
# Network optimizations
net.core.somaxconn = 4096
net.core.netdev_max_backlog = 4096
net.ipv4.tcp_max_syn_backlog = 2048
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 500000
net.ipv4.ip_local_port_range = 1024 65535
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_syncookies = 1

# File system optimizations
fs.file-max = 100000
fs.inotify.max_user_instances = 256
fs.inotify.max_user_watches = 524288

# VM optimizations
vm.swappiness = 10
vm.dirty_ratio = 60
vm.dirty_background_ratio = 2
"""
    return sysctl_settings

def main():
    """Main optimization function"""
    try:
        # Create directories if they don't exist
        os.makedirs('/etc/gunicorn/conf.d', exist_ok=True)
        os.makedirs('/var/log/gunicorn', exist_ok=True)
        os.makedirs('/var/run/gunicorn', exist_ok=True)
        
        # Write configurations
        with open('/etc/gunicorn/conf.d/tcgsync.py', 'w') as f:
            f.write(optimize_gunicorn())
            
        with open('/etc/nginx/nginx.conf', 'w') as f:
            f.write(optimize_nginx())
            
        with open('/etc/mongod.conf', 'w') as f:
            f.write(optimize_mongodb())
            
        with open('/etc/sysctl.d/99-tcgsync.conf', 'w') as f:
            f.write(optimize_system())
        
        # Apply system settings
        subprocess.run(['sysctl', '--system'], check=True)
        
        # Restart services
        services = ['nginx', 'mongod', 'gunicorn']
        for service in services:
            subprocess.run(['systemctl', 'restart', service], check=True)
            
        print("Server optimization completed successfully")
        
    except Exception as e:
        print(f"Error during optimization: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()

