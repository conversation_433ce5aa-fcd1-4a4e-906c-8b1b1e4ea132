from config.config import Config
#!/usr/bin/env python3
"""
Debug script for testing the repricing service with a specific product.

This script tests the repricing service by repricing a specific product
for a specific user, with additional debugging information.

Usage:
    python test_specific_product_debug.py [--url URL] [--api-key API_KEY] [--username USERNAME] [--product-id PRODUCT_ID]

Options:
    --url URL          The URL of the repricing service [default: https://webhooks.tcgsync.com]
    --api-key API_KEY  The API key for the repricing service [default: savyQYfxgyzdzZNUvk59PAajTKTSUbzu]
    --username USERNAME The username to test repricing with [default: admintcg]
    --product-id PRODUCT_ID The product ID to test repricing with [default: 487665]
"""

import argparse
import sys
import time
import json
import requests
from datetime import datetime

try:
    from colorama import init, Fore, Style
    init()  # Initialize colorama
except ImportError:
    print("Required packages not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "colorama"])
    from colorama import init, Fore, Style
    init()  # Initialize colorama

def print_success(message):
    """Print a success message in green."""
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

def print_error(message):
    """Print an error message in red."""
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

def print_info(message):
    """Print an info message in blue."""
    print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")

def print_warning(message):
    """Print a warning message in yellow."""
    print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

def print_header(message):
    """Print a header message in cyan."""
    print(f"\n{Fore.CYAN}=== {message} ==={Style.RESET_ALL}")

def print_json(data):
    """Print JSON data in a formatted way."""
    print(json.dumps(data, indent=2))

def test_specific_product_debug(url, api_key, username, product_id):
    """Test repricing a specific product with debug information."""
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    print_info(f"Using API Key: {api_key}")

    # First, check if the product exists in the database
    print_header("Checking if product exists in the database")
    try:
        # We'll use a custom endpoint to check if the product exists
        check_url = f"{url}/api/check_product"
        data = {
            'username': username,
            'product_id': product_id
        }
        print_info(f"POST {check_url}")
        print_info(f"Data: {data}")
        
        response = requests.post(check_url, headers=headers, json=data)
        
        if response.status_code == 200:
            print_success(f"Product check successful (Status: {response.status_code})")
            result = response.json()
            print_json(result)
            
            if result.get('exists', False):
                print_success(f"Product {product_id} exists for user {username}")
                print_info(f"Product details:")
                print_info(f"  Title: {result.get('product', {}).get('title', 'Unknown')}")
                print_info(f"  Product Type: {result.get('product', {}).get('product_type', 'Unknown')}")
                print_info(f"  Game Name: {result.get('product', {}).get('gameName', 'Unknown')}")
                print_info(f"  Expansion: {result.get('product', {}).get('expansionName', 'Unknown')}")
                print_info(f"  Rarity: {result.get('product', {}).get('rarity', 'Unknown')}")
                
                # Print variants
                variants = result.get('product', {}).get('variants', [])
                if variants:
                    print_info(f"  Variants:")
                    for variant in variants:
                        print_info(f"    - {variant.get('title', 'Unknown')}: ${variant.get('price', '0.00')}")
            else:
                print_error(f"Product {product_id} does not exist for user {username}")
        else:
            print_error(f"Product check failed (Status: {response.status_code})")
            print(response.text)
    except Exception as e:
        print_error(f"Error checking product: {str(e)}")

    # Test health endpoint
    print_header("Testing Health Endpoint")
    try:
        health_url = f"{url}/api/health"
        print_info(f"GET {health_url}")
        response = requests.get(health_url, headers=headers)
        
        if response.status_code == 200:
            print_success(f"Health check successful (Status: {response.status_code})")
            print_json(response.json())
        else:
            print_error(f"Health check failed (Status: {response.status_code})")
            print(response.text)
            return
    except Exception as e:
        print_error(f"Error testing health endpoint: {str(e)}")
        return

    # Start repricing job with debug flag
    print_header(f"Starting Repricing Job for Product ID {product_id} and User {username} with Debug")
    try:
        reprice_url = f"{url}/api/reprice_debug"
        data = {
            'username': username,
            'product_id': product_id
        }
        print_info(f"POST {reprice_url}")
        print_info(f"Data: {data}")
        
        response = requests.post(reprice_url, headers=headers, json=data)
        
        if response.status_code in [200, 201]:
            print_success(f"Start repricing with debug successful (Status: {response.status_code})")
            result = response.json()
            print_json(result)
            
            if 'job_id' in result:
                job_id = result['job_id']
                print_success(f"Job ID: {job_id}")
            else:
                print_error("No job ID returned")
                return
        else:
            print_error(f"Start repricing with debug failed (Status: {response.status_code})")
            print(response.text)
            return
    except Exception as e:
        print_error(f"Error starting repricing job with debug: {str(e)}")
        return

    # Check job status
    print_header("Checking Job Status")
    try:
        status_url = f"{url}/api/status/{job_id}"
        print_info(f"GET {status_url}")
        
        # Check status multiple times to see progress
        for i in range(10):
            response = requests.get(status_url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status', 'unknown')
                print_success(f"Check #{i+1}: Status is '{status}' (Status code: {response.status_code})")
                print_json(result)
                
                # If the job is complete or has an error, stop checking
                if status in ['complete', 'error']:
                    # Check for debug information
                    if 'debug_info' in result:
                        print_header("Debug Information")
                        debug_info = result['debug_info']
                        print_json(debug_info)
                        
                        # Print pricing rules explanation
                        if 'user_settings' in debug_info and 'variant_results' in debug_info:
                            print_header("Pricing Rules Explanation")
                            user_settings = debug_info['user_settings']
                            variant_results = debug_info['variant_results']
                            
                            # Print user settings
                            print_info("User Settings:")
                            print_info(f"  Price Preference Order: {user_settings.get('price_preference_order', [])}")
                            print_info(f"  Minimum Price: ${user_settings.get('minPrice', 0)}")
                            print_info(f"  Custom Stepping: {user_settings.get('customStepping', {})}")
                            print_info(f"  Price Rounding Enabled: {user_settings.get('price_rounding_enabled', False)}")
                            print_info(f"  Price Rounding Thresholds: {user_settings.get('price_rounding_thresholds', [])}")
                            
                            # Print variant pricing explanations
                            print_info("\nVariant Pricing Explanations:")
                            for variant in variant_results:
                                title = variant.get('title', 'Unknown')
                                old_price = variant.get('old_price', 0)
                                new_price = variant.get('new_price', 0)
                                condition = variant.get('condition', 'Unknown')
                                pricing_info = variant.get('pricing_info', {})
                                price_history = variant.get('price_history', [])
                                
                                print_info(f"  {title}:")
                                print_info(f"    Old Price: ${old_price}")
                                print_info(f"    New Price: ${new_price}")
                                print_info(f"    Condition: {condition}")
                                
                                if pricing_info:
                                    print_info(f"    TCG Player Prices:")
                                    for price_type, price in pricing_info.items():
                                        print_info(f"      {price_type}: ${price}")
                                
                                if price_history:
                                    print_info(f"    Price Calculation Steps:")
                                    for step in price_history:
                                        print_info(f"      {step}")
                    break
            else:
                print_error(f"Check status failed (Status: {response.status_code})")
                print(response.text)
                break
                
            # Wait before checking again
            if i < 9:  # Don't wait after the last check
                print_info("Waiting 2 seconds before checking again...")
                time.sleep(2)
    except Exception as e:
        print_error(f"Error checking job status: {str(e)}")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test the Repricing Service with a specific product.')
    parser.add_argument('--url', default='https://webhooks.tcgsync.com',
                        help='The URL of the repricing service')
    parser.add_argument('--api-key', default='savyQYfxgyzdzZNUvk59PAajTKTSUbzu',
                        help='The API key for the repricing service')
    parser.add_argument('--username', default='admintcg',
                        help='The username to test repricing with')
    parser.add_argument('--product-id', type=int, default=487665,
                        help='The product ID to test repricing with')
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    print_header("Starting Specific Product Repricing Debug Test")
    print_info(f"Base URL: {args.url}")
    print_info(f"API Key: {'*' * len(args.api_key)}")
    print_info(f"Username: {args.username}")
    print_info(f"Product ID: {args.product_id}")
    print_info(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_specific_product_debug(args.url, args.api_key, args.username, args.product_id)

if __name__ == '__main__':
    main()

