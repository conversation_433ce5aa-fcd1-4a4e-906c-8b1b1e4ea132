from config.config import Config
import requests
import logging
from config import Config
from models.database import mongo

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_test_user():
    """Set up a test user with cardmarketUsername and email"""
    test_user = {
        "username": "testuser",
        "email": "<EMAIL>",  # Using admin email for testing
        "cardmarketUsername": "testmarketuser"  # Non-empty cardmarketUsername
    }
    
    # Insert or update test user
    mongo.pymongo_db.user.update_one(
        {"username": test_user["username"]},
        {"$set": test_user},
        upsert=True
    )
    
    return test_user["username"]

def send_test_cardmarket_email(username, order_data):
    """Send test email about checking Cardmarket listings"""
    try:
        logger.info(f"Starting send_test_cardmarket_email for user: {username}")
        
        # Get user data from MongoDB
        user = mongo.pymongo_db.user.find_one({"username": username})
        if not user:
            logger.error(f"No user found with username: {username}")
            return False
            
        # Check if user has Cardmarket integration
        cardmarket_username = user.get('cardmarketUsername')
        if not cardmarket_username or cardmarket_username.strip() == '':
            logger.info(f"User {username} does not have Cardmarket integration, skipping email")
            return False
            
        # Get user's email
        user_email = user.get('email')
        if not user_email:
            logger.error(f"No email found for user: {username}")
            return False
            
        logger.info(f"Sending Cardmarket check email to: {user_email}")
        
        MAILGUN_API_KEY = Config.MAILGUN_API_KEY
        MAILGUN_DOMAIN = Config.MAILGUN_DOMAIN

        if not MAILGUN_API_KEY or not MAILGUN_DOMAIN:
            logger.error("Mailgun API key or domain not set in config")
            raise ValueError("Mailgun API key or domain not set in config")

        # Format line items for email
        line_items = order_data.get('line_items', [])
        items_html = "<table style='width: 100%; border-collapse: collapse; margin-top: 10px;'>"
        items_html += """
            <tr style='background-color: #f8f9fa;'>
                <th style='padding: 10px; text-align: left; border: 1px solid #dee2e6;'>Item</th>
                <th style='padding: 10px; text-align: left; border: 1px solid #dee2e6;'>SKU</th>
                <th style='padding: 10px; text-align: left; border: 1px solid #dee2e6;'>Variant</th>
                <th style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>Price</th>
                <th style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>Quantity</th>
                <th style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>Total</th>
            </tr>
        """
        
        for item in line_items:
            items_html += f"""
                <tr>
                    <td style='padding: 10px; border: 1px solid #dee2e6;'>{item.get('title', 'N/A')}</td>
                    <td style='padding: 10px; border: 1px solid #dee2e6;'>{item.get('sku', 'N/A')}</td>
                    <td style='padding: 10px; border: 1px solid #dee2e6;'>{item.get('variant_title', 'N/A')}</td>
                    <td style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>£{item.get('price', '0.00')}</td>
                    <td style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>{item.get('quantity', 0)}</td>
                    <td style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>£{item.get('total_price', '0.00')}</td>
                </tr>
            """
        items_html += "</table>"

        # Get shipping address
        shipping_address = order_data.get('shipping_address', {})
        address_html = f"""
            <p><strong>Shipping to:</strong><br>
            {shipping_address.get('address1', '')}<br>
            {shipping_address.get('city', '')}<br>
            {shipping_address.get('province', '')}<br>
            {shipping_address.get('country', '')}<br>
            {shipping_address.get('zip', '')}</p>
        """

        # Create HTML content for the email
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
                .order-info {{
                    background-color: #fff;
                    padding: 20px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
                .alert {{
                    color: #004085;
                    background-color: #cce5ff;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>Shopify Order Received - Check Cardmarket Listings</h2>
            </div>
            <div class="alert">
                <p>A new order has been received that may affect your Cardmarket listings.</p>
                <p>Please check if these items need to be removed from your Cardmarket inventory.</p>
            </div>
            <div class="order-info">
                <h3>Order Details:</h3>
                <p><strong>Order ID:</strong> {order_data.get('id')}</p>
                <p><strong>Order Number:</strong> {order_data.get('order_number')}</p>
                <h4>Items to Check on Cardmarket:</h4>
                {items_html}
                {address_html}
            </div>
        </body>
        </html>
        """

        # Mailgun API endpoint
        url = f"https://api.eu.net/v3/{MAILGUN_DOMAIN}/messages"
        logger.info(f"Using Mailgun endpoint: {url}")

        # Email data
        data = {
            "from": f"TCGSync Order Alert <noreply@{MAILGUN_DOMAIN}>",
            "to": user_email,
            "subject": f"Action Required: Check Your Cardmarket Listings - New Shopify Order",
            "html": html_content
        }
        logger.info("Prepared email data")

        # Send the email
        logger.info("Sending email via Mailgun...")
        response = requests.post(
            url,
            auth=("api", MAILGUN_API_KEY),
            data=data,
            timeout=30  # 30 second timeout
        )

        if response.status_code == 200:
            logger.info(f"Test Cardmarket check email sent successfully to {user_email}. Response: {response.text}")
            return True
        else:
            logger.error(f"Failed to send test Cardmarket check email. Status code: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error sending test Cardmarket check email: {str(e)}")
        return False

def main():
    # Set up test user with cardmarketUsername
    username = setup_test_user()
    logger.info(f"Set up test user: {username}")

    # Mock order data with the exact item details provided
    test_order = {
        'id': '12345',
        'order_number': '1001',
        'line_items': [
            {
                'title': 'Aether Icevein (Blue) (UPR) (115)',
                'variant_title': 'Normal - Near Mint - EN',
                'sku': '5685617',
                'price': '0.07',
                'quantity': 1,
                'total_price': '0.07'
            }
        ],
        'shipping_address': {
            'address1': '1 Arran Park',
            'city': 'City',
            'province': 'Province',
            'country': 'United Kingdom',
            'zip': 'Postal Code'
        }
    }

    # Send test email
    result = send_test_cardmarket_email(username, test_order)
    
    if result:
        print("\nTest email sent successfully!")
        print("\nEmail Content Preview:")
        print("-" * 50)
        print(f"From: TCGSync Order Alert <noreply@{Config.MAILGUN_DOMAIN}>")
        print(f"To: {mongo.pymongo_db.user.find_one({'username': username})['email']}")
        print(f"Subject: Action Required: Check Your Cardmarket Listings - New Shopify Order")
        print("\nOrder Details:")
        print(f"Order ID: {test_order['id']}")
        print(f"Order Number: {test_order['order_number']}")
        print("\nItems to Check:")
        for item in test_order['line_items']:
            print(f"- {item['title']}")
            print(f"  SKU: {item['sku']}")
            print(f"  Variant: {item['variant_title']}")
            print(f"  Price: £{item['price']}")
            print(f"  Quantity: {item['quantity']}")
            print(f"  Total: £{item['total_price']}")
        print("\nShipping Address:")
        addr = test_order['shipping_address']
        print(f"{addr['address1']}")
        print(f"{addr['city']}")
        print(f"{addr['province']}")
        print(f"{addr['country']}")
        print(f"{addr['zip']}")
        print("-" * 50)
        print("\nCheck your email for the formatted message.")
    else:
        print("Failed to send test email - check the logs for details")

if __name__ == "__main__":
    main()

