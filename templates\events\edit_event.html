{% extends "base.html" %}

{% block title %}Edit Event - {{ event.title }}{% endblock %}

{% block styles %}
{{ super() }}
{% include "events/event_styles.html" %}
<style>
    .custom-field {
        margin-bottom: 10px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        position: relative;
    }
    .remove-field {
        position: absolute;
        top: 5px;
        right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid event-page-content">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit Event - {{ event.title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('event.event_detail', event_id=event.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Event
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Basic Information</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="title">Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" value="{{ event.title }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="game">Game <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="game" name="game" value="{{ event.game }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="event_type">Event Type <span class="text-danger">*</span></label>
                                            <select class="form-control" id="event_type" name="event_type" required>
                                                <option value="casual" {% if event.event_type == 'casual' %}selected{% endif %}>Casual</option>
                                                <option value="learn-to-play" {% if event.event_type == 'learn-to-play' %}selected{% endif %}>Learn-to-Play</option>
                                                <option value="competitive" {% if event.event_type == 'competitive' %}selected{% endif %}>Competitive</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="format">Format</label>
                                            <input type="text" class="form-control" id="format" name="format" value="{{ event.format }}" placeholder="e.g., Sealed, Draft, Tournament">
                                        </div>
                                        <div class="form-group">
                                            <label for="description">Description</label>
                                            <textarea class="form-control" id="description" name="description" rows="4">{{ event.description }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Date, Time, and Location -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Date, Time, and Location</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="start_datetime">Start Date & Time <span class="text-danger">*</span></label>
                                            <input type="datetime-local" class="form-control" id="start_datetime" name="start_datetime" value="{{ event.start_datetime.strftime('%Y-%m-%dT%H:%M') }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="end_datetime">End Date & Time <span class="text-danger">*</span></label>
                                            <input type="datetime-local" class="form-control" id="end_datetime" name="end_datetime" value="{{ event.end_datetime.strftime('%Y-%m-%dT%H:%M') }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="location">Location <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="location" name="location" value="{{ event.location }}" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label>Recurrence</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recurrence_type" id="recurrence_single" value="single" {% if event.recurrence.type == 'single' %}checked{% endif %}>
                                                <label class="form-check-label" for="recurrence_single">
                                                    Single Event
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recurrence_type" id="recurrence_daily" value="daily" {% if event.recurrence.type == 'daily' %}checked{% endif %}>
                                                <label class="form-check-label" for="recurrence_daily">
                                                    Daily
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recurrence_type" id="recurrence_weekly" value="weekly" {% if event.recurrence.type == 'weekly' %}checked{% endif %}>
                                                <label class="form-check-label" for="recurrence_weekly">
                                                    Weekly
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recurrence_type" id="recurrence_monthly" value="monthly" {% if event.recurrence.type == 'monthly' %}checked{% endif %}>
                                                <label class="form-check-label" for="recurrence_monthly">
                                                    Monthly
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div id="recurrence_options" class="{% if event.recurrence.type == 'single' %}d-none{% endif %}">
                                            <div class="form-group" id="weekly_options" {% if event.recurrence.type != 'weekly' %}style="display: none;"{% endif %}>
                                                <label>Days of Week</label>
                                                <div class="row">
                                                    <div class="col">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="day_0" id="day_0" {% if 0 in event.recurrence.days_of_week %}checked{% endif %}>
                                                            <label class="form-check-label" for="day_0">Monday</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="day_1" id="day_1" {% if 1 in event.recurrence.days_of_week %}checked{% endif %}>
                                                            <label class="form-check-label" for="day_1">Tuesday</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="day_2" id="day_2" {% if 2 in event.recurrence.days_of_week %}checked{% endif %}>
                                                            <label class="form-check-label" for="day_2">Wednesday</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="day_3" id="day_3" {% if 3 in event.recurrence.days_of_week %}checked{% endif %}>
                                                            <label class="form-check-label" for="day_3">Thursday</label>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="day_4" id="day_4" {% if 4 in event.recurrence.days_of_week %}checked{% endif %}>
                                                            <label class="form-check-label" for="day_4">Friday</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="day_5" id="day_5" {% if 5 in event.recurrence.days_of_week %}checked{% endif %}>
                                                            <label class="form-check-label" for="day_5">Saturday</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="day_6" id="day_6" {% if 6 in event.recurrence.days_of_week %}checked{% endif %}>
                                                            <label class="form-check-label" for="day_6">Sunday</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="form-group" id="monthly_options" {% if event.recurrence.type != 'monthly' %}style="display: none;"{% endif %}>
                                                <label for="day_of_month">Day of Month</label>
                                                <select class="form-control" id="day_of_month" name="day_of_month">
                                                    {% for i in range(1, 32) %}
                                                    <option value="{{ i }}" {% if event.recurrence.day_of_month == i %}selected{% endif %}>{{ i }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="recurrence_end_date">End Date</label>
                                                <input type="datetime-local" class="form-control" id="recurrence_end_date" name="recurrence_end_date" value="{{ event.recurrence.end_date.strftime('%Y-%m-%dT%H:%M') if event.recurrence.end_date else '' }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing and Tickets -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Pricing and Tickets</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="ticket_price">Ticket Price ($)</label>
                                            <input type="number" class="form-control" id="ticket_price" name="ticket_price" step="0.01" min="0" value="{{ event.ticket_price }}">
                                            <small class="form-text text-muted">Leave as 0 for free events.</small>
                                        </div>
                                        <div class="form-group">
                                            <label for="store_cost_per_ticket">Store Cost Per Ticket ($)</label>
                                            <input type="number" class="form-control" id="store_cost_per_ticket" name="store_cost_per_ticket" step="0.01" min="0" value="{{ event.store_cost_per_ticket }}">
                                            <small class="form-text text-muted">For margin tracking.</small>
                                        </div>
                                        <div class="form-group">
                                            <label for="max_attendees">Maximum Attendees</label>
                                            <input type="number" class="form-control" id="max_attendees" name="max_attendees" min="1" value="{{ event.max_attendees }}">
                                            <small class="form-text text-muted">Leave blank for unlimited.</small>
                                        </div>
                                        <div class="form-group">
                                            <label for="prize_description">Prize Support Description</label>
                                            <textarea class="form-control" id="prize_description" name="prize_description" rows="3">{{ event.prize_description }}</textarea>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="show_attendees" name="show_attendees" {% if event.show_attendees %}checked{% endif %}>
                                            <label class="form-check-label" for="show_attendees">Show list of attendees on event page</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Images and Custom Fields -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Images and Custom Fields</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="banner_image">Banner Image</label>
                                            {% if event.banner_image_url %}
                                            <div class="mb-2">
                                                <img src="{{ event.banner_image_url }}" alt="Current banner" style="max-width: 100%; max-height: 200px;">
                                                <p class="text-muted">Current banner image</p>
                                            </div>
                                            {% endif %}
                                            <input type="file" class="form-control-file" id="banner_image" name="banner_image" accept="image/*">
                                            <small class="form-text text-muted">Recommended size: 1200x400 pixels. Leave empty to keep current image.</small>
                                        </div>
                                        <div class="form-group">
                                            <label for="calendar_icon">Calendar Icon</label>
                                            {% if event.calendar_icon_url %}
                                            <div class="mb-2">
                                                <img src="{{ event.calendar_icon_url }}" alt="Current icon" style="max-width: 100px; max-height: 100px;">
                                                <p class="text-muted">Current calendar icon</p>
                                            </div>
                                            {% endif %}
                                            <input type="file" class="form-control-file" id="calendar_icon" name="calendar_icon" accept="image/*">
                                            <small class="form-text text-muted">Recommended size: 100x100 pixels. Leave empty to keep current image.</small>
                                        </div>
                                        
                                        <hr>
                                        
                                        <h5>Custom Fields for Attendees</h5>
                                        <p class="text-muted">Add custom fields to collect additional information from attendees.</p>
                                        
                                        <div id="custom_fields_container">
                                            {% for field in event.custom_fields %}
                                            <div class="custom-field">
                                                <button type="button" class="btn btn-sm btn-danger remove-field">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                <div class="form-group">
                                                    <label for="custom_field_name_{{ loop.index0 }}">Field Name</label>
                                                    <input type="text" class="form-control" id="custom_field_name_{{ loop.index0 }}" name="custom_field_name" value="{{ field.name }}" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="custom_field_type_{{ loop.index0 }}">Field Type</label>
                                                    <select class="form-control" id="custom_field_type_{{ loop.index0 }}" name="custom_field_type">
                                                        <option value="text" {% if field.type == 'text' %}selected{% endif %}>Text</option>
                                                        <option value="number" {% if field.type == 'number' %}selected{% endif %}>Number</option>
                                                        <option value="email" {% if field.type == 'email' %}selected{% endif %}>Email</option>
                                                        <option value="url" {% if field.type == 'url' %}selected{% endif %}>URL</option>
                                                        <option value="textarea" {% if field.type == 'textarea' %}selected{% endif %}>Text Area</option>
                                                    </select>
                                                </div>
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" id="custom_field_required_{{ loop.index0 }}" name="custom_field_required" {% if field.required %}checked{% endif %}>
                                                    <label class="form-check-label" for="custom_field_required_{{ loop.index0 }}">Required</label>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                        
                                        <button type="button" class="btn btn-secondary" id="add_custom_field">
                                            <i class="fas fa-plus"></i> Add Custom Field
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary btn-lg">Save Changes</button>
                                <a href="{{ url_for('event.event_detail', event_id=event.id) }}" class="btn btn-secondary btn-lg ml-2">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Handle recurrence options
        $('input[name="recurrence_type"]').change(function() {
            var type = $(this).val();
            
            if (type === 'single') {
                $('#recurrence_options').addClass('d-none');
            } else {
                $('#recurrence_options').removeClass('d-none');
                
                // Show/hide specific options based on recurrence type
                if (type === 'weekly') {
                    $('#weekly_options').show();
                    $('#monthly_options').hide();
                } else if (type === 'monthly') {
                    $('#weekly_options').hide();
                    $('#monthly_options').show();
                } else {
                    $('#weekly_options').hide();
                    $('#monthly_options').hide();
                }
            }
        });
        
        // Custom fields functionality
        var customFieldCount = {{ event.custom_fields|length }};
        
        $('#add_custom_field').click(function() {
            var fieldHtml = `
                <div class="custom-field">
                    <button type="button" class="btn btn-sm btn-danger remove-field">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="form-group">
                        <label for="custom_field_name_${customFieldCount}">Field Name</label>
                        <input type="text" class="form-control" id="custom_field_name_${customFieldCount}" name="custom_field_name" required>
                    </div>
                    <div class="form-group">
                        <label for="custom_field_type_${customFieldCount}">Field Type</label>
                        <select class="form-control" id="custom_field_type_${customFieldCount}" name="custom_field_type">
                            <option value="text">Text</option>
                            <option value="number">Number</option>
                            <option value="email">Email</option>
                            <option value="url">URL</option>
                            <option value="textarea">Text Area</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="custom_field_required_${customFieldCount}" name="custom_field_required">
                        <label class="form-check-label" for="custom_field_required_${customFieldCount}">Required</label>
                    </div>
                </div>
            `;
            
            $('#custom_fields_container').append(fieldHtml);
            customFieldCount++;
        });
        
        // Remove custom field
        $(document).on('click', '.remove-field', function() {
            $(this).closest('.custom-field').remove();
        });
    });
</script>
{% endblock %}
