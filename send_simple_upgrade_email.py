from config.config import Config
import requests
import logging
from config import Config

logger = logging.getLogger(__name__)

def send_subscription_upgrade_email(user_email):
    """Send an email notifying user about their subscription upgrade to 50 free daily scans"""
    try:
        logger.info(f"Starting send_subscription_upgrade_email for: {user_email}")
        
        MAILGUN_API_KEY = Config.MAILGUN_API_KEY
        MAILGUN_DOMAIN = Config.MAILGUN_DOMAIN

        if not MAILGUN_API_KEY or not MAILGUN_DOMAIN:
            logger.error("Mailgun API key or domain not set in config")
            raise ValueError("Mailgun API key or domain not set in config")

        # Create HTML content for the email
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header {
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                    text-align: center;
                }
                .content {
                    background-color: #fff;
                    padding: 20px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
                .upgrade-box {
                    background-color: #e8f5e9;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                    text-align: center;
                }
                .highlight {
                    color: #2e7d32;
                    font-weight: bold;
                    font-size: 1.2em;
                }
                .footer {
                    margin-top: 20px;
                    text-align: center;
                    font-size: 0.9em;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🎉 Your TCGSync Account Has Been Upgraded! 🎉</h2>
            </div>
            <div class="content">
                <p>Dear Valued User,</p>
                
                <div class="upgrade-box">
                    <h3>Your Account Has Been Enhanced!</h3>
                    <p class="highlight">You now have 50 FREE daily scans for life!</p>
                </div>

                <p>We're excited to inform you that your TCGSync account has been upgraded. This permanent enhancement to your subscription includes:</p>
                
                <ul>
                    <li><strong>50 free scans every day</strong></li>
                    <li><strong>Daily refresh</strong> - Your scan limit resets every 24 hours</li>
                    <li><strong>Lifetime access</strong> - This is a permanent upgrade to your account</li>
                </ul>

                <p>This upgrade is our way of thanking you for being a valued member of the TCGSync community. You can start using your increased scan limit right away!</p>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="https://login.tcgsync.com/login" style="background-color: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">Login to TCGSync</a>
                </div>

                <p>If you have any questions about your upgraded account or need assistance, please don't hesitate to contact our support team.</p>

                <p>Happy scanning!</p>
                
                <p>Best regards,<br>The TCGSync Team</p>
            </div>
            <div class="footer">
                <p>TCGSync - Empowering your trading card business</p>
            </div>
        </body>
        </html>
        """

        # Mailgun API endpoint
        url = f"https://api.eu.mailgun.net/v3/{MAILGUN_DOMAIN}/messages"

        # Email data
        data = {
            "from": f"TCGSync <admin@{MAILGUN_DOMAIN}>",
            "to": user_email,
            "subject": "🎉 Your TCGSync Account Has Been Upgraded - 50 Free Daily Scans!",
            "html": html_content
        }

        # Send the email
        response = requests.post(
            url,
            auth=("api", MAILGUN_API_KEY),
            data=data,
            timeout=30
        )

        if response.status_code == 200:
            logger.info(f"Subscription upgrade email sent successfully to {user_email}")
            return True
        else:
            logger.error(f"Failed to send subscription upgrade email. Status code: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error sending subscription upgrade email: {str(e)}")
        return False

if __name__ == "__main__":
    # Send the upgrade notification email
    recipient_email = "<EMAIL>"
    result = send_subscription_upgrade_email(recipient_email)

    if result:
        print(f"Successfully sent subscription upgrade email to {recipient_email}")
    else:
        print(f"Failed to send subscription upgrade email to {recipient_email}")

