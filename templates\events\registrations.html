{% extends "base.html" %}

{% block title %}Event Registrations - {{ event.title }}{% endblock %}

{% block styles %}
{{ super() }}
{% include "events/event_styles.html" %}
<style>
    .attendee-card {
        transition: all 0.2s ease;
    }

    .attendee-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .attendee-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3498db;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 1rem;
    }

    .checked-in {
        background-color: #2ecc71;
    }

    .registration-details {
        max-height: 200px;
        overflow-y: auto;
    }

    .custom-field-item {
        display: flex;
        margin-bottom: 0.5rem;
    }

    .custom-field-label {
        font-weight: 600;
        min-width: 120px;
        margin-right: 1rem;
    }

    .badge-waitlist {
        background-color: #f39c12;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3">Registrations: {{ event.title }}</h1>
                    <p class="text-muted">
                        {{ event.date.strftime('%A, %B %d, %Y') }} | {{ event.start_time }} - {{ event.end_time }}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('events.event_details', event_id=event.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Event
                    </a>
                    <a href="{{ url_for('events.add_attendee', event_id=event.id) }}" class="btn btn-primary ms-2">
                        <i class="fas fa-user-plus me-2"></i> Add Attendee
                    </a>
                    <div class="btn-group ms-2">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-file-export me-2"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('events.export_registrations', event_id=event.id, format='csv') }}">CSV</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('events.export_registrations', event_id=event.id, format='excel') }}">Excel</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Total Registrations</h6>
                            <h2 class="mt-2 mb-0">{{ registrations|length }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Checked In</h6>
                            <h2 class="mt-2 mb-0">{{ checked_in_count }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-clipboard-check fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Capacity</h6>
                            <h2 class="mt-2 mb-0">
                                {% if event.max_tickets == 0 %}
                                    {{ registrations|length }} / ∞
                                {% else %}
                                    {{ registrations|length }} / {{ event.max_tickets }}
                                {% endif %}
                            </h2>
                        </div>
                        <div>
                            <i class="fas fa-ticket-alt fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Revenue</h6>
                            <h2 class="mt-2 mb-0">${{ total_revenue }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-dollar-sign fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('events.event_registrations', event_id=event.id) }}" class="row g-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" placeholder="Search by name or email" name="search" value="{{ request.args.get('search', '') }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="status">
                                <option value="">All Statuses</option>
                                <option value="checked_in" {% if request.args.get('status') == 'checked_in' %}selected{% endif %}>Checked In</option>
                                <option value="not_checked_in" {% if request.args.get('status') == 'not_checked_in' %}selected{% endif %}>Not Checked In</option>
                                <option value="cancelled" {% if request.args.get('status') == 'cancelled' %}selected{% endif %}>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="sort">
                                <option value="registered_at" {% if request.args.get('sort') == 'registered_at' or not request.args.get('sort') %}selected{% endif %}>Sort by Registration Date</option>
                                <option value="customer_name" {% if request.args.get('sort') == 'customer_name' %}selected{% endif %}>Sort by Name</option>
                                <option value="checked_in_at" {% if request.args.get('sort') == 'checked_in_at' %}selected{% endif %}>Sort by Check-in Time</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">Apply</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Registrations List -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Attendees ({{ registrations|length }})</h5>
                </div>
                <div class="card-body">
                    {% if registrations %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Attendee</th>
                                        <th>Registration Date</th>
                                        <th>Status</th>
                                        <th>Check-in Time</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for registration in registrations %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="attendee-avatar {% if registration.is_checked_in %}checked-in{% endif %}">
                                                        {{ registration.customer_name[0]|upper }}
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ registration.customer_name }}</div>
                                                        <div class="small text-muted">{{ registration.customer_email }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ registration.registered_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>
                                                {% if registration.is_cancelled %}
                                                    <span class="badge bg-danger">Cancelled</span>
                                                {% elif registration.is_checked_in %}
                                                    <span class="badge bg-success">Checked In</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Registered</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if registration.is_checked_in %}
                                                    {{ registration.checked_in_at.strftime('%Y-%m-%d %H:%M') }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#detailsModal{{ registration.id }}">
                                                        <i class="fas fa-eye"></i>
                                                    </button>

                                                    {% if not registration.is_checked_in and not registration.is_cancelled %}
                                                        <button type="button" class="btn btn-sm btn-outline-success check-in-btn" data-registration-id="{{ registration.id }}">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    {% endif %}

                                                    {% if not registration.is_cancelled %}
                                                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelModal{{ registration.id }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>

                                                <!-- Details Modal -->
                                                <div class="modal fade" id="detailsModal{{ registration.id }}" tabindex="-1" aria-labelledby="detailsModalLabel{{ registration.id }}" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="detailsModalLabel{{ registration.id }}">Registration Details</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="mb-3">
                                                                    <h6>Attendee Information</h6>
                                                                    <p class="mb-1"><strong>Name:</strong> {{ registration.customer_name }}</p>
                                                                    <p class="mb-1"><strong>Email:</strong> {{ registration.customer_email }}</p>
                                                                    <p class="mb-1"><strong>Phone:</strong> {{ registration.customer_phone or 'N/A' }}</p>
                                                                    <p class="mb-1"><strong>Registered:</strong> {{ registration.registered_at.strftime('%Y-%m-%d %H:%M') }}</p>

                                                                    {% if registration.is_checked_in %}
                                                                        <p class="mb-1"><strong>Checked In:</strong> {{ registration.checked_in_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                                                    {% endif %}

                                                                    {% if registration.is_cancelled %}
                                                                        <p class="mb-1"><strong>Cancelled:</strong> {{ registration.cancelled_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                                                    {% endif %}
                                                                </div>

                                                                {% if registration.custom_fields %}
                                                                    <div class="mb-3">
                                                                        <h6>Custom Fields</h6>
                                                                        <div class="registration-details">
                                                                            {% for field in registration.custom_fields %}
                                                                                <div class="custom-field-item">
                                                                                    <div class="custom-field-label">{{ field.name }}:</div>
                                                                                    <div class="custom-field-value">{{ field.value }}</div>
                                                                                </div>
                                                                            {% endfor %}
                                                                        </div>
                                                                    </div>
                                                                {% endif %}

                                                                {% if registration.notes %}
                                                                    <div class="mb-3">
                                                                        <h6>Notes</h6>
                                                                        <p>{{ registration.notes }}</p>
                                                                    </div>
                                                                {% endif %}

                                                                {% if registration.order_id %}
                                                                    <div class="mb-0">
                                                                        <h6>Order Information</h6>
                                                                        <p class="mb-1"><strong>Order ID:</strong> {{ registration.order_id }}</p>
                                                                        <p class="mb-1"><strong>Payment Status:</strong> {{ registration.payment_status or 'N/A' }}</p>
                                                                        <p class="mb-0"><strong>Price Paid:</strong> ${{ registration.price_paid }}</p>
                                                                    </div>
                                                                {% endif %}
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Cancel Modal -->
                                                <div class="modal fade" id="cancelModal{{ registration.id }}" tabindex="-1" aria-labelledby="cancelModalLabel{{ registration.id }}" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="cancelModalLabel{{ registration.id }}">Cancel Registration</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <p>Are you sure you want to cancel the registration for <strong>{{ registration.customer_name }}</strong>?</p>
                                                                <p class="text-danger">This action cannot be undone. If this was a paid registration, you'll need to issue a refund separately.</p>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                <form action="{{ url_for('events.cancel_registration', event_id=event.id, registration_id=registration.id) }}" method="post">
                                                                    <button type="submit" class="btn btn-danger">Cancel Registration</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5>No registrations found</h5>
                            <p class="text-muted">There are no registrations for this event yet.</p>
                            <a href="{{ url_for('events.add_attendee', event_id=event.id) }}" class="btn btn-primary mt-2">
                                <i class="fas fa-user-plus me-2"></i> Add Attendee
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Check-in functionality
        $('.check-in-btn').click(function() {
            const registrationId = $(this).data('registration-id');
            const button = $(this);

            $.ajax({
                url: "{{ url_for('events.check_in_attendee', event_id=event.id, registration_id='') }}" + registrationId,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        // Update UI
                        const row = button.closest('tr');
                        row.find('td:nth-child(3)').html('<span class="badge bg-success">Checked In</span>');
                        row.find('td:nth-child(4)').text(response.checked_in_at);
                        row.find('.attendee-avatar').addClass('checked-in');
                        button.remove();

                        // Show success message
                        toastr.success('Attendee checked in successfully');

                        // Update stats without page reload
                        const checkedInCount = $('.bg-success h2');
                        checkedInCount.text(parseInt(checkedInCount.text()) + 1);
                    } else {
                        toastr.error('Error checking in attendee: ' + response.error);
                    }
                },
                error: function() {
                    toastr.error('Error checking in attendee');
                }
            });
        });

        // Auto-submit form when filters change
        $('select[name="status"], select[name="sort"]').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
