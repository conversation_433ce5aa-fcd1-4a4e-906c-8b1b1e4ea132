from config.config import Config
import requests
import time
import random

# Proxy URL from the history.py script
PROXY_URL = "https://proxy.webshare.io/api/v2/proxy/list/download/mppaungsmfcdtflcalmqswojioauylnrbmfjimqd/-/any/username/direct/-/"

def load_proxies():
    try:
        response = requests.get(PROXY_URL)
        response.raise_for_status()
        proxy_list = []
        for line in response.text.splitlines():
            if not line.strip():
                continue
            try:
                parts = line.strip().split(':')
                if len(parts) == 4:
                    ip, port, username, password = parts
                    proxy = {
                        'http': f"http://{username}:{password}@{ip}:{port}",
                        'https': f"http://{username}:{password}@{ip}:{port}"
                    }
                    proxy_list.append(proxy)
            except Exception as e:
                print(f"Error parsing proxy: {e}")
                continue
        return proxy_list
    except Exception as e:
        print(f"Error loading proxies: {e}")
        return []

def test_proxy(proxy, test_url="https://httpbin.org/ip"):
    start_time = time.time()
    try:
        response = requests.get(test_url, proxies=proxy, timeout=10)
        elapsed = time.time() - start_time
        if response.status_code == 200:
            return True, elapsed, response.text
        else:
            return False, elapsed, f"Status code: {response.status_code}"
    except Exception as e:
        elapsed = time.time() - start_time
        return False, elapsed, str(e)

def main():
    print("Loading proxies...")
    proxies = load_proxies()
    
    if not proxies:
        print("No proxies loaded. Check the URL or network connection.")
        return
    
    print(f"Loaded {len(proxies)} proxies")
    
    # Test a sample of proxies (up to 10)
    num_to_test = min(10, len(proxies))
    test_proxies = random.sample(proxies, num_to_test)
    
    print(f"\nTesting {num_to_test} random proxies:")
    
    working = 0
    for i, proxy in enumerate(test_proxies, 1):
        print(f"\nProxy {i}:")
        print(f"  URL: {proxy['http']}")
        
        success, elapsed, result = test_proxy(proxy)
        
        if success:
            working += 1
            print(f"  Status: ✅ Working (response time: {elapsed:.2f}s)")
            print(f"  Response: {result}")
        else:
            print(f"  Status: ❌ Failed (after {elapsed:.2f}s)")
            print(f"  Error: {result}")
    
    print(f"\nSummary: {working}/{num_to_test} proxies are working")
    
    # Now test specifically with TCGPlayer API
    print("\nTesting proxies with TCGPlayer API:")
    tcg_url = "https://mpapi.tcgplayer.com/v2/product/1/latestsales"
    headers = {
        "Cache-Control": "no-cache",
        "Content-Type": "application/json",
        "User-Agent": "PostmanRuntime/7.42.0"
    }
    
    tcg_working = 0
    for i, proxy in enumerate(test_proxies[:3], 1):  # Test only first 3 proxies with TCGPlayer
        print(f"\nTesting TCGPlayer API with Proxy {i}:")
        try:
            start_time = time.time()
            response = requests.post(tcg_url, headers=headers, json={}, proxies=proxy, timeout=15)
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                tcg_working += 1
                print(f"  Status: ✅ Working with TCGPlayer (response time: {elapsed:.2f}s)")
                print(f"  Response status: {response.status_code}")
            elif response.status_code == 429:
                print(f"  Status: ⚠️ Rate limited by TCGPlayer (after {elapsed:.2f}s)")
                print(f"  Response status: {response.status_code}")
            else:
                print(f"  Status: ❌ Failed with TCGPlayer (after {elapsed:.2f}s)")
                print(f"  Response status: {response.status_code}")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"  Status: ❌ Error with TCGPlayer (after {elapsed:.2f}s)")
            print(f"  Error: {str(e)}")
    
    print(f"\nTCGPlayer API Summary: {tcg_working}/3 proxies worked with TCGPlayer API")

if __name__ == "__main__":
    main()

