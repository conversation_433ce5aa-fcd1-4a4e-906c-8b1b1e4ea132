# PowerShell script to run the webhook backlog processor
# This script can be scheduled to run periodically using Windows Task Scheduler

# Set error action preference
$ErrorActionPreference = "Stop"

# Script configuration
$scriptPath = $PSScriptRoot
$pythonExe = "python"  # Change this if you need to specify a full path to Python
$scriptName = "process_webhook_backlog.py"
$logFile = "webhook_backlog_processor_run.log"

# Command line arguments - adjust these as needed
$batchSize = 200       # Process 200 webhooks per batch
$sleepTime = 0.02      # 20ms sleep between adding webhooks to queue
$ageMinutes = 5        # Process webhooks older than 5 minutes
$runOnce = $true       # Run once and exit

# Function to write to log file
function Write-Log {
    param (
        [string]$message
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $message" | Out-File -FilePath "$scriptPath\$logFile" -Append
    Write-Host "$timestamp - $message"
}

# Start logging
Write-Log "Starting webhook backlog processor run"

try {
    # Change to the script directory
    Set-Location $scriptPath
    Write-Log "Working directory: $scriptPath"
    
    # Build the command
    $arguments = @(
        $scriptName,
        "--batch-size", $batchSize,
        "--sleep", $sleepTime,
        "--age-minutes", $ageMinutes
    )
    
    if ($runOnce) {
        $arguments += "--run-once"
    }
    
    $command = "$pythonExe $($arguments -join ' ')"
    Write-Log "Running command: $command"
    
    # Run the command
    $process = Start-Process -FilePath $pythonExe -ArgumentList $arguments -NoNewWindow -PassThru -Wait
    
    # Check the exit code
    if ($process.ExitCode -eq 0) {
        Write-Log "Webhook backlog processor completed successfully"
    } else {
        Write-Log "Webhook backlog processor failed with exit code: $($process.ExitCode)"
    }
} catch {
    Write-Log "Error running webhook backlog processor: $_"
    exit 1
}

Write-Log "Webhook backlog processor run completed"
