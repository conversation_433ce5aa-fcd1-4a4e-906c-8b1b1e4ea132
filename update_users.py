from config.config import Config
from mongoengine import connect
from models.user_model import User
from datetime import datetime
from config import Config

def update_users_with_last_reprice_all():
    # Connect to the database
    connect(host=Config.MONGODB_SETTINGS['host'])

    # Update all users
    result = User.objects.update(set__last_reprice_all=None, upsert=False, multi=True)

    print(f"Updated {result} user documents with 'last_reprice_all' field.")

if __name__ == "__main__":
    update_users_with_last_reprice_all()

