from config.config import Config
from datetime import datetime, timedelta
from threading import Lock

class CurrencyCache:
    def __init__(self, ttl_hours=24):
        self.cache = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.lock = Lock()
        self.last_cleanup = datetime.utcnow()

    def get(self, currency):
        with self.lock:
            if currency in self.cache:
                rate_data = self.cache[currency]
                if datetime.utcnow() - rate_data['timestamp'] < self.ttl:
                    return rate_data['rate']
                else:
                    del self.cache[currency]
            return None

    def set(self, currency, rate):
        with self.lock:
            self.cache[currency] = {
                'rate': rate,
                'timestamp': datetime.utcnow()
            }

    def clear_expired(self):
        with self.lock:
            current_time = datetime.utcnow()
            expired = [currency for currency, data in self.cache.items() 
                      if current_time - data['timestamp'] >= self.ttl]
            for currency in expired:
                del self.cache[currency]
            self.last_cleanup = current_time

