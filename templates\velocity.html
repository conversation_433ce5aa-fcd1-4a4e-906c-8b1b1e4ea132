{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>Top 10 Sales Velocity By Game</h2>
    <div class="mb-4">
        <label for="gameFilter" class="form-label">Select Game:</label>
        <select class="form-select" id="gameFilter">
            <option value="">Select a game...</option>
        </select>
    </div>
    <div id="velocityAccordion" class="mt-4">
        <!-- Velocity data will be populated here -->
    </div>
    <div id="noDataMessage" class="alert alert-info d-none" style="color: #000;">
        No velocity data available.
    </div>
</div>

<style>
    .velocity-item {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        color: #000;
    }
    .velocity-item:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .velocity-title {
        font-weight: bold;
        margin-bottom: 5px;
        color: #000;
    }
    .velocity-details {
        font-size: 0.9em;
        color: #000;
    }
    .velocity-value {
        font-weight: bold;
        color: #28a745;
    }
    .price-info {
        background-color: #f8f9fa;
        padding: 8px;
        border-radius: 4px;
        margin-top: 8px;
    }
    .recommendations {
        background-color: #e9ecef;
        padding: 8px;
        border-radius: 4px;
        margin-top: 8px;
    }
    .form-select {
        max-width: 300px;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const gameFilter = document.getElementById('gameFilter');
        const accordion = document.getElementById('velocityAccordion');
        const noDataMessage = document.getElementById('noDataMessage');

        function updateGameFilter(games) {
            gameFilter.innerHTML = '<option value="">Select a game...</option>';
            games.forEach(game => {
                const option = document.createElement('option');
                option.value = game;
                option.textContent = game;
                gameFilter.appendChild(option);
            });
        }

        function fetchGames() {
            fetch('/api/velocity-games')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.games && data.games.length > 0) {
                        updateGameFilter(data.games);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    accordion.innerHTML = '<p>Error loading games. Please try again.</p>';
                });
        }

        function fetchAndUpdateData() {
            const selectedGame = gameFilter.value;
            
            if (!selectedGame) {
                accordion.innerHTML = '<p>Please select a game to view velocity data.</p>';
                return;
            }

            accordion.innerHTML = '<p>Loading data...</p>';
            
            fetch(`/api/velocity-data?game=${encodeURIComponent(selectedGame)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.velocity_data && data.velocity_data.games) {
                        updateVelocityList(data.velocity_data.games);
                        accordion.classList.remove('d-none');
                        noDataMessage.classList.add('d-none');
                    } else {
                        accordion.classList.add('d-none');
                        noDataMessage.classList.remove('d-none');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    accordion.innerHTML = '<p>Error loading data. Please try again.</p>';
                });
        }

        function updateVelocityList(games) {
            accordion.innerHTML = '';

            if (games.length === 0) {
                accordion.innerHTML = '<p>No velocity data available.</p>';
                return;
            }

            games.forEach((game) => {
                if (!game.products || game.products.length === 0) return;

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';

                game.products.forEach(product => {
                    const velocityDiv = document.createElement('div');
                    velocityDiv.className = 'velocity-item mb-3';
                    velocityDiv.innerHTML = `
                        <div class="velocity-title">${product.name || 'Unknown Product'}</div>
                        <div class="velocity-details">
                            Product ID: ${product.productId || 'N/A'}
                        </div>
                        <div class="velocity-details">
                            ${product.expansionName || 'N/A'}
                        </div>
                        <div class="velocity-details">
                            ${product.variant || 'Normal'}
                        </div>
                        <div class="velocity-value">
                            Velocity: ${(product.velocity || 0).toFixed(3)} sales per listing per day
                        </div>
                        <div class="velocity-details">
                            Daily Sales: ${(product.daily_sales || 0).toFixed(1)} | Listings: ${product.listings_count || 0}
                        </div>
                        <div class="price-info">
                            <div class="velocity-details">
                                Price Range: $${(product.price_range?.low || 0).toFixed(2)} - $${(product.price_range?.high || 0).toFixed(2)}
                            </div>
                            <div class="velocity-details">
                                Average Price: $${(product.price_range?.avg || 0).toFixed(2)}
                            </div>
                            <div class="velocity-details">
                                Market Trend: ${product.market_trend_7d || 0}% (7d) | ${product.market_trend_30d || 0}% (30d)
                            </div>
                        </div>
                        <div class="recommendations">
                            <div class="velocity-details">
                                Quick Sale (1-2 days): $${(product.recommendations?.quick_sale || 0).toFixed(2)}
                            </div>
                            <div class="velocity-details">
                                Medium Sale (3-5 days): $${(product.recommendations?.medium_sale || 0).toFixed(2)}
                            </div>
                            <div class="velocity-details">
                                Patient Sale (7+ days): $${(product.recommendations?.patient_sale || 0).toFixed(2)}
                            </div>
                        </div>
                    `;
                    cardBody.appendChild(velocityDiv);
                });

                accordion.appendChild(cardBody);
            });

            if (accordion.children.length === 0) {
                accordion.innerHTML = '<p>No velocity data available.</p>';
            }
        }

        // Initial games fetch
        fetchGames();

        // Add event listener for game selection
        gameFilter.addEventListener('change', fetchAndUpdateData);
    });
</script>
{% endblock %}
