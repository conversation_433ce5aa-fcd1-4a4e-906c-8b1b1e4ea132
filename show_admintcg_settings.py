from config.config import Config
#!/usr/bin/env python3
"""
Script to show the user settings and product types for admintcg directly from the database.

This script:
1. Connects to the MongoDB database
2. Fetches the user profile for admintcg
3. Fetches the autopricer configuration for admintcg
4. Displays the user settings and product types

Usage:
    python show_admintcg_settings.py
"""

import sys
import logging
from pymongo import MongoClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def main():
    username = "admintcg"
    
    logger.info(f"=== Fetching settings for user: {username} ===")
    
    try:
        # Connect to MongoDB
        mongo_uri = '*******************************************************************'
        mongo_dbname = 'test'
        
        logger.info(f"Connecting to MongoDB at {mongo_uri}...")
        mongo_client = MongoClient(mongo_uri)
        db = mongo_client[mongo_dbname]
        
        # Get user profile
        logger.info(f"Fetching user profile for {username}...")
        user_profile = db['user'].find_one({'username': username})
        
        if not user_profile:
            logger.error(f"User profile not found for {username}")
            return
        
        # Get autopricer configuration
        logger.info(f"Fetching autopricer configuration for {username}...")
        user_config = db['autopricerShopify'].find_one({"username": username})
        
        if not user_config:
            logger.error(f"No autopricer configuration found for {username}")
            return
        
        # Import prepare_shopify_settings from saautopricing
        logger.info("Preparing user settings...")
        from saautopricing import prepare_shopify_settings
        settings = prepare_shopify_settings(user_profile)
        
        # Get selected product types
        selected_product_types = user_config.get('selectedProductTypes', [])
        
        # Display user settings
        logger.info(f"\n=== User Settings for {username} ===")
        logger.info(f"Min Price: {settings.get('minPrice')}")
        logger.info(f"Price Preference Order: {settings.get('price_preference_order')}")
        logger.info(f"Use Highest Price: {settings.get('use_highest_price')}")
        logger.info(f"Price Modifiers: {settings.get('price_modifiers')}")
        logger.info(f"Custom Stepping: {settings.get('customStepping')}")
        logger.info(f"Price Rounding Enabled: {settings.get('price_rounding_enabled')}")
        logger.info(f"Price Rounding Thresholds: {settings.get('price_rounding_thresholds')}")
        logger.info(f"TCG Trend Increasing: {settings.get('tcg_trend_increasing')}%")
        logger.info(f"TCG Trend Decreasing: {settings.get('tcg_trend_decreasing')}%")
        
        # Display game minimum prices if available
        game_min_prices = settings.get('game_minimum_prices', {})
        if game_min_prices:
            logger.info(f"\n=== Game Minimum Prices for {username} ===")
            for game, game_settings in game_min_prices.items():
                logger.info(f"  {game}: Default Min: {game_settings.get('default_min_price')}")
                if 'rarities' in game_settings:
                    for rarity, price in game_settings['rarities'].items():
                        logger.info(f"    {rarity}: {price}")
        
        # Display selected product types
        logger.info(f"\n=== Selected Product Types for {username} ===")
        logger.info(f"{selected_product_types}")
        
        # Count products for this user
        product_count = db['shProducts'].count_documents({"username": username})
        logger.info(f"\nTotal products for {username}: {product_count}")
        
        # Count products by product type
        logger.info(f"\n=== Product Counts by Type for {username} ===")
        for product_type in selected_product_types:
            count = db['shProducts'].count_documents({
                "username": username,
                "product_type": {"$regex": f"^{product_type}$", "$options": "i"}
            })
            logger.info(f"  {product_type}: {count} products")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    logger.info("\n=== Finished ===")

if __name__ == "__main__":
    main()
