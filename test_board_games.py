from config.config import Config
from flask import Flask, jsonify, request
from flask_cors import CORS
from utils.shopify_api import format_board_game_description
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

@app.route('/api/board-games/preview', methods=['POST'])
def preview_product():
    """Preview how a board game product will look without creating it"""
    try:
        logger.debug("Received request with headers: %s", request.headers)
        logger.debug("Received request data: %s", request.get_data(as_text=True))
        game_data = request.json
        logger.debug("Parsed JSON data: %s", game_data)
        if not game_data:
            return jsonify({'error': 'No game data provided'}), 400

        # Format the description
        description = format_board_game_description(game_data)

        # Prepare tags
        tags = []
        tags.extend(game_data.get('categories', []))
        tags.extend(game_data.get('mechanics', []))
        tags.extend([
            f"{game_data['min_players']}-{game_data['max_players']} Players",
            f"{game_data['min_age']}+",
            f"Weight {game_data['weight']:.1f}",
            f"{game_data['playing_time']} Minutes"
        ])

        # Return preview data
        return jsonify({
            'title': f"{game_data['name']} ({game_data['year_published']})",
            'description': description,
            'vendor': 'Board Games',
            'product_type': 'Board Games',
            'tags': tags,
            'image_url': game_data.get('image_url'),
            'metafields': {
                'bgg_id': str(game_data['id']),
                'year_published': str(game_data['year_published']),
                'min_players': str(game_data['min_players']),
                'max_players': str(game_data['max_players']),
                'playing_time': str(game_data['playing_time']),
                'min_age': str(game_data['min_age']),
                'weight': f"{game_data['weight']:.2f}",
                'bgg_rank': str(game_data['rank']),
                'strategy_rank': str(game_data['strategygames_rank']),
                'average_rating': f"{game_data['average']:.2f}",
                'users_rated': str(game_data['usersrated']),
                'designers': ', '.join(game_data['designers']),
                'artists': ', '.join(game_data['artists']),
                'publishers': ', '.join(game_data['publishers'][:3])
            }
        })

    except Exception as e:
        logger.exception("Error generating preview:")
        return jsonify({
            'error': f'Error generating preview: {str(e)}'
        }), 500

if __name__ == '__main__':
    app.run(debug=True, port=5011)

