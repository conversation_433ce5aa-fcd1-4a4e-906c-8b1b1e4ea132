from config.config import Config
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

try:
    from main import celery, run_celery_worker
except ImportError as e:
    logger.error(f"Failed to import from main: {e}")
    sys.exit(1)

if __name__ == '__main__':
    try:
        logger.info("Starting Celery worker...")
        run_celery_worker()
    except Exception as e:
        logger.error(f"Failed to start Celery worker: {e}")
        sys.exit(1)

