{% extends "base.html" %}

{% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<style>
    .staff-card {
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        margin-bottom: 20px;
        background-color: #2c3e50;
        color: white;
    }
    
    .staff-card:hover {
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-5px);
    }
    
    .staff-header {
        background: linear-gradient(135deg, #4a90e2, #357abd);
        color: white;
        padding: 15px;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }
    
    .staff-body {
        padding: 20px;
        background-color: #2c3e50;
        color: white;
    }
    
    .staff-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 20px;
    }
    
    .staff-table th, .staff-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #4a5568;
        color: white;
    }
    
    .staff-table th {
        background-color: #1a202c;
        font-weight: 600;
        color: white;
    }
    
    .staff-table tr:last-child td {
        border-bottom: none;
    }
    
    .staff-table tr:hover {
        background-color: #3a4a5c;
        color: white;
    }
    
    .role-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .role-admin {
        background-color: #dc3545;
        color: white;
    }
    
    .role-manager {
        background-color: #fd7e14;
        color: white;
    }
    
    .role-staff {
        background-color: #28a745;
        color: white;
    }
    
    .role-limited {
        background-color: #6c757d;
        color: white;
    }
    
    .action-btn {
        padding: 5px 10px;
        border-radius: 4px;
        margin-right: 5px;
        font-size: 14px;
    }
    
    .form-section {
        background-color: #1a202c;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        color: white;
    }
    
    .form-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: white;
    }
    
    .permission-group {
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #4a5568;
        border-radius: 8px;
        background-color: #2c3e50;
        color: white;
    }
    
    .permission-group-title {
        font-weight: 600;
        margin-bottom: 10px;
        color: white;
    }
    
    .permission-item {
        margin-bottom: 5px;
        color: white;
    }
    
    .form-check-label {
        color: white;
    }
    
    .status-active {
        color: #28a745;
    }
    
    .status-inactive {
        color: #dc3545;
    }
    
    .modal-content {
        background-color: #2c3e50;
        color: white;
    }
    
    .modal-header {
        background-color: #1a202c;
        color: white;
        border-bottom: 1px solid #4a5568;
    }
    
    .modal-footer {
        background-color: #1a202c;
        border-top: 1px solid #4a5568;
    }
    
    .form-control, .form-select {
        background-color: #1a202c;
        color: white;
        border: 1px solid #4a5568;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #2c3e50;
        color: white;
    }
    
    .form-control::placeholder {
        color: #a0aec0;
    }
    
    .form-text {
        color: #a0aec0;
    }
    
    .password-info {
        background-color: #1a202c;
        border: 1px solid #4a5568;
        border-radius: 5px;
        padding: 10px;
        margin-top: 15px;
    }
    
    .password-info pre {
        background-color: #2c3e50;
        color: #4CAF50;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
        font-family: monospace;
        overflow-x: auto;
    }
    
    .magic-link {
        word-break: break-all;
        background-color: #2c3e50;
        color: #4a90e2;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
        font-family: monospace;
        overflow-x: auto;
    }
    
    .copy-btn {
        background-color: #4a90e2;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 5px 10px;
        margin-top: 5px;
        cursor: pointer;
        font-size: 12px;
    }
    
    .copy-btn:hover {
        background-color: #357abd;
    }
</style>

<div class="container-fluid px-4 py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-4">Staff Management</h2>
            <p class="text-muted">Manage your staff members, assign roles, and control permissions.</p>
            <a href="{{ url_for('staff_auth.dashboard') }}" class="btn btn-primary mb-3">
                <i class="fas fa-tachometer-alt me-2"></i>Go to Staff Dashboard
            </a>
        </div>
    </div>

    <!-- Staff List Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="staff-card">
                <div class="staff-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">Staff Members</h3>
                    <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addStaffModal">
                        <i class="fas fa-plus me-2"></i>Add Staff
                    </button>
                </div>
                <div class="staff-body">
                    <div class="table-responsive">
                        <table class="staff-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="staffTableBody">
                                <!-- Staff members will be loaded dynamically -->
                                <tr id="noStaffRow">
                                    <td colspan="6" class="text-center">No staff members found. Add your first staff member using the "Add Staff" button.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Permission Management Section -->
    <div class="row">
        <div class="col-12">
            <div class="staff-card">
                <div class="staff-header">
                    <h3 class="mb-0">Permission Management</h3>
                </div>
                <div class="staff-body">
                    <div class="form-section">
                        <div class="form-title">Configure Permissions by Staff Member</div>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <select class="form-select" id="staffPermissionSelect">
                                    <option selected value="">Select Staff Member</option>
                                    <!-- Staff members will be loaded dynamically -->
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="permission-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="perm_pos" checked>
                                        <label class="form-check-label" for="perm_pos">POS</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="permission-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="perm_buylist" disabled>
                                        <label class="form-check-label" for="perm_buylist">
                                            BUYLIST ORDERS
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="permission-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="perm_inventory" disabled>
                                        <label class="form-check-label" for="perm_inventory">
                                            INVENTORY
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="permission-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="perm_scanning" checked>
                                        <label class="form-check-label" for="perm_scanning">CARD SCANNING</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="permission-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="perm_customer" disabled>
                                        <label class="form-check-label" for="perm_customer">
                                            CUSTOMER MANAGEMENT
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button class="btn btn-primary" id="savePermissionsBtn">Save Permissions</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Staff Modal -->
<div class="modal fade" id="addStaffModal" tabindex="-1" aria-labelledby="addStaffModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStaffModalLabel">Add New Staff Member</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addStaffForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="staffName" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="staffName" placeholder="Enter full name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="staffEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="staffEmail" placeholder="Enter email address" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="hidden" id="staffRole" value="staff">
                            <div class="form-text">Staff members will receive a magic link to login.</div>
                        </div>
                        <div class="col-md-6">
                            <label for="staffStatus" class="form-label">Status</label>
                            <select class="form-select" id="staffStatus">
                                <option value="active" selected>Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label">Permissions</label>
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="posPermission" checked>
                                        <label class="form-check-label" for="posPermission">POS</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="buylistOrdersPermission" disabled>
                                        <label class="form-check-label" for="buylistOrdersPermission">
                                            Buylist Orders
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="inventoryPermission" disabled>
                                        <label class="form-check-label" for="inventoryPermission">
                                            Inventory
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cardScanningPermission" checked>
                                        <label class="form-check-label" for="cardScanningPermission">Card Scanning</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="customerManagementPermission" disabled>
                                        <label class="form-check-label" for="customerManagementPermission">
                                            Customer Management
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="staffNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="staffNotes" rows="3" placeholder="Enter any additional notes"></textarea>
                    </div>
                    
                    <div id="addStaffResultContainer" class="password-info d-none">
                        <h6>Staff Member Added Successfully</h6>
                        <p>An invitation has been sent to <strong id="resultStaffEmail"></strong> with login instructions.</p>
                        
                        <div class="mt-3">
                            <strong>Magic Login Link:</strong>
                            <div class="magic-link" id="resultMagicLink"></div>
                            <button type="button" class="copy-btn" id="copyStaffLinkBtn">Copy Link</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="addStaffBtn">Add Staff Member</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Staff Modal -->
<div class="modal fade" id="editStaffModal" tabindex="-1" aria-labelledby="editStaffModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStaffModalLabel">Edit Staff Member</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editStaffForm">
                    <input type="hidden" id="editStaffId">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="editStaffName" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="editStaffName" placeholder="Enter full name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="editStaffEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="editStaffEmail" placeholder="Enter email address" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="hidden" id="editStaffRole" value="staff">
                        </div>
                        <div class="col-md-6">
                            <label for="editStaffStatus" class="form-label">Status</label>
                            <select class="form-select" id="editStaffStatus">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label">Permissions</label>
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editPosPermission">
                                        <label class="form-check-label" for="editPosPermission">POS</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editBuylistOrdersPermission" disabled>
                                        <label class="form-check-label" for="editBuylistOrdersPermission">
                                            Buylist Orders
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editInventoryPermission" disabled>
                                        <label class="form-check-label" for="editInventoryPermission">
                                            Inventory
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editCardScanningPermission">
                                        <label class="form-check-label" for="editCardScanningPermission">Card Scanning</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editCustomerManagementPermission" disabled>
                                        <label class="form-check-label" for="editCustomerManagementPermission">
                                            Customer Management
                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">IN DEVELOPMENT</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editStaffNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="editStaffNotes" rows="3" placeholder="Enter any additional notes"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateStaffBtn">Update Staff Member</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Staff Confirmation Modal -->
<div class="modal fade" id="deleteStaffModal" tabindex="-1" aria-labelledby="deleteStaffModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteStaffModalLabel">Confirm Staff Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to permanently delete the staff member <strong id="deleteStaffName"></strong>?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>This action cannot be undone and will remove all associated data.</p>
                <input type="hidden" id="deleteStaffId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash-alt me-2"></i>Delete Permanently
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Set Password Modal -->
<div class="modal fade" id="setPasswordModal" tabindex="-1" aria-labelledby="setPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="setPasswordModalLabel">Set Password for <span id="staffNameDisplay"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="setPasswordForm">
                    <input type="hidden" id="staffIdInput">
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">New Password</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="newPassword" placeholder="Leave blank to generate a random password">
                            <button class="btn btn-outline-secondary" type="button" id="generatePasswordBtn">Generate</button>
                        </div>
                        <div class="form-text">If left blank, a secure random password will be generated.</div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="sendEmailCheck" checked>
                        <label class="form-check-label" for="sendEmailCheck">
                            Send email to user with new password and magic login link
                        </label>
                    </div>
                    
                    <div id="passwordResultContainer" class="password-info d-none">
                        <h6>Password Set Successfully</h6>
                        <p>The password for <strong id="resultStaffName"></strong> has been set and an email has been sent to <span id="resultStaffEmail"></span>.</p>
                        
                        <div>
                            <strong>Password:</strong>
                            <pre id="resultPassword"></pre>
                            <button type="button" class="copy-btn" id="copyPasswordBtn">Copy Password</button>
                        </div>
                        
                        <div class="mt-3">
                            <strong>Magic Login Link:</strong>
                            <div class="magic-link" id="resultMagicLink"></div>
                            <button type="button" class="copy-btn" id="copyLinkBtn">Copy Link</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="setPasswordBtn">Set Password</button>
            </div>
        </div>
    </div>
</div>

<!-- Include the staff management JavaScript file -->
<script src="{{ url_for('static', filename='js/staff_management.js') }}"></script>
{% endblock %}
