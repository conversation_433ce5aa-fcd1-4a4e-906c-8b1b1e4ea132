{% extends "base.html" %}

{% block title %}Join Waitlist - {{ event.title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/events.css') }}">
<style>
    .waitlist-form {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .event-info {
        background-color: rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .event-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .event-meta-item i {
        width: 24px;
        margin-right: 10px;
        color: var(--primary-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="{{ url_for('events.event_details', event_id=event.id) }}" class="btn btn-outline-secondary mb-2">
                        <i class="fas fa-arrow-left"></i> Back to Event
                    </a>
                    <h1 class="h3 mb-0">Join Waitlist</h1>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="waitlist-form">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">{{ event.title }}</h4>
                    </div>
                    <div class="card-body">
                        <!-- Event Info -->
                        <div class="event-info">
                            <div class="event-meta-item">
                                <i class="fas fa-calendar-day"></i>
                                <span>{{ event.date.strftime('%A, %B %d, %Y') }}</span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>{{ event.start_time }} - {{ event.end_time }}</span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ event.location }}</span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-gamepad"></i>
                                <span>{{ event.game }} - {{ event.format|capitalize }}</span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-users"></i>
                                <span>
                                    {% if event.max_tickets == 0 %}
                                        {{ event.tickets_sold }} registered (unlimited capacity)
                                    {% else %}
                                        {{ event.tickets_sold }} / {{ event.max_tickets }} registered
                                    {% endif %}
                                </span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-ticket-alt"></i>
                                <span>
                                    {% if event.is_free() %}
                                        <span class="badge bg-success">Free</span>
                                    {% else %}
                                        ${{ event.ticket_price }}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>This event is currently full.</strong> Join the waitlist to be notified if a spot becomes available.
                            {% if event.waitlist_limit > 0 %}
                                <div class="mt-2">
                                    <small>Waitlist spots: {{ event.waitlist_count }} / {{ event.waitlist_limit }}</small>
                                </div>
                            {% endif %}
                        </div>
                        
                        <form method="POST">
                            <div class="form-group mb-3">
                                <label for="customer_name">Your Name *</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="customer_email">Email Address *</label>
                                <input type="email" class="form-control" id="customer_email" name="customer_email" required>
                                <small class="form-text text-muted">We'll use this to notify you if a spot becomes available.</small>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="customer_phone">Phone Number</label>
                                <input type="tel" class="form-control" id="customer_phone" name="customer_phone">
                                <small class="form-text text-muted">Optional, but helpful for quick notifications.</small>
                            </div>
                            
                            <div class="form-group mb-4">
                                <label for="notes">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                <small class="form-text text-muted">Any additional information you'd like to share.</small>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-clipboard-list me-2"></i> Join Waitlist
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
