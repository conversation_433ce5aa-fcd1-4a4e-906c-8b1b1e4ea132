from config.config import Config
from functools import lru_cache
from typing import Optional
from datetime import datetime, timedelta

# Cache for storing values with expiration
_cache = {}

def cache_with_ttl(ttl_seconds: int = 3600):
    """
    Decorator that caches a function's return value with a time-to-live (TTL).
    
    Args:
        ttl_seconds (int): Number of seconds to keep the value in cache
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
            
            # Check if value exists in cache and is not expired
            if cache_key in _cache:
                result, expiry = _cache[cache_key]
                if datetime.now() < expiry:
                    return result
                else:
                    del _cache[cache_key]
            
            # Get fresh value
            result = func(*args, **kwargs)
            
            # Cache the value with expiration
            expiry = datetime.now() + timedelta(seconds=ttl_seconds)
            _cache[cache_key] = (result, expiry)
            
            return result
        return wrapper
    return decorator

@lru_cache(maxsize=1)
def get_cached_value(key: str) -> Optional[str]:
    """
    Get a cached value using Python's built-in LRU cache.
    Useful for values that don't need expiration.
    
    Args:
        key (str): The key to look up
        
    Returns:
        Optional[str]: The cached value or None if not found
    """
    return _cache.get(key, (None, None))[0]

def set_cached_value(key: str, value: str, ttl_seconds: int = 3600) -> None:
    """
    Set a value in the cache with optional TTL.
    
    Args:
        key (str): The key to store the value under
        value (str): The value to store
        ttl_seconds (int): Number of seconds until the value expires
    """
    expiry = datetime.now() + timedelta(seconds=ttl_seconds)
    _cache[key] = (value, expiry)

def clear_cache() -> None:
    """Clear all cached values."""
    _cache.clear()
    get_cached_value.cache_clear()

