# Webhook System Deployment Guide

This guide explains how to deploy and test the webhook processing system.

## Prerequisites

1. Server access with root privileges
2. Python 3.7+ installed
3. pip package manager
4. Git (for cloning repository)

## Deployment Steps

1. Install deployment requirements:
```bash
pip install -r webhook_requirements.txt
```

2. Run the deployment script:
```bash
python deploy_webhook_system.py
```

The deployment script will:
- Connect to the server
- Install and configure Redis
- Set up the webhook processing service
- Deploy all required files
- Run initial tests

## Monitoring Deployment

The deployment script creates detailed logs in `webhook_deployment.log`. Monitor this file for:
- Installation progress
- Configuration changes
- Test results
- Any errors that occur

## Testing the Installation

After deployment, run the test suite:
```bash
python test_webhook_system.py
```

The test suite will verify:
1. Redis connectivity
2. Queue operations
3. Webhook endpoint functionality
4. End-to-end webhook processing

Test results are logged to `webhook_tests.log`.

## Monitoring the System

### Service Status
Check service status:
```bash
systemctl status webhook_worker
```

View service logs:
```bash
journalctl -u webhook_worker -f
```

### Redis Monitoring
Monitor Redis queue:
```bash
redis-cli
> LLEN shopify_webhooks  # Check queue length
> LRANGE shopify_webhooks 0 -1  # View all queued items
```

## Troubleshooting

### Common Issues

1. Service Won't Start
```bash
# Check service status
systemctl status webhook_worker
# Check logs for errors
journalctl -u webhook_worker -n 50
```

2. Redis Connection Issues
```bash
# Check Redis status
systemctl status redis-server
# Test Redis connection
redis-cli ping
```

3. Permission Issues
```bash
# Check file permissions
ls -l /var/www/html/cfc.tcgsync.com/utils/
# Fix permissions if needed
chown -R www-data:www-data /var/www/html/cfc.tcgsync.com/utils/
chmod -R 750 /var/www/html/cfc.tcgsync.com/utils/
```

### Rollback Procedure

If deployment fails, the script automatically creates backups. To rollback:

1. Stop the service:
```bash
systemctl stop webhook_worker
```

2. Restore from backup:
```bash
# Find latest backup
ls -lt /var/www/html/cfc.tcgsync.com/backups/
# Restore files
cp -r /var/www/html/cfc.tcgsync.com/backups/[backup_dir]/* /var/www/html/cfc.tcgsync.com/utils/
```

3. Restore Redis config:
```bash
cp /etc/redis/redis.conf.bak /etc/redis/redis.conf
systemctl restart redis-server
```

## Performance Tuning

### Redis Configuration
Edit `/etc/redis/redis.conf`:
```
maxmemory 512mb
maxmemory-policy allkeys-lru
appendonly yes
appendfsync everysec
```

### Service Resources
Edit `/etc/systemd/system/webhook_worker.service`:
```
[Service]
CPUQuota=50%
MemoryLimit=512M
LimitNOFILE=65535
```

## Security Considerations

1. Redis Security:
   - Redis is configured to listen only on localhost
   - Memory limits prevent resource exhaustion
   - Persistence enabled for data recovery

2. Service Security:
   - Running as www-data user
   - Limited file permissions
   - Resource quotas enforced
   - No new privileges allowed

3. File Permissions:
   - Utils directory: 750 (www-data:www-data)
   - Service file: 644 (root:root)
   - Log files: 640 (www-data:adm)

## Maintenance

### Regular Tasks

1. Log Rotation
   - Logs are automatically rotated daily
   - Kept for 7 days
   - Compressed to save space

2. Queue Monitoring
   - Check queue length regularly
   - Monitor processing rate
   - Watch for backed-up items

3. Performance Monitoring
   - Watch CPU usage
   - Monitor memory consumption
   - Check disk space for logs

### Updating

To update the webhook system:

1. Stop the service:
```bash
systemctl stop webhook_worker
```

2. Run deployment script:
```bash
python deploy_webhook_system.py
```

3. Run tests:
```bash
python test_webhook_system.py
```

The deployment script will handle backups and updates automatically.
