#!/usr/bin/env python3
"""
Standalone Shopify Auto Pricing Script

This script provides a standalone version of the shautopricing functionality,
allowing for independent execution without relying on the main application.
It handles TCGPlayer price fetching, price calculation, and pushing updates to Shopify.

Usage:
    python shstandalone.py --username USERNAME [--product_id PRODUCT_ID] [--product_type PRODUCT_TYPE]
"""

from concurrent.futures import ThreadPoolExecutor
from pymongo import MongoClient, UpdateOne
from requests.adapters import HTTPAdapter 
from requests.packages.urllib3.util.retry import Retry
from datetime import datetime, timedelta, timezone
from tqdm import tqdm
import requests
import sys
import time
import re
import logging
import json
import argparse
import os
import traceback
from threading import Lock

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('shstandalone.log')
    ]
)
logger = logging.getLogger(__name__)

# Pre-compile regex patterns
CONDITION_PATTERNS = {
   re.compile(r'near\s*mint|nm\b', re.I): 'nm',
   re.compile(r'lightly\s*played|lp\b', re.I): 'lp',
   re.compile(r'moderately\s*played|mp\b', re.I): 'mp',
   re.compile(r'heavily\s*played|hp\b', re.I): 'hp',
   re.compile(r'damaged|dm\b', re.I): 'dm'
}

SEALED_PATTERN = re.compile(r'.*seal.*', re.I)

# Constants
EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"
BATCH_SIZE = 500
DEFAULT_MIN_PRICE_USD = 0.2
MONGO_POOL_SIZE = 200

# MongoDB Configuration
MONGO_URI = '*******************************************************************'
MONGO_DBNAME = 'test'

# Enhanced session configuration
session = requests.Session()
retries = Retry(
   total=5,
   backoff_factor=0.5,
   status_forcelist=[500, 502, 503, 504],
   allowed_methods=frozenset(['GET'])
)
adapter = HTTPAdapter(
   max_retries=retries,
   pool_connections=MONGO_POOL_SIZE,
   pool_maxsize=MONGO_POOL_SIZE,
   pool_block=False
)
session.mount('https://', adapter)
session.mount('http://', adapter)

# MongoDB collections
client = None
db = None
shopify_collection = None
user_collection = None
tcgplayer_key_collection = None
autopricer_collection = None

def init_mongodb():
    """Initialize MongoDB connection and collections"""
    global client, db, shopify_collection, user_collection, tcgplayer_key_collection, autopricer_collection
    try:
        client = MongoClient(MONGO_URI)
        db = client[MONGO_DBNAME]
        shopify_collection = db['shProducts']
        user_collection = db['user']
        tcgplayer_key_collection = db['tcgplayerKey']
        autopricer_collection = db['autopricerShopify']
        logger.info("MongoDB connection initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing MongoDB connection: {str(e)}")
        sys.exit(1)

# Currency cache implementation
class CurrencyCache:
   def __init__(self, ttl_hours=24):
       self.cache = {}
       self.ttl = timedelta(hours=ttl_hours)
       self.lock = Lock()
       self.last_cleanup = datetime.now(timezone.utc)

   def get(self, currency):
       with self.lock:
           if currency in self.cache:
               rate_data = self.cache[currency]
               if datetime.now(timezone.utc) - rate_data['timestamp'] < self.ttl:
                   return rate_data['rate']
               else:
                   del self.cache[currency]
           return None

   def set(self, currency, rate):
       with self.lock:
           self.cache[currency] = {
               'rate': rate,
               'timestamp': datetime.now(timezone.utc)
           }

   def clear_expired(self):
       with self.lock:
           current_time = datetime.now(timezone.utc)
           expired = [currency for currency, data in self.cache.items() 
                     if current_time - data['timestamp'] >= self.ttl]
           for currency in expired:
               del self.cache[currency]
           self.last_cleanup = current_time

# TCGPlayer price cache implementation
class TCGPriceCache:
    def __init__(self, ttl_minutes=60):  # Increased to 60 minutes
        self.cache = {}
        self.ttl = timedelta(minutes=ttl_minutes)
        self.lock = Lock()
        self.hits = 0
        self.misses = 0
        self.last_cleanup = datetime.now(timezone.utc)
        self.max_cache_size = 10000  # Maximum number of items to store
    
    def get_stats(self):
        total = self.hits + self.misses
        hit_rate = (self.hits / total * 100) if total > 0 else 0
        return {
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache)
        }

    def clear_expired(self):
        with self.lock:
            current_time = datetime.now(timezone.utc)
            expired = [pid for pid, data in self.cache.items()
                      if current_time - data['timestamp'] >= self.ttl]
            for pid in expired:
                del self.cache[pid]
            self.last_cleanup = current_time

    def get_batch(self, product_ids):
        now = datetime.now(timezone.utc)
        cached = {}
        missing = []
        
        with self.lock:
            for pid in product_ids:
                if pid in self.cache:
                    data = self.cache[pid]
                    if now - data['timestamp'] < self.ttl:
                        cached[pid] = data['pricing']
                        self.hits += 1
                    else:
                        missing.append(pid)
                        del self.cache[pid]
                        self.misses += 1
                else:
                    missing.append(pid)
                    self.misses += 1
                    
            # Cleanup if cache gets too large
            if len(self.cache) > self.max_cache_size:
                self.clear_expired()
                
        return cached, missing

    def update_batch(self, pricing_data):
        with self.lock:
            now = datetime.now(timezone.utc)
            
            # Clear expired entries if we haven't done so recently
            if now - self.last_cleanup > timedelta(minutes=30):
                self.clear_expired()
            
            for pid, data in pricing_data.items():
                self.cache[pid] = {
                    'pricing': data,
                    'timestamp': now
                }

# Initialize caches
currency_cache = CurrencyCache()
tcgplayer_cache = TCGPriceCache()

def get_exchange_rate(target_currency):
   if target_currency == 'USD':
       return 1.0
   
   cached_rate = currency_cache.get(target_currency)
   if cached_rate is not None:
       return cached_rate
   
   try:
       url = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
       response = session.get(url, timeout=10)
       response.raise_for_status()
       rates = response.json().get('conversion_rates', {})
       rate = rates.get(target_currency)
       
       if rate is None:
           return 1.0
           
       currency_cache.set(target_currency, rate)
       return rate
   except Exception as e:
       logger.error(f"Error fetching exchange rate: {str(e)}")
       return 1.0

def get_pricing_rules(user_profile, product):
   advanced_key = f"{product.get('vendor')}_{product.get('product_type')}_{product.get('expansionName')}"
   
   if advanced_key in user_profile.get('advancedPricingRules', {}):
       return user_profile['advancedPricingRules'][advanced_key]
   return user_profile.get('customStepping', {'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65})

def has_valid_price(price_data):
    """Check if a price data object has any valid prices."""
    return (price_data.get('marketPrice') is not None or 
            price_data.get('lowPrice') is not None or 
            price_data.get('directLowPrice') is not None)

def determine_printing_type(variant_title, valid_subtypes):
    """
    Determine the printing type from variant title and valid TCGPlayer subtypes.
    Prioritizes matching against subtypes that have valid prices.
    """
    if not variant_title:
        return 'Normal'

    # Clean up variant title
    variant_title = variant_title.lower()
    variant_title = re.sub(r'\s+', ' ', variant_title)
    
    # Remove condition prefixes
    for cond in ['near mint', 'nm', 'lightly played', 'lp', 'moderately played', 'mp', 
                'heavily played', 'hp', 'damaged', 'dmg']:
        variant_title = variant_title.replace(cond, '').strip()
    variant_title = variant_title.strip('- ').strip()
    
    # First try exact match against valid subtypes
    for subtype in valid_subtypes:
        if subtype and variant_title == subtype.lower():
            return subtype
    
    # Then try partial matches
    if 'cold foil' in variant_title:
        return 'Cold Foil'
    if 'rainbow foil' in variant_title:
        return 'Rainbow Foil'
    if 'reverse holofoil' in variant_title or 'reverse holo' in variant_title:
        return 'Reverse Holofoil'
    if 'etched foil' in variant_title:
        return 'Etched Foil'
    if 'etched' in variant_title:
        return 'Etched'
    if 'gilded' in variant_title:
        return 'Gilded'
    if any(s in variant_title for s in ['holofoil', 'holo foil']):
        return 'Holofoil'
    if '1st edition' in variant_title:
        return '1st Edition'
    if 'unlimited' in variant_title:
        return 'Unlimited'
    if 'foil' in variant_title:
        for subtype in valid_subtypes:
            if subtype and 'foil' in subtype.lower():
                return subtype
        return 'Foil'
    
    # If no specific match found, return Normal
    return 'Normal'

def extract_condition(variant_title):
   title = variant_title.lower()
   for pattern, condition in CONDITION_PATTERNS.items():
       if pattern.search(title):
           return condition
   return 'nm'

def fetch_pricing_data(product_ids, tcgplayer_api_key):
   if not product_ids:
       return {}

   cached_data, missing_ids = tcgplayer_cache.get_batch(product_ids)
   
   if not missing_ids:
       return cached_data
       
   headers = {
       'Authorization': f'Bearer {tcgplayer_api_key}',
       'Accept': 'application/json',
   }
   
   new_data = {}
   chunks = [missing_ids[i:i + 100] for i in range(0, len(missing_ids), 100)]
   
   for chunk in chunks:
       try:
           url = f"https://api.tcgplayer.com/pricing/product/{','.join(chunk)}"
           response = session.get(url, headers=headers, timeout=10)
           response.raise_for_status()
           
           raw_response = response.text
           logger.info(f"Raw TCGPlayer response: {raw_response}")
           
           response_data = response.json()
           logger.info(f"TCGPlayer response data: {response_data}")
           logger.info(f"TCGPlayer results: {response_data.get('results', [])}")
           
           # Log the structure of the response
           if 'results' in response_data:
               for result in response_data['results']:
                   logger.info(f"Processing result: {result}")
                   logger.info(f"  Product ID: {result.get('productId')}")
                   logger.info(f"  Subtype: {result.get('subTypeName')}")
                   logger.info(f"  Market Price: {result.get('marketPrice')}")
                   logger.info(f"  Low Price: {result.get('lowPrice')}")
                   logger.info(f"  Direct Low Price: {result.get('directLowPrice')}")
                   logger.info(f"  Mid Price: {result.get('midPrice')}")
                   logger.info(f"  High Price: {result.get('highPrice')}")
           
           for item in response_data.get('results', []):
               product_id = str(item.get('productId'))
               if product_id:
                   if product_id not in new_data:
                       new_data[product_id] = []
                   new_data[product_id].append(item)
                   logger.info(f"Added price data for product {product_id}: {item}")
                   
       except Exception as e:
           logger.error(f"Error fetching TCGPlayer prices for chunk {chunk}: {str(e)}")
           logger.error(f"URL: {url}")
           continue
   
   tcgplayer_cache.update_batch(new_data)
   return {**cached_data, **new_data}

class PricingCalculator:
    def __init__(self, settings: dict, currency: str = 'USD'):
        """
        Initialize the pricing calculator with Shopify pricing settings.
        
        Args:
            settings (dict): Shopify pricing settings from ShopifySettings model
            currency (str): Currency code (e.g., 'USD', 'GBP')
        """
        self.settings = settings
        self.currency = currency
        
        # Set defaults for any missing settings
        required_settings = [
            'minPrice', 'price_point', 'price_rounding_enabled', 'price_rounding_thresholds',
            'use_highest_price', 'price_comparison_pairs', 'price_modifiers', 'price_preference_order',
            'game_minimum_prices', 'advancedPricingRules', 'customStepping',
            'tcg_trend_increasing', 'tcg_trend_decreasing'
        ]
        
        defaults = {
            'minPrice': 0.50,
            'price_point': 'Low Price',
            'price_rounding_enabled': False,
            'price_rounding_thresholds': [49, 99],
            'use_highest_price': False,
            'price_comparison_pairs': [],
            'price_modifiers': {},
            'price_preference_order': ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'],
            'game_minimum_prices': {},
            'advancedPricingRules': {},
            'customStepping': {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50},
            'tcg_trend_increasing': 0.0,
            'tcg_trend_decreasing': 0.0
        }
        
        for setting in required_settings:
            if setting not in self.settings:
                self.settings[setting] = defaults[setting]

    def apply_price_comparison(self, pricing_data: dict) -> tuple:
        """
        Apply price comparison rules to determine the base price.
        
        Args:
            pricing_data (Dict[str, float]): Dictionary of price types and their values
            
        Returns:
            Tuple[float, bool]: (calculated price, is_missing flag)
        """
        try:
            if not pricing_data:
                logger.error("No pricing data provided")
                return None, True
                
            # Log available prices
            logger.info("Available prices:")
            for price_type, price in pricing_data.items():
                logger.info(f"  {price_type}: {price}")
                
            price_modifiers = self.settings.get('price_modifiers', {})
            
            # Apply modifiers to all prices upfront
            modified_prices = {}
            for price_type, price in pricing_data.items():
                if price is not None:
                    try:
                        modifier = price_modifiers.get(price_type, 0)
                        modified_price = round(float(price) * (1 + modifier/100), 2)
                        modified_prices[price_type] = modified_price
                        if modifier != 0:
                            logger.info(f"Applied {modifier}% modifier to {price_type}: {price} -> {modified_price}")
                    except (ValueError, TypeError) as e:
                        logger.error(f"Error applying modifier to {price_type}: {price} - {str(e)}")
                        # Skip this price but continue with others
                        continue
            
            if not modified_prices:
                logger.error("No valid prices after applying modifiers")
                return None, True
            
            if self.settings.get('use_highest_price', False):
                pairs = self.settings.get('price_comparison_pairs', [])
                logger.info("Using highest price mode with pairs:")
                
                highest_price = None
                winning_pair = None
                
                for pair in pairs:
                    try:
                        price1 = modified_prices.get(pair[0])
                        price2 = modified_prices.get(pair[1])
                        
                        if price1 is not None and price2 is not None:
                            pair_max = max(price1, price2)
                            winner = pair[0] if price1 >= price2 else pair[1]
                            logger.info(f"  Comparing {pair[0]}({price1}) vs {pair[1]}({price2}) -> {winner}({pair_max})")
                            
                            if highest_price is None or pair_max > highest_price:
                                highest_price = pair_max
                                winning_pair = f"{pair[0]} vs {pair[1]}"
                    except Exception as e:
                        logger.error(f"Error comparing pair {pair}: {str(e)}")
                        continue
                
                if highest_price is not None:
                    logger.info(f"Selected highest price {highest_price} from {winning_pair}")
                    return highest_price, False
            
            # Use price preference order
            price_preferences = self.settings.get('price_preference_order', 
                ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])
                
            logger.info("Using price preference order:")
            for i, price_type in enumerate(price_preferences):
                logger.info(f"  {i+1}. {price_type}")
                if price_type in modified_prices:
                    price = modified_prices[price_type]
                    logger.info(f"Selected {price_type} with value {price}")
                    return price, False
            
            # If we get here, try any available price as a fallback
            logger.warning("No price found using preference order, trying any available price")
            for price_type, price in modified_prices.items():
                logger.info(f"Using fallback price {price_type}: {price}")
                return price, False
                    
            logger.error("No valid prices found after applying all rules")
            return None, True
            
        except Exception as e:
            logger.error(f"Error in apply_price_comparison: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None, True

    def apply_game_rules(self, price: float, game_name: str, product_type: str, rarity: str = None) -> float:
        if not game_name:
            return price
            
        game_settings = self.settings.get('game_minimum_prices', {}).get(game_name, {})
        
        game_default_min = game_settings.get('default_min_price')
        if game_default_min is not None:
            price = max(price, game_default_min)
                
        if rarity and 'rarities' in game_settings:
            rarity_min = game_settings['rarities'].get(rarity)
            if rarity_min is not None:
                price = max(price, rarity_min)
                    
        return price

    def apply_condition_stepping(self, price: float, condition: str, vendor: str, 
                               product_type: str, expansion: str) -> float:
        """
        Apply condition-based price stepping rules
        """
        # First check advanced rules
        key = f"{vendor}_{product_type}_{expansion}"
        stepping_rules = self.settings.get('advancedPricingRules', {}).get(key)
        
        # If no advanced rules, use custom stepping
        if not stepping_rules:
            stepping_rules = self.settings.get('customStepping')
            if not stepping_rules:
                stepping_rules = {
                    'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50
                }
            logger.info(f"Using custom stepping rules: {stepping_rules}")
        else:
            logger.info(f"Using advanced stepping rules for {key}: {stepping_rules}")

        condition_map = {
            'near mint': 'nm', 'near-mint': 'nm', 'nearmint': 'nm', 'nm': 'nm',
            'lightly played': 'lp', 'lightly-played': 'lp', 'lightlyplayed': 'lp', 'lp': 'lp',
            'moderately played': 'mp', 'moderately-played': 'mp', 'moderatelyplayed': 'mp', 'mp': 'mp',
            'heavily played': 'hp', 'heavily-played': 'hp', 'heavilyplayed': 'hp', 'hp': 'hp',
            'damaged': 'dm', 'dm': 'dm'
        }
            
        condition_code = condition_map.get(condition.lower().strip(), 'nm')
        
        stepping_percentage = stepping_rules.get(condition_code, 100)
        
        # Convert percentage to decimal
        stepping_decimal = float(stepping_percentage) / 100
        
        logger.info(f"Condition: {condition} -> {condition_code}")
        logger.info(f"Stepping percentage: {stepping_percentage}% -> {stepping_decimal}")
        logger.info(f"Original price: ${price}")
        logger.info(f"After stepping: ${round(price * stepping_decimal, 2)}")
        
        return round(price * stepping_decimal, 2)

    def apply_price_rounding(self, price: float) -> float:
        if not self.settings.get('price_rounding_enabled', False):
            return round(price, 2)
            
        thresholds = sorted(self.settings.get('price_rounding_thresholds', [49, 99]))
        dollars = int(price)
        cents = round((price - dollars) * 100)
        
        for threshold in thresholds:
            if cents <= threshold:
                return dollars + (threshold / 100)
                
        return dollars + 1

    def calculate_final_price(self, sku_info: dict, catalog_item: dict) -> tuple:
        """
        Calculate the final price in local currency.
        All input prices in sku_info['pricingInfo'] should already be in local currency.
        """
        price_history = []
        try:
            # Log input data for debugging
            logger.info(f"Starting price calculation with:")
            logger.info(f"  SKU Info: {sku_info}")
            logger.info(f"  Catalog Item: {catalog_item}")
            
            # Get base price in local currency using price comparison rules
            base_price, is_missing = self.apply_price_comparison(sku_info.get('pricingInfo', {}))
            if is_missing:
                logger.error("Failed to get base price from price comparison")
                return None, True, price_history
                
            price_history.append({
                'step': 'Initial Price',
                'old_price': 0,
                'new_price': base_price,
                'details': 'Price after applying comparison rules'
            })

            # Apply TCG trend adjustments if enabled
            if base_price > 0:
                old_price = base_price
                try:
                    tcg_trend_increasing = float(self.settings.get('tcg_trend_increasing', 0))
                    tcg_trend_decreasing = float(self.settings.get('tcg_trend_decreasing', 0))
                    
                    if tcg_trend_increasing > 0 and base_price > old_price:
                        increase = tcg_trend_increasing / 100
                        base_price = round(base_price * (1 + increase), 2)
                        price_history.append({
                            'step': 'TCG Trend Increasing',
                            'old_price': old_price,
                            'new_price': base_price,
                            'details': f'Applied {tcg_trend_increasing}% increase'
                        })
                    elif tcg_trend_decreasing > 0 and base_price < old_price:
                        decrease = tcg_trend_decreasing / 100
                        base_price = round(base_price * (1 - decrease), 2)
                        price_history.append({
                            'step': 'TCG Trend Decreasing',
                            'old_price': old_price,
                            'new_price': base_price,
                            'details': f'Applied {tcg_trend_decreasing}% decrease'
                        })
                except (ValueError, TypeError) as e:
                    logger.error(f"Error applying TCG trend adjustments: {str(e)}")
                    # Continue with the base price

            # Apply game rules (min prices are already in local currency)
            old_price = base_price
            try:
                nm_price = self.apply_game_rules(
                    base_price,
                    catalog_item.get('gameName'),
                    catalog_item.get('product_type'),
                    catalog_item.get('rarity')
                )
                if nm_price != old_price:
                    price_history.append({
                        'step': 'Game Rules',
                        'old_price': old_price,
                        'new_price': nm_price,
                        'details': f'Applied game minimum price rules for {catalog_item.get("gameName")}'
                    })
            except Exception as e:
                logger.error(f"Error applying game rules: {str(e)}")
                nm_price = base_price  # Continue with the base price

            condition = sku_info.get('condName', 'Near Mint').lower().strip()
            
            # Get the NM multiplier from custom stepping
            key = f"{catalog_item.get('vendor', '')}_{catalog_item.get('product_type', '')}_{catalog_item.get('expansionName', '')}"
            stepping_rules = self.settings.get('advancedPricingRules', {}).get(key)
            
            # If no advanced rules, use custom stepping
            if not stepping_rules:
                stepping_rules = self.settings.get('customStepping')
                if not stepping_rules:
                    stepping_rules = {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50}
                logger.info(f"Using custom stepping rules: {stepping_rules}")
            else:
                logger.info(f"Using advanced stepping rules for {key}: {stepping_rules}")
            
            # Apply condition stepping
            old_price = nm_price
            try:
                price = self.apply_condition_stepping(
                    nm_price,  # Use the min-price-adjusted NM price as base
                    condition,
                    catalog_item.get('vendor', ''),
                    catalog_item.get('product_type', ''),
                    catalog_item.get('expansionName', '')
                )
                if price != old_price:
                    price_history.append({
                        'step': 'Condition Stepping',
                        'old_price': old_price,
                        'new_price': price,
                        'details': f'Applied condition stepping for {condition}'
                    })
            except Exception as e:
                logger.error(f"Error applying condition stepping: {str(e)}")
                price = old_price  # Continue with the previous price

            # Apply minimum price check
            try:
                min_price = float(self.settings.get('minPrice', 0))
                old_price = price
                if price < min_price:
                    price = min_price
                    price_history.append({
                        'step': 'Minimum Price',
                        'old_price': old_price,
                        'new_price': price,
                        'details': f'Applied minimum price of {min_price}'
                    })
            except (ValueError, TypeError) as e:
                logger.error(f"Error applying minimum price: {str(e)}")
                # Continue with the previous price

            # Apply price rounding in local currency
            old_price = price
            try:
                if self.settings.get('price_rounding_enabled', False):
                    price = self.apply_price_rounding(price)
                    if price != old_price:
                        price_history.append({
                            'step': 'Price Rounding',
                            'old_price': old_price,
                            'new_price': price,
                            'details': f'Rounded to thresholds {self.settings.get("price_rounding_thresholds", [])}'
                        })
                else:
                    price = round(price, 2)
                    if price != old_price:
                        price_history.append({
                            'step': 'Standard Rounding',
                            'old_price': old_price,
                            'new_price': price,
                            'details': 'Rounded to 2 decimal places'
                        })
            except Exception as e:
                logger.error(f"Error applying price rounding: {str(e)}")
                price = round(old_price, 2)  # Ensure we have a rounded price
            
            logger.info(f"Final calculated price: {price}")
            return price, False, price_history
            
        except Exception as e:
            logger.error(f"Error calculating final price: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            price_history.append({
                'step': 'Error',
                'old_price': 0,
                'new_price': 0,
                'details': f'Error calculating price: {str(e)}'
            })
            return None, True, price_history


def prepare_shopify_settings(username):
    """
    Get or create Shopify pricing settings for a user.
    
    Args:
        username (str): Username to get settings for
        
    Returns:
        dict: Standardized settings for PricingCalculator
    """
    # Get user profile directly since it contains all settings
    user_profile = user_collection.find_one({'username': username})
    if not user_profile:
        logger.error(f"No user profile found for {username}")
        return {}
        
    # Convert to dict format expected by PricingCalculator
    return {
        'use_highest_price': user_profile.get('use_highest_price', False),
        'price_comparison_pairs': user_profile.get('price_comparison_pairs', []),
        'price_modifiers': user_profile.get('price_modifiers', {}),
        'price_preference_order': user_profile.get('price_preference_order', ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']),
        'minPrice': float(user_profile.get('minPrice', 0.2)),
        'game_minimum_prices': user_profile.get('game_minimum_prices', {}),
        'advancedPricingRules': user_profile.get('advancedPricingRules', {}),
        'customStepping': user_profile.get('customStepping', {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50}),
        'price_rounding_enabled': user_profile.get('price_rounding_enabled', False),
        'price_rounding_thresholds': user_profile.get('price_rounding_thresholds', [49, 99]),
        'tcg_trend_increasing': user_profile.get('tcg_trend_increasing', 0.0),
        'tcg_trend_decreasing': user_profile.get('tcg_trend_decreasing', 0.0)
    }


def process_single_product(product, username, tcgplayer_api_key):
    """Process a single product for repricing using the same logic as bulk repricing"""
    try:
        # Get user profile and settings
        user_profile = user_collection.find_one({'username': username})
        if not user_profile:
            logger.error(f"User profile not found for {username}")
            return False
        
        user_currency = user_profile.get('currency', 'USD')
        exchange_rate = get_exchange_rate(user_currency)

        # Skip products with manual override
        if product.get('manualOverride', False):
            logger.info(f"Skipping product {product.get('_id')} due to manual override")
            return False

        # Get pricing data
        product_id = str(product.get('productId'))
        if not product_id:
            logger.error(f"Product {product.get('_id')} has no productId")
            return False

        pricing_data = fetch_pricing_data([product_id], tcgplayer_api_key)
        if not product_id in pricing_data:
            logger.error(f"No pricing data found for product {product_id}")
            return False

        product_pricing = pricing_data.get(product_id, [])
        if not isinstance(product_pricing, list):
            product_pricing = [product_pricing]
            
        # Only include subtypes that have valid prices
        valid_subtypes = [p.get('subTypeName') for p in product_pricing 
                         if p.get('subTypeName') and 
                         (p.get('marketPrice') is not None or 
                          p.get('lowPrice') is not None or 
                          p.get('directLowPrice') is not None)]
        logger.info(f"Valid subtypes: {valid_subtypes}")
        pricing_rules = get_pricing_rules(user_profile, product)
        
        variants_changed = False
        variants = product['variants']
        price_changes = []
        
        for variant in variants:
            try:
                old_price = float(variant.get('price', 0))
                printing_type = determine_printing_type(variant['title'], valid_subtypes)
            
                logger.info(f"Processing variant: {variant['title']}")
                logger.info(f"Looking for printing type: {printing_type}")
                
                # First find the price data for this printing type
                matched_price = None
                
                # First try exact match
                for p in product_pricing:
                    subtype = p.get('subTypeName', '').lower()
                    logger.info(f"Checking for exact match - subtype: {subtype}")
                    if subtype == printing_type.lower() and has_valid_price(p):
                        matched_price = p
                        logger.info(f"Found exact match with valid price: {p}")
                        break
                
                # If no exact match, try holofoil match
                if not matched_price:
                    for p in product_pricing:
                        subtype = p.get('subTypeName', '').lower()
                        logger.info(f"Checking for holofoil match - subtype: {subtype}")
                        if 'holofoil' in subtype and has_valid_price(p):
                            matched_price = p
                            logger.info(f"Found holofoil match with valid price: {p}")
                            break
                
                # If still no match, try normal match
                if not matched_price:
                    for p in product_pricing:
                        subtype = p.get('subTypeName', '').lower()
                        logger.info(f"Checking for normal match - subtype: {subtype}")
                        if subtype == 'normal' and has_valid_price(p):
                            matched_price = p
                            logger.info(f"Found normal match with valid price: {p}")
                            break
                
                # If no matches with valid prices, try to find any valid price
                if not matched_price:
                    for p in product_pricing:
                        if has_valid_price(p):
                            matched_price = p
                            logger.info(f"Using first available price with valid data: {p}")
                            break

                if matched_price:
                    logger.info(f"Found matching price data for {printing_type}:")
                    logger.info(f"Raw price data: {matched_price}")
                    
                    # Extract pricing info for this printing type
                    pricing_info = {}
                    
                    # Get market price first as it's often the most reliable
                    market_price = matched_price.get('marketPrice')
                    if market_price is not None:
                        pricing_info['marketPrice'] = float(market_price)
                        logger.info(f"Market Price: ${market_price}")
                    
                    # Get low price (try directLowPrice first, then lowPrice)
                    low_price = matched_price.get('directLowPrice')
                    if low_price is None:
                        low_price = matched_price.get('lowPrice')
                    if low_price is not None:
                        pricing_info['lowPrice'] = float(low_price)
                        logger.info(f"Low Price: ${low_price}")
                    
                    # Get mid price (fallback to market price if not available)
                    mid_price = matched_price.get('midPrice')
                    if mid_price is None and market_price is not None:
                        mid_price = market_price
                    if mid_price is not None:
                        pricing_info['midPrice'] = float(mid_price)
                        logger.info(f"Mid Price: ${mid_price}")
                    
                    # Get high price (fallback to market price if not available)
                    high_price = matched_price.get('highPrice')
                    if high_price is None and market_price is not None:
                        high_price = market_price
                    if high_price is not None:
                        pricing_info['highPrice'] = float(high_price)
                        logger.info(f"High Price: ${high_price}")

                    # Update product info to include printing type and condition
                    product['printingType'] = printing_type
                    product['condition'] = extract_condition(variant.get('option1', ''))
                    
                    # Prepare settings with custom stepping rules
                    settings = prepare_shopify_settings(username)
                    
                    # Override customStepping with the user's pricing rules
                    settings['customStepping'] = pricing_rules
                    min_price = settings.get('minPrice', 0)
                    
                    logger.info(f"Using custom stepping rules: {pricing_rules}")
                    
                    # Create sku_info for price calculation
                    sku_info = {
                        'pricingInfo': pricing_info,
                        'condName': extract_condition(variant.get('option1', '')),
                        'printingName': printing_type,
                        'skuId': variant.get('id'),
                        'variantTitle': variant.get('title')
                    }
                    
                    # Calculate new price
                    calculator = PricingCalculator(settings, user_currency)
                    new_price, is_missing, price_history = calculator.calculate_final_price(sku_info, product)
                    
                    if not is_missing and new_price is not None:
                        if abs(old_price - new_price) > 0.01:
                            variant['price'] = str(new_price)
                            variants_changed = True
                            price_changes.append({
                                'variant_id': variant['id'],
                                'variant_title': variant['title'],
                                'old_price': old_price,
                                'new_price': new_price
                            })
                            logger.info(f"Price changed for variant {variant['title']}: ${old_price} -> ${new_price}")
            except Exception as e:
                logger.error(f"Error processing variant {variant.get('title')}: {str(e)}")
                logger.error(traceback.format_exc())
                continue

        # If any variant price changed, mark the product as needing pushing
        if variants_changed:
            try:
                # Update the product in the database
                shopify_collection.update_one(
                    {'_id': product['_id']},
                    {
                        '$set': {
                            'variants': variants,
                            'needsPushing': True,  # Mark for pushing to Shopify
                            'last_repriced': datetime.now(timezone.utc)
                        }
                    }
                )
                logger.info(f"Updated product {product.get('title')} (ID: {product.get('_id')}) with new prices and marked for pushing")
                return True
            except Exception as e:
                logger.error(f"Error updating product in database: {str(e)}")
                logger.error(traceback.format_exc())
                return False
        else:
            logger.info(f"No price changes for product {product.get('title')} (ID: {product.get('_id')})")
            return False

    except Exception as e:
        logger.error(f"Error processing product: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def process_user_products(username, product_type=None, product_id=None):
    """Process all products for a user, optionally filtered by product type or ID"""
    try:
        # Get TCGPlayer API key
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            logger.error("No TCGPlayer API key found")
            return False
        
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        
        # Build query
        query = {'username': username}
        
        # Add product type filter if specified
        if product_type:
            query['product_type'] = {'$regex': f'^{re.escape(product_type)}$', '$options': 'i'}
        
        # Add product ID filter if specified
        if product_id:
            query['productId'] = product_id
        
        # Exclude sealed products
        query['rarity'] = {'$not': {'$regex': r'.*seal.*', '$options': 'i'}}
        
        # Count products
        total_products = shopify_collection.count_documents(query)
        if not total_products:
            logger.info(f"No products found for user {username} with the specified filters")
            return False
        
        logger.info(f"Processing {total_products} products for user {username}")
        
        # Process products
        products = shopify_collection.find(query)
        total_updated = 0
        
        with tqdm(total=total_products, desc=f"Processing {username}", leave=True) as pbar:
            for product in products:
                pbar.update(1)
                if process_single_product(product, username, tcgplayer_api_key):
                    total_updated += 1
        
        logger.info(f"Completed processing for user {username}")
        logger.info(f"Total products processed: {total_products}")
        logger.info(f"Products updated: {total_updated}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error processing user products: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def main():
    """Main function to run the script"""
    parser = argparse.ArgumentParser(description='Standalone Shopify Auto Pricing Script')
    parser.add_argument('--username', required=True, help='Username to process products for')
    parser.add_argument('--product_type', help='Filter by product type (e.g., "Singles")')
    parser.add_argument('--product_id', help='Process a specific product by ID')
    
    args = parser.parse_args()
    
    try:
        # Initialize MongoDB connection
        init_mongodb()
        
        # Process products
        success = process_user_products(args.username, args.product_type, args.product_id)
        
        if success:
            logger.info("Processing completed successfully")
            return 0
        else:
            logger.error("Processing failed")
            return 1
    
    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        logger.error(traceback.format_exc())
        return 1
    finally:
        # Close MongoDB connection
        if client:
            client.close()


if __name__ == "__main__":
    sys.exit(main())
