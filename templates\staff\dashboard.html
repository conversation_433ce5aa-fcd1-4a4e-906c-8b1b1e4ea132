<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Dashboard - TCGSync</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
/* Theme variables */
:root {
    --bg-color: #f8f9fa;
    --text-color: #212529;
    --card-bg: #ffffff;
    --card-header-bg: linear-gradient(135deg, #4a90e2, #357abd);
    --card-header-text: white;
    --stats-card-bg: #ffffff;
    --stats-card-text: #212529;
    --stats-card-title: #6c757d;
    --table-header-bg: #e9ecef;
    --table-header-text: #212529;
    --table-border: #dee2e6;
    --table-hover: #f2f2f2;
    --tool-card-bg: #ffffff;
    --tool-card-text: #212529;
    --tool-card-hover: #e9ecef;
    --icon-color: #4a90e2;
    --accent-color: #4a90e2;
    --accent-hover: #357abd;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}

[data-theme="dark"] {
    --bg-color: #121212;
    --text-color: #e0e0e0;
    --card-bg: #1e1e1e;
    --card-header-bg: linear-gradient(135deg, #2c3e50, #1a202c);
    --card-header-text: #e0e0e0;
    --stats-card-bg: #2c3e50;
    --stats-card-text: white;
    --stats-card-title: #a0aec0;
    --table-header-bg: #1a202c;
    --table-header-text: white;
    --table-border: #4a5568;
    --table-hover: #3a4a5c;
    --tool-card-bg: #2c3e50;
    --tool-card-text: white;
    --tool-card-hover: #3a4a5c;
    --icon-color: #4a90e2;
    --accent-color: #4a90e2;
    --accent-hover: #357abd;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Modern Topbar */
.topbar {
    background-color: var(--card-bg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 15px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.topbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
}

.topbar-brand i {
    color: var(--accent-color);
}

.topbar-brand:hover {
    color: var(--accent-color);
}

.topbar-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.theme-toggle {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    color: var(--text-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--accent-color);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-menu .dropdown-toggle {
    color: var(--text-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
    background-color: transparent;
}

.user-menu .dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .user-menu .dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.dropdown-menu {
    background-color: var(--card-bg);
    border-color: var(--table-border);
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 10px;
    margin-top: 10px;
    min-width: 200px;
}

.dropdown-item {
    color: var(--text-color);
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: var(--tool-card-hover);
    color: var(--text-color);
}

.dropdown-item i {
    margin-right: 10px;
    color: var(--accent-color);
}

.dropdown-divider {
    border-top-color: var(--table-border);
    margin: 8px 0;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--accent-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Stats Cards */
.stats-section {
    margin-bottom: 30px;
}

.stats-card {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    background-color: var(--stats-card-bg);
    color: var(--stats-card-text);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.stats-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.stats-card .card-body {
    padding: 20px;
}

.stats-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--stats-card-title);
    margin-bottom: 5px;
}

.stats-card .card-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-top: 10px;
    margin-bottom: 5px;
    color: var(--stats-card-text);
}

.stats-card .trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.stats-card .trend-up {
    color: var(--success-color);
}

.stats-card .trend-down {
    color: var(--danger-color);
}

.stats-card .icon-container {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.stats-card .icon-bg-primary {
    background-color: rgba(74, 144, 226, 0.1);
    color: var(--accent-color);
}

.stats-card .icon-bg-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.stats-card .icon-bg-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.stats-card .icon-bg-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* Tool cards */
.tool-card {
    text-decoration: none;
    color: inherit;
    display: block;
    margin-bottom: 25px;
}

.tool-card .card {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    background-color: var(--tool-card-bg);
    color: var(--tool-card-text);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.tool-card .card:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-8px);
}

.tool-card .card-title {
    font-weight: 600;
    margin-top: 10px;
    font-size: 1.3rem;
}

.tool-card .card-text {
    color: var(--stats-card-title);
    margin-top: 10px;
}

.tool-card .card-text ul {
    margin-top: 15px;
    padding-left: 20px;
}

.tool-card .card-text ul li {
    margin-bottom: 8px;
    position: relative;
}

.tool-card .card-text ul li::before {
    content: "•";
    color: var(--accent-color);
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1em;
}

.tool-card i {
    color: var(--accent-color);
    transition: all 0.3s ease;
}

.tool-card:hover i {
    transform: scale(1.1);
}

.tool-card .btn {
    border-radius: 30px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.tool-card .btn-primary {
    background-color: var(--accent-color);
}

.tool-card .btn-primary:hover {
    background-color: var(--accent-hover);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Card headers */
.card-header {
    background: var(--card-header-bg);
    color: var(--card-header-text);
    border-top-left-radius: 16px !important;
    border-top-right-radius: 16px !important;
    border-bottom: none;
    padding: 20px;
}

.card {
    border: none;
    border-radius: 16px;
    overflow: hidden;
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* Table styling */
.table {
    color: var(--text-color);
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: var(--table-header-bg);
    color: var(--table-header-text);
    border-bottom: 2px solid var(--table-border);
    padding: 15px;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: var(--table-hover);
}

.table td, .table th {
    border-color: var(--table-border);
    padding: 15px;
    vertical-align: middle;
}

/* Badge styling */
.badge {
    padding: 6px 12px;
    font-weight: 500;
    border-radius: 20px;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

/* Register selection modal styling */
.modal-content {
    background-color: var(--card-bg);
    color: var(--text-color);
    border: none;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modal-header {
    border-bottom-color: var(--table-border);
    padding: 20px 25px;
}

.modal-header .modal-title {
    font-weight: 600;
    font-size: 1.3rem;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top-color: var(--table-border);
    padding: 20px 25px;
}

.register-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.register-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.register-card.border-primary {
    border: 2px solid var(--accent-color) !important;
}

.register-card .card-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.register-card ul {
    margin-bottom: 20px;
}

.register-card li {
    margin-bottom: 8px;
}

.form-check-input {
    background-color: var(--tool-card-hover);
    border-color: var(--table-border);
    width: 18px;
    height: 18px;
}

.form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.form-check-label {
    color: var(--text-color);
    font-size: 1rem;
    padding-left: 5px;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.15);
    color: var(--warning-color);
    border-color: rgba(255, 193, 7, 0.2);
    border-radius: 12px;
    padding: 15px 20px;
}

.alert-warning i {
    margin-right: 10px;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loading spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
    color: var(--accent-color);
}

/* Section titles */
.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-title i {
    color: var(--accent-color);
}

/* Welcome banner */
.welcome-banner {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    color: white;
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.welcome-banner h2 {
    font-weight: 600;
    margin-bottom: 10px;
}

.welcome-banner p {
    opacity: 0.9;
    margin-bottom: 0;
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    border-color: var(--accent-hover);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .topbar {
        padding: 15px;
    }
    
    .container-fluid {
        padding: 15px;
    }
    
    .stats-card .card-value {
        font-size: 2rem;
    }
}
</style>

</head>
<body>
<!-- Topbar -->
<div class="topbar">
    <a href="{{ url_for('staff_auth.dashboard') }}" class="topbar-brand">
        <i class="fas fa-shield-alt"></i>TCGSync Staff
    </a>
    <div class="topbar-actions">
        <button id="theme-toggle" class="theme-toggle">
            <i class="fas fa-moon"></i>
        </button>
        <div class="user-menu">
            <div class="user-avatar">
                {{ staff.username[0].upper() if staff and staff.username else current_user.username[0].upper() }}
            </div>
            <div class="dropdown">
                <button class="btn dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    {% if staff and staff.name %}
                        {{ staff.name }}
                    {% else %}
                        {{ staff.username if staff else current_user.username }}
                    {% endif %}
                </button>
                <ul class="dropdown-menu" aria-labelledby="userDropdown">
                    {% if not staff %}
                    <li><a class="dropdown-item" href="{{ url_for('dashboard.dashboard') }}"><i class="fas fa-tachometer-alt"></i>Main Dashboard</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('staff_management.staff_management') }}"><i class="fas fa-users-cog"></i>Staff Management</a></li>
                    <li><hr class="dropdown-divider"></li>
                    {% endif %}
                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid px-4 pb-4">
    <!-- Welcome Banner -->
    <div class="welcome-banner animate__animated animate__fadeIn">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2>Welcome, {{ staff.name if staff and staff.name else (staff.username if staff else current_user.username) }}!</h2>
                <p>Access all your staff tools and manage your store operations from this dashboard.</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="current-time">
                    <i class="far fa-clock me-2"></i><span id="currentTime"></span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tools Section -->
    <h3 class="section-title mb-4"><i class="fas fa-tools"></i>Staff Tools</h3>
    <!-- Grid Layout for Staff Tools -->
    <div class="row g-4">
        <!-- Point of Sale -->
        {% if staff and staff.permissions and staff.permissions.pos %}
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="tool-card" id="pos-card">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-cash-register fa-4x mb-4"></i>
                        <h4 class="card-title">Point of Sale</h4>
                        <!-- Card text removed for minimal design -->
                        <div class="mt-4">
                            <button class="btn btn-primary" id="open-pos-btn">Open POS System</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Inventory Management -->
        {% if staff and staff.permissions and staff.permissions.inventory %}
        <div class="col-md-6 col-lg-4 col-xl-3">
            <a href="{{ url_for('staff_auth.inventory') }}" class="tool-card">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-boxes fa-4x mb-4"></i>
                        <h4 class="card-title">Inventory</h4>
                        <!-- Card text removed for minimal design -->
                        <div class="mt-4">
                            <span class="btn btn-primary">Manage Inventory</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endif %}
        
        <!-- Buylist Management -->
        {% if staff and staff.permissions and staff.permissions.buylist_orders %}
        <div class="col-md-6 col-lg-4 col-xl-3">
            <a href="{{ url_for('staff_auth.buylist') }}" class="tool-card">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-shopping-basket fa-4x mb-4"></i>
                        <h4 class="card-title">Buylist</h4>
                        <!-- Card text removed for minimal design -->
                        <div class="mt-4">
                            <span class="btn btn-primary">Open Buylist</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endif %}
        
        <!-- Card Scanning -->
        {% if staff and staff.permissions and staff.permissions.card_scanning %}
        <div class="col-md-6 col-lg-4 col-xl-3">
            <a href="{{ url_for('staff_auth.card_scanning') }}" class="tool-card">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-camera fa-4x mb-4"></i>
                        <h4 class="card-title">Card Scanning</h4>
                        <!-- Card text removed for minimal design -->
                        <div class="mt-4">
                            <span class="btn btn-primary">Start Scanning</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endif %}
        
        {% if not staff %}
        <!-- Staff Management -->
        <div class="col-md-6 col-lg-4 col-xl-3">
            <a href="{{ url_for('staff_management.staff_management') }}" class="tool-card">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-users-cog fa-4x mb-4"></i>
                        <h4 class="card-title">Staff Management</h4>
                        <!-- Card text removed for minimal design -->
                        <div class="mt-4">
                            <span class="btn btn-primary">Manage Staff</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Main Dashboard -->
        <div class="col-md-6 col-lg-4 col-xl-3">
            <a href="{{ url_for('dashboard.dashboard') }}" class="tool-card">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-tachometer-alt fa-4x mb-4"></i>
                        <h4 class="card-title">Main Dashboard</h4>
                        <!-- Card text removed for minimal design -->
                        <div class="mt-4">
                            <span class="btn btn-primary">Go to Dashboard</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Register Selection Modal -->
<div class="modal fade" id="registerSelectionModal" tabindex="-1" aria-labelledby="registerSelectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="registerSelectionModalLabel"><i class="fas fa-cash-register me-2"></i>Select a Register</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="rememberRegister" checked>
                        <label class="form-check-label" for="rememberRegister">
                            Remember my register selection
                        </label>
                    </div>
                </div>
                <div class="row g-3" id="registerList">
                    <!-- Registers will be populated here -->
                    <div class="col-12 text-center" id="loadingRegisters">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading available registers...</p>
                    </div>
                    <div class="col-12 text-center d-none" id="noRegisters">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No registers are available. Please contact an administrator.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Theme toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const themeToggle = document.getElementById('theme-toggle');
        const icon = themeToggle.querySelector('i');
        
        // Check for saved theme preference or default to dark mode
        const savedTheme = localStorage.getItem('staff-theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateIcon(savedTheme);
        } else {
            // Set dark mode as default
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('staff-theme', 'dark');
            updateIcon('dark');
        }
        
        // Toggle theme when button is clicked
        themeToggle.addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('staff-theme', newTheme);
            
            updateIcon(newTheme);
        });
        
        function updateIcon(theme) {
            if (theme === 'dark') {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        }
        
        // Current time display
        function updateCurrentTime() {
            const now = new Date();
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            document.getElementById('currentTime').textContent = now.toLocaleDateString('en-US', options);
        }
        
        updateCurrentTime();
        setInterval(updateCurrentTime, 60000); // Update every minute
        
        // Add animation classes to elements
        document.querySelectorAll('.tool-card').forEach((card, index) => {
            card.classList.add('animate__animated', 'animate__fadeInUp');
            card.style.animationDelay = `${index * 0.1}s`;
        });
        
        
        // POS Register Selection functionality
        const openPosBtn = document.getElementById('open-pos-btn');
        
        // Only set up POS functionality if the button exists (staff has POS permission)
        if (openPosBtn) {
            const registerModal = new bootstrap.Modal(document.getElementById('registerSelectionModal'));
            const registerList = document.getElementById('registerList');
            const loadingRegisters = document.getElementById('loadingRegisters');
            const noRegisters = document.getElementById('noRegisters');
            const rememberRegisterCheckbox = document.getElementById('rememberRegister');
            
            // Check if there's a remembered register
            const rememberedRegisterId = localStorage.getItem('staff-preferred-register');
            
            // Function to fetch registers
        function fetchRegisters() {
            // Show loading state
            loadingRegisters.classList.remove('d-none');
            noRegisters.classList.add('d-none');
            
            // Clear previous register list (except loading and no registers messages)
            Array.from(registerList.children).forEach(child => {
                if (child !== loadingRegisters && child !== noRegisters) {
                    child.remove();
                }
            });
            
            // Fetch registers from the server
            fetch('/pos')
                .then(response => response.text())
                .then(html => {
                    // Parse the HTML to extract tills
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const tills = Array.from(doc.querySelectorAll('.list-group-item'));
                    
                    // Hide loading state
                    loadingRegisters.classList.add('d-none');
                    
                    if (tills.length === 0) {
                        // Show no registers message
                        noRegisters.classList.remove('d-none');
                    } else {
                        // Create register cards
                        tills.forEach(till => {
                            // Extract till information
                            const tillId = till.querySelector('a.btn-success').href.split('/').pop();
                            
                            // Extract data directly from the till element's text content
                            // This is more reliable than using regex
                            
                            // Default values
                            let location = "Unknown Location";
                            let startingFloat = "0.00";
                            let runningTotal = "0.00";
                            let taxRate = "0";
                            
                            // First try to find the location in the first h5 element (most likely location)
                            const h5Element = till.querySelector('h5');
                            if (h5Element) {
                                location = h5Element.textContent.trim();
                            }
                            
                            // If that fails, try to extract from the innerHTML using a more flexible regex
                            if (location === "Unknown Location") {
                                const locationMatches = till.innerHTML.match(/Location:.*?([^<>\n]+?)(?:<|$)/i);
                                if (locationMatches && locationMatches[1]) {
                                    location = locationMatches[1].replace(/<\/strong>/g, '').trim();
                                }
                            }
                            
                            // Try to extract starting float
                            const startingFloatMatch = till.innerHTML.match(/Starting Float:<\/strong>\s*\$([^<]+)/);
                            if (startingFloatMatch && startingFloatMatch[1]) {
                                startingFloat = startingFloatMatch[1].trim();
                            }
                            
                            // Try to extract running total
                            const runningTotalMatch = till.innerHTML.match(/Running Total:<\/strong>\s*\$([^<]+)/);
                            if (runningTotalMatch && runningTotalMatch[1]) {
                                runningTotal = runningTotalMatch[1].trim();
                            }
                            
                            // Try to extract tax rate
                            const taxRateMatch = till.innerHTML.match(/Tax Rate:<\/strong>\s*([^%]+)/);
                            if (taxRateMatch && taxRateMatch[1]) {
                                taxRate = taxRateMatch[1].trim();
                            }
                            
                            // Create register card
                            const registerCard = document.createElement('div');
                            registerCard.className = 'col-md-6 col-lg-4 mb-3';
                            registerCard.innerHTML = `
                                <div class="card h-100 register-card" data-register-id="${tillId}">
                                    <div class="card-body">
                                        <h5 class="card-title">${location}</h5>
                                        <ul class="list-unstyled">
                                            <li><strong>Starting Float:</strong> $${startingFloat}</li>
                                            <li><strong>Running Total:</strong> $${runningTotal}</li>
                                            <li><strong>Tax Rate:</strong> ${taxRate}%</li>
                                        </ul>
                                        <button class="btn btn-primary w-100 select-register-btn">Select Register</button>
                                    </div>
                                </div>
                            `;
                            
                            // Add click event to select button
                            registerCard.querySelector('.select-register-btn').addEventListener('click', function() {
                                selectRegister(tillId);
                            });
                            
                            // Add click event to the whole card
                            registerCard.querySelector('.register-card').addEventListener('click', function(e) {
                                // Only trigger if the click wasn't on the button (which has its own handler)
                                if (!e.target.classList.contains('select-register-btn')) {
                                    selectRegister(tillId);
                                }
                            });
                            
                            // Highlight remembered register
                            if (tillId === rememberedRegisterId) {
                                registerCard.querySelector('.register-card').classList.add('border-primary');
                                registerCard.querySelector('.card-title').innerHTML += ' <span class="badge bg-primary">Preferred</span>';
                            }
                            
                            registerList.appendChild(registerCard);
                        });
                        
                        // If there's only one register and a remembered register, auto-select it
                        if (tills.length === 1 && rememberedRegisterId) {
                            const tillId = tills[0].querySelector('a.btn-success').href.split('/').pop();
                            selectRegister(tillId);
                            return;
                        }
                        
                        // If there's a remembered register, show a "Use preferred register" button
                        if (rememberedRegisterId) {
                            const preferredBtn = document.createElement('div');
                            preferredBtn.className = 'col-12 mb-3';
                            preferredBtn.innerHTML = `
                                <button class="btn btn-success w-100" id="usePreferredRegisterBtn">
                                    <i class="fas fa-star me-2"></i>Use Preferred Register
                                </button>
                            `;
                            preferredBtn.querySelector('#usePreferredRegisterBtn').addEventListener('click', function() {
                                selectRegister(rememberedRegisterId);
                            });
                            registerList.insertBefore(preferredBtn, registerList.firstChild);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching registers:', error);
                    loadingRegisters.classList.add('d-none');
                    noRegisters.classList.remove('d-none');
                    noRegisters.querySelector('.alert').textContent = 'Error loading registers. Please try again.';
                });
        }
        
        // Function to select a register
        function selectRegister(registerId) {
            // Remember register if checkbox is checked
            if (rememberRegisterCheckbox.checked) {
                localStorage.setItem('staff-preferred-register', registerId);
            } else {
                localStorage.removeItem('staff-preferred-register');
            }
            
            // Navigate to the POS start page with the selected register
            window.location.href = `/staff/pos/start/${registerId}`;
        }
        
            // Open register selection modal when POS button is clicked
            openPosBtn.addEventListener('click', function() {
                // If there's a remembered register and the checkbox is checked, go directly to that register
                if (rememberedRegisterId && rememberRegisterCheckbox.checked) {
                    selectRegister(rememberedRegisterId);
                    return;
                }
                
                // Otherwise, show the register selection modal
                registerModal.show();
                fetchRegisters();
            });
        }
    });
</script>
</body>
</html>
