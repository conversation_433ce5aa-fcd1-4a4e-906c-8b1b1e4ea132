#!/usr/bin/env python3
"""
Simplified Main Application

This script creates a minimal Flask application that connects to MongoDB Atlas
and provides basic functionality without registering all blueprints at once.
"""

import os
import logging
from flask import Flask, redirect, url_for, jsonify
from pymongo import MongoClient
from datetime import datetime
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("simplified_main")

def create_app():
    """Create and configure a minimal Flask application."""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Connect to MongoDB
    try:
        logger.info("Connecting to MongoDB Atlas...")
        client = MongoClient(Config.MONGO_URI, **Config.MONGO_CONNECTION_SETTINGS)
        
        # Force a connection to verify it works
        client.server_info()
        logger.info("MongoDB connection successful!")
        
        # Store the client in the app
        app.mongo_client = client
        
    except Exception as e:
        logger.error(f"MongoDB connection failed: {e}")
        # Continue with the app creation even if MongoDB connection fails
    
    # Add a health check endpoint
    @app.route('/health', methods=['GET'])
    def health_check():
        health_status = {
            "status": "ok",
            "mongodb_connected": hasattr(app, 'mongo_client'),
            "timestamp": datetime.now().isoformat()
        }
        return jsonify(health_status)
    
    # Add root route to redirect to health check
    @app.route('/', methods=['GET'])
    def index():
        return redirect(url_for('health_check'))
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=8000)
