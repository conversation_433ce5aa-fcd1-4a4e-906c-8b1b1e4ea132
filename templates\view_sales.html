{% extends "base.html" %}

{% block title %}View Sales for {{ month }}/{{ year }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="text-white">Sales for {{ month }}/{{ year }}</h1>
        <a href="{{ url_for('fbt.fbt_uk') }}" class="btn btn-primary">Back to FBT Dashboard</a>
    </div>
    <div class="alert alert-info mb-4">
        These are confirmed received orders and therefore the dates will be up to 7 days previous depending on delivery times.
    </div>
    <table class="table table-striped table-dark text-white">
        <thead>
            <tr>
                <th>Date of Purchase</th>
                <th>Article</th>
                <th>Expansion</th>
                <th>Amount</th>
                <th>Article Value (£)</th>
                <th>Total Value (£)</th>
                <th>Platform Fee (£)</th>
                <th>FBT Fee (£)</th>
                <th>Take Home (£)</th>
            </tr>
        </thead>
        <tbody>
            {% for record in sales_records %}
                <tr>
                    <td>{{ record['Date of purchase'] }}</td>
                    <td>{{ record['Localized Product Name'] }}</td>
                    <td>{{ record['Expansion'] }}</td>
                    <td>{{ record['Amount'] }}</td>
                    <td>£{{ "%.2f"|format(record['Article Value']) }}</td>
                    <td>£{{ "%.2f"|format(record['total_value']) }}</td>
                    <td>£{{ "%.2f"|format(record['platform_fee']) }}</td>
                    <td>£{{ "%.2f"|format(record['fbt_fee']) }}</td>
                    <td>£{{ "%.2f"|format(record['take_home']) }}</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
    <div class="mt-4">
        <a href="{{ url_for('fbt.fbt_uk') }}" class="btn btn-primary">Back to FBT Dashboard</a>
        <a href="{{ url_for('fbt.fbt_uk') }}" class="btn btn-secondary ml-2">Back to FBT UK</a>
    </div>
</div>
{% endblock %}
