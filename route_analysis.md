# Route Analysis Report

## Duplicate Endpoints Found

| Endpoint Path | Files Containing Duplicate |
|--------------|---------------------------|
| `/api/product-types` | shopify_products.py, shopify_routes.py, shopify_autopricing/advanced_rules.py |
| `/api/expansion-names` | shopify_products.py, shopify_routes.py |
| `/webhook` | shopify_webhooks.py, shopify_products.py |
| `/view_order/<order_id>` | buylist/routes.py, view_order_routes.py |
| `/api/staff` | new_staff_management.py, staff_management.py |
| `/api/staff/<string:staff_id>/set-password` | new_staff_management.py, staff_management.py |
| `/cardmarket/process-order/<order_id>` | Multiple variations in buylist/routes.py |

## Optimization Opportunities

### 1. Endpoint Consolidation
- Multiple files handle Shopify product types/expansions (shopify_products.py, shopify_routes.py, shopify_autopricing)
- Consider creating a dedicated `shopify_metadata.py` for all metadata endpoints

### 2. Standardize Path Patterns
- Inconsistent path formats for similar operations:
  - `/api/product/<product_id>` vs `/api/products/<product_id>`
  - `/api/staff` vs `/staff/api`

### 3. Duplicate Webhook Handlers
- Webhook endpoints exist in multiple files (shopify_webhooks.py, shopify_products.py)
- Could be centralized in shopify_webhooks.py

### 4. Deprecated Endpoints
- Found backup files with duplicate routes (shopify_webhooks.py.bak_*)
- Should be removed from codebase

### 5. Buylist Endpoint Variations
- Multiple similar paths for order processing:
  - `/process-order/<order_id>`
  - `/cardmarket/process-order/<order_id>`
  - `/cardmarket/buylist/process-order/<order_id>`

## Recommendations

1. **Create a Route Registry**:
   - Maintain a central documentation of all endpoints
   - Enforce naming conventions

2. **Consolidate Similar Functionality**:
   - Merge duplicate endpoints
   - Create shared utility modules for common operations

3. **Standardize Naming**:
   - Choose either singular or plural resource names (/product vs /products)
   - Consistent path parameter formats (<id> vs <product_id>)

4. **Cleanup Deprecated Code**:
   - Remove backup files from routes directory
   - Archive unused endpoints

5. **Implement Route Versioning**:
   - Add `/v1/` prefix to all API endpoints
   - Helps with future migrations
