{% if pushed_logs %}
<div class="table-responsive">
    <table class="table table-hover">
        <thead class="table-light">
            <tr>
                <th>Date</th>
                <th>Status</th>
                <th>Items</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for log in pushed_logs %}
            <tr data-bs-toggle="collapse" data-bs-target="#details{{ loop.index }}" class="accordion-toggle clickable {% if not log.shopify_results %}table-warning{% endif %}">
                <td>{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') if log.timestamp else 'No timestamp' }}</td>
                <td>
                    {% if log.shopify_results and log.shopify_results.status %}
                        <span class="badge {% if log.shopify_results.status == 'COMPLETED' %}bg-success{% elif log.shopify_results.status == 'FAILED' %}bg-danger{% else %}bg-info{% endif %}">
                            {{ log.shopify_results.status }}
                        </span>
                    {% else %}
                        <span class="badge bg-secondary">{{ log.status }}</span>
                    {% endif %}
                </td>
                <td>
                    {% if log.shopify_results and log.shopify_results.summary %}
                        <div class="d-flex align-items-center">
                            <span class="me-2 badge bg-primary">{{ log.shopify_results.summary.total_items }}</span>
                            {% if log.shopify_results.summary.successful_items %}
                                <span class="me-2 badge bg-success">{{ log.shopify_results.summary.successful_items }} ✓</span>
                            {% endif %}
                            {% if log.shopify_results.summary.failed_items %}
                                <span class="badge bg-danger">{{ log.shopify_results.summary.failed_items }} ✗</span>
                            {% endif %}
                        </div>
                    {% else %}
                        <span class="text-muted">No details</span>
                    {% endif %}
                </td>
                <td>
                    <div class="d-flex">
                        <form method="POST" action="{{ url_for('reprice_logs.refresh_shopify_results', log_id=log._id) }}">
                            <button type="submit" class="btn btn-sm btn-outline-primary" title="Refresh Results">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </form>
                        
                        <a href="{{ url_for('reprice_logs.download_results_csv', log_id=log._id) }}" class="btn btn-sm btn-outline-secondary ms-2" title="Download CSV">
                            <i class="fas fa-file-csv"></i> CSV
                        </a>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="4" class="p-0">
                    <div id="details{{ loop.index }}" class="collapse">
                        <div class="p-3 bg-light">
                            {% if log.shopify_results and log.shopify_results.line_items %}
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Operation</th>
                                                <th>Status</th>
                                                <th>Details</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in log.shopify_results.line_items %}
                                            <tr class="{% if not item.success %}table-danger{% endif %}">
                                                <td>{{ item.title }}</td>
                                                <td>
                                                    {% if item.operation_type %}
                                                        <span class="badge bg-info">{{ item.operation_type }}</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">Unknown</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if item.success %}
                                                        <span class="badge bg-success">Success</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">Failed</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#resultModal{{ log._id }}_{{ loop.index }}">
                                                        View Details
                                                    </button>
                                                    
                                                    <!-- Modal for item details -->
                                                    <div class="modal fade" id="resultModal{{ log._id }}_{{ loop.index }}" tabindex="-1" aria-labelledby="resultModalLabel{{ loop.index }}" aria-hidden="true">
                                                        <div class="modal-dialog modal-lg">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="resultModalLabel{{ loop.index }}">{{ item.title }}</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    {% if item.errors and item.errors|length > 0 %}
                                                                    <div class="alert alert-danger mb-3">
                                                                        <h6 class="alert-heading">Errors:</h6>
                                                                        <ul class="mb-0">
                                                                            {% for error in item.errors %}
                                                                                <li>{{ error.message if error.message else error }}</li>
                                                                            {% endfor %}
                                                                        </ul>
                                                                    </div>
                                                                    {% endif %}
                                                                    
                                                                    <h6>Raw Data:</h6>
                                                                    <pre class="bg-light p-3 rounded"><code>{{ item.raw_data|tojson(indent=2) }}</code></pre>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> No detailed results available. Click the refresh button to fetch the latest data.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="no-logs-message">
    <i class="fas fa-upload fa-3x mb-3 text-muted"></i>
    <h3>No pushed items logs found</h3>
    <p class="text-muted">There are no pushed items logs available for your account.</p>
</div>
{% endif %}

<script>
    // Initialize clickable rows for dynamically loaded content
    document.addEventListener('DOMContentLoaded', function() {
        initializeClickableRows(document.getElementById('pushedItemsData'));
    });
</script>
