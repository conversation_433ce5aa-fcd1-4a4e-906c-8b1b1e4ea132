{% extends "base.html" %}

{% block title %}View Uploads{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>Uploaded Files</h2>
    {% if files %}
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Filename</th>
                    <th>Size</th>
                    <th>Last Modified</th>
                </tr>
            </thead>
            <tbody>
                {% for file in files %}
                <tr>
                    <td>{{ file.name }}</td>
                    <td>{{ (file.size / 1024)|round(2) }} KB</td>
                    <td>{{ file.modified.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <p>No files uploaded yet.</p>
    {% endif %}
</div>
{% endblock %}