import os
import sys
import logging
from datetime import datetime
from pymongo import MongoClient
from routes.warehouse.shopify_product_creator import create_shopify_product

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('warehouse_catalog_to_pending.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

MONGO_URI = "*******************************************************************"
DB_NAME = "test"

def main():
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    catalog = db['catalog']
    shProducts = db['shProducts']
    users = db['user']
    pending_files = db['pendingFiles']

    # For each user with auto update enabled
    users_cursor = users.find({})
    for user in users_cursor:
        username = user.get('username')
        if not username:
            continue
        logger.info(f"Processing user: {username}")

        # Find catalog products NOT in shProducts for this user
        pipeline = [
            {"$match": {}},
            {"$lookup": {
                "from": "shProducts",
                "let": {"pid": "$productId"},
                "pipeline": [
                    {"$match": {
                        "$expr": {
                            "$and": [
                                {"$eq": ["$productId", "$$pid"]},
                                {"$eq": ["$username", username]}
                            ]
                        }
                    }}
                ],
                "as": "existing"
            }},
            {"$match": {"existing": {"$size": 0}}}
        ]

        unmatched = list(catalog.aggregate(pipeline))
        logger.info(f"Found {len(unmatched)} unmatched catalog products for user {username}")

        for doc in unmatched:
            skus = doc.get('skus', [])
            for sku in skus:
                try:
                    # Build Shopify product payload using warehouse method
                    product_data = create_shopify_product(doc, sku, user)

                    # Save to pendingFiles collection
                    pending_files.update_one(
                        {
                            "username": username,
                            "productId": doc.get('productId'),
                            "skuId": sku.get('skuId')
                        },
                        {
                            "$set": {
                                "username": username,
                                "productId": doc.get('productId'),
                                "skuId": sku.get('skuId'),
                                "payload": product_data,
                                "status": "pending",
                                "created_at": datetime.utcnow(),
                                "attempts": 0
                            }
                        },
                        upsert=True
                    )
                    logger.info(f"Saved pending file for user {username}, product {doc.get('productId')}, sku {sku.get('skuId')}")
                except Exception as e:
                    logger.error(f"Error creating pending file for user {username}, product {doc.get('productId')}, sku {sku.get('skuId')}: {str(e)}")

    client.close()
    logger.info("Completed processing all users.")

if __name__ == "__main__":
    main()
