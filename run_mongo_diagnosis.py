from config.config import Config
#!/usr/bin/env python3
"""
MongoDB Replica Set Diagnostic Runner

This script provides a user-friendly interface for running the MongoDB replica set
diagnostic tool and applying fixes if needed.

Usage:
    python run_mongo_diagnosis.py [--uri <PERSON>] [--dbname DBNAME]
"""

import os
import sys
import argparse
import subprocess
import json
from datetime import datetime

def print_header(text):
    """Print a formatted header."""
    print("\n" + "=" * 80)
    print(f"{text}")
    print("=" * 80)

def print_section(text):
    """Print a formatted section header."""
    print("\n" + "-" * 80)
    print(f"{text}")
    print("-" * 80)

def get_latest_diagnosis_file():
    """Get the most recent diagnosis result file."""
    diagnosis_files = [f for f in os.listdir('.') if f.startswith('mongo_diagnosis_') and f.endswith('.json')]
    if not diagnosis_files:
        return None
    
    # Sort by modification time (newest first)
    diagnosis_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    return diagnosis_files[0]

def parse_diagnosis_results(filename):
    """Parse the diagnosis results from a JSON file."""
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error parsing diagnosis results: {str(e)}")
        return None

def run_diagnostic(uri=None, dbname=None, fix=False, verbose=False):
    """Run the MongoDB replica set diagnostic tool."""
    cmd = ["python", "diagnose_mongo_replica.py"]
    
    if uri:
        cmd.extend(["--uri", uri])
    if dbname:
        cmd.extend(["--dbname", dbname])
    if fix:
        cmd.append("--fix")
    if verbose:
        cmd.append("--verbose")
    
    print_header("RUNNING MONGODB REPLICA SET DIAGNOSTICS")
    print(f"Command: {' '.join(cmd)}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\nThis may take a few moments...\n")
    
    # Run the diagnostic tool
    try:
        subprocess.run(cmd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running diagnostic tool: {str(e)}")
        return False
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return False

def display_common_fixes(results):
    """Display common fixes for identified issues."""
    if not results or "issues_found" not in results:
        return
    
    issues = results.get("issues_found", [])
    if not issues:
        return
    
    print_section("COMMON FIXES FOR IDENTIFIED ISSUES")
    
    # Group issues by type
    issue_types = {}
    for issue in issues:
        issue_type = issue.get("issue", "unknown")
        if issue_type not in issue_types:
            issue_types[issue_type] = []
        issue_types[issue_type].append(issue)
    
    # Display fixes for each issue type
    for issue_type, issues_of_type in issue_types.items():
        if issue_type == "no_primary":
            print("\n🔴 No Primary Node:")
            print("  1. Check if all replica set members are running")
            print("  2. Connect to one of the nodes with mongo shell:")
            print("     mongo *****************************:port/admin")
            print("  3. Check replica set status:")
            print("     rs.status()")
            print("  4. If needed, reconfigure the replica set:")
            print("     rs.reconfig(rs.conf())")
            print("  5. If still no primary, force a new election:")
            print("     rs.stepDown()")
            
        elif issue_type == "replication_lag":
            print("\n🟠 Replication Lag:")
            print("  1. Check network connectivity between nodes")
            print("  2. Verify server resources (CPU, memory, disk I/O)")
            print("  3. Consider reducing write load during peak times")
            print("  4. Check for long-running operations on secondary nodes:")
            print("     db.currentOp()")
            
        elif "index" in issue_type:
            print("\n🟡 Index Issues:")
            print("  1. Run the diagnostic tool with --fix option to rebuild indexes")
            print("  2. Or manually rebuild indexes for affected collections:")
            print("     db.collection.dropIndexes()")
            print("     db.collection.createIndex({field: 1}, {unique: true})")
            
        elif "connection" in issue_type:
            print("\n🔵 Connection Issues:")
            print("  1. Verify all hosts in the connection string are reachable")
            print("  2. Check firewall settings for MongoDB ports (default: 27017)")
            print("  3. Ensure authentication credentials are correct")
            print("  4. Update connection parameters in your application:")
            print("     - Increase serverSelectionTimeoutMS")
            print("     - Increase connectTimeoutMS")
            print("     - Use retryWrites and retryReads")
            
        elif "authentication" in issue_type:
            print("\n🟣 Authentication Issues:")
            print("  1. Verify user credentials are correct")
            print("  2. Check that the user exists in the admin database")
            print("  3. Ensure the user has appropriate roles")
            print("  4. If needed, create a new user:")
            print("     db.createUser({user: 'username', pwd: 'password', roles: ['readWrite']})")

def main():
    """Main function to parse arguments and run the diagnostic tool."""
    parser = argparse.ArgumentParser(description='MongoDB Replica Set Diagnostic Runner')
    parser.add_argument('--uri', help='MongoDB connection URI')
    parser.add_argument('--dbname', help='Database name')
    parser.add_argument('--fix', action='store_true', help='Attempt to fix identified issues')
    parser.add_argument('--verbose', action='store_true', help='Show detailed output')
    args = parser.parse_args()
    
    # Run the diagnostic tool
    success = run_diagnostic(args.uri, args.dbname, args.fix, args.verbose)
    
    if not success:
        print("\nDiagnostic tool failed to run. Please check the error messages above.")
        sys.exit(1)
    
    # Get the latest diagnosis results
    latest_file = get_latest_diagnosis_file()
    if not latest_file:
        print("\nNo diagnosis results found.")
        sys.exit(1)
    
    # Parse the results
    results = parse_diagnosis_results(latest_file)
    if not results:
        print("\nFailed to parse diagnosis results.")
        sys.exit(1)
    
    # Display common fixes
    display_common_fixes(results)
    
    # Offer to run with --fix if issues were found and --fix wasn't already used
    if not args.fix and results.get("issues_found"):
        print_section("APPLY FIXES")
        print("\nIssues were found with your MongoDB replica set configuration.")
        print("Would you like to run the diagnostic tool with the --fix option to attempt to fix these issues?")
        print("WARNING: This will modify your MongoDB configuration. Make sure you have a backup.")
        
        response = input("\nRun with --fix? (y/n): ").strip().lower()
        if response == 'y':
            print("\nRunning diagnostic tool with --fix option...")
            run_diagnostic(args.uri, args.dbname, fix=True, verbose=args.verbose)
    
    print_section("NEXT STEPS")
    print("\n1. Review the detailed diagnosis results in the JSON file")
    print(f"   {latest_file}")
    print("\n2. Update your application to use the mongo_connection_manager.py module")
    print("   See make_admin_updated.py for an example")
    print("\n3. Implement the recommendations from the MONGODB_REPLICA_SET_GUIDE.md file")
    print("\n4. Run this diagnostic tool regularly to monitor your replica set health")

if __name__ == "__main__":
    main()

