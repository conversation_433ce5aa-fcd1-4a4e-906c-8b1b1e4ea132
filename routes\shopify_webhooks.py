import logging
from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
import re
from bson import ObjectId
from config import Config
import threading
import queue
# Removed unused imports
import time
from decimal import Decimal
from concurrent.futures import ThreadPoolExecutor
from email_sender import send_restock_alert, send_out_of_stock_alert, send_cardmarket_check_email
import requests

# Create Blueprint
shopify_webhook_bp = Blueprint('shopify_webhook', __name__)

# Configure logging with both file and console handlers
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('shopify_webhook.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration with single optimized client
mongo_client = Config.get_mongo_client()
db = mongo_client[Config.MONGO_DBNAME]

# Note: Connection pooling is handled by Config.get_mongo_client()

# Use a single db reference and access collections when needed
# This avoids keeping multiple collection references in memory
shopify_webhooks_collection = db['shopifyWebhooks']
catalog_collection = db['catalog']

def get_catalog_details(product_id):
    """Get catalog details for a product ID"""
    try:
        if not product_id:
            return None
            
        catalog_record = db['catalog'].find_one(
            {"productId": product_id},
            {
                "_id": 0,
                "abbreviation": 1,
                "expansionName": 1,
                "rarity": 1,
                "number": 1,
                "groupId": 1,
                "gameName": 1,
                "categoryId": 1
            }
        )
        return catalog_record
    except Exception as e:
        logger.error(f"Error getting catalog details for product {product_id}: {str(e)}")
        return None

class MonitoredQueue(queue.Queue):
    def __init__(self, maxsize=10000):  # Increased queue size to handle more webhooks
        super().__init__(maxsize)
        self.processed_count = 0
        self.error_count = 0
        self.start_time = time.time()
        self.processing_times = []
        self.last_processed_time = None
        self.thread_pool = ThreadPoolExecutor(max_workers=4)  # Reduced to prevent server overload
        self.rate_limiter = time.time()
        self.batch_size = 10  # Process webhooks in batches
        self.priority_topics = ['products/create', 'products/update', 'inventory_levels/update']  # High priority topics

    def get_metrics(self):
        uptime = time.time() - self.start_time
        avg_processing_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
        return {
            "queue_size": self.qsize(),
            "processed_count": self.processed_count,
            "error_count": self.error_count,
            "uptime_seconds": uptime,
            "average_processing_time": avg_processing_time,
            "last_processed_time": self.last_processed_time,
            "active_threads": len(self.thread_pool._threads)
        }

webhook_queue = MonitoredQueue()

def create_webhook_record(username, topic, data, webhook_id=None):
    """Create a new webhook record in the tracking collection"""
    webhook_record = {
        "username": username,
        "topic": topic,
        "status": "pending",
        "createdAt": datetime.utcnow(),
        "updatedAt": datetime.utcnow(),
        "data": data,
        "attempts": 0,
        "errors": [],
        "webhookId": webhook_id or str(ObjectId())
    }
    
    try:
        result = shopify_webhooks_collection.insert_one(webhook_record)
        logger.info(f"Created webhook record: {result.inserted_id}")
        return str(result.inserted_id)
    except Exception as e:
        logger.error(f"Error creating webhook record: {str(e)}")
        return None

def update_webhook_status(webhook_id, status, error=None):
    """Update the status of a webhook record"""
    try:
        # If completed, delete the webhook record to save space
        if status == "completed":
            result = shopify_webhooks_collection.delete_one({"_id": ObjectId(webhook_id)})
            logger.info(f"Deleted completed webhook {webhook_id}")
            return result.deleted_count > 0
        
        # Otherwise update the status
        update_doc = {
            "status": status,
            "updatedAt": datetime.utcnow()
        }
        
        # Use $inc operator in a separate update field
        inc_doc = {
            "attempts": 1
        }
        
        if error:
            update_doc["$push"] = {
                "errors": {
                    "message": str(error),
                    "timestamp": datetime.utcnow()
                }
            }
        
        result = shopify_webhooks_collection.update_one(
            {"_id": ObjectId(webhook_id)},
            {
                "$set": update_doc,
                "$inc": inc_doc
            }
        )
        
        logger.info(f"Updated webhook {webhook_id} status to {status}")
        return result.modified_count > 0
    except Exception as e:
        logger.error(f"Error updating webhook status: {str(e)}")
        return False


def log_action(username, action, success, details=None, changes=None):
    """Log actions to the MongoDB logs collection"""
    try:
        log_entry = {
            "username": username,
            "action": action,
            "success": success,
            "timestamp": datetime.utcnow(),
            "details": details,
            "changes": changes
        }
        db['logs'].insert_one(log_entry)
        logger.info(f"Logged action: {action} for user {username} - Success: {success}")
    except Exception as e:
        logger.error(f"Failed to log action: {str(e)}")


def update_product_metadata(shopify_id, product_id, username):
    """Update basic product metadata"""
    try:
        # Handle non-TCG items
        if not product_id:
            logger.info(f"Non-TCG item detected for {shopify_id}")
            update_fields = {
                "needsMatching": False,
                "tcgItem": False,
                "lastUpdated": datetime.utcnow()
            }
            result = db['shProducts'].update_one(
                {"id": shopify_id, "username": username},
                {"$set": update_fields}
            )
            log_action(username, "update_product_metadata", True, 
                      f"Updated metadata for non-TCG item {shopify_id}")
            return True

        # For TCG items, just mark as TCG item
        update_fields = {
            "needsMatching": False,
            "tcgItem": True,
            "productId": product_id,
            "lastUpdated": datetime.utcnow()
        }
        
        result = db['shProducts'].update_one(
            {"id": shopify_id, "username": username},
            {"$set": update_fields}
        )
        
        if result.modified_count > 0:
            logger.info(f"Successfully updated product metadata for {shopify_id}")
            return True
        else:
            logger.warning(f"No changes made for product {shopify_id}")
            return False

    except Exception as e:
        logger.error(f"Error updating product metadata for {shopify_id}: {str(e)}")
        log_action(username, "update_product_metadata", False, f"Error: {str(e)}")
        return False

def process_webhook_task(webhook_data):
    """Process a single webhook in a thread pool worker"""
    start_time = time.time()
    webhook_id = webhook_data.get('webhook_id')
    
    try:
        topic = webhook_data['topic']
        username = webhook_data['username']
        data = webhook_data['data']

        logger.info(f"Processing {topic} webhook for {username}")

        if topic in ['products/create', 'products/update']:
            update_product(username, data)
        elif topic == 'products/delete':
            delete_product(username, data)
        elif topic in ['orders/create', 'orders/updated']:
            update_order(username, data)
        elif topic in ['customers/create', 'customers/update']:
            update_customer(username, data)
        elif topic == 'inventory_levels/update':
            update_inventory_level(username, data)
        else:
            logger.warning(f"Unhandled webhook topic: {topic}")

        # Update webhook status to completed
        update_webhook_status(webhook_id, "completed")

        # Update metrics
        processing_time = time.time() - start_time
        webhook_queue.processing_times.append(processing_time)
        webhook_queue.processed_count += 1
        webhook_queue.last_processed_time = datetime.utcnow().isoformat()

        # Log metrics periodically
        if webhook_queue.processed_count % 10 == 0:
            metrics = webhook_queue.get_metrics()
            logger.info(f"Queue metrics: {metrics}")

    except Exception as e:
        webhook_queue.error_count += 1
        logger.error(f"Error processing webhook data: {str(e)}")
        update_webhook_status(webhook_id, "error", error=e)
        log_action(username, f"process_{topic}", False, f"Error: {str(e)}")

def process_webhook_batch(webhook_batch):
    """Process a batch of webhooks efficiently"""
    try:
        for webhook_data in webhook_batch:
            try:
                process_webhook_task(webhook_data)
            except Exception as e:
                logger.error(f"Error processing webhook in batch: {str(e)}")
                webhook_queue.error_count += 1
    except Exception as e:
        logger.error(f"Error processing webhook batch: {str(e)}")

def process_webhook_queue():
    """Process webhooks from the queue using optimized batching and rate limiting"""
    batch = []
    last_process_time = time.time()
    
    while True:
        try:
            # Rate limiting - ensure we don't overwhelm the server
            current_time = time.time()
            if current_time - webhook_queue.rate_limiter < 0.05:  # 50ms minimum between operations
                time.sleep(0.05)
                webhook_queue.rate_limiter = time.time()
            
            # Try to get webhook data with timeout to allow batching
            try:
                webhook_data = webhook_queue.get(timeout=0.1)
                
                # Check if this is a priority webhook
                topic = webhook_data.get('topic', '')
                if topic in webhook_queue.priority_topics:
                    # Process priority webhooks immediately
                    webhook_queue.thread_pool.submit(process_webhook_task, webhook_data)
                    webhook_queue.task_done()
                else:
                    # Add to batch for non-priority webhooks
                    batch.append(webhook_data)
                    webhook_queue.task_done()
                
                # Process batch when it reaches batch_size or after timeout
                if (len(batch) >= webhook_queue.batch_size or 
                    (batch and current_time - last_process_time > 1.0)):  # 1 second timeout
                    
                    # Submit batch for processing
                    webhook_queue.thread_pool.submit(process_webhook_batch, batch.copy())
                    batch.clear()
                    last_process_time = current_time
                    
            except queue.Empty:
                # Process any remaining batch items after timeout
                if batch:
                    webhook_queue.thread_pool.submit(process_webhook_batch, batch.copy())
                    batch.clear()
                    last_process_time = current_time
                
                # Brief sleep when queue is empty
                time.sleep(0.1)
                continue
            
        except Exception as e:
            logger.error(f"Error in webhook queue processing: {str(e)}")
            webhook_queue.error_count += 1
            time.sleep(0.5)
            continue

# Further reduced number of processing threads
NUM_PROCESSING_THREADS = 8
for _ in range(NUM_PROCESSING_THREADS):
    webhook_thread = threading.Thread(target=process_webhook_queue, daemon=True)
    webhook_thread.start()

@shopify_webhook_bp.route('/shopify/webhook/status', methods=['GET'])
@shopify_webhook_bp.route('/cardmarket/shopify/webhook/status', methods=['GET'])
def get_queue_status():
    """Get current status of the webhook processing queue"""
    try:
        metrics = webhook_queue.get_metrics()
        return jsonify({
            "status": "healthy",
            "metrics": metrics
        }), 200
    except Exception as e:
        logger.error(f"Error getting queue status: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500

@shopify_webhook_bp.route('/webhook', methods=['POST'])
def shopify_webhook():
    """Handle incoming Shopify webhooks with circuit breaker and prioritization"""
    try:
        topic = request.headers.get('X-Shopify-Topic')
        shop_domain = request.headers.get('X-Shopify-Shop-Domain')
        username = request.args.get('username')
        
        logger.info(f"Received webhook: {topic} from {shop_domain}")
        
        # Circuit breaker - check if queue is too full
        current_queue_size = webhook_queue.qsize()
        if current_queue_size > 8000:  # 80% of max queue size
            logger.warning(f"Queue nearly full ({current_queue_size}/10000), implementing backpressure")
            # Only accept high priority webhooks when queue is nearly full
            if topic not in webhook_queue.priority_topics:
                logger.warning(f"Dropping non-priority webhook {topic} due to queue pressure")
                return '', 200  # Still acknowledge receipt
        
        if username:
            user = db['user'].find_one({'username': username})
            logger.info(f"Found user by username: {username}")
        else:
            user = db['user'].find_one({'shopifyStoreName': shop_domain})
            logger.info(f"Found user by shop domain: {shop_domain}")
        
        if not user:
            logger.error(f"No user found for Shopify store: {shop_domain} or username: {username}")
            return '', 200  # Still return 200 to acknowledge receipt

        username = user['username']
        data = request.json

        # Create webhook record (only for priority topics when queue is full)
        if current_queue_size > 8000 and topic not in webhook_queue.priority_topics:
            webhook_id = None  # Don't create record for dropped webhooks
        else:
            webhook_id = create_webhook_record(username, topic, data)
        
        # Add the webhook data to the queue with timeout to prevent blocking
        try:
            webhook_queue.put({
                'topic': topic,
                'username': username,
                'data': data,
                'webhook_id': webhook_id,
                'priority': topic in webhook_queue.priority_topics
            }, timeout=1.0)  # 1 second timeout
            
            logger.info(f"Added webhook to queue: {webhook_id}")
            return '', 200
            
        except queue.Full:
            logger.error(f"Queue full, dropping webhook {topic} for {username}")
            return '', 200  # Still acknowledge receipt

    except Exception as e:
        logger.error(f"Error processing webhook request: {str(e)}")
        return '', 200  # Still return 200 to acknowledge receipt

@shopify_webhook_bp.route('/webhook/status/<webhook_id>', methods=['GET'])
def get_webhook_status(webhook_id):
    """Get the status of a specific webhook"""
    try:
        webhook = shopify_webhooks_collection.find_one({"_id": ObjectId(webhook_id)})
        if webhook:
            return jsonify({
                "status": webhook.get("status"),
                "createdAt": webhook.get("createdAt"),
                "updatedAt": webhook.get("updatedAt"),
                "attempts": webhook.get("attempts"),
                "errors": webhook.get("errors")
            }), 200
        return jsonify({"error": "Webhook not found"}), 404
    except Exception as e:
        logger.error(f"Error getting webhook status: {str(e)}")
        return jsonify({"error": str(e)}), 500

def update_product(username, data):
    """Handle product creation and updates"""
    try:
        shopify_id = data.get('id')
        
        if not shopify_id:
            log_action(username, "update_product", False, "No Shopify ID found")
            return
        
        logger.info(f"Processing product update/create for shopify_id: {shopify_id}, username: {username}")
        
        # Extract the productId from the body_html
        html_content = data.get('body_html', '')
        product_id = None
        is_tcg_item = False
        
        try:
            # Look for the catalogMetaData div and extract productId
            if 'catalogMetaData' in html_content:
                match = re.search(r'data-tcgid=["\'](\d+)["\']', html_content)
                if match:
                    product_id = int(match.group(1))
                    is_tcg_item = True
                    logger.info(f"Successfully extracted productId: {product_id} from Shopify product {shopify_id}")
                else:
                    logger.warning(f"No tcgid found in catalogMetaData for Shopify product {shopify_id}")
            else:
                logger.warning(f"No catalogMetaData div found in body_html for Shopify product {shopify_id}")
                
        except (ValueError, AttributeError) as e:
            logger.error(f"Error extracting productId for Shopify product {shopify_id}: {str(e)}")
            log_action(username, "update_product", False, f"Error extracting productId: {str(e)}")

        # Prepare the product document
        product_doc = {
            "username": username,
            "id": shopify_id,
            "admin_graphql_api_id": str(data.get('admin_graphql_api_id')),
            "productId": product_id,
            "needsMatching": is_tcg_item and product_id is None,
            "tcgItem": is_tcg_item,
            "lastUpdated": datetime.utcnow().isoformat()
        }
        
        # Add all fields from the Shopify data
        product_doc.update(data)
        
        # Check if product already exists
        existing_product = db['shProducts'].find_one({'username': username, 'id': shopify_id})
        changes = {}
        
        # If product doesn't exist and we have a valid product ID, get catalog details
        if not existing_product and product_id:
            catalog_details = get_catalog_details(product_id)
            if catalog_details:
                logger.info(f"Found catalog details for new product {product_id}")
                product_doc.update({
                    "abbreviation": catalog_details.get("abbreviation"),
                    "expansionName": catalog_details.get("expansionName"),
                    "rarity": catalog_details.get("rarity"),
                    "number": catalog_details.get("number"),
                    "groupId": catalog_details.get("groupId"),
                    "gameName": catalog_details.get("gameName"),
                    "categoryId": catalog_details.get("categoryId")
                })
            else:
                logger.warning(f"No catalog details found for new product {product_id}")
        
        # Update basic product metadata
        if product_id or not is_tcg_item:
            update_product_metadata(shopify_id, product_id, username)
        
        if existing_product:
            logger.info(f"Found existing product {shopify_id} for user {username}")
            if existing_product.get('title') != data.get('title'):
                changes['title'] = {'old': existing_product.get('title'), 'new': data.get('title')}
            if existing_product.get('status') != data.get('status'):
                changes['status'] = {'old': existing_product.get('status'), 'new': data.get('status')}
            if existing_product.get('productId') != product_id:
                changes['productId'] = {'old': existing_product.get('productId'), 'new': product_id}
            
            old_inventory = existing_product.get('variants', [{}])[0].get('inventory_quantity')
            new_inventory = data.get('variants', [{}])[0].get('inventory_quantity')
            if old_inventory != new_inventory:
                changes['inventory_quantity'] = {'old': old_inventory, 'new': new_inventory}
        else:
            logger.info(f"No existing product found for {shopify_id}, will create new record")
            
        # Update or insert the product document
        result = db['shProducts'].update_one(
            {'username': username, 'id': shopify_id},
            {'$set': product_doc},
            upsert=True
        )
        
        logger.info(f"Update result - matched: {result.matched_count}, "
                   f"modified: {result.modified_count}, "
                   f"upserted_id: {result.upserted_id}")
        
        if result.upserted_id:
            log_action(username, "create_product", True, 
                      f"Created new product with Shopify ID {shopify_id} and productId {product_id}", 
                      changes)
        else:
            log_action(username, "update_product", True, 
                      f"Updated existing product with Shopify ID {shopify_id} and productId {product_id}", 
                      changes)
            
    except Exception as e:
        logger.error(f"Error in update_product: {str(e)}")
        log_action(username, "update_product", False, f"Error: {str(e)}")

def update_inventory_level(username, data):
    """Handle inventory level updates"""
    try:
        logger.info(f"Received inventory update webhook data: {data}")
        
        inventory_item_id = str(data.get('inventory_item_id'))
        new_quantity = int(data.get('available', 0))
        
        logger.info(f"Updating inventory for item {inventory_item_id}, new quantity: {new_quantity}")
        
        if not inventory_item_id:
            logger.error(f"Invalid webhook data: inventory_item_id missing")
            log_action(username, "update_inventory", False, 
                      f"Invalid webhook data: inventory_item_id missing")
            return

        # Find the product with this inventory item
        query = {
            'username': username,
            'variants': {
                '$elemMatch': {
                    'inventory_item_id': inventory_item_id
                }
            }
        }
        
        product = db['shProducts'].find_one(query)
        if product:
            logger.info(f"Found product: {product.get('title')} for inventory update")
            # Get the variant that matches the inventory_item_id
            variant = next((v for v in product['variants'] 
                          if v['inventory_item_id'] == inventory_item_id), None)
            
            if variant:
                old_quantity = int(variant.get('inventory_quantity', 0))
                logger.info(f"Old quantity: {old_quantity}, New quantity: {new_quantity}")
                
                # Check if inventory has transitioned from positive to zero
                if old_quantity > 0 and new_quantity == 0:
                    logger.info(f"Inventory transitioned to 0 for {product.get('title')}, sending out-of-stock alert email")
                    # Prepare product data for email
                    product_data = {
                        'title': product.get('title'),
                        'sku': variant.get('sku'),
                        'variant_title': variant.get('title'),
                        'old_quantity': old_quantity,
                        'new_quantity': new_quantity,
                        'store_name': username,
                        'username': username,
                        'vendor': product.get('vendor'),
                        'product_type': product.get('product_type'),
                        'tags': product.get('tags'),
                        'image_src': product.get('image', {}).get('src', '')
                    }
                    
                    # Send out-of-stock alert email
                    try:
                        logger.info(f"Attempting to send out-of-stock alert email for {product.get('title')}")
                        email_sent = send_out_of_stock_alert(product_data)
                        if email_sent:
                            logger.info(f"Successfully sent out-of-stock alert email for {product.get('title')}")
                        else:
                            logger.error(f"Failed to send out-of-stock alert email for {product.get('title')}")
                    except Exception as e:
                        logger.error(f"Error sending out-of-stock alert email: {str(e)}")
                else:
                    logger.info(f"Inventory not transitioned to zero, no out-of-stock alert sent")

                # Update the inventory quantity
                update = {
                    '$set': {
                        'variants.$.inventory_quantity': new_quantity,
                        'variants.$.old_inventory_quantity': old_quantity,
                        'lastUpdated': datetime.utcnow().isoformat()
                    }
                }
                
                result = db['shProducts'].update_one(query, update)
                
                changes = {'inventory_quantity': {'old': old_quantity, 'new': new_quantity}}
                
                if result.modified_count > 0:
                    logger.info(f"Successfully updated inventory for item {inventory_item_id}")
                    log_action(username, "update_inventory", True, 
                             f"Updated inventory for item {inventory_item_id}", changes)
                else:
                    logger.warning(f"No inventory update performed for item {inventory_item_id}")
                    log_action(username, "update_inventory", False, 
                             f"No inventory update performed for item {inventory_item_id}", changes)
            else:
                logger.warning(f"No matching variant found for inventory_item_id: {inventory_item_id}")
        else:
            logger.warning(f"No product found for inventory update with inventory_item_id: {inventory_item_id}")
            
    except Exception as e:
        logger.error(f"Error in update_inventory_level: {str(e)}")
        log_action(username, "update_inventory", False, f"Error: {str(e)}")

def update_order(username, data):
    """Handle order creation and updates"""
    try:
        order_id = str(data['id'])
        
        # Check if user has Cardmarket integration
        user = db['user'].find_one({'username': username})
        if user and user.get('cardmarketUsername'):
            # Send email to admin about checking Cardmarket listings
            send_cardmarket_check_email(username, data)
        
        # Prepare the order document
        order_doc = {
            "username": username,
            "id": order_id,
            "lastUpdated": datetime.utcnow().isoformat()
        }
        
        # Add all fields from the Shopify data
        order_doc.update(data)
        
        # Update or insert the order document
        result = db['shOrders'].update_one(
            {'username': username, 'id': order_id},
            {'$set': order_doc},
            upsert=True
        )
        
        if result.upserted_id:
            logger.info(f"Created new order {order_id} for user {username}")
            log_action(username, "create_order", True, 
                      f"Created new order {order_id}")
        else:
            logger.info(f"Updated existing order {order_id} for user {username}")
            log_action(username, "update_order", True, 
                      f"Updated existing order {order_id}")
            
    except Exception as e:
        logger.error(f"Error in update_order: {str(e)}")
        log_action(username, "update_order", False, f"Error: {str(e)}")

def update_customer(username, data):
    """Handle customer creation and updates"""
    try:
        customer_id = str(data['id'])
        
        # Prepare the customer document
        customer_doc = {
            "username": username,
            "id": customer_id,
            "lastUpdated": datetime.utcnow().isoformat()
        }
        
        # Add all fields from the Shopify data
        customer_doc.update(data)
        
        # Update or insert the customer document
        result = db['shCustomers'].update_one(
            {'username': username, 'id': customer_id},
            {'$set': customer_doc},
            upsert=True
        )
        
        if result.upserted_id:
            logger.info(f"Created new customer {customer_id} for user {username}")
            log_action(username, "create_customer", True, 
                      f"Created new customer {customer_id}")
        else:
            logger.info(f"Updated existing customer {customer_id} for user {username}")
            log_action(username, "update_customer", True, 
                      f"Updated existing customer {customer_id}")
            
    except Exception as e:
        logger.error(f"Error in update_customer: {str(e)}")
        log_action(username, "update_customer", False, f"Error: {str(e)}")

def delete_product(username, data):
    """Handle product deletions"""
    try:
        shopify_id = data.get('id')
        
        if not shopify_id:
            log_action(username, "delete_product", False, 
                      f"No Shopify ID found for product deletion")
            return
        
        # First, try to find the product
        product = db['shProducts'].find_one({
            'username': username,
            'id': shopify_id
        })
        
        if product:
            result = db['shProducts'].delete_one({
                'username': username,
                'id': shopify_id
            })
            
            if result.deleted_count > 0:
                log_action(username, "delete_product", True, 
                          f"Successfully deleted product with Shopify ID {shopify_id}", 
                          {"deleted_product": product})
            else:
                log_action(username, "delete_product", False, 
                          f"Failed to delete product with Shopify ID {shopify_id}")
        else:
            log_action(username, "delete_product", False, 
                      f"No product found to delete with Shopify ID {shopify_id}")
        
        # Log the entire data payload for debugging
        log_action(username, "delete_product_webhook", True, 
                  f"Received deletion webhook", 
                  {"webhook_data": data})
            
    except Exception as e:
        logger.error(f"Error in delete_product: {str(e)}")
        log_action(username, "delete_product", False, f"Error: {str(e)}")

# Register error handlers
# Register error handlers
@shopify_webhook_bp.errorhandler(Exception)
def handle_error(error):
    logger.error(f"Unhandled error in webhook: {str(error)}")
    return '', 200  # Always return 200 to acknowledge receipt by Shopify

# Additional utility endpoints for webhook management

@shopify_webhook_bp.route('/webhook/retry/<webhook_id>', methods=['POST'])
def retry_webhook(webhook_id):
    """Retry processing a failed webhook"""
    try:
        webhook = db['shopifyWebhooks'].find_one({"_id": ObjectId(webhook_id)})
        if not webhook:
            return jsonify({"error": "Webhook not found"}), 404

        if webhook['status'] != 'error':
            return jsonify({"error": "Can only retry failed webhooks"}), 400

        # Reset status and add to queue
        update_webhook_status(webhook_id, "pending")
        
        webhook_queue.put({
            'topic': webhook['topic'],
            'username': webhook['username'],
            'data': webhook['data'],
            'webhook_id': str(webhook['_id'])
        })

        return jsonify({
            "message": "Webhook queued for retry",
            "webhook_id": webhook_id
        }), 200

    except Exception as e:
        logger.error(f"Error retrying webhook: {str(e)}")
        return jsonify({"error": str(e)}), 500

@shopify_webhook_bp.route('/webhook/clean-old', methods=['POST'])
def clean_old_webhooks():
    """Clean up old completed webhooks"""
    try:
        # Keep only last 7 days of error webhooks
        cutoff_date = datetime.utcnow() - timedelta(days=7)
        
        result = db['shopifyWebhooks'].delete_many({
            "status": "error",
            "createdAt": {"$lt": cutoff_date}
        })

        return jsonify({
            "message": "Cleaned up old webhooks",
            "deleted_count": result.deleted_count
        }), 200

    except Exception as e:
        logger.error(f"Error cleaning old webhooks: {str(e)}")
        return jsonify({"error": str(e)}), 500

@shopify_webhook_bp.route('/webhook/stats', methods=['GET'])
def get_webhook_stats():
    """Get statistics about webhook processing"""
    try:
        # Get counts by status
        pipeline = [
            {"$group": {
                "_id": "$status",
                "count": {"$sum": 1},
                "average_processing_time": {
                    "$avg": {
                        "$subtract": ["$updatedAt", "$createdAt"]
                    }
                }
            }}
        ]
        
        stats = list(db['shopifyWebhooks'].aggregate(pipeline))
        
        # Get recent error count
        recent_errors = db['shopifyWebhooks'].count_documents({
            "status": "error",
            "createdAt": {
                "$gte": datetime.utcnow() - timedelta(hours=1)
            }
        })

        return jsonify({
            "status_counts": {stat["_id"]: stat["count"] for stat in stats},
            "average_processing_times": {
                stat["_id"]: stat["average_processing_time"] for stat in stats
            },
            "recent_error_count": recent_errors,
            "queue_metrics": webhook_queue.get_metrics()
        }), 200

    except Exception as e:
        logger.error(f"Error getting webhook stats: {str(e)}")
        return jsonify({"error": str(e)}), 500

@shopify_webhook_bp.route('/webhook/list', methods=['GET'])
def list_webhooks():
    """List recent webhooks with filtering options"""
    try:
        # Parse query parameters
        status = request.args.get('status')
        username = request.args.get('username')
        topic = request.args.get('topic')
        limit = int(request.args.get('limit', 50))  # Reduced default limit
        skip = int(request.args.get('skip', 0))
        
        # Build query
        query = {}
        if status:
            query['status'] = status
        if username:
            query['username'] = username
        if topic:
            query['topic'] = topic
            
        # Get webhooks
        webhooks = list(db['shopifyWebhooks'].find(
            query,
            {'data': 0}  # Exclude webhook data for performance
        ).sort('createdAt', -1).skip(skip).limit(limit))
        
        # Convert ObjectId to string
        for webhook in webhooks:
            webhook['_id'] = str(webhook['_id'])
            
        return jsonify({
            "webhooks": webhooks,
            "total": db['shopifyWebhooks'].count_documents(query),
            "limit": limit,
            "skip": skip
        }), 200

    except Exception as e:
        logger.error(f"Error listing webhooks: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Periodic retry task for failed webhooks
def retry_failed_webhooks():
    """Periodically retry failed webhooks up to 3 times"""
    while True:
        try:
            # Find failed webhooks with less than 3 attempts
            failed_webhooks = list(db['shopifyWebhooks'].find({
                "status": "error",
                "attempts": {"$lt": 3}
            }).limit(50))  # Process in batches to avoid memory issues
            
            retry_count = 0
            for webhook in failed_webhooks:
                webhook_id = str(webhook['_id'])
                
                # Reset status to pending
                update_webhook_status(webhook_id, "pending")
                
                # Add to queue for processing
                webhook_queue.put({
                    'topic': webhook['topic'],
                    'username': webhook['username'],
                    'data': webhook['data'],
                    'webhook_id': webhook_id
                })
                retry_count += 1
                
                # Brief pause between adding to queue
                time.sleep(0.1)
            
            if retry_count > 0:
                logger.info(f"Requeued {retry_count} failed webhooks for retry")
            
            # Sleep for 5 minutes before next retry check
            time.sleep(300)
            
        except Exception as e:
            logger.error(f"Error in retry task: {str(e)}")
            time.sleep(60)  # Sleep for a minute on error

# Periodic cleanup task
def cleanup_old_webhooks():
    """Periodically clean up old webhooks"""
    while True:
        try:
            # Clean up error webhooks older than 7 days
            cutoff_date = datetime.utcnow() - timedelta(days=7)
            result = db['shopifyWebhooks'].delete_many({
                "status": "error",
                "createdAt": {"$lt": cutoff_date}
            })
            
            logger.info(f"Cleaned up {result.deleted_count} old error webhooks")
            
            # Sleep for 24 hours
            time.sleep(86400)
            
        except Exception as e:
            logger.error(f"Error in cleanup task: {str(e)}")
            time.sleep(3600)  # Sleep for an hour on error

# Start the retry thread
retry_thread = threading.Thread(target=retry_failed_webhooks, daemon=True)
retry_thread.start()
