{% extends "base.html" %}

{% block title %}Events Dashboard{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/events.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid mt-4 event-page-content">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">Events Dashboard</h1>
                <div>
                    <a href="{{ url_for('events.events_calendar') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-calendar-alt"></i> Calendar View
                    </a>
                    <a href="{{ url_for('events.create_event') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Event
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Upcoming Events -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Upcoming Events</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_events %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Date</th>
                                        <th>Game</th>
                                        <th>Tickets</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for event in upcoming_events %}
                                    <tr>
                                        <td>
                                            <a href="{{ url_for('events.event_details', event_id=event.id) }}">
                                                {{ event.title }}
                                            </a>
                                        </td>
                                        <td>{{ event.date.strftime('%Y-%m-%d') }} {{ event.start_time }}</td>
                                        <td>{{ event.game }}</td>
                                        <td>
                                            {% if event.max_tickets == 0 %}
                                                {{ event.tickets_sold }} / ∞
                                            {% else %}
                                                {{ event.tickets_sold }} / {{ event.max_tickets }}
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if event.is_published %}
                                                <span class="badge bg-success">Published</span>
                                            {% else %}
                                                <span class="badge bg-warning">Draft</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('events.event_details', event_id=event.id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('events.edit_event', event_id=event.id) }}" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if not event.is_published %}
                                                <a href="{{ url_for('events.publish_event', event_id=event.id) }}" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-globe"></i>
                                                </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            No upcoming events. <a href="{{ url_for('events.create_event') }}">Create one now</a>!
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Past Events -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">Past Events</h5>
                </div>
                <div class="card-body">
                    {% if past_events %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Date</th>
                                        <th>Game</th>
                                        <th>Tickets</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for event in past_events %}
                                    <tr>
                                        <td>
                                            <a href="{{ url_for('events.event_details', event_id=event.id) }}">
                                                {{ event.title }}
                                            </a>
                                        </td>
                                        <td>{{ event.date.strftime('%Y-%m-%d') }} {{ event.start_time }}</td>
                                        <td>{{ event.game }}</td>
                                        <td>
                                            {% if event.max_tickets == 0 %}
                                                {{ event.tickets_sold }} / ∞
                                            {% else %}
                                                {{ event.tickets_sold }} / {{ event.max_tickets }}
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('events.event_details', event_id=event.id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('events.event_registrations', event_id=event.id) }}" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-users"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            No past events.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Event Stats</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ upcoming_events.count() }}</h3>
                                    <p class="mb-0">Upcoming</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ past_events.count() }}</h3>
                                    <p class="mb-0">Past</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="{{ url_for('events.events_analytics') }}" class="btn btn-outline-primary">
                            <i class="fas fa-chart-bar"></i> View Analytics
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Quick Links</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{{ url_for('events.create_event') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-plus me-2"></i> Create New Event
                        </a>
                        <a href="{{ url_for('events.events_calendar') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-alt me-2"></i> Calendar View
                        </a>
                        <a href="{{ url_for('events.events_analytics') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> Analytics Dashboard
                        </a>
                        <a href="{{ url_for('events.public_events', username=current_user.username) }}" class="list-group-item list-group-item-action" target="_blank">
                            <i class="fas fa-globe me-2"></i> Public Events Page
                            <i class="fas fa-external-link-alt ms-1" style="font-size: 0.8em;"></i>
                        </a>
                        <a href="https://enterprise.tcgsync.com/events/{{ current_user.username }}" class="list-group-item list-group-item-action" target="_blank">
                            <i class="fas fa-user-circle me-2"></i> Personal Events Section
                            <i class="fas fa-external-link-alt ms-1" style="font-size: 0.8em;"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Any dashboard-specific JavaScript can go here
    });
</script>
{% endblock %}
