from config.config import Config
#!/usr/bin/env python3
"""
<PERSON>ript to send Friday Flash Sale emails with magic links to users.
The script implements rate limiting to send only 50 emails per hour.

Usage:
    python send_flash_sale_emails.py [--test] [--file FILENAME]

Options:
    --test          Send a test email only to the admintcg user
    --file FILENAME Read usernames from the specified file (one username per line)
"""

import os
import sys
import time
import logging
import argparse
import secrets
import requests
from datetime import datetime, timedelta
from pymongo import MongoClient
from config import Config
from urllib.parse import urljoin

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bulk_email_send.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def connect_to_mongodb():
    """Connect to MongoDB and return the client and database"""
    try:
        # Get MongoDB connection details from Config
        mongo_uri = Config.MONGO_URI
        mongo_dbname = Config.MONGO_DBNAME
        
        logger.info(f"Connecting to MongoDB at {mongo_uri}...")
        mongo_client = MongoClient(mongo_uri)
        db = mongo_client[mongo_dbname]
        
        # Test the connection
        db.command('ping')
        logger.info("Successfully connected to MongoDB")
        
        return mongo_client, db
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {str(e)}")
        sys.exit(1)

def get_user_email(db, username):
    """Get the email address for a user"""
    try:
        user = db['user'].find_one({'username': username})
        if not user:
            logger.error(f"User not found: {username}")
            return None
        
        email = user.get('email')
        if not email:
            logger.error(f"No email found for user: {username}")
            return None
        
        return email
    except Exception as e:
        logger.error(f"Error retrieving user email: {str(e)}")
        return None

def generate_magic_link(db, username):
    """Generate a magic link for the user"""
    try:
        # Find the user
        user = db['user'].find_one({'username': username})
        if not user:
            logger.error(f"User not found: {username}")
            return None
        
        # Generate a secure token
        token = secrets.token_urlsafe(32)
        
        # Set the token expiration (24 hours from now)
        expiration = datetime.utcnow() + timedelta(hours=24)
        
        # Update the user record with the token and expiration
        db['user'].update_one(
            {'_id': user['_id']},
            {'$set': {
                'reset_token': token,
                'reset_token_expiration': expiration
            }}
        )
        
        # Construct the magic link URL
        base_url = "https://login.tcgsync.com"
        magic_link = urljoin(base_url, f"/auth/magic_login/{token}")
        
        logger.info(f"Generated magic link for user {username}")
        return magic_link
    except Exception as e:
        logger.error(f"Error generating magic link: {str(e)}")
        return None

def send_email(username, email, magic_link):
    """Send the flash sale email with magic link to the user"""
    try:
        # Mailgun API credentials from config
        MAILGUN_API_KEY = Config.MAILGUN_API_KEY
        MAILGUN_DOMAIN = Config.MAILGUN_DOMAIN

        if not MAILGUN_API_KEY or not MAILGUN_DOMAIN:
            raise ValueError("Mailgun API key or domain not set in config")

        # Read the email template with UTF-8 encoding
        with open('templates/friday_flash_sale_email.html', 'r', encoding='utf-8') as file:
            html_content = file.read()
        
        # Replace placeholders in the template
        html_content = html_content.replace('{{magic_link}}', magic_link)
        html_content = html_content.replace('{{email}}', email)
        
        # Mailgun API endpoint
        url = f"https://api.eu.mailgun.net/v3/{MAILGUN_DOMAIN}/messages"

        # Email data
        data = {
            "from": f"TCGSync Flash Sale <admin@{MAILGUN_DOMAIN}>",
            "to": email,
            "subject": "🔥 FRIDAY FLASH SALE: 33% OFF All Annual Subscriptions - Today Only!",
            "html": html_content
        }

        # Send the email
        response = requests.post(
            url,
            auth=("api", MAILGUN_API_KEY),
            data=data,
            timeout=30  # 30 second timeout
        )

        if response.status_code == 200:
            logger.info(f"Email sent successfully to {email}")
            return True
        else:
            logger.error(f"Failed to send email to {email}. Status code: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error sending email to {email}: {str(e)}")
        return False

def process_users(db, usernames, rate_limit=50):
    """Process the list of usernames and send emails with rate limiting"""
    total_users = len(usernames)
    successful = 0
    failed = 0
    
    logger.info(f"Starting to send emails to {total_users} users with rate limit of {rate_limit} per hour...")
    
    # Calculate the delay between emails to achieve the rate limit
    # 3600 seconds in an hour / rate_limit = seconds between emails
    delay = 3600 / rate_limit
    
    for i, username in enumerate(usernames, 1):
        logger.info(f"Processing user {i}/{total_users}: {username}")
        
        # Get the user's email
        email = get_user_email(db, username)
        if not email:
            logger.error(f"Skipping user {username} - email not found")
            failed += 1
            continue
        
        # Generate a magic link for the user
        magic_link = generate_magic_link(db, username)
        if not magic_link:
            logger.error(f"Skipping user {username} - failed to generate magic link")
            failed += 1
            continue
        
        # Send the email
        logger.info(f"Sending email to {username} ({email})...")
        if send_email(username, email, magic_link):
            successful += 1
            logger.info(f"✓ Successfully sent to {username} ({email})")
        else:
            failed += 1
            logger.error(f"✗ Failed to send to {username} ({email})")
        
        # Apply rate limiting delay if not the last user
        if i < total_users:
            logger.info(f"Waiting {delay:.2f} seconds before sending next email...")
            time.sleep(delay)
    
    logger.info(f"\nEmail sending completed!")
    logger.info(f"Successfully sent: {successful}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Total processed: {total_users}")
    
    return successful, failed

def read_usernames_from_file(filename):
    """Read usernames from a file, one per line"""
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            # Read lines and strip whitespace
            usernames = [line.strip() for line in file.readlines()]
            # Filter out empty lines
            usernames = [username for username in usernames if username]
        
        logger.info(f"Read {len(usernames)} usernames from {filename}")
        return usernames
    except Exception as e:
        logger.error(f"Error reading usernames from file {filename}: {str(e)}")
        sys.exit(1)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Send Friday Flash Sale emails with magic links to users')
    parser.add_argument('--test', action='store_true', help='Send a test email only to the admintcg user')
    parser.add_argument('--file', type=str, help='Read usernames from the specified file (one username per line)')
    
    args = parser.parse_args()
    
    # Connect to MongoDB
    mongo_client, db = connect_to_mongodb()
    
    try:
        if args.test:
            # Test mode - send only to admintcg
            logger.info("Running in TEST mode - sending only to admintcg")
            usernames = ['admintcg']
        elif args.file:
            # Read usernames from file
            usernames = read_usernames_from_file(args.file)
        else:
            # No arguments provided, show usage and exit
            parser.print_help()
            sys.exit(0)
        
        # Process the users
        successful, failed = process_users(db, usernames)
        
        # Log summary
        logger.info(f"Email sending summary:")
        logger.info(f"Total users: {len(usernames)}")
        logger.info(f"Successfully sent: {successful}")
        logger.info(f"Failed: {failed}")
        
    finally:
        # Close the MongoDB connection
        mongo_client.close()
        logger.info("MongoDB connection closed")

if __name__ == "__main__":
    main()

