{% extends "base.html" %}

{% block title %}Shopify Product Standardizer{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Shopify Product Standardizer</h1>
    
    <!-- Summary Statistics -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Discrepancies</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Review</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Approved</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ approved }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Rejected</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ rejected }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Products Checked Statistics -->
    <div class="row">
        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Products Checked</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ checked_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Products Remaining</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ remaining_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Run Analysis Card -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Run New Analysis</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('standardizer.run_analysis') }}" method="post" id="analysisForm">
                        <div class="form-group">
                            <label for="limit">Limit</label>
                            <input type="number" class="form-control" id="limit" name="limit" value="1000">
                            <small class="form-text text-muted">Maximum number of products to analyze</small>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" id="analyze_all" name="analyze_all">
                            <label class="form-check-label" for="analyze_all">Analyze All Products (no limit)</label>
                            <small class="form-text text-muted">This will analyze all products in the background</small>
                        </div>
                        <div class="form-group">
                            <label for="product_type">Product Type</label>
                            <select class="form-control" id="product_type" name="product_type">
                                <option value="all">All Products</option>
                                <option value="singles">Singles Only</option>
                                <option value="sealed">Sealed Only</option>
                            </select>
                            <small class="form-text text-muted">Select which type of products to analyze</small>
                        </div>
                        <p class="text-muted">Analysis will be run on your products only.</p>
                        <button type="submit" class="btn btn-primary btn-block">Run Analysis</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Apply Repairs Card -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Apply Approved Repairs</h6>
                </div>
                <div class="card-body">
                    <p>Apply all approved repairs to Shopify products</p>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form action="{{ url_for('standardizer.repair_discrepancies') }}" method="post" id="repairForm">
                                <button type="submit" class="btn btn-success btn-block">
                                    <i class="fas fa-sync-alt mr-2"></i>Push Changes to Shopify
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form action="{{ url_for('standardizer.approve_all_discrepancies') }}" method="post" id="approveAllForm">
                                <button type="button" class="btn btn-primary btn-block" onclick="confirmApproveAll()">
                                    <i class="fas fa-check-double mr-2"></i>Approve All Changes
                                </button>
                            </form>
                        </div>
                    </div>
                    <small class="form-text text-muted mt-2">
                        "Push Changes" will update all approved discrepancies in your Shopify store.<br>
                        "Approve All" will only mark all pending discrepancies as approved (without updating Shopify).<br>
                        Use "Approve All" first, then "Push Changes" to update Shopify when you're ready.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Spacer -->
    <div class="mb-4"></div>

    <!-- Pending Discrepancies Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recent Pending Discrepancies</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered text-white" id="dataTable" width="100%" cellspacing="0">
                <style>
                    #dataTable tbody tr {
                        background-color: transparent !important;
                    }
                    #dataTable {
                        color: white !important;
                    }
                </style>
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Username</th>
                            <th>Product ID</th>
                            <th>Issues</th>
                            <th>Identified</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th>Title</th>
                            <th>Username</th>
                            <th>Product ID</th>
                            <th>Issues</th>
                            <th>Identified</th>
                            <th>Actions</th>
                        </tr>
                    </tfoot>
                    <tbody id="pendingDiscrepancies">
                        <!-- Will be populated via AJAX -->
                    </tbody>
                </table>
            </div>
            <a href="{{ url_for('standardizer.list_discrepancies') }}" class="btn btn-primary">View All Discrepancies</a>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<style>
/* Loading overlay styles */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading-spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: white;
    font-size: 18px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading overlay to the page
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-spinner"></div>
        <div class="loading-text" id="loadingText">Processing...</div>
    `;
    document.body.appendChild(loadingOverlay);
    
    // Add event listener to the Run Analysis form
    const analysisForm = document.getElementById('analysisForm');
    if (analysisForm) {
        analysisForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show confirmation dialog
            const isAnalyzeAll = document.getElementById('analyze_all').checked;
            const message = isAnalyzeAll 
                ? 'This will analyze ALL products in your catalog. The process will run in the background and may take some time. Check back later for results. Do you want to continue?'
                : 'This analysis will run in the background. Check back later for results. Do you want to continue?';
                
            if (confirm(message)) {
                // Show loading overlay
                document.getElementById('loadingText').textContent = 'Starting analysis in the background...';
                loadingOverlay.style.display = 'flex';
                
                // Submit the form after a short delay to show the message
                setTimeout(() => {
                    this.submit();
                }, 1500);
            }
        });
        
        // Add event listener to the analyze_all checkbox
        const analyzeAllCheckbox = document.getElementById('analyze_all');
        const limitInput = document.getElementById('limit');
        
        analyzeAllCheckbox.addEventListener('change', function() {
            if (this.checked) {
                limitInput.disabled = true;
                limitInput.value = 1000000; // Set a very high limit
            } else {
                limitInput.disabled = false;
                limitInput.value = 1000; // Reset to default
            }
        });
    }
    
    // Modify the repair form submit handler
    document.getElementById('repairForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // First confirm they want to push changes
        if (confirm('Are you sure you want to push these changes to Shopify? This action cannot be undone.')) {
            // Then show background processing message
            if (confirm('This operation will run in the background and may take some time. Check back later to see the results. Do you want to continue?')) {
                // Add a hidden field to indicate this is a POST request
                var input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'confirm';
                input.value = 'true';
                this.appendChild(input);
                
                // Show loading overlay
                document.getElementById('loadingText').textContent = 'Starting Shopify update in the background...';
                loadingOverlay.style.display = 'flex';
                
                // Submit the form after a short delay to show the message
                setTimeout(() => {
                    this.submit();
                }, 1500);
            }
        }
    });
    
    // Function to confirm approving all discrepancies
    window.confirmApproveAll = function() {
        if (confirm('Are you sure you want to approve ALL pending discrepancies? This will mark them as approved without updating Shopify.')) {
            // Then show background processing message
            if (confirm('This operation will run in the background and may take some time. Check back later to see the results. Do you want to continue?')) {
                // Show loading overlay
                document.querySelector('.loading-overlay').style.display = 'flex';
                document.getElementById('loadingText').textContent = 'Marking all discrepancies as approved...';
                
                // Submit the form after a short delay to show the message
                setTimeout(() => {
                    document.getElementById('approveAllForm').submit();
                }, 1500);
            }
        }
    };
    
    // Load recent pending discrepancies
    fetch('/shopify/standardizer/api/discrepancies?status=pending&limit=10')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('pendingDiscrepancies');
            tbody.innerHTML = '';
            
            if (data.discrepancies && data.discrepancies.length > 0) {
                data.discrepancies.forEach(item => {
                    const issueTypes = Object.keys(item.discrepancies || {}).join(', ');
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${item.title || 'Unknown'}</td>
                        <td>${item.username || 'Unknown'}</td>
                        <td>${item.product_id_int || 'N/A'}</td>
                        <td>${issueTypes || 'None'}</td>
                        <td>${new Date(item.timestamp).toLocaleString()}</td>
                        <td>
                            <a href="/shopify/standardizer/discrepancy/${item._id}" class="btn btn-info btn-sm">View</a>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="6" class="text-center">No pending discrepancies found</td>
                `;
                tbody.appendChild(row);
            }
        })
        .catch(error => {
            console.error('Error loading discrepancies:', error);
            const tbody = document.getElementById('pendingDiscrepancies');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="6" class="text-center">Error loading discrepancies</td>
            `;
            tbody.appendChild(row);
        });
});
</script>
{% endblock %}
