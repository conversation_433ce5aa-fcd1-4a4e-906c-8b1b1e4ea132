from config.config import Config
#!/usr/bin/env python3
"""
Test script for the Prepare Repricing endpoint.

This script tests the new prepare_reprice endpoint that was added to the repricing service.
"""

import requests
import json
import sys

# Configuration
BASE_URL = "https://webhooks.tcgsync.com"
API_KEY = "IPBRMIwlm5a7DWoZqPnxyamQSIPMyG19"
USERNAME = "admintcg"  # Replace with a valid username for testing

def print_success(message):
    """Print a success message in green."""
    print(f"\033[1;32m✓ {message}\033[0m")

def print_error(message):
    """Print an error message in red."""
    print(f"\033[1;31m✗ {message}\033[0m")

def print_info(message):
    """Print an info message in blue."""
    print(f"\033[1;34mℹ {message}\033[0m")

def print_json(data):
    """Print JSON data in a formatted way."""
    print(json.dumps(data, indent=2))

def test_prepare_reprice():
    """Test the prepare_reprice endpoint."""
    print_info(f"Testing prepare_reprice endpoint for user {USERNAME}")
    
    url = f"{BASE_URL}/api/prepare_reprice"
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
    }
    data = {'username': USERNAME}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 200:
            print_success(f"Prepare repricing successful (Status: {response.status_code})")
            result = response.json()
            print_json(result)
            
            # Check if the response contains the expected fields
            expected_fields = ['job_id', 'username', 'total_products', 'selected_product_types', 'settings']
            missing_fields = [field for field in expected_fields if field not in result]
            
            if missing_fields:
                print_error(f"Response is missing expected fields: {', '.join(missing_fields)}")
                return False
            
            # Check if settings contains the expected fields
            expected_settings = ['minPrice', 'price_preference_order', 'use_highest_price', 
                                'price_rounding_enabled', 'tcg_trend_increasing', 'tcg_trend_decreasing']
            missing_settings = [field for field in expected_settings if field not in result['settings']]
            
            if missing_settings:
                print_error(f"Settings is missing expected fields: {', '.join(missing_settings)}")
                return False
            
            print_success("All expected fields are present in the response")
            
            # Now test confirming the job
            job_id = result['job_id']
            print_info(f"Testing confirm_reprice endpoint for job {job_id}")
            
            confirm_url = f"{BASE_URL}/api/reprice"
            confirm_data = {
                'username': USERNAME,
                'job_id': job_id
            }
            
            confirm_response = requests.post(confirm_url, headers=headers, json=confirm_data)
            
            if confirm_response.status_code == 200:
                print_success(f"Confirm repricing successful (Status: {confirm_response.status_code})")
                confirm_result = confirm_response.json()
                print_json(confirm_result)
                
                # Cancel the job to clean up
                cancel_url = f"{BASE_URL}/api/cancel/{job_id}"
                cancel_response = requests.post(cancel_url, headers=headers)
                
                if cancel_response.status_code == 200:
                    print_success(f"Cancelled test job {job_id}")
                else:
                    print_error(f"Failed to cancel test job: {cancel_response.status_code}")
                    print(cancel_response.text)
                
                return True
            else:
                print_error(f"Confirm repricing failed (Status: {confirm_response.status_code})")
                print(confirm_response.text)
                return False
        else:
            print_error(f"Prepare repricing failed (Status: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print_error(f"Error testing prepare_reprice: {str(e)}")
        return False

if __name__ == "__main__":
    print_info(f"Testing against {BASE_URL}")
    success = test_prepare_reprice()
    
    if success:
        print_success("\nAll tests passed!")
        sys.exit(0)
    else:
        print_error("\nTests failed!")
        sys.exit(1)

