from config.config import Config
import os
import sys
import signal
import logging
import dramatiq
from dotenv import load_dotenv
from dramatiq.brokers.redis import RedisBroker
from dramatiq.worker import Worker
from dramatiq.middleware import TimeLimit
from dramatiq_config import broker

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the current directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def shutdown(signum, frame):
    """Handle shutdown signals gracefully"""
    logger.info(f"Received signal {signum}. Starting cleanup...")
    logger.info("Stopping Dramatiq worker...")
    if hasattr(shutdown, 'worker'):
        shutdown.worker.stop()
    logger.info("Dramatiq worker stopped")
    sys.exit(0)

def start_worker():
    """Start the Dramatiq worker process"""
    try:
        logger.info("Starting Dramatiq worker...")

        # Set up the broker
        dramatiq.set_broker(broker)

        # Import all modules containing actors
        from tasks.shopify_tasks import (
            adjust_shopify_inventory,
            create_product_task,
            create_metafield_task,
            get_locations_task,
            create_board_game_product_task,
            fetch_all_orders,
            fetch_all_customers,
            fetch_all_products
        )
        from routes.shopify_sync_routes import sync_shopify_data_task

        logger.info("Imported task modules successfully")
        logger.info("Registered tasks:")
        logger.info("- sync_shopify_data_task")
        logger.info("- adjust_shopify_inventory")
        logger.info("- create_product_task")
        logger.info("- create_metafield_task")
        logger.info("- get_locations_task")
        logger.info("- create_board_game_product_task")
        logger.info("- fetch_all_orders")
        logger.info("- fetch_all_customers")
        logger.info("- fetch_all_products")

        # Get configuration from environment
        worker_timeout = int(os.getenv('DRAMATIQ_WORKER_TIMEOUT', 3600))
        task_timeout = int(os.getenv('DRAMATIQ_TASK_TIMEOUT', 3600))
        worker_threads = int(os.getenv('DRAMATIQ_WORKER_THREADS', 8))
        
        # Configure middleware with timeouts
        broker.middleware = [
            m for m in broker.middleware 
            if not isinstance(m, TimeLimit)
        ]
        broker.middleware.append(TimeLimit(time_limit=task_timeout * 1000))  # Convert to milliseconds
        
        # Create and start worker with configured settings
        worker = Worker(
            broker,
            worker_timeout=worker_timeout * 1000,  # Convert to milliseconds
            worker_threads=worker_threads
        )
        shutdown.worker = worker
        worker.start()

    except Exception as e:
        logger.error(f"Failed to start Dramatiq worker: {e}")
        return 1

if __name__ == '__main__':
    # Register signal handlers
    signal.signal(signal.SIGTERM, shutdown)
    signal.signal(signal.SIGINT, shutdown)

    # Start the worker
    start_worker()

    # Keep the main thread alive
    try:
        while True:
            import time
            time.sleep(1)
    except KeyboardInterrupt:
        shutdown(signal.SIGINT, None)

