from config.config import Config
from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from bson import ObjectId
import requests
import logging
import pymongo
from tasks import fetch_all_orders

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_orders_bp(mongo_client):
    orders_bp = Blueprint('shopify_orders', __name__)

    def calculate_order_fees(order):
        """Calculate fees for an order at 2% of product value (excluding shipping) but only for tcgItem=true products"""
        try:
            # Calculate shipping costs from shipping lines
            shipping_cost = 0
            shipping_lines = order.get('shipping_lines', [])
            for shipping_line in shipping_lines:
                shipping_cost += float(shipping_line.get('price', 0))
            
            # Calculate fees only for tcgItem=true products
            tcg_product_value = 0
            line_items = order.get('line_items', [])
            
            for item in line_items:
                # Get the variant admin_graphql_api_id from the line item
                variant_admin_id = item.get('admin_graphql_api_id')
                if not variant_admin_id:
                    continue
                
                # Find the product in shProducts collection by matching variant admin_graphql_api_id
                product = mongo_client.test.shProducts.find_one({
                    'username': order.get('username'),
                    'variants.admin_graphql_api_id': variant_admin_id
                })
                
                # Only include in fee calculation if tcgItem is true
                if product and product.get('tcgItem') is True:
                    item_price = float(item.get('price', 0))
                    item_quantity = int(item.get('quantity', 0))
                    tcg_product_value += (item_price * item_quantity)
            
            # Calculate 2% fee on tcgItem products only
            fee = tcg_product_value * 0.02
            
            return max(0, fee)  # Ensure fee is not negative
        except (ValueError, TypeError):
            return 0

    def fetch_order_from_shopify(order_id, user):
        """Fetch order details from Shopify API"""
        shop_name = user.get('shopifyStoreName')  # Changed from shopify_url
        access_token = user.get('shopifyAccessToken')  # Changed from shopify_access_token
        
        if not shop_name or not access_token:
            raise ValueError(f"Missing Shopify credentials. Store: {shop_name}, Token: {'Present' if access_token else 'Missing'}")

        # Append .myshopify.com to store name
        shop_url = f"{shop_name}.myshopify.com"

        headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }

        # Get order from Shopify using order ID
        url = f"https://{shop_url}/admin/api/2023-10/orders/{order_id}.json"
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        order = response.json().get('order')
        if not order:
            raise ValueError(f"Order {order_id} not found")
            
        return order

    @orders_bp.route('/shopify/orders/refresh', methods=['POST'])
    @login_required
    def refresh_orders():
        try:
            data = request.get_json()
            if not data or 'order_numbers' not in data:
                return jsonify({'success': False, 'error': 'No orders provided'}), 400

            order_numbers = data['order_numbers']
            if not order_numbers:
                return jsonify({'success': False, 'error': 'No orders provided'}), 400

            # Get user from database - try both collections
            user = mongo_client.test.user.find_one({'username': current_user.username})
            if not user:
                # Try users collection as fallback
                user = mongo_client.test.users.find_one({'username': current_user.username})
            if not user:
                return jsonify({'success': False, 'error': f'User not found: {current_user.username}'}), 404

            # Log user details for debugging
            print(f"Found user: {user.get('username')}")
            print(f"Shopify store: {user.get('shopifyStoreName')}")
            print(f"Has access token: {'Yes' if user.get('shopifyAccessToken') else 'No'}")

            # Fetch and update each order
            for order_number in order_numbers:
                try:
                    # First get the order from MongoDB to get the Shopify ID
                    local_order = mongo_client.test.shOrders.find_one({
                        'username': current_user.username,
                        'order_number': int(order_number.replace('#', ''))
                    })
                    
                    if not local_order:
                        raise ValueError(f"Order {order_number} not found in local database")
                    
                    # Get the Shopify order ID
                    shopify_id = local_order.get('id')
                    if not shopify_id:
                        raise ValueError(f"Shopify ID not found for order {order_number}")
                    
                    # Handle different ID formats
                    if isinstance(shopify_id, dict):
                        if '$numberLong' in shopify_id:
                            shopify_id = shopify_id['$numberLong']
                        else:
                            print(f"Unexpected ID format: {shopify_id}")
                            shopify_id = str(shopify_id)
                    
                    print(f"Fetching Shopify order: {shopify_id}")
                    
                    # Get fresh order data from Shopify
                    shopify_order = fetch_order_from_shopify(shopify_id, user)
                    
                    # Add username and update timestamp
                    shopify_order['username'] = current_user.username
                    shopify_order['last_synced'] = datetime.utcnow().isoformat()

                    # Preserve restocked status if it exists
                    if local_order.get('fulfillment_status') == 'restocked':
                        shopify_order['fulfillment_status'] = 'restocked'

                    # Update order in MongoDB
                    mongo_client.test.shOrders.update_one(
                        {
                            'username': current_user.username,
                            'order_number': int(order_number.replace('#', ''))
                        },
                        {'$set': shopify_order},
                        upsert=True
                    )
                except Exception as e:
                    print(f"Error refreshing order {order_number}: {str(e)}")
                    return jsonify({
                        'success': False,
                        'error': f'Error refreshing order {order_number}: {str(e)}'
                    }), 500

            return jsonify({'success': True})

        except Exception as e:
            print(f"Error in refresh_orders: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @orders_bp.route('/shopify/orders/<order_id>/details', methods=['GET'])
    @login_required
    def get_order_details(order_id):
        try:
            # Get order from MongoDB
            order = mongo_client.test.shOrders.find_one({
                '_id': ObjectId(order_id),
                'username': current_user.username
            })
            
            if not order:
                return jsonify({'error': 'Order not found'}), 404

            # Format dates
            order['created_at'] = datetime.fromisoformat(order['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
            
            # Format prices to ensure they have 2 decimal places
            order['total_price'] = "{:.2f}".format(float(order['total_price']))
            order['subtotal_price'] = "{:.2f}".format(float(order['subtotal_price']))
            if order.get('total_tax'):
                order['total_tax'] = "{:.2f}".format(float(order['total_tax']))

            # Ensure fulfillment_status is properly set
            order['fulfillment_status'] = order.get('fulfillment_status', 'unfulfilled')

            # Format line item prices and extract set codes
            line_items = order.get('line_items', [])
            for item in line_items:
                item['price'] = "{:.2f}".format(float(item['price']))
                # Extract set code from title using square brackets only
                # Find all square bracket contents
                import re
                square_brackets = re.findall(r'\[(.*?)\]', item['title'])
                # Use the last square bracket content as the set code, or 'ZZZ' if none found
                item['set_code'] = square_brackets[-1] if square_brackets else 'ZZZ'
                # Store clean title without any brackets for display
                item['clean_title'] = re.sub(r'\[.*?\]|\{.*?\}', '', item['title']).strip()

            # Sort line items by set code then title
            order['line_items'] = sorted(line_items, 
                key=lambda x: (x['set_code'], x['clean_title']))

            return render_template('order_details.html', order=order)
        except Exception as e:
            print(f"Error getting order details: {str(e)}")
            return jsonify({'error': str(e)}), 500

    @orders_bp.route('/shopify/orders', methods=['GET'])
    @login_required
    def get_orders():
        # Get filter parameters
        date_range = request.args.get('date_range', 'last7')
        payment_status = request.args.get('payment_status', 'all')
        fulfillment_status = request.args.get('fulfillment_status', 'all')
        # Default hide_in_store to true if not specified
        hide_in_store = request.args.get('hide_in_store', 'true') == 'true'
        # Get customer_id if provided
        customer_id = request.args.get('customer_id')

        # Calculate date range
        end_date = datetime.utcnow()
        if date_range == 'today':
            start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif date_range == 'yesterday':
            start_date = (end_date - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = start_date + timedelta(days=1)
        elif date_range == 'last30':
            start_date = end_date - timedelta(days=30)
        elif date_range == 'ytd':
            # Year to date - from January 1st of current year
            start_date = end_date.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        else:  # default to last7
            start_date = end_date - timedelta(days=7)

        # Build base match query
        match_query = {
            'username': current_user.username
        }
        
        # Add date range filter only if not filtering by customer
        if not customer_id:
            match_query['created_at'] = {
                '$gte': start_date.strftime('%Y-%m-%dT%H:%M:%S-00:00'),
                '$lte': end_date.strftime('%Y-%m-%dT%H:%M:%S-00:00')
            }
        
        # Add customer filter if provided
        if customer_id:
            match_query['customer.id'] = customer_id

        # Add payment status filter if needed
        if payment_status != 'all':
            match_query['financial_status'] = payment_status

        # Add fulfillment status filter if needed
        if fulfillment_status != 'all':
            if fulfillment_status == 'unfulfilled':
                match_query['$or'] = [
                    {'fulfillment_status': None},
                    {'fulfillment_status': 'unfulfilled'}
                ]
            else:
                match_query['fulfillment_status'] = fulfillment_status

        # Add source filter to exclude in-store orders if needed
        if hide_in_store:
            match_query['source_name'] = {'$ne': 'in_store'}
            
        # Always exclude restocked orders
        match_query['$or'] = match_query.get('$or', [])
        match_query['$or'].extend([
            {'fulfillment_status': {'$ne': 'restocked'}},
            {'fulfillment_status': None}
        ])

        # Build pipeline for aggregation
        pipeline = [
            # Match stage with all filters
            {'$match': match_query},
            # Group by order number to prevent duplicates
            {
                '$group': {
                    '_id': '$order_number',
                    'latest': {'$first': '$$ROOT'}
                }
            },
            # Replace root with the full document
            {
                '$replaceRoot': {'newRoot': '$latest'}
            },
            # Sort by date descending
            {
                '$sort': {'created_at': -1}
            }
        ]

        # Execute aggregation pipeline
        orders = list(mongo_client.test.shOrders.aggregate(pipeline))

        # Format orders for display and calculate total fees
        formatted_orders = []
        total_fees = 0
        
        for order in orders:
            # Safely get customer name
            customer = order.get('customer', {}) or {}
            first_name = customer.get('first_name', '')
            last_name = customer.get('last_name', '')
            customer_name = ' '.join(filter(None, [first_name, last_name])) or 'No Name'

            # Handle different ID formats for Shopify ID
            shopify_id = order.get('id')
            if isinstance(shopify_id, dict):
                if '$numberLong' in shopify_id:
                    shopify_id = shopify_id['$numberLong']
                else:
                    shopify_id = str(shopify_id)

            # Calculate fees for this order
            order_fee = calculate_order_fees(order)
            total_fees += order_fee

            formatted_orders.append({
                'id': str(order['_id']),
                'shopify_id': shopify_id,
                'order_number': f"#{order['order_number']}",
                'date': datetime.fromisoformat(order['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M'),
                'customer': customer_name,
                'total': f"${order['total_price']}",
                'fees': f"${order_fee:.2f}",
                'payment_status': order['financial_status'],
                'fulfillment_status': order['fulfillment_status'] or 'unfulfilled',
                'items_count': len(order.get('line_items', [])),
                'delivery_status': 'pending' if order['fulfillment_status'] != 'fulfilled' else 'shipped'
            })

        return render_template('orders.html', 
                             orders=formatted_orders,
                             total_fees=f"${total_fees:.2f}",
                             hide_in_store=hide_in_store)

    @orders_bp.route('/shopify/orders/<order_id>/fulfill', methods=['POST'])
    @login_required
    def fulfill_order(order_id):
        try:
            # Get user from database
            user = mongo_client.test.user.find_one({'username': current_user.username})
            if not user:
                user = mongo_client.test.users.find_one({'username': current_user.username})
            if not user:
                return jsonify({'success': False, 'error': f'User not found: {current_user.username}'}), 404

            # Get order from MongoDB to get the Shopify ID
            local_order = mongo_client.test.shOrders.find_one({
                '_id': ObjectId(order_id),
                'username': current_user.username
            })
            
            if not local_order:
                return jsonify({'success': False, 'error': 'Order not found'}), 404

            # Get the Shopify order ID
            shopify_id = local_order.get('id')
            if isinstance(shopify_id, dict) and '$numberLong' in shopify_id:
                shopify_id = shopify_id['$numberLong']

            # Prepare Shopify API request
            shop_name = user.get('shopifyStoreName')
            access_token = user.get('shopifyAccessToken')
            
            if not shop_name or not access_token:
                return jsonify({'success': False, 'error': 'Missing Shopify credentials'}), 400

            shop_url = f"{shop_name}.myshopify.com"
            headers = {
                'X-Shopify-Access-Token': access_token,
                'Content-Type': 'application/json'
            }

            # Create fulfillment for all line items
            fulfillment_data = {
                "fulfillment": {
                    "location_id": "manual",  # Use manual fulfillment
                    "tracking_number": None,
                    "line_items": [
                        {"id": item['id']} for item in local_order.get('line_items', [])
                    ]
                }
            }

            # Send fulfillment request to Shopify
            url = f"https://{shop_url}/admin/api/2023-10/orders/{shopify_id}/fulfillments.json"
            response = requests.post(url, headers=headers, json=fulfillment_data)
            
            if response.status_code != 201:
                return jsonify({
                    'success': False, 
                    'error': f'Shopify API error: {response.text}'
                }), 500

            # Update local order status
            mongo_client.test.shOrders.update_one(
                {'_id': ObjectId(order_id)},
                {'$set': {
                    'fulfillment_status': 'fulfilled',
                    'last_synced': datetime.utcnow().isoformat()
                }}
            )

            return jsonify({'success': True})

        except Exception as e:
            print(f"Error fulfilling order: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @orders_bp.route('/shopify/customers/api/customer/<customer_id>/orders', methods=['GET'])
    @login_required
    def get_customer_orders(customer_id):
        try:
            print(f"Fetching orders for customer ID: {customer_id}")
            
            # Build match query for customer orders
            match_query = {
                'username': current_user.username,
                '$and': [
                    # Try different formats of customer ID
                    {
                        '$or': [
                            {'customer.id': customer_id},
                            {'customer.id': str(customer_id)},
                            {'customer.id': int(customer_id) if customer_id.isdigit() else customer_id}
                        ]
                    },
                    # Always exclude restocked orders
                    {
                        '$or': [
                            {'fulfillment_status': {'$ne': 'restocked'}},
                            {'fulfillment_status': None}
                        ]
                    }
                ]
            }
            
            # Debug the query
            print(f"Match query: {match_query}")

            # Build pipeline for aggregation
            pipeline = [
                # Match stage with customer filter
                {'$match': match_query},
                # Group by order number to prevent duplicates
                {
                    '$group': {
                        '_id': '$order_number',
                        'latest': {'$first': '$$ROOT'}
                    }
                },
                # Replace root with the full document
                {
                    '$replaceRoot': {'newRoot': '$latest'}
                },
                # Sort by date descending
                {
                    '$sort': {'created_at': -1}
                }
            ]

            # Execute aggregation pipeline
            orders = list(mongo_client.test.shOrders.aggregate(pipeline))

            # Format orders for display
            formatted_orders = []
            for order in orders:
                # Format date
                created_at = datetime.fromisoformat(order['created_at'].replace('Z', '+00:00'))
                
                # Handle different ID formats for Shopify ID
                shopify_id = order.get('id')
                if isinstance(shopify_id, dict):
                    if '$numberLong' in shopify_id:
                        shopify_id = shopify_id['$numberLong']
                    else:
                        shopify_id = str(shopify_id)

                formatted_orders.append({
                    'id': str(order['_id']),
                    'shopify_id': shopify_id,
                    'order_number': f"#{order['order_number']}",
                    'date': created_at.strftime('%Y-%m-%d %H:%M'),
                    'total': f"${order['total_price']}",
                    'payment_status': order['financial_status'],
                    'fulfillment_status': order['fulfillment_status'] or 'unfulfilled',
                    'items_count': len(order.get('line_items', [])),
                    'delivery_status': 'pending' if order['fulfillment_status'] != 'fulfilled' else 'shipped'
                })

            return jsonify({
                'success': True,
                'orders': formatted_orders
            })
        except Exception as e:
            print(f"Error getting customer orders: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @orders_bp.route('/shopify/orders/pull-new', methods=['POST'])
    @login_required
    def pull_new_orders():
        try:
            # Get user from database with error handling for MongoDB connection issues
            try:
                user = mongo_client.test.user.find_one({'username': current_user.username})
                if not user:
                    # Try users collection as fallback
                    user = mongo_client.test.users.find_one({'username': current_user.username})
            except pymongo.errors.AutoReconnect as e:
                logger.error(f"MongoDB connection error: {str(e)}")
                return jsonify({
                    'success': False, 
                    'error': 'Database connection error. Please try again later.'
                }), 503
            
            if not user:
                return jsonify({'success': False, 'error': f'User not found: {current_user.username}'}), 404
            
            # Check if user has Shopify credentials
            shop_name = user.get('shopifyStoreName')
            access_token = user.get('shopifyAccessToken')
            
            if not shop_name or not access_token:
                return jsonify({
                    'success': False, 
                    'error': 'Missing Shopify credentials. Please set up your Shopify integration first.'
                }), 400
            
            # Delete existing records for this user
            logger.info(f"Deleting existing orders for user {current_user.username}")
            mongo_client.test.shOrders.delete_many({"username": current_user.username})
            
            # Build the initial URL for fetching orders
            next_url = f"https://{shop_name}.myshopify.com/admin/api/2023-10/orders.json?limit=250&status=any"
            
            # Set up headers for Shopify API
            headers = {
                "X-Shopify-Access-Token": access_token,
                "Content-Type": "application/json"
            }
            
            # Process orders directly
            total_processed = 0
            
            # Process all batches
            while next_url:
                logger.info(f"Fetching orders batch from {next_url}")
                response = requests.get(next_url, headers=headers)
                response.raise_for_status()
                data = response.json()
                
                # Process orders in this batch
                if 'orders' in data:
                    batch_operations = []
                    for order in data['orders']:
                        # Add username to each order
                        order['username'] = current_user.username
                        # Add last_synced timestamp
                        order['last_synced'] = datetime.utcnow().isoformat()
                        
                        # Create update operation with upsert
                        batch_operations.append(pymongo.UpdateOne(
                            {"username": current_user.username, "id": order['id']},
                            {"$set": order},
                            upsert=True
                        ))
                    
                    if batch_operations:
                        # Use shOrders collection
                        result = mongo_client.test.shOrders.bulk_write(batch_operations)
                        batch_processed = len(batch_operations)
                        total_processed += batch_processed
                        logger.info(f"Processed {batch_processed} orders for user {current_user.username}")
                
                # Check for pagination
                next_url = None
                link_header = response.headers.get('Link')
                if link_header and 'next' in link_header:
                    # Extract the next link
                    for link in link_header.split(','):
                        if 'rel="next"' in link:
                            next_url = link.split(';')[0].strip('<>')
                            break
            
            # Update user's last fetched date
            mongo_client.test.user.update_one(
                {'username': current_user.username},
                {'$set': {'lastFetchedOrderDate': datetime.utcnow()}}
            )
            
            logger.info(f"Completed fetching orders for user {current_user.username}. Total processed: {total_processed}")
            
            return jsonify({
                'success': True,
                'message': f'Successfully fetched {total_processed} orders.',
                'ordersProcessed': total_processed
            })
            
        except Exception as e:
            logger.error(f"Error in pull_new_orders: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @orders_bp.route('/shopify/orders/print', methods=['POST'])
    @login_required
    def print_orders():
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No JSON data received'}), 400
                
            order_ids = data.get('order_ids', [])
            combined = data.get('combined', False)
            
            if not order_ids:
                return jsonify({'error': 'No order IDs provided'}), 400

            print(f"Looking for orders with IDs: {order_ids} (Combined: {combined})")
            
            # Get full order details for printing
            orders = []
            for order_id in order_ids:
                # Get all orders for this user and filter in Python
                orders_cursor = mongo_client.test.shOrders.find({
                    'username': current_user.username
                })
                
                order = None
                # Find the matching order by comparing IDs after normalization
                for potential_order in orders_cursor:
                    stored_id = potential_order.get('id')
                    if isinstance(stored_id, dict) and '$numberLong' in stored_id:
                        stored_id = stored_id['$numberLong']
                    
                    if str(stored_id) == str(order_id):
                        order = potential_order
                        break
                
                if order:
                    orders.append(order)
                else:
                    print(f"Order not found for ID: {order_id}")
            
            if not orders:
                return jsonify({'error': 'No orders found'}), 404
                
            print(f"Found {len(orders)} orders")

            if combined:
                # Combine all line items from all orders
                all_line_items = []
                order_numbers = []
                
                for order in orders:
                    order_numbers.append(f"#{order['order_number']}")
                    for item in order.get('line_items', []):
                        # Extract set code and clean title
                        import re
                        square_brackets = re.findall(r'\[(.*?)\]', item['title'])
                        set_code = square_brackets[-1] if square_brackets else 'ZZZ'
                        clean_title = re.sub(r'\[.*?\]|\{.*?\}', '', item['title']).strip()
                        
                        # Look for existing item
                        existing_item = next(
                            (x for x in all_line_items 
                             if x['clean_title'] == clean_title 
                             and x.get('variant_title') == item.get('variant_title')), 
                            None
                        )
                        
                        if existing_item:
                            existing_item['quantity'] += int(item['quantity'])
                        else:
                            item['set_code'] = set_code
                            item['clean_title'] = clean_title
                            item['price'] = "{:.2f}".format(float(item['price']))
                            all_line_items.append(item)
                
                # Sort combined items
                sorted_items = sorted(all_line_items, 
                    key=lambda x: (x['set_code'], x['clean_title']))
                
                # Create single combined order
                formatted_orders = [{
                    'order_number': f"Combined Orders ({', '.join(order_numbers)})",
                    'date': datetime.now().strftime('%Y-%m-%d %H:%M'),
                    'customer': 'Multiple Orders',
                    'line_items': sorted_items,
                    'fulfillment_status': 'Combined Pick List'
                }]
            else:
                # Format individual orders
                formatted_orders = []
                for order in orders:
                    # Safely get customer name
                    customer = order.get('customer', {}) or {}
                    first_name = customer.get('first_name', '')
                    last_name = customer.get('last_name', '')
                    customer_name = ' '.join(filter(None, [first_name, last_name])) or 'No Name'

                    # Process line items
                    line_items = order.get('line_items', [])
                    for item in line_items:
                        # Extract set code from title using square brackets only
                        import re
                        square_brackets = re.findall(r'\[(.*?)\]', item['title'])
                        # Use the last square bracket content as the set code, or 'ZZZ' if none found
                        item['set_code'] = square_brackets[-1] if square_brackets else 'ZZZ'
                        # Store clean title without any brackets for display
                        item['clean_title'] = re.sub(r'\[.*?\]|\{.*?\}', '', item['title']).strip()

                    # Sort line items by set code then title
                    sorted_line_items = sorted(line_items, 
                        key=lambda x: (x['set_code'], x['clean_title']))

                    formatted_order = {
                        'order_number': f"#{order['order_number']}",
                        'date': datetime.fromisoformat(order['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M'),
                        'customer': customer_name,
                        'total': float(order['total_price']),
                        'subtotal_price': float(order.get('subtotal_price', 0)),
                        'total_tax': float(order.get('total_tax', 0)),
                        'total_shipping_price_set': order.get('total_shipping_price_set'),
                        'line_items': sorted_line_items,
                        'fulfillment_status': order.get('fulfillment_status'),
                        'shipping_address': order.get('shipping_address')  # Include shipping address
                    }
                    formatted_orders.append(formatted_order)

            return render_template('order_print.html', 
                                 orders=formatted_orders,
                                 current_time=datetime.now().strftime('%B %d, %Y %I:%M %p'))
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    return orders_bp

try:
    from pymongo import MongoClient
    mongo_uri = '*******************************************************************'
    client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
    # Force a connection to verify it works
    client.admin.command('ping')
    print("MongoDB connection successful at module level")
    shopify_orders_bp = create_orders_bp(client)
except Exception as e:
    print(f"Failed to connect to MongoDB: {str(e)}")
    raise
