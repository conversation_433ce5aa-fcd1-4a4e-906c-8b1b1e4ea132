from config.config import Config
import requests
import json

# URL for the resync request
url = "http://localhost:5000/request-resync"

# Headers for the request
headers = {
    "Content-Type": "application/json"
}

# Make the request
response = requests.post(url, headers=headers)

# Print the response
print("Status Code:", response.status_code)
print("Response:", response.json())

# Make a second request to test the "already in progress" case
response2 = requests.post(url, headers=headers)
print("\nSecond Request:")
print("Status Code:", response2.status_code)
print("Response:", response2.json())

