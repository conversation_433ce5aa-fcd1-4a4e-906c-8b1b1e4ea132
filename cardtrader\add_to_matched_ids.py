# Script to cycle through records and add to matchedIds collection
from pymongo import MongoClient
from datetime import datetime
import logging
import sys
import argparse
from concurrent.futures import ThreadPoolExecutor
import time
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"add_to_matched_ids_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

def process_batch(blueprints, batch_size=1000):
    """Process a batch of blueprints and add them to matchedIds collection"""
    if not blueprints:
        return 0
    
    # Create matchedIds collection if it doesn't exist
    if 'matchedIds' not in cardtrader_db.list_collection_names():
        logger.info("Creating matchedIds collection")
        cardtrader_db.create_collection('matchedIds')
    
    # Use bulk operations for better performance
    from pymongo.operations import ReplaceOne
    bulk_operations = []
    
    for blueprint in blueprints:
        # Extract only the required fields
        matched_record = {
            "blueprint_id": blueprint.get("id"),
            "card_market_id": blueprint.get("card_market_id"),
            "scryfall_id": blueprint.get("scryfall_id"),
            "tcg_player_id": blueprint.get("tcg_player_id")
        }
        
        # Only add records that have at least one of the ID fields
        if any([matched_record.get("card_market_id"), 
                matched_record.get("scryfall_id"), 
                matched_record.get("tcg_player_id")]):
            
            # Use upsert to update if exists or insert if not
            bulk_operations.append(
                ReplaceOne(
                    {'blueprint_id': matched_record['blueprint_id']},
                    matched_record,
                    upsert=True
                )
            )
    
    if bulk_operations:
        result = cardtrader_db.matchedIds.bulk_write(bulk_operations)
        return result.upserted_count + result.modified_count
    
    return 0

def process_all_blueprints(batch_size=1000, num_threads=20):
    """Process all blueprints and add them to matchedIds collection"""
    # Check if blueprints collection exists
    if 'blueprints' not in cardtrader_db.list_collection_names():
        logger.error("blueprints collection not found in cardtrader database")
        return 0
    
    # Count total blueprints
    total_blueprints = cardtrader_db.blueprints.count_documents({})
    logger.info(f"Found {total_blueprints} blueprints in database")
    
    if total_blueprints == 0:
        logger.error("No blueprints found. Exiting.")
        return 0
    
    # Process blueprints in batches
    total_processed = 0
    total_added = 0
    
    # Calculate number of batches
    num_batches = (total_blueprints + batch_size - 1) // batch_size
    
    with tqdm(total=total_blueprints, desc="Processing blueprints") as pbar:
        for i in range(0, total_blueprints, batch_size):
            # Get a batch of blueprints
            blueprints = list(cardtrader_db.blueprints.find().skip(i).limit(batch_size))
            
            # Process the batch
            added = process_batch(blueprints, batch_size)
            
            # Update counters
            total_processed += len(blueprints)
            total_added += added
            
            # Update progress bar
            pbar.update(len(blueprints))
            
            # Log progress
            logger.info(f"Progress: {total_processed}/{total_blueprints} blueprints processed, {total_added} added to matchedIds")
    
    return total_added

def process_with_threads(batch_size=1000, num_threads=20):
    """Process blueprints using multiple threads"""
    # Check if blueprints collection exists
    if 'blueprints' not in cardtrader_db.list_collection_names():
        logger.error("blueprints collection not found in cardtrader database")
        return 0
    
    # Count total blueprints
    total_blueprints = cardtrader_db.blueprints.count_documents({})
    logger.info(f"Found {total_blueprints} blueprints in database")
    
    if total_blueprints == 0:
        logger.error("No blueprints found. Exiting.")
        return 0
    
    # Calculate number of batches
    num_batches = (total_blueprints + batch_size - 1) // batch_size
    logger.info(f"Processing {num_batches} batches with {num_threads} threads")
    
    # Function to process a single batch
    def process_batch_range(batch_index):
        start_idx = batch_index * batch_size
        blueprints = list(cardtrader_db.blueprints.find().skip(start_idx).limit(batch_size))
        return process_batch(blueprints, batch_size)
    
    # Process batches using thread pool
    total_added = 0
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # Submit all tasks
        future_to_batch = {executor.submit(process_batch_range, i): i for i in range(num_batches)}
        
        # Process results as they complete
        with tqdm(total=num_batches, desc="Processing batches") as pbar:
            for future in future_to_batch:
                try:
                    added = future.result()
                    total_added += added
                    pbar.update(1)
                except Exception as exc:
                    logger.error(f"Batch {future_to_batch[future]} generated an exception: {exc}")
    
    return total_added

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Add records to matchedIds collection')
    parser.add_argument('--batch-size', type=int, default=1000,
                        help='Batch size for processing blueprints (default: 1000)')
    parser.add_argument('--threads', type=int, default=20,
                        help='Number of threads to use (default: 20)')
    parser.add_argument('--sequential', action='store_true',
                        help='Use sequential processing instead of threaded (default: False)')
    return parser.parse_args()

def main():
    # Parse command line arguments
    args = parse_arguments()
    
    start_time = time.time()
    logger.info(f"Starting to add records to matchedIds collection")
    logger.info(f"Configuration: batch_size: {args.batch_size}, threads: {args.threads}, sequential: {args.sequential}")
    
    # Process blueprints
    if args.sequential:
        total_added = process_all_blueprints(args.batch_size)
    else:
        total_added = process_with_threads(args.batch_size, args.threads)
    
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info(f"Completed in {duration:.2f} seconds")
    logger.info(f"Added {total_added} records to matchedIds collection")
    
    # Count documents in matchedIds collection
    matchedIds_count = cardtrader_db.matchedIds.count_documents({})
    logger.info(f"Total documents in matchedIds collection: {matchedIds_count}")
    
    # Count documents with each ID type
    tcgplayer_count = cardtrader_db.matchedIds.count_documents({"tcg_player_id": {"$exists": True, "$ne": None}})
    cardmarket_count = cardtrader_db.matchedIds.count_documents({"card_market_id": {"$exists": True, "$ne": None}})
    scryfall_count = cardtrader_db.matchedIds.count_documents({"scryfall_id": {"$exists": True, "$ne": None}})
    
    logger.info(f"Records with TCGPlayer ID: {tcgplayer_count}")
    logger.info(f"Records with CardMarket ID: {cardmarket_count}")
    logger.info(f"Records with Scryfall ID: {scryfall_count}")

if __name__ == "__main__":
    main()
