{% if logs %}
<div class="mb-4">
    <form method="GET" action="{{ url_for('reprice_logs.view_logs') }}" class="row g-3">
        <div class="col-md-4">
            <label for="date_from" class="form-label">From Date</label>
            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.args.get('date_from', '') }}">
        </div>
        <div class="col-md-4">
            <label for="date_to" class="form-label">To Date</label>
            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.args.get('date_to', '') }}">
        </div>
        <div class="col-md-4 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100">Filter</button>
        </div>
    </form>
</div>

{% for log in logs %}
    <div class="log-card">
        <div class="log-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-history me-2"></i> 
                {{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
            </div>
            <div>
                Execution Time: {{ log.execution_time_seconds|round(2) }} seconds
            </div>
        </div>
        <div class="log-body">
            <div class="log-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ log.total_processed }}</div>
                    <div class="stat-label">Products Processed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ log.total_updated }}</div>
                    <div class="stat-label">Products Updated</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ log.total_price_changes }}</div>
                    <div class="stat-label">Price Changes</div>
                </div>
            </div>
            
            <div class="results-section">
                <div class="results-title">Results</div>
                <ul class="results-list">
                    {% for result in log.results %}
                        <li>{{ result }}</li>
                    {% endfor %}
                </ul>
            </div>

            {% if log.random_changed_records and log.random_changed_records.random_changed_record %}
            <div class="settings-section">
                <div class="settings-title">Sample Price Change</div>
                {% set record = log.random_changed_records.random_changed_record %}
                
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-grow-1">
                        <h5 class="mb-2 fw-bold" style="color: #000; font-size: 1.1rem;">{{ record.game_name }} - {{ record.product_type }}</h5>
                        {% if record.title and record.title != "Unknown Product" %}
                        <p class="mb-2 fw-medium" style="color: #000;">{{ record.title }}</p>
                        {% endif %}
                        <p class="mb-0 small">
                            <span class="badge bg-secondary me-2">{{ record.changed_variant.condition }}</span>
                            <span class="badge bg-secondary">{{ record.changed_variant.printing_type }}</span>
                        </p>
                    </div>
                    <div class="text-end ms-3">
                        <div class="small text-muted mb-1">Price Change</div>
                        <div class="d-flex align-items-center">
                            <span class="text-danger text-decoration-line-through me-2">${{ "%.2f"|format(record.changed_variant.old_price) }}</span>
                            <i class="fas fa-arrow-right mx-2 text-muted"></i>
                            <span class="text-success fw-bold">${{ "%.2f"|format(record.changed_variant.new_price) }}</span>
                        </div>
                    </div>
                </div>
                
                {% if record.price_history %}
                <div class="settings-title mt-4 mb-3">Price Calculation Steps</div>
                <div class="table-responsive">
                    <table class="table table-sm table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 20%">Step</th>
                                <th style="width: 15%">Old Price</th>
                                <th style="width: 15%">New Price</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for step in record.price_history %}
                            <tr>
                                <td class="fw-medium">{{ step.step }}</td>
                                <td class="text-danger">${{ "%.2f"|format(step.old_price) }}</td>
                                <td class="text-success">${{ "%.2f"|format(step.new_price) }}</td>
                                <td class="text-muted">{{ step.details }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
            {% endif %}
            
            <div class="settings-section">
                <div class="settings-title">Settings Used</div>
                <div class="settings-grid">
                    <div class="setting-item">
                        <span class="setting-label">Use Highest Price:</span>
                        <span class="setting-value">{{ log.settings.use_highest_price|string }}</span>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">Min Price:</span>
                        <span class="setting-value">${{ log.settings.minPrice }}</span>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">Price Rounding:</span>
                        <span class="setting-value">{{ log.settings.price_rounding_enabled|string }}</span>
                    </div>
                    {% if log.settings.price_rounding_enabled %}
                    <div class="setting-item">
                        <span class="setting-label">Rounding Thresholds:</span>
                        <span class="setting-value">{{ log.settings.price_rounding_thresholds|join(', ') }}</span>
                    </div>
                    {% endif %}
                    <div class="setting-item">
                        <span class="setting-label">TCG Trend Increasing:</span>
                        <span class="setting-value">{{ log.settings.tcg_trend_increasing }}%</span>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">TCG Trend Decreasing:</span>
                        <span class="setting-value">{{ log.settings.tcg_trend_decreasing }}%</span>
                    </div>
                </div>
                
                <div class="mt-3">
                    <div class="settings-title">Price Comparison Pairs</div>
                    <ul class="list-group">
                        {% for pair in log.settings.price_comparison_pairs %}
                            <li class="list-group-item">{{ pair|join(' vs ') }}</li>
                        {% endfor %}
                    </ul>
                </div>
                
                <div class="mt-3">
                    <div class="settings-title">Price Preference Order</div>
                    <ol class="list-group list-group-numbered">
                        {% for price in log.settings.price_preference_order %}
                            <li class="list-group-item">{{ price }}</li>
                        {% endfor %}
                    </ol>
                </div>
                
                <div class="mt-3">
                    <div class="settings-title">Product Types</div>
                    <div class="product-types">
                        {% for type in log.product_types %}
                            <span class="product-type-badge">{{ type }}</span>
                        {% endfor %}
                    </div>
                </div>
                
                {% if log.settings.game_minimum_prices %}
                <div class="mt-3">
                    <div class="settings-title">Game Minimum Prices</div>
                    <div class="accordion" id="gameMinPricesAccordion{{ loop.index }}">
                        {% for game, settings in log.settings.game_minimum_prices.items() %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ loop.index }}">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ loop.index }}" aria-expanded="false" aria-controls="collapse{{ loop.index }}">
                                    {{ game }} - Default Min: ${{ settings.default_min_price }}
                                </button>
                            </h2>
                            <div id="collapse{{ loop.index }}" class="accordion-collapse collapse" aria-labelledby="heading{{ loop.index }}" data-bs-parent="#gameMinPricesAccordion{{ loop.index }}">
                                <div class="accordion-body">
                                    {% if settings.print_types %}
                                    <div class="mb-3">
                                        <strong>Print Types:</strong>
                                        <ul class="list-group">
                                            {% for print_type, value in settings.print_types.items() %}
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                {{ print_type }}
                                                <span class="badge bg-primary rounded-pill">${{ value }}</span>
                                            </li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                    {% endif %}
                                    
                                    {% if settings.rarities %}
                                    <div>
                                        <strong>Rarities:</strong>
                                        <ul class="list-group">
                                            {% for rarity, value in settings.rarities.items() %}
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                {{ rarity }}
                                                <span class="badge bg-primary rounded-pill">${{ value }}</span>
                                            </li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                {% if log.settings.customStepping %}
                <div class="mt-3">
                    <div class="settings-title">Custom Stepping</div>
                    <div class="settings-grid">
                        {% for condition, value in log.settings.customStepping.items() %}
                        <div class="setting-item">
                            <span class="setting-label">{{ condition }}:</span>
                            <span class="setting-value">{{ value }}%</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endfor %}

<!-- Pagination -->
{% if pagination.pages > 1 %}
<div class="pagination-container">
    <ul class="pagination">
        <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
            <a class="page-link" href="{{ url_for('reprice_logs.view_logs', page=pagination.page-1, date_from=request.args.get('date_from', ''), date_to=request.args.get('date_to', '')) }}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
        
        {% for p in range(1, pagination.pages + 1) %}
            {% if p == 1 or p == pagination.pages or (p >= pagination.page - 2 and p <= pagination.page + 2) %}
                <li class="page-item {% if p == pagination.page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('reprice_logs.view_logs', page=p, date_from=request.args.get('date_from', ''), date_to=request.args.get('date_to', '')) }}">{{ p }}</a>
                </li>
            {% elif p == 2 or p == pagination.pages - 1 %}
                <li class="page-item disabled">
                    <a class="page-link" href="#">...</a>
                </li>
            {% endif %}
        {% endfor %}
        
        <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
            <a class="page-link" href="{{ url_for('reprice_logs.view_logs', page=pagination.page+1, date_from=request.args.get('date_from', ''), date_to=request.args.get('date_to', '')) }}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    </ul>
</div>
{% endif %}

{% else %}
<div class="no-logs-message">
    <i class="fas fa-search fa-3x mb-3 text-muted"></i>
    <h3>No reprice logs found</h3>
    <p class="text-muted">There are no reprice logs available for your account.</p>
</div>
{% endif %}
