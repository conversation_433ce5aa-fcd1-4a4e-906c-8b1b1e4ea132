from config.config import Config
import requests
from config import Config
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def send_test_email():
    """Send a test email to verify Mailgun configuration"""
    try:
        logger.info("Starting send_test_email")
        
        MAILGUN_API_KEY = Config.MAILGUN_API_KEY
        MAILGUN_DOMAIN = Config.MAILGUN_DOMAIN

        logger.info(f"Mailgun Domain: {MAILGUN_DOMAIN}")
        logger.info(f"Mailgun API Key length: {len(MAILGUN_API_KEY)}")

        if not MAILGUN_API_KEY or not MAILGUN_DOMAIN:
            logger.error("Mailgun API key or domain not set in config")
            raise ValueError("Mailgun API key or domain not set in config")

        # Create HTML content for the email
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header {
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }
                .content {
                    background-color: #fff;
                    padding: 20px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>Test Email</h2>
            </div>
            <div class="content">
                <p>This is a test email to verify the Mailgun configuration.</p>
                <p>If you received this email, the Mailgun integration is working correctly.</p>
            </div>
        </body>
        </html>
        """

        # Mailgun API endpoint
        url = f"https://api.eu.mailgun.net/v3/{MAILGUN_DOMAIN}/messages"
        logger.info(f"Using Mailgun endpoint: {url}")

        # Email data
        data = {
            "from": f"TCGSync Test <admin@{MAILGUN_DOMAIN}>",
            "to": "<EMAIL>",
            "subject": "TCGSync Mailgun Test",
            "html": html_content
        }
        logger.info("Prepared email data")

        # Send the email
        logger.info(f"Sending email via Mailgun to {url}...")
        logger.info("Email data:")
        logger.info(f"From: {data['from']}")
        logger.info(f"To: {data['to']}")
        logger.info(f"Subject: {data['subject']}")
        
        try:
            response = requests.post(
                url,
                auth=("api", MAILGUN_API_KEY),
                data=data,
                timeout=30  # 30 second timeout
            )
            
            # Log response details
            logger.info(f"Mailgun API Response Status: {response.status_code}")
            logger.info(f"Mailgun API Response: {response.text}")
            
            if response.status_code == 200:
                logger.info("Test email sent successfully")
                return True
            else:
                logger.error(f"Failed to send test email. Status code: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request to Mailgun API failed: {str(e)}")
            return False
            
    except Exception as e:
        logger.error(f"Unexpected error while sending test email: {str(e)}")
        return False

if __name__ == "__main__":
    # Send test email
    if send_test_email():
        print("Test email sent successfully")
    else:
        print("Failed to send test email")

