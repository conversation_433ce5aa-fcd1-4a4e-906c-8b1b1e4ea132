# Template Analysis Report

## Duplicate/Redundant Templates

| Template Name | Potential Duplicates | Notes |
|--------------|----------------------|-------|
| kiosk.html | kiosk_new.html, kiosk_staff.html | Similar kiosk interfaces could be consolidated |
| buylist.html | buylist_orders.html, buylist_top_purchased_products.html | Related buylist views |
| cardmarket.html | cardmarket_products.html, cardmarket_prices.html | Multiple cardmarket views |
| shopify.html | shopify_inventory.html, shopify_overview.html | Shopify related pages |
| mobile_card_scanning.html | mobile_card_scanning_connect.html, mobile_card_scanning_standalone.html | Mobile scanning variants |

## Optimization Opportunities

### 1. Template Consolidation
- Multiple Shopify-related templates could use a more modular approach
- Kiosk interfaces have several similar templates that could be unified
- Cardmarket views could be combined with dynamic tabs

### 2. Inheritance Improvements
- Many templates extend base.html but could benefit from more intermediate layouts
- Common UI components (tables, forms) could be broken into partials

### 3. Unused Templates
- Several templates appear deprecated:
  - ctswork.html, stswork.html (test/work templates)
  - sudoku.html, space_invaders.html (game templates)
  - poke_code.html (likely unused utility)

### 4. Naming Consistency
- Mix of singular/plural names (order.html vs orders.html)
- Some inconsistent naming patterns:
  - shopify-binder.html vs shopify.html
  - card_scanning.html vs card_scanning_trial.html

## Recommendations

1. **Create Template Partial System**:
   - Break common components into reusable partials
   - Standardize naming (_partial.html suffix)

2. **Implement Layout Hierarchy**:
   - Create intermediate layouts between base.html and specific pages
   - Standardize content blocks

3. **Template Cleanup**:
   - Archive unused templates
   - Consolidate duplicate/similar templates

4. **Naming Convention Enforcement**:
   - Choose singular or plural for resource names
   - Consistent prefix/suffix patterns
