#!/usr/bin/env python3
"""
MongoDB Configuration Update Script

This script updates the MongoDB connection configuration in config/config.py
to use the more robust mongo_connection_manager.py module. This provides better
error handling, connection pooling, and retry logic.

Usage:
    python update_mongo_config.py [--backup] [--apply]
"""

import os
import sys
import shutil
import argparse
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mongo_config_update")

def backup_file(file_path):
    """
    Create a backup of the specified file.
    
    Args:
        file_path (str): Path to the file to backup
        
    Returns:
        str: Path to the backup file
    """
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return None
        
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.bak_{timestamp}"
    
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Failed to create backup: {str(e)}")
        return None

def update_config_file(file_path, apply_changes=False):
    """
    Update the MongoDB connection configuration in config/config.py.
    
    Args:
        file_path (str): Path to the config file
        apply_changes (bool): Whether to apply the changes or just show them
        
    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(file_path):
        logger.error(f"Config file not found: {file_path}")
        return False
        
    try:
        # Read the current config file
        with open(file_path, 'r') as f:
            content = f.read()
            
        # Check if the file already uses mongo_connection_manager
        if "from mongo_connection_manager import" in content:
            logger.info(f"Config file {file_path} already uses mongo_connection_manager")
            return True
            
        # Create the updated content
        updated_content = update_config_content(content)
        
        # Show the changes
        print("\n" + "="*80)
        print(f"CHANGES TO {file_path}")
        print("="*80)
        print(updated_content)
        print("="*80)
        
        # Apply the changes if requested
        if apply_changes:
            # Create a backup first
            backup_path = backup_file(file_path)
            if not backup_path:
                logger.error("Failed to create backup, aborting update")
                return False
                
            # Write the updated content
            with open(file_path, 'w') as f:
                f.write(updated_content)
                
            logger.info(f"Updated config file: {file_path}")
            logger.info(f"Backup created at: {backup_path}")
            return True
        else:
            logger.info("Changes not applied. Use --apply to apply changes.")
            return True
            
    except Exception as e:
        logger.error(f"Error updating config file: {str(e)}")
        return False

def update_config_content(content):
    """
    Update the content of the config file to use mongo_connection_manager.
    
    Args:
        content (str): Current content of the config file
        
    Returns:
        str: Updated content
    """
    # Add import for mongo_connection_manager
    import_line = "from mongo_connection_manager import get_mongo_client, execute_with_retry\n"
    
    # Find the import section
    import_section_end = content.find("# Configure logging")
    if import_section_end == -1:
        import_section_end = content.find("class Config:")
    
    # Insert the import line
    if import_section_end != -1:
        content = content[:import_section_end] + import_line + content[import_section_end:]
    else:
        # If we can't find a good place, just add it at the top
        content = import_line + content
    
    # Replace the get_mongo_client method
    old_method = """    @staticmethod
    def get_mongo_client():
        \"\"\"
        Get a MongoDB client with the configured connection settings.
        
        Returns:
            MongoClient: A configured MongoDB client instance
        \"\"\"
        global _mongo_client_instance
        
        # Return existing client if already initialized
        if _mongo_client_instance is not None:
            return _mongo_client_instance
            
        try:
            client = MongoClient(
                Config.MONGO_URI,
                **Config.MONGO_CONNECTION_SETTINGS
            )
            
            # Force a connection to verify it works
            client.server_info()
            logger.info("Successfully connected to MongoDB cluster")
            
            # Store the client instance
            _mongo_client_instance = client
            return client
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB cluster: {str(e)}")
            raise"""
    
    new_method = """    @staticmethod
    def get_mongo_client():
        \"\"\"
        Get a MongoDB client with the configured connection settings.
        Uses the mongo_connection_manager module for better error handling and retry logic.
        
        Returns:
            MongoClient: A configured MongoDB client instance
        \"\"\"
        try:
            # Use the mongo_connection_manager to get a client with retry logic
            client = get_mongo_client(
                uri=Config.MONGO_URI,
                **Config.MONGO_CONNECTION_SETTINGS
            )
            
            # Test the connection with retry logic
            def test_connection():
                client.server_info()
                return client
                
            client = execute_with_retry(test_connection, max_retries=3, retry_delay=2)
            
            logger.info("Successfully connected to MongoDB cluster using connection manager")
            return client
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB cluster: {str(e)}")
            raise"""
    
    # Replace the method
    content = content.replace(old_method, new_method)
    
    # If the old method wasn't found exactly, try a more flexible approach
    if old_method not in content:
        # Find the get_mongo_client method
        method_start = content.find("    @staticmethod\n    def get_mongo_client(")
        if method_start != -1:
            # Find the end of the method
            method_end = content.find("    MONGODB_SETTINGS", method_start)
            if method_end != -1:
                # Replace the entire method
                content = content[:method_start] + new_method + content[method_end:]
    
    return content

def main():
    """Main function to parse arguments and run the update"""
    parser = argparse.ArgumentParser(description='MongoDB Configuration Update Script')
    parser.add_argument('--backup', action='store_true', help='Create a backup of the config file')
    parser.add_argument('--apply', action='store_true', help='Apply the changes to the config file')
    args = parser.parse_args()
    
    # Config file paths
    config_paths = [
        os.path.join('config', 'config.py'),
        'config.py'
    ]
    
    print("\n" + "="*80)
    print("MONGODB CONFIGURATION UPDATE")
    print("="*80)
    
    # Process each config file
    for config_path in config_paths:
        if os.path.exists(config_path):
            print(f"\nProcessing {config_path}...")
            
            # Create a backup if requested
            if args.backup and not args.apply:  # --apply already creates a backup
                backup_path = backup_file(config_path)
                if backup_path:
                    print(f"Backup created: {backup_path}")
            
            # Update the config file
            success = update_config_file(config_path, args.apply)
            
            if success:
                print(f"Successfully processed {config_path}")
            else:
                print(f"Failed to process {config_path}")
        else:
            print(f"Config file not found: {config_path}")
    
    # Summary
    print("\n" + "="*80)
    print("UPDATE SUMMARY")
    print("="*80)
    
    if args.apply:
        print("Changes have been applied to the config files.")
        print("To test the changes, run:")
        print("  python test_mongo_connection.py")
    else:
        print("No changes have been applied. To apply changes, run:")
        print("  python update_mongo_config.py --apply")
    
    print("\nTo revert to the original configuration, restore from the backup files.")

if __name__ == "__main__":
    main()
