#!/usr/bin/env python3
"""
Simple MongoDB Connection Test Script

This script tests the MongoDB connection using the exact same approach
as the successful migration script.

Usage:
    python simple_mongo_test.py
"""

import pymongo
import sys
import time
import threading

def log_message(message, level="INFO"):
    """Print a formatted log message with thread info."""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    thread_id = threading.current_thread().name
    print(f"[{timestamp}] [{level}] [{thread_id}] {message}")

def test_connection(uri, description="MongoDB"):
    """Test connection to a MongoDB server using the exact same approach as the migration script."""
    log_message(f"Testing connection to {description}...")
    
    try:
        # Create MongoDB client with minimal parameters - exactly like the migration script
        client = pymongo.MongoClient(uri, serverSelectionTimeoutMS=30000)
        
        # Force a connection to verify it works
        client.admin.command('ping')
        
        # Get server info
        server_info = client.server_info()
        version = server_info.get('version', 'unknown')
        
        log_message(f"Successfully connected to {description}")
        log_message(f"MongoDB version: {version}")
        
        # Close the connection
        client.close()
        return True
    except Exception as e:
        log_message(f"Failed to connect to {description}: {e}", "ERROR")
        return False

def main():
    """Main function to test MongoDB connections"""
    # MongoDB connection URIs - using the new mongodb+srv format
    atlas_uri = "mongodb+srv://admin:<EMAIL>/admin?replicaSet=replicaset&tls=true"
    old_uri = "mongodb://admin:Reggie2805!@*************:27017/?authSource=admin"
    
    print("\n" + "="*80)
    print("MONGODB CONNECTION TEST")
    print("="*80)
    
    # Test with new Atlas URI
    log_message("Testing with new MongoDB Atlas URI...")
    atlas_success = test_connection(atlas_uri, "MongoDB Atlas")
    
    # Test with old direct URI
    log_message("Testing with old direct URI...")
    old_success = test_connection(old_uri, "Old MongoDB")
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"MongoDB Atlas URI: {'SUCCESS' if atlas_success else 'FAILED'}")
    print(f"Old MongoDB URI: {'SUCCESS' if old_success else 'FAILED'}")
    
    # Recommendations
    print("\nRECOMMENDATIONS:")
    if atlas_success:
        print("- Use the new MongoDB Atlas URI")
        print("- Update all MongoDB connection strings to use the Atlas URI")
        print("- This connection requires TLS/SSL and uses the mongodb+srv protocol")
    elif old_success:
        print("- The old MongoDB connection still works")
        print("- Consider migrating to the new MongoDB Atlas instance")
    else:
        print("- Both connections failed")
        print("- Check network connectivity")
        print("- Verify that the MongoDB servers are running")
        print("- Check firewall settings and SSL/TLS requirements")
    
    # Update instructions
    print("\nTo update your application:")
    if atlas_success:
        print("1. Update the MongoDB URI in all configuration files to use the Atlas URI")
        print("2. Ensure SSL/TLS is enabled for the connection")
        print("3. Restart your application")
    else:
        print("1. Troubleshoot the connection issues before updating the application")

if __name__ == "__main__":
    main()
