<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Card Recognition</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<style>
    body {
        background-color: #121212;
        color: #e0e0e0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .card-scanning-table {
        background-color: #2c3e50;
        color: white;
    }
    .card-scanning-table th,
    .card-scanning-table td {
        color: white;
        vertical-align: middle;
    }
    .card-scanning-table tr.in-inventory {
        background-color: rgba(40, 167, 69, 0.3) !important;
    }
    .card-scanning-table img {
        max-width: 375px;
        max-height: 375px;
        object-fit: contain;
    }
    .form-group {
        margin-bottom: 0;
    }
    .form-control, .btn-file-input {
        height: 38px;
        padding: 0.375rem 0.75rem;
    }
    .card-body {
        padding: 1rem;
    }
    .file-input-wrapper {
        position: relative;
        overflow: hidden;
        display: inline-block;
        width: 100%;
    }
    .file-input-wrapper input[type=file] {
        font-size: 100px;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }
    .btn-file-input {
        border: 1px solid #ced4da;
        display: inline-block;
        width: 100%;
        cursor: pointer;
        background-color: #fff;
        color: #495057;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .form-label {
        margin-bottom: 0.25rem;
    }
    .actions-card {
        margin-bottom: 20px;
    }
    .actions-card .card-body {
        padding: 15px;
    }
    .actions-card .btn {
        margin-right: 10px;
        margin-bottom: 10px;
    }
    .form-check {
        padding-top: 0.5rem;
    }
    .form-check-input {
        cursor: pointer;
    }
    .form-check-label {
        cursor: pointer;
        user-select: none;
    }
    /* Approval Mode Modal Styles */
    .approval-modal .modal-dialog {
        max-width: 75%;
        margin: 1rem auto;
        height: 90vh;
    }
    .approval-modal .modal-content {
        background-color: #1a2634;
        color: white;
        border-radius: 12px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .approval-modal .modal-header {
        background-color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding: 1rem 1.5rem;
        border-radius: 12px 12px 0 0;
    }
    .approval-modal .modal-title {
        color: white;
        font-size: 1.5rem;
        font-weight: 600;
    }
    .approval-modal .modal-body {
        flex: 1;
        overflow-y: auto;
        padding: 1.5rem;
        display: flex;
        gap: 1.5rem;
    }
    .approval-modal .image-section {
        flex: 0 0 60%;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .approval-modal .image-comparison {
        display: flex;
        gap: 1rem;
        background-color: #2c3e50;
        padding: 1rem;
        border-radius: 12px;
        height: 450px;
    }
    .approval-modal .image-comparison > div {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
    }
    .approval-modal .image-comparison img {
        max-width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        transition: transform 0.3s ease;
    }
    .approval-modal .image-comparison img:hover {
        transform: scale(1.05);
    }
    .approval-modal .image-comparison h6 {
        margin: 0.5rem 0;
        color: #3498db;
        font-size: 1rem;
        font-weight: 600;
    }
    .approval-modal .search-section {
        flex: 0 0 40%;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .approval-modal .search-container {
        background-color: #2c3e50;
        padding: 1rem;
        border-radius: 12px;
    }
    .approval-modal .search-container .input-group {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .approval-modal .filter-controls {
        background-color: #34495e;
        padding: 0.75rem;
        border-radius: 8px;
        margin-top: 1rem;
    }
    .approval-modal .filter-controls select {
        background-color: #2c3e50;
        color: white;
        border: 1px solid #3498db;
        border-radius: 6px;
    }
    .approval-modal .filter-controls select:focus {
        border-color: #2ecc71;
        box-shadow: 0 0 0 0.2rem rgba(46, 204, 113, 0.25);
    }
    .approval-modal .search-results {
        flex: 1;
        overflow-y: auto;
        background-color: #2c3e50;
        border-radius: 12px;
        padding: 1rem;
    }
    .approval-modal .search-results .card {
        cursor: pointer;
        margin-bottom: 1rem;
        background-color: #34495e;
        border: 2px solid transparent;
        border-radius: 8px;
        transition: all 0.2s ease;
    }
    .approval-modal .search-results .card:hover {
        border-color: #3498db;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    .approval-modal .search-results .card.selected {
        border-color: #2ecc71;
        background-color: #27ae60;
    }
    .approval-modal .search-results .card-body {
        padding: 1rem;
    }
    .approval-modal .search-results img {
        max-width: 120px;
        max-height: 160px;
        object-fit: contain;
        border-radius: 4px;
    }
    .approval-modal .search-results .card-title {
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
    }
    .approval-modal .search-results .card-text {
        color: #bdc3c7;
        font-size: 0.9rem;
        line-height: 1.4;
    }
    .approval-modal .modal-footer {
        background-color: #2c3e50;
        border-top: 2px solid #3498db;
        padding: 1rem 1.5rem;
        border-radius: 0 0 12px 12px;
    }
    .approval-modal .btn {
        padding: 0.5rem 1.5rem;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
    }
    .approval-modal .btn-primary {
        background-color: #3498db;
        border-color: #3498db;
    }
    .approval-modal .btn-primary:hover:not(:disabled) {
        background-color: #2980b9;
        border-color: #2980b9;
        transform: translateY(-1px);
    }
    .approval-modal .btn-secondary {
        background-color: #95a5a6;
        border-color: #95a5a6;
    }
    .approval-modal .btn-secondary:hover {
        background-color: #7f8c8d;
        border-color: #7f8c8d;
        transform: translateY(-1px);
    }

    /* Card status styles */
    .card-scanning-table tr.approved {
        background-color: rgba(40, 167, 69, 0.2) !important;
    }
    .card-scanning-table tr.skipped {
        background-color: rgba(255, 193, 7, 0.2) !important;
    }
    .keyboard-shortcut {
        opacity: 0.7;
        font-size: 0.8em;
        margin-left: 0.5em;
    }

    /* Grid View Styles */
    .grid-view {
        display: none;
        gap: 2rem;
        padding: 2rem;
        background: linear-gradient(145deg, #1a2634, #243447);
        border-radius: 16px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .grid-view.active {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .grid-item {
        background: linear-gradient(145deg, #2c3e50, #34495e);
        border-radius: 16px;
        padding: 1.5rem;
        color: white;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        border: 1px solid rgba(52, 152, 219, 0.1);
    }

    .grid-item .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding: 0.5rem;
        background: rgba(26, 38, 52, 0.4);
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    .grid-item .card-details {
        display: none;
        transition: all 0.3s ease;
    }

    .grid-item .card-details.show {
        display: block;
    }

    .grid-item .toggle-details {
        background: none;
        border: none;
        color: #3498db;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .grid-item .toggle-details:hover {
        background: rgba(52, 152, 219, 0.1);
    }

    .grid-item .toggle-details i {
        transition: transform 0.3s ease;
    }

    .grid-item .toggle-details.expanded i {
        transform: rotate(180deg);
    }

    .grid-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
        border-color: rgba(52, 152, 219, 0.3);
    }

    .grid-item .grid-images {
        display: flex;
        gap: 2rem;
        margin-bottom: 1rem;
        justify-content: center;
        background: linear-gradient(145deg, #1a2634, #243447);
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .grid-item .grid-images img {
        width: 225px;
        height: 315px;
        object-fit: contain;
        border-radius: 8px;
        transition: transform 0.3s ease;
        cursor: pointer;
    }

    .grid-item .grid-images img:hover {
        transform: scale(1.1);
    }

    .grid-item h4 {
        color: #3498db;
        font-size: 1.4rem;
        margin: 0;
        font-weight: 600;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    }

    .grid-item p {
        margin: 0.5rem 0;
        color: #ecf0f1;
        font-size: 1.1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        background: rgba(26, 38, 52, 0.4);
        border-radius: 6px;
    }

    .grid-item p span:last-child {
        color: #3498db;
        font-weight: 600;
        background: rgba(52, 152, 219, 0.1);
        padding: 4px 10px;
        border-radius: 6px;
        min-width: 80px;
        text-align: center;
    }

    .grid-item .highlight-info {
        background: rgba(46, 204, 113, 0.15) !important;
        border-left: 4px solid #2ecc71;
        margin: 0.75rem 0 !important;
        padding: 0.75rem !important;
    }

    .grid-item .highlight-info .value-badge {
        background: rgba(46, 204, 113, 0.25) !important;
        color: #2ecc71 !important;
        font-weight: 700 !important;
        padding: 6px 12px !important;
        border-radius: 8px !important;
        min-width: 100px !important;
        display: inline-block;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .grid-item .highlight-info {
        background: rgba(46, 204, 113, 0.15) !important;
        border-left: 4px solid #2ecc71;
        margin: 0.75rem 0 !important;
        padding: 0.75rem !important;
    }

    .grid-item .highlight-info .value-badge {
        background: rgba(46, 204, 113, 0.25) !important;
        color: #2ecc71 !important;
        font-weight: 700 !important;
        padding: 6px 12px !important;
        border-radius: 8px !important;
        min-width: 100px !important;
        display: inline-block;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .grid-item .form-group {
        margin-bottom: 1rem;
    }

    .grid-item .form-group label {
        display: block;
        color: #3498db;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .grid-item select {
        background: #1a2634;
        color: white;
        border: 2px solid rgba(52, 152, 219, 0.3);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 1.1rem;
        transition: all 0.2s ease;
        width: 100%;
    }

    .grid-item select:hover {
        border-color: rgba(52, 152, 219, 0.5);
    }

    .grid-item select:focus {
        border-color: #2ecc71;
        box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.25);
        outline: none;
    }

    .grid-item .btn {
        margin-top: auto;
    }

    .table-view {
        display: none;
    }

    .table-view.active {
        display: block;
    }

    /* Zoom container for grid images */
    .zoom-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
        cursor: pointer;
    }

    .zoom-container img {
        max-width: 90%;
        max-height: 90vh;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    /* Staff Dashboard Topbar */
    .topbar {
        background-color: #1e1e1e;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        padding: 15px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        position: sticky;
        top: 0;
        z-index: 1000;
        backdrop-filter: blur(10px);
    }

    .topbar-brand {
        font-size: 1.5rem;
        font-weight: 700;
        color: #e0e0e0;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .topbar-brand i {
        color: #4a90e2;
    }

    .topbar-brand:hover {
        color: #4a90e2;
    }

    .topbar-actions {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #4a90e2;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.1rem;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .card {
        background-color: #2c3e50;
        color: white;
        border: none;
        border-radius: 16px;
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background: linear-gradient(135deg, #2c3e50, #1a202c);
        color: white;
        border-bottom: none;
        padding: 20px;
    }

    .btn-primary {
        background-color: #4a90e2;
        border-color: #4a90e2;
    }

    .btn-primary:hover {
        background-color: #357abd;
        border-color: #357abd;
    }

    .form-control, .form-select {
        background-color: #1a202c;
        color: white;
        border: 1px solid #4a5568;
    }

    .form-control:focus, .form-select:focus {
        background-color: #2c3e50;
        color: white;
        border-color: #4a90e2;
        box-shadow: 0 0 0 0.25rem rgba(74, 144, 226, 0.25);
    }

    .form-control::placeholder {
        color: #a0aec0;
    }
</style>

</head>
<body>
<!-- Topbar -->
<div class="topbar">
    <a href="{{ url_for('staff_auth.dashboard') }}" class="topbar-brand">
        <i class="fas fa-shield-alt"></i>TCGSync Staff
    </a>
    <div class="topbar-actions">
        <div class="user-menu">
            <div class="user-avatar">
                {{ staff.username[0].upper() if staff and staff.username else current_user.username[0].upper() }}
            </div>
            <span>{{ staff.name if staff and staff.name else (staff.username if staff else current_user.username) }}</span>
        </div>
    </div>
</div>

<!-- Breadcrumbs -->
<div class="container-fluid px-4 mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('staff_auth.dashboard') }}" class="text-decoration-none"><i class="fas fa-home"></i> Staff Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Card Scanning</li>
        </ol>
    </nav>
</div>

<div class="container">
    <style>
        /* Enhanced Toast Notification System */
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 400px;
            width: 100%;
        }

        .toast-notification {
            background: rgba(44, 62, 80, 0.95);
            color: white;
            border-radius: 12px;
            padding: 15px 20px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            transform: translateX(120%);
            transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(52, 152, 219, 0.3);
            opacity: 0;
            position: relative;
            overflow: hidden;
        }

        .toast-notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast-notification .toast-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .toast-notification .toast-icon {
            margin-right: 12px;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
        }

        .toast-notification .toast-title {
            font-weight: 600;
            font-size: 16px;
            margin: 0;
            flex-grow: 1;
        }

        .toast-notification .toast-close {
            background: none;
            border: none;
            color: rgba(255,255,255,0.7);
            cursor: pointer;
            padding: 0;
            font-size: 20px;
            transition: color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
        }

        .toast-notification .toast-close:hover {
            color: white;
        }

        .toast-notification .toast-message {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
            color: rgba(255,255,255,0.9);
        }

        .toast-notification.success {
            background: linear-gradient(145deg, rgba(46, 204, 113, 0.95), rgba(39, 174, 96, 0.95));
            border-left: 4px solid #2ecc71;
        }

        .toast-notification.success .toast-icon {
            color: #2ecc71;
        }

        .toast-notification.error {
            background: linear-gradient(145deg, rgba(231, 76, 60, 0.95), rgba(192, 57, 43, 0.95));
            border-left: 4px solid #e74c3c;
        }

        .toast-notification.error .toast-icon {
            color: #e74c3c;
        }

        .toast-notification.warning {
            background: linear-gradient(145deg, rgba(241, 196, 15, 0.95), rgba(243, 156, 18, 0.95));
            border-left: 4px solid #f1c40f;
        }

        .toast-notification.warning .toast-icon {
            color: #f1c40f;
        }

        .toast-notification.info {
            background: linear-gradient(145deg, rgba(52, 152, 219, 0.95), rgba(41, 128, 185, 0.95));
            border-left: 4px solid #3498db;
        }

        .toast-notification.info .toast-icon {
            color: #3498db;
        }

        .toast-notification .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 0 0 12px 12px;
            width: 100%;
        }

        .toast-notification .progress-bar-inner {
            height: 100%;
            width: 100%;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 0 0 12px 12px;
            transition: width linear;
        }

        /* Custom notification styles (for backward compatibility) */
        .custom-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 400px;
            background: rgba(44, 62, 80, 0.95);
            color: white;
            border-radius: 12px;
            padding: 15px 20px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            transform: translateX(120%);
            transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        .custom-notification.show {
            transform: translateX(0);
        }

        .custom-notification .notification-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .custom-notification .notification-icon {
            margin-right: 12px;
            font-size: 20px;
            color: #3498db;
        }

        .custom-notification .notification-title {
            font-weight: 600;
            font-size: 16px;
            margin: 0;
            flex-grow: 1;
        }

        .custom-notification .notification-close {
            background: none;
            border: none;
            color: rgba(255,255,255,0.7);
            cursor: pointer;
            padding: 0;
            font-size: 20px;
            transition: color 0.2s;
        }

        .custom-notification .notification-close:hover {
            color: white;
        }

        .custom-notification .notification-message {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
            color: rgba(255,255,255,0.9);
        }

        .custom-notification.warning {
            border-left: 4px solid #f1c40f;
        }

        .custom-notification.info {
            border-left: 4px solid #3498db;
        }

        .custom-notification.success {
            border-left: 4px solid #2ecc71;
        }

        .custom-notification.error {
            border-left: 4px solid #e74c3c;
        }

        @keyframes notification-progress {
            from { width: 100%; }
            to { width: 0%; }
        }

        .custom-notification .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: rgba(52, 152, 219, 0.5);
            border-radius: 0 0 12px 12px;
            animation: notification-progress 5s linear forwards;
        }
    </style>

    <!-- Notification popup -->
    <div id="notificationPopup" class="custom-notification warning" style="display: none;">
        <div class="notification-header">
            <i class="fas fa-exclamation-triangle notification-icon"></i>
            <h4 class="notification-title">Action Required</h4>
            <button type="button" class="notification-close" onclick="this.closest('.custom-notification').classList.remove('show');">&times;</button>
        </div>
        <p class="notification-message">Please select a game before scanning cards.</p>
        <div class="progress-bar"></div>
    </div>
    
    <!-- Scan limit popup -->
    <div id="scanLimitPopup" class="custom-notification info" style="display: none;">
        <div class="notification-header">
            <i class="fas fa-info-circle notification-icon"></i>
            <h4 class="notification-title">Scan Limit Status</h4>
            <button type="button" class="notification-close" onclick="this.closest('.custom-notification').classList.remove('show');">&times;</button>
        </div>
        <p class="notification-message"><span id="remainingScans">Loading...</span></p>
        <div class="progress-bar"></div>
    </div>
    
    <!-- Scan limit reached modal -->
    <div class="modal fade" id="scanLimitReachedModal" tabindex="-1" aria-labelledby="scanLimitReachedModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background-color: #2c3e50; color: white; border-radius: 12px;">
                <div class="modal-header" style="border-bottom: 2px solid #3498db;">
                    <h5 class="modal-title" id="scanLimitReachedModalLabel">Daily Scan Limit Reached</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning" style="background-color: rgba(255, 193, 7, 0.2); border-color: #ffc107; color: #ffc107;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span>You have reached your daily scan limit.</span>
                    </div>
                    <p>You have two options to continue scanning cards:</p>
                    <div class="d-grid gap-3 mt-4">
                        <a href="{{ url_for('activation.packages') }}" class="btn btn-primary" id="purchaseMoreScansBtn">
                            <i class="fas fa-shopping-cart me-2"></i>Purchase More Scans
                        </a>
                        <a href="{{ url_for('activation.packages') }}" class="btn btn-success">
                            <i class="fas fa-arrow-circle-up me-2"></i>Upgrade Your Package
                        </a>
                    </div>
                    <div class="mt-4">
                        <p class="text-center"><small>Upgrading to a Premium plan gives you unlimited scans!</small></p>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 2px solid #3498db;">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <h1>Staff Card Recognition</h1>
    <p>Total QTY: <span id="totalQty">0</span> | Unique Cards: <span id="uniqueCards">0</span> | Total Value: <span id="totalPrice">0.00</span> | Currency: <span id="currencyDisplay">Loading...</span> | <span id="scanLimitDisplay"></span> <a href="{{ url_for('activation.packages') }}" class="btn btn-sm btn-primary ms-2"><i class="fas fa-shopping-cart me-1"></i>Get More Scans</a></p>


    <div class="card mb-4">
        <div class="card-header">
            <h2 class="mb-0">Set Your Defaults</h2>
        </div>
        <div class="card-body">
            <div class="row align-items-end">
                <div class="col-md-3 form-group">
                    <label for="gameNameSelect" class="form-label">Game:</label>
                    <select id="gameNameSelect" class="form-control">
                        <option value="" disabled selected>Select a game</option>
                    </select>
                </div>
                <div class="col-md-3 form-group">
                    <label for="expansionNameSelect" class="form-label">Expansion:</label>
                    <select id="expansionNameSelect" class="form-control" disabled>
                        <option value="">Select an expansion</option>
                    </select>
                </div>
                <div class="col-md-2 form-group">
                    <label for="cardType" class="form-label">Card Type:</label>
                    <select id="cardType" class="form-control">
                        <option value="Normal">Normal</option>
                        <option value="Foil">Foil</option>
                        <option value="Reverse Holo">Reverse Holo</option>
                    </select>
                </div>
                <div class="col-md-2 form-group">
                    <label for="condition" class="form-label">Condition:</label>
                    <select id="condition" class="form-control">
                        <option value="NM">NM</option>
                        <option value="LP">LP</option>
                        <option value="MP">MP</option>
                        <option value="HP">HP</option>
                        <option value="DM">DM</option>
                    </select>
                </div>
                <div class="col-md-2 form-group">
                    <label for="language" class="form-label">Language:</label>
                    <select id="language" class="form-control">
                        <option value="EN" selected>English (EN)</option>
                        <option value="JP">Japanese (JP)</option>
                        <option value="DE">German (DE)</option>
                        <option value="FR">French (FR)</option>
                        <option value="IT">Italian (IT)</option>
                        <option value="ES">Spanish (ES)</option>
                        <option value="PT">Portuguese (PT)</option>
                        <option value="KR">Korean (KR)</option>
                        <option value="CS">Chinese Simplified (CS)</option>
                        <option value="CT">Chinese Traditional (CT)</option>
                        <option value="RU">Russian (RU)</option>
                    </select>
                </div>
                <div class="col-md-2 form-group">
                    <label class="form-label">&nbsp;</label>
                    <div class="file-input-wrapper">
                        <button class="btn btn-primary w-100">
                            <i class="fas fa-file me-2"></i>Choose Files
                        </button>
                        <input type="file" id="fileInput" accept="image/*" multiple>
                    </div>
                </div>
                <div class="col-md-2 form-group">
                    <label class="form-label">&nbsp;</label>
                    <button id="cameraButton" class="btn btn-primary w-100">
                        <i class="fas fa-camera me-2"></i>Use Camera
                    </button>
                </div>
                <div class="col-md-2 form-group">
                    <label class="form-label">&nbsp;</label>
                    <button id="loadMobileUploads" class="btn btn-primary w-100">
                        <i class="fas fa-mobile-alt me-2"></i>Load Mobile Images
                    </button>
                </div>
                <div class="col-md-2 form-group">
                    <label class="form-label">&nbsp;</label>
                    <a href="{{ url_for('mobile_uploads.mobile_uploads') }}" class="btn btn-info w-100">
                        <i class="fas fa-images me-2"></i>Mobile Images
                    </a>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="excludeListCards" checked>
                        <label class="form-check-label" for="excludeListCards">
                            Exclude List Cards
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="excludePromoCards" checked>
                        <label class="form-check-label" for="excludePromoCards">
                            Exclude Promo Cards
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="autoDeleteMobileUploads">
                        <label class="form-check-label" for="autoDeleteMobileUploads">
                            Auto Delete Mobile Uploads
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card actions-card">
        <div class="card-header">
            <h2 class="mb-0">Actions</h2>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col">
                    <button id="tcgPlayerQuicklistBtn" class="btn btn-primary">TCGPlayer Quicklist</button>
                    <button id="ccgSellerBtn" class="btn btn-primary">CCG Seller</button>
                    <button id="tcgPlayerCsvBtn" class="btn btn-primary">TCGPlayer CSV</button>
                    <button id="scryfallCsvBtn" class="btn btn-primary">Scryfall CSV</button>
                    <button id="cardmarketCsvBtn" class="btn btn-primary">Cardmarket CSV</button>
                    <button id="uncombinedCsvBtn" class="btn btn-primary">Uncombined CSV</button>
                    <button id="moxfieldCsvBtn" class="btn btn-primary">Moxfield CSV</button>
                    <button id="sendToCardMarketBtn" class="btn btn-primary">Send to CardMarket</button>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <button id="viewToggleBtn" class="btn btn-info">Show Table</button>
                    <button id="sendToWarehouseBtn" class="btn btn-success">Send to Warehouse</button>
                    <button id="clearAllBtn" class="btn btn-danger">Clear All</button>
                </div>
            </div>
        </div>
    </div>

    <div id="loadingScreen" class="d-none" style="margin: 2rem 0; padding: 2rem; background: rgba(0,0,0,0.1); border-radius: 8px;">
        <div class="text-center">
            <h3 class="mb-4">Processing Cards</h3>
            <div class="progress mb-3" style="height: 30px;">
                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-primary" 
                     role="progressbar" style="width: 0%; font-size: 16px; line-height: 30px;" 
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <p id="progressText" class="h5 mt-3">Processed 0 of 0 cards</p>
            <div id="processingStatus" class="mt-3 text-muted">
                <i class="fas fa-sync fa-spin"></i> Scanning cards...
            </div>
        </div>
    </div>

    <div id="result"></div>
</div>

<!-- Approval Mode Modal -->
<div class="modal approval-modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approve Card Match</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="image-section">
                    <div class="image-comparison">
                        <div>
                            <h6>Scanned Image</h6>
                            <img id="scannedImage" src="" alt="Scanned Card">
                        </div>
                        <div>
                            <h6>Matched Image</h6>
                            <img id="matchedImage" src="" alt="Matched Card">
                        </div>
                    </div>
                </div>
                <div class="search-section">
                    <div class="search-container">
                        <div class="input-group">
                            <input type="text" id="cardSearchInput" class="form-control" placeholder="Search for a different card...">
                            <button class="btn btn-primary" id="cardSearchBtn">Search</button>
                        </div>
                        <div class="filter-controls mt-3">
                            <div class="row">
                                <div class="col-6">
                                    <select id="setFilter" class="form-control">
                                        <option value="">All Sets</option>
                                    </select>
                                </div>
                                <div class="col-6">
                                    <select id="numberFilter" class="form-control">
                                        <option value="">All Numbers</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="search-results" id="cardSearchResults">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <button type="button" class="btn btn-secondary" id="prevCardBtn">
                            <i class="fas fa-arrow-left"></i> Previous
                            <span class="keyboard-shortcut">[←]</span>
                        </button>
                        <button type="button" class="btn btn-warning" id="skipCardBtn">
                            <i class="fas fa-forward"></i> Skip
                            <span class="keyboard-shortcut">[S]</span>
                        </button>
                        <button type="button" class="btn btn-secondary" id="nextCardBtn">
                            Next <i class="fas fa-arrow-right"></i>
                            <span class="keyboard-shortcut">[→]</span>
                        </button>
                        <span class="ms-3 text-light" id="cardCounter">Card 0 of 0</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="approveMatchBtn" disabled>
                            Approve Match
                            <span class="keyboard-shortcut">[Enter]</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<!-- Store URLs in hidden inputs -->
<input type="hidden" id="upgradeUrl" value="{{ url_for('activation.packages') }}">

<script src="{{ url_for('static', filename='js/card_scanning.js') }}"></script>
<script src="{{ url_for('static', filename='js/card_scanning_camera.js') }}"></script>
<script src="{{ url_for('static', filename='js/tcgplayer_csv_export.js') }}"></script>
<script src="{{ url_for('static', filename='js/scryfall_csv_export.js') }}"></script>
<script src="{{ url_for('static', filename='js/ccg_seller_export.js') }}"></script>
<script src="{{ url_for('static', filename='js/moxfield_csv_export.js') }}"></script>
<script src="{{ url_for('static', filename='js/staff_dashboard.js') }}"></script>
<script>
    // Create toast container if it doesn't exist
    document.addEventListener('DOMContentLoaded', function() {
        if (!document.getElementById('toast-container')) {
            const toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            document.body.appendChild(toastContainer);
        }
    });

    // Function to show toast notifications
    function showToast(type, title, message, duration = 5000) {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast-notification ${type}`;
        
        // Get icon based on type
        let icon = 'fa-info-circle';
        if (type === 'success') icon = 'fa-check-circle';
        if (type === 'error') icon = 'fa-exclamation-circle';
        if (type === 'warning') icon = 'fa-exclamation-triangle';
        
        // Create toast content
        toast.innerHTML = `
            <div class="toast-header">
                <div class="toast-icon"><i class="fas ${icon}"></i></div>
                <h4 class="toast-title">${title}</h4>
                <button type="button" class="toast-close">&times;</button>
            </div>
            <p class="toast-message">${message}</p>
            <div class="progress-bar">
                <div class="progress-bar-inner"></div>
            </div>
        `;
        
        // Add to container
        toastContainer.appendChild(toast);
        
        // Show toast with animation
        setTimeout(() => {
            toast.classList.add('show');
            
            // Animate progress bar
            const progressBar = toast.querySelector('.progress-bar-inner');
            progressBar.style.width = '100%';
            progressBar.style.transition = `width ${duration}ms linear`;
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 10);
        }, 10);
        
        // Set up auto-dismiss
        const dismissTimeout = setTimeout(() => {
            dismissToast(toast);
        }, duration);
        
        // Set up close button
        const closeButton = toast.querySelector('.toast-close');
        closeButton.addEventListener('click', () => {
            clearTimeout(dismissTimeout);
            dismissToast(toast);
        });
        
        // Return the toast element
        return toast;
    }
    
    // Function to dismiss a toast
    function dismissToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300); // Wait for fade out animation
    }

    // Function to check if a game is selected
    function isGameSelected() {
        const gameSelect = document.getElementById('gameNameSelect');
        return gameSelect && gameSelect.value !== "";
    }

    // Function to show notification popup
    function showGameRequiredNotification() {
        const popup = document.getElementById('notificationPopup');
        popup.style.display = 'block';
        popup.classList.add('show');
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            popup.classList.remove('show');
            setTimeout(() => {
                popup.style.display = 'none';
            }, 300); // Wait for fade out animation
        }, 5000);
    }

    // Add validation to file input
    document.getElementById('fileInput').addEventListener('click', function(e) {
        if (!isGameSelected()) {
            e.preventDefault();
            e.stopPropagation();
            showGameRequiredNotification();
        }
    });

    // Add validation to camera button
    document.getElementById('cameraButton').addEventListener('click', function(e) {
        if (!isGameSelected()) {
            e.preventDefault();
            showGameRequiredNotification();
            return false;
        }
    });

    // Add validation to load mobile uploads button
    document.getElementById('loadMobileUploads').addEventListener('click', function(e) {
        if (!isGameSelected()) {
            e.preventDefault();
            showGameRequiredNotification();
            return false;
        }
    });

    // Add validation to mobile images link
    const mobileImagesLink = document.querySelector('a[href*="mobile_uploads"]');
    if (mobileImagesLink) {
        mobileImagesLink.addEventListener('click', function(e) {
            if (!isGameSelected()) {
                e.preventDefault();
                showGameRequiredNotification();
                return false;
            }
        });
    }

    // Attach CSV export functions to buttons
    document.getElementById('tcgPlayerCsvBtn').addEventListener('click', generateAndDownloadTCGPlayerCSV);
    document.getElementById('scryfallCsvBtn').addEventListener('click', generateAndDownloadScryfallCSV);
    document.getElementById('ccgSellerBtn').addEventListener('click', generateAndDownloadCCGSellerCSV);
    document.getElementById('moxfieldCsvBtn').addEventListener('click', generateAndDownloadMoxfieldCSV);
    
    // Completely replace the warehouse button functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Function to create our custom warehouse button
        function replaceWarehouseButton() {
            // Find the original button
            const originalBtn = document.getElementById('sendToWarehouseBtn');
            if (!originalBtn) return;
            
            // Hide the original button
            originalBtn.style.display = 'none';
            
            // Create a new button with the same styling
            const newBtn = document.createElement('button');
            newBtn.id = 'customWarehouseBtn';
            newBtn.className = originalBtn.className;
            newBtn.innerHTML = originalBtn.innerHTML;
            newBtn.setAttribute('type', 'button');
            
            // Insert the new button after the original
            originalBtn.parentNode.insertBefore(newBtn, originalBtn.nextSibling);
            
            // Add event listener to the new button
            newBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Custom warehouse button clicked');
                
                // Disable the button immediately
                this.disabled = true;
                this.classList.remove('btn-success');
                this.classList.add('btn-secondary');
                this.setAttribute('title', 'Cards have been sent to warehouse. Refresh the page to scan more cards.');
                
                // Check if there's a table with results
                const table = document.querySelector('.card-scanning-table');
                if (!table) {
                    console.error('No card-scanning-table found');
                    showToast('warning', 'Warning', 'No results to send to warehouse.');
                    
                    // Re-enable the button
                    this.disabled = false;
                    this.classList.remove('btn-secondary');
                    this.classList.add('btn-success');
                    this.removeAttribute('title');
                    return;
                }
                
                // Get the card data
                const rows = Array.from(table.querySelectorAll('tbody tr'));
                console.log(`Found ${rows.length} rows in the table`);
                
                const cards = rows.map(row => {
                    const cells = Array.from(row.querySelectorAll('td'));
                    return {
                        name: cells[1].textContent,
                        set: cells[2].textContent,
                        number: cells[3].textContent,
                        condition: cells[4].querySelector('select').value,
                        sku_id: cells[7].textContent,
                        low_price: parseFloat(cells[8].textContent.replace(/[^0-9.-]+/g,"")),
                        market_price: parseFloat(cells[9].textContent.replace(/[^0-9.-]+/g,"")),
                        id_product: cells[10].textContent,
                        category_id: cells[11].textContent,
                        quantity: parseInt(cells[12].textContent) || 1
                    };
                });
                
                console.log('Warehouse data prepared:', cards);
                
                // Send data to the server
                fetch('/card_scanning/send_to_warehouse', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ cards: cards }),
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Server response:', data);
                    if (data.message) {
                        showToast('success', 'Success', data.message);
                        if (data.cardmarket_message) {
                            setTimeout(() => {
                                showToast('info', 'CardMarket', data.cardmarket_message);
                            }, 500);
                        }
                    } else {
                        showToast('success', 'Success', 'Data successfully sent to warehouse and CardMarket.');
                    }
                })
                .catch(error => {
                    console.error('Error sending data to warehouse and CardMarket:', error);
                    showToast('error', 'Error', 'An error occurred while sending data. Please check the console for more details.');
                    
                    // Re-enable the button if there was an error
                    this.disabled = false;
                    this.classList.remove('btn-secondary');
                    this.classList.add('btn-success');
                    this.removeAttribute('title');
                });
            });
        }
        
        // Wait for all scripts to load, then replace the button
        setTimeout(replaceWarehouseButton, 1000);
    });
</script>
</body>
</html>
