from config.config import Config
import requests
import json
import sys

def test_ebay_listing_creation():
    """
    Test the eBay listing creation endpoint with a real product from the database.
    This test uses the product with ID 522791 (Abundance (Foil) [B]) for the user 'admintcg'.
    """
    print("Starting eBay listing creation test...")
    
    # Product data for the test
    product_id = 522791
    variant_sku = "7489066"  # The SKU for the Near Mint variant
    
    # Prepare the request data
    request_data = {
        "productId": product_id,
        "variantSku": variant_sku,
        "title": "Abundance (Foil) [B]",
        "description": "<ul>\n<li>\n<strong>Rarity:</strong> Elite</li>\n<li>\n<strong>Description:</strong> Each affected site provides one additional mana.</li>\n<li>\n<strong>Cost:</strong> 5</li>\n<li>\n<strong>Threshold:</strong> WW</li>\n<li>\n<strong>Element:</strong> Water</li>\n<li>\n<strong>Type Line:</strong> An Elite Aura of milk and honey</li>\n<li>\n<strong>Card Category:</strong> Spell</li>\n<li>\n<strong>Card Type:</strong> Aura</li>\n<li>\n<strong>Grid:</strong> false</li>\n</ul><p>Each affected site provides one additional mana.</p>",
        "price": 21.99,
        "quantity": 2,
        "condition": "40002"  # Near Mint
    }
    
    # Send the request to the new endpoint
    try:
        print(f"Sending request to create eBay listing for product ID {product_id}, variant SKU {variant_sku}...")
        response = requests.post(
            "http://localhost:8000/ebay/automation/create-listing-new",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        # Print the response status code and content
        print(f"Response status code: {response.status_code}")
        print(f"Response content: {response.text}")
        
        # Parse the response JSON
        try:
            response_data = response.json()
            
            # Check if the request was successful
            if response.status_code == 200 and response_data.get('success'):
                print("✅ Test PASSED: eBay listing created successfully!")
                print(f"Listing ID: {response_data.get('listing_id')}")
                print(f"Listing URL: {response_data.get('listing_url')}")
                return True
            else:
                print("❌ Test FAILED: eBay listing creation failed.")
                print(f"Error: {response_data.get('error', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print("❌ Test FAILED: Could not parse response JSON.")
            print(f"Response text: {response.text}")
            return False
    except requests.RequestException as e:
        print(f"❌ Test FAILED: Request exception: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_ebay_listing_creation()
    sys.exit(0 if success else 1)

