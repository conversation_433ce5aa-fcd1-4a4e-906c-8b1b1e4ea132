from config.config import Config
import requests
from flask import current_app
from models.email_record_model import EmailRecord
import logging
import time
from datetime import datetime, timedelta
from mongoengine.queryset.visitor import Q

logger = logging.getLogger(__name__)

def send_email(username, to, subject, content, cc=None, is_html=False, attachments=None, max_retries=3, retry_delay=5):
    retries = 0
    while retries <= max_retries:
        try:
            logger.info(f"Attempting to send email to {to} with subject '{subject}' (Attempt {retries + 1}/{max_retries + 1})")
            
            data = {
                "from": f"Admin TCG Sync <admin@{current_app.config['MAILGUN_DOMAIN']}>",
                "to": to,
                "subject": subject,
            }
            if is_html:
                data["html"] = content
            else:
                data["text"] = content

            if cc:
                data["cc"] = cc

            files = []
            if attachments:
                for attachment in attachments:
                    if isinstance(attachment, tuple):
                        # If attachment is (filename, file_data) tuple
                        files.append(("attachment", attachment))
                    else:
                        # If attachment is a file path
                        files.append(("attachment", open(attachment, 'rb')))

            response = requests.post(
                f"https://api.eu.mailgun.net/v3/{current_app.config['MAILGUN_DOMAIN']}/messages",
                auth=("api", current_app.config['MAILGUN_API_KEY']),
                data=data,
                files=files
            )

            # Close any opened file handles
            for _, file_obj in files:
                if hasattr(file_obj, 'close'):
                    file_obj.close()
            
            response.raise_for_status()
            logger.info(f"Email sent successfully with status code {response.status_code}")

            # Extract Mailgun ID from the response
            mailgun_id = response.json().get('id')
            logger.info(f"Mailgun ID: {mailgun_id}")

            # Record the email in the database
            email_record = EmailRecord(
                username=username,
                to=to,
                cc=cc,
                subject=subject,
                text=content,
                status_code=str(response.status_code),
                mailgun_id=mailgun_id
            )
            email_record.save()
            logger.info(f"Email record saved for user {username}")

            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Error sending email (Attempt {retries + 1}/{max_retries + 1}): {str(e)}")
            logger.error(f"Request details: URL={e.request.url}, Method={e.request.method}, Headers={e.request.headers}")
            if e.response:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response content: {e.response.text}")
            
            # If we've reached max retries, record the failure
            if retries >= max_retries:
                # Record the failed email attempt
                EmailRecord(
                    username=username,
                    to=to,
                    cc=cc,
                    subject=subject,
                    text=content,
                    status_code="Error",
                    mailgun_id=f"Failed after {max_retries + 1} attempts: {str(e)}"
                ).save()
                logger.info(f"Failed email record saved for user {username} after {max_retries + 1} attempts")
                return None
            
            # Wait before retrying
            retries += 1
            logger.info(f"Retrying in {retry_delay} seconds... ({retries}/{max_retries})")
            time.sleep(retry_delay)
            # Increase delay for next retry (exponential backoff)
            retry_delay *= 2

def check_pending_verification_emails(hours=24):
    """
    Check for users who registered in the last X hours but haven't verified their email.
    Returns a list of users who need verification email resent.
    """
    cutoff_time = datetime.utcnow() - timedelta(hours=hours)
    
    # Find users who:
    # 1. Registered within the cutoff time
    # 2. Have not verified their email
    # 3. Have a verification token (meaning they should have received an email)
    from models.user_model import User
    
    pending_users = User.objects(
        Q(registered_on__gte=cutoff_time) &
        Q(email_verified=False) &
        Q(email_verification_token__ne=None)
    )
    
    logger.info(f"Found {pending_users.count()} users with pending email verification")
    return pending_users

def resend_verification_emails(max_age_hours=24):
    """
    Resend verification emails to users who registered recently but haven't verified their email.
    Only resends to users who registered within max_age_hours.
    """
    from flask import url_for
    
    pending_users = check_pending_verification_emails(hours=max_age_hours)
    resent_count = 0
    
    for user in pending_users:
        # Check if we've already sent multiple verification emails
        email_records = EmailRecord.objects(
            username=user.username,
            subject="Verify Your TCGSync Email",
            created_at__gte=user.registered_on
        )
        
        # If we've already sent 3 or more verification emails, skip this user
        if email_records.count() >= 3:
            logger.info(f"Skipping resend for user {user.username} - already sent {email_records.count()} verification emails")
            continue
        
        # Generate a new token if the old one is more than 12 hours old
        token_age = datetime.utcnow() - user.email_verification_sent_at
        if token_age > timedelta(hours=12):
            token = user.generate_email_verification_token()
        else:
            token = user.email_verification_token
        
        if token:
            try:
                # We need to use _external=True but we can't use url_for directly here
                # since we're not in a request context, so we'll construct the URL manually
                base_url = current_app.config.get('BASE_URL', 'https://login.tcgsync.com')
                verification_link = f"{base_url}/verify-email/{token}"
                
                subject = "Verify Your TCGSync Email"
                body = f'''
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <h2 style="color: #007bff;">TCGSync Email Verification</h2>
                    <p>Dear {user.username},</p>
                    <p>Please verify your email address by clicking the link below:</p>
                    <p><a href="{verification_link}">{verification_link}</a></p>
                    <p>This link will expire in 24 hours.</p>
                    <p>If you did not create this account, please ignore this email.</p>
                    <p>Best regards,<br>The TCGSync Team</p>
                </body>
                </html>
                '''
                
                # Send with higher retry count for verification emails
                send_email(user.username, user.email, subject, body, is_html=True, max_retries=5)
                resent_count += 1
                logger.info(f"Resent verification email to {user.username} ({user.email})")
            except Exception as e:
                logger.error(f"Error resending verification email to {user.username}: {str(e)}")
    
    logger.info(f"Resent verification emails to {resent_count} users")
    return resent_count

