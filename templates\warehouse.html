{% extends "base.html" %}
{% block title %}Warehouse{% endblock %}
{% block content %}
<div class="container-fluid mt-5">
    <div class="row">
        <div class="col-12 text-white mb-3">
            <small>Hold the control button when dragging</small>
        </div>
        <!-- Notice Section -->
        <div class="col-12 text-white mb-3">
            <strong>Note:</strong> You can only process each queue once before coming back to the page. This is a safeguard against duplicates. Please ensure all queues are processed before refreshing or leaving the page.
        </div>
        <!-- Staged Inventory Section -->
        <div class="col-md-4">
            <!-- Staged Inventory -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <a class="d-flex justify-content-between align-items-center" data-toggle="collapse" href="#stagedInventoryCollapse" role="button" aria-expanded="false" aria-controls="stagedInventoryCollapse">
                            Staged Inventory
                            <span id="stagedInventoryCounter" class="badge">0</span>
                        </a>
                    </h5>
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <button id="selectAllStagedBtn" class="btn btn-primary btn-sm">Select All</button>
                                <button id="deleteAllStagedBtn" class="btn btn-danger btn-sm">Delete All</button>
                            </div>
                            <input type="text" id="stagedInventorySearch" class="form-control form-control-sm" style="max-width: 200px;" placeholder="Search inventory...">
                        </div>
                        <div class="d-flex gap-2 mt-2">
                            <select id="setFilter" class="form-select form-select-sm w-100">
                                <option value="">All Sets</option>
                            </select>
                        </div>
                    </div>
                    <div class="collapse show" id="stagedInventoryCollapse">
                        <div class="table-responsive">
                            <table class="table table-striped table-dark" id="stagedInventoryTable">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Set</th>
                                        <th>Condition</th>
                                        <th>Print Type</th>
                                        <th>Quantity</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="stagedInventoryBody">
                                    <!-- Staged inventory items will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">Shopify</h5>
                    <div class="mb-3">
                        <button class="btn btn-success" id="processShopifyBtn">Process Shopify</button>
                        <button class="btn btn-warning" id="retryFailedBtn" style="display: none;">Retry Failed Items</button>
                        <!-- Status filter dropdown -->
                        <select id="shopifyStatusFilter" class="form-select form-select-sm d-inline-block ml-2" style="width: auto; margin-left: 10px;">
                            <option value="all">All Items</option>
                            <option value="new">New Items</option>
                            <option value="update">Update Items</option>
                        </select>
                    </div>
                    <div id="shopifyDropzone" class="dropzone">
                        <p>Drop items here</p>
                        <div class="table-responsive">
                            <table class="table table-striped table-dark dropzone-table" id="shopifyTable">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Quantity</th>
                                        <th>Variant</th>
                                        <th>Price</th>
                                        <th>Location ID</th>
                                        <th>Variant ID</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="shopifyBody">
                                    <!-- Shopify items will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<template id="stagedInventoryRowTemplate">
    <tr>
        <td class="item-name"></td>
        <td class="expansion-name"></td>
        <td class="condition"></td>
        <td class="print-type"></td>
        <td>
            <input type="number" class="form-control form-control-sm quantity-input" min="1">
        </td>
        <td>
            <button class="btn btn-link btn-sm delete-btn p-0" style="color: red; font-size: 1.2em;">❌</button>
        </td>
    </tr>
</template>

<style>
    #loadingOverlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    #loadingContent {
        text-align: center;
        color: white;
        max-width: 80%;
    }
    #loadingProgress {
        width: 100%;
        max-width: 400px;
        margin: 20px 0;
    }
    .progress-bar {
        height: 20px;
        background-color: #2ecc71;
        width: 0%;
        transition: width 0.3s ease;
        border-radius: 10px;
    }
    .progress-container {
        width: 100%;
        background-color: #34495e;
        border-radius: 10px;
        padding: 3px;
    }
    #loadingStatus {
        color: white;
        margin: 10px 0;
        font-size: 14px;
    }
    #loadingJoke {
        color: white;
        text-align: center;
        max-width: 80%;
        margin-top: 20px;
        font-style: italic;
    }
    #retryCount {
        color: #e74c3c;
        font-size: 12px;
        margin-top: 5px;
    }
    .card {
        border-radius: 10px;
        background-color: #2c3e50;
    }
    .card-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #ffffff;
    }
    .card-body {
        padding: 20px;
    }
    .table {
        margin-top: 10px;
        color: #ffffff;
        font-size: 0.875rem;
    }
    .table th, .table td {
        color: #ffffff;
        vertical-align: middle;
        padding: 0.5rem;
    }
    .table-dark {
        background-color: #34495e;
    }
    .dropzone {
        border: 2px dashed #ccc;
        border-radius: 10px;
        text-align: center;
        cursor: pointer;
        padding: 20px;
        color: #ffffff;
        background-color: #34495e;
    }
    .btn-group {
        display: flex;
        gap: 10px;
    }
    .btn-group .btn {
        flex: 1;
    }
    .dropzone-table {
        margin-bottom: 0;
    }
    /* Override the table-striped pattern for selected rows with maximum specificity */
    .table-striped tbody tr.selected,
    .table-striped tbody tr.selected td,
    .table-striped tbody tr:nth-of-type(odd).selected,
    .table-striped tbody tr:nth-of-type(even).selected,
    .table-striped tbody tr:nth-of-type(odd).selected td,
    .table-striped tbody tr:nth-of-type(even).selected td,
    #stagedInventoryTable tbody tr.selected,
    #stagedInventoryTable tbody tr.selected td,
    #stagedInventoryTable tbody tr:nth-of-type(odd).selected,
    #stagedInventoryTable tbody tr:nth-of-type(even).selected,
    #stagedInventoryTable tbody tr:nth-of-type(odd).selected td,
    #stagedInventoryTable tbody tr:nth-of-type(even).selected td {
        background-color: #3498db !important; /* Bright blue */
        color: white !important;
        font-weight: bold;
        border: 2px solid #2980b9 !important;
        box-shadow: 0 0 5px rgba(52, 152, 219, 0.5) !important;
    }
    .badge {
        font-size: 1rem;
        padding: 0.5rem;
        border-radius: 0.5rem;
    }
    .action-btn {
        cursor: pointer;
        color: #ff6347;
    }
    .drag-over {
        border-color: #00ff00;
    }
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        min-width: 200px;
        padding: 15px;
        background-color: #333;
        color: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }
    .toast.show {
        opacity: 1;
    }
    .toast-success {
        background-color: #28a745;
    }
    .toast-error {
        background-color: #dc3545;
    }
    .toast-warning {
        background-color: #ffc107;
        color: #333;
    }
    .toast-info {
        background-color: #17a2b8;
    }
    .failed-item {
        background-color: rgba(220, 53, 69, 0.3) !important;
    }
    .success-item {
        background-color: rgba(40, 167, 69, 0.3) !important;
    }
</style>

<script>
let failedItems = new Set();
let processingQueue = [];
let isProcessing = false;

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

document.addEventListener('DOMContentLoaded', function() {
    let selectedRows = new Set();
    
    // Add loading overlay
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loadingOverlay';
    loadingOverlay.style.display = 'none';
    loadingOverlay.innerHTML = `
        <div id="loadingContent">
            <div class="spinner-border text-light" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <div id="loadingProgress">
                <div id="loadingStatus">Processing items...</div>
                <div class="progress-container">
                    <div class="progress-bar"></div>
                </div>
                <div id="retryCount"></div>
            </div>
            <div id="loadingJoke"></div>
        </div>
    `;
    document.body.appendChild(loadingOverlay);

    // Initialize staged inventory
    fetchStagedInventory();

    // Set up button event listeners
    const processShopifyBtn = document.getElementById('processShopifyBtn');
    const retryFailedBtn = document.getElementById('retryFailedBtn');

    if (processShopifyBtn) {
        processShopifyBtn.addEventListener('click', () => {
            if (!processShopifyBtn.disabled) {
                processShopifyBtn.disabled = true;
                processShopify()
                    .then(() => {
                        console.log('Processing completed successfully');
                        checkFailedItems();
                    })
                    .catch((error) => {
                        console.error('Processing failed:', error);
                        showToast('Error processing items. Check console for details.', 'error');
                    })
                    .finally(() => {
                        processShopifyBtn.disabled = false;
                    });
            }
        });
    }

    if (retryFailedBtn) {
        retryFailedBtn.addEventListener('click', () => {
            if (!retryFailedBtn.disabled) {
                retryFailedBtn.disabled = true;
                retryFailedItems()
                    .then(() => {
                        console.log('Retry completed successfully');
                        checkFailedItems();
                    })
                    .catch((error) => {
                        console.error('Retry failed:', error);
                        showToast('Error retrying items. Check console for details.', 'error');
                    })
                    .finally(() => {
                        retryFailedBtn.disabled = false;
                    });
            }
        });
    }

    function checkFailedItems() {
        const hasFailedItems = failedItems.size > 0;
        retryFailedBtn.style.display = hasFailedItems ? 'inline-block' : 'none';
        if (hasFailedItems) {
            showToast(`${failedItems.size} items failed to process. Click 'Retry Failed Items' to try again.`, 'warning');
        }
    }

    async function retryFailedItems() {
        const failedItemsArray = Array.from(failedItems);
        failedItems.clear();
        
        showLoading();
        resetProgress();
        
        try {
            const locations = await getShopifyLocations();
            if (!locations || locations.length === 0) {
                throw new Error('No Shopify locations available');
            }
            
            const locationId = await showLocationSelectionPopup(locations);
            if (!locationId) {
                throw new Error('No location selected');
            }
            
            await processItems(failedItemsArray, locationId);
            showToast('Retry completed successfully', 'success');
        } catch (error) {
            console.error('Error retrying items:', error);
            showToast('Error retrying items: ' + error.message, 'error');
        } finally {
            hideLoading();
            checkFailedItems();
        }
    }

    async function processItems(items, locationId, isRetry = false) {
        const batchSize = 5;
        const totalItems = items.length;
        let processedCount = 0;
        
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            updateProgress(processedCount, totalItems);
            
            try {
                const response = await fetch('/warehouse/process_shopify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        items: batch,
                        locationId: locationId
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const results = await response.json();
                
                results.results.forEach((result, index) => {
                    const item = batch[index];
                    const row = document.querySelector(`tr[data-sku-id="${item.skuId}"]`);
                    
                    if (row) {
                        if (result.status === 'success') {
                            row.classList.remove('failed-item');
                            row.classList.add('success-item');
                            failedItems.delete(item.skuId);
                        } else {
                            row.classList.remove('success-item');
                            row.classList.add('failed-item');
                            failedItems.add(item.skuId);
                        }
                        
                        const statusCell = row.querySelector('.status-cell');
                        if (statusCell) {
                            statusCell.textContent = `${result.status} - ${result.action || 'unknown'}`;
                        }
                    }
                });
                
                processedCount += batch.length;
                
                // Add delay between batches
                if (i + batchSize < items.length) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                // If this is the last batch, refresh the staged inventory
                if (i + batch.length >= items.length) {
                    // Refresh the staged inventory table
                    fetchStagedInventory();
                }
            } catch (error) {
                console.error(`Error processing batch starting at index ${i}:`, error);
                batch.forEach(item => failedItems.add(item.skuId));
                showToast(`Error processing batch: ${error.message}`, 'error');
            }
        }
    }

    async function getShopifyLocations() {
        try {
            const response = await fetch('/warehouse/get_shopify_locations');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }
            return data.locations;
        } catch (error) {
            console.error('Error fetching Shopify locations:', error);
            showToast('Error fetching Shopify locations: ' + error.message, 'error');
            return null;
        }
    }

    function showLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
            fetchRandomJoke();
            resetProgress();
        }
    }

    function hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    function fetchRandomJoke() {
        fetch('https://official-joke-api.appspot.com/random_joke')
            .then(response => response.json())
            .then(data => {
                const jokeElement = document.getElementById('loadingJoke');
                if (jokeElement) {
                    jokeElement.innerHTML = `<p>${data.setup}</p><p><em>${data.punchline}</em></p>`;
                }
            })
            .catch(error => {
                console.error('Error fetching joke:', error);
            });
    }

    function resetProgress() {
        const progressBar = document.querySelector('.progress-bar');
        const loadingStatus = document.getElementById('loadingStatus');
        const retryCount = document.getElementById('retryCount');
        if (progressBar) progressBar.style.width = '0%';
        if (loadingStatus) loadingStatus.textContent = 'Processing items...';
        if (retryCount) retryCount.textContent = '';
    }

    function updateProgress(current, total, status = null, retries = null) {
        const progressBar = document.querySelector('.progress-bar');
        const loadingStatus = document.getElementById('loadingStatus');
        const retryCount = document.getElementById('retryCount');
        
        if (progressBar) {
            const percentage = (current / total) * 100;
            progressBar.style.width = `${percentage}%`;
        }
        
        if (loadingStatus && status) {
            loadingStatus.textContent = status;
        }
        
        if (retryCount && retries !== null) {
            retryCount.textContent = `Retries: ${retries}`;
        }
    }

    async function fetchStagedInventory() {
        try {
            const response = await fetch('/warehouse/inventory');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            displayStagedInventory(data.inventory);
        } catch (error) {
            console.error('Error fetching staged inventory:', error);
            showToast('Error fetching inventory: ' + error.message, 'error');
        }
    }

    let allInventoryData = [];

    function displayStagedInventory(data) {
        // Store the full data for filtering
        allInventoryData = data;
        
        // Update filters
        updateFilterOptions(data);
        
        // Apply current filters
        const filteredData = filterInventoryData(data);
        const stagedInventoryBody = document.getElementById('stagedInventoryBody');
        const stagedInventoryCounter = document.getElementById('stagedInventoryCounter');
        
        if (!stagedInventoryBody || !stagedInventoryCounter) {
            console.error('Required elements not found');
            return;
        }
        
        stagedInventoryBody.innerHTML = '';
        
        if (Array.isArray(filteredData) && filteredData.length > 0) {
            filteredData.sort((a, b) => (a.item || '').localeCompare(b.item || ''));
            
            filteredData.forEach(item => {
                const tr = document.createElement('tr');
                tr.dataset.skuId = item.skuId;
                tr.dataset.item = JSON.stringify(item);
                
                tr.innerHTML = `
                    <td class="item-name">${item.item || 'N/A'}</td>
                    <td class="expansion-name">${item.expansionName || 'N/A'}</td>
                    <td class="condition">${item.condAbbr || 'N/A'}</td>
                    <td class="print-type">${item.printingName || 'Normal'}</td>
                    <td>
                        <input type="number" class="form-control form-control-sm quantity-input" 
                               min="1" value="${item.quantity || 0}">
                    </td>
                    <td>
                        <button class="btn btn-link btn-sm delete-btn p-0" 
                                style="color: red; font-size: 1.2em;">❌</button>
                    </td>
                `;
                
                const quantityInput = tr.querySelector('.quantity-input');
                if (quantityInput) {
                    quantityInput.addEventListener('change', (e) => {
                        updateQuantity(item.skuId, e.target.value);
                    });
                }
                
                const deleteBtn = tr.querySelector('.delete-btn');
                if (deleteBtn) {
                    deleteBtn.addEventListener('click', () => {
                        deleteItem(item.skuId, tr);
                    });
                }
                
                tr.draggable = true;
                tr.addEventListener('dragstart', handleDragStart);
                tr.addEventListener('dragend', handleDragEnd);
                tr.addEventListener('click', handleRowClick);
                
                stagedInventoryBody.appendChild(tr);
            });
            
            stagedInventoryCounter.textContent = data.length;
            stagedInventoryCounter.style.color = 'red';
        } else {
            stagedInventoryCounter.textContent = '0';
            stagedInventoryCounter.style.color = 'green';
        }
    }

    async function processShopify() {
        // Only select visible rows (respecting the current filter)
        const shopifyData = Array.from(document.querySelectorAll('#shopifyBody tr')).filter(row => {
            // Check if the row is visible (not hidden by the filter)
            return row.style.display !== 'none';
        }).map(row => {
            try {
                return JSON.parse(row.dataset.item);
            } catch (error) {
                console.error('Error parsing row data:', error);
                return null;
            }
        }).filter(item => item !== null);

        if (shopifyData.length === 0) {
            showToast('No items to process', 'warning');
            return;
        }
        
        // Get the current filter for user feedback
        const currentFilter = document.getElementById('shopifyStatusFilter').value;
        const filterMessage = currentFilter !== 'all' ? 
            `Processing ${currentFilter} items only` : 
            'Processing all items';
        showToast(filterMessage, 'info');

        try {
            const locations = await getShopifyLocations();
            if (!locations || locations.length === 0) {
                throw new Error('No Shopify locations available');
            }

            const locationId = await showLocationSelectionPopup(locations);
            if (!locationId) {
                throw new Error('No location selected');
            }

            showLoading();
            resetProgress();
                    await processItems(shopifyData, locationId);
                    showToast('Processing completed successfully. Items have been removed from inventory.', 'success');
        } catch (error) {
            console.error('Error processing Shopify items:', error);
            showToast('Error processing items: ' + error.message, 'error');
        } finally {
            hideLoading();
            checkFailedItems();
        }
    }

    const selectAllStagedBtn = document.getElementById('selectAllStagedBtn');
    if (selectAllStagedBtn) {
        selectAllStagedBtn.addEventListener('click', () => {
            const rows = document.querySelectorAll('#stagedInventoryBody tr');
            const allSelected = Array.from(rows).every(row => row.classList.contains('selected'));
            
            rows.forEach(row => {
                if (allSelected) {
                    row.classList.remove('selected');
                    selectedRows.delete(row);
                } else {
                    row.classList.add('selected');
                    selectedRows.add(row);
                }
            });
        });
    }

    const deleteAllStagedBtn = document.getElementById('deleteAllStagedBtn');
    if (deleteAllStagedBtn) {
        deleteAllStagedBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to delete all staged inventory items?')) {
                showLoading();
                fetch('/warehouse/delete_all_staged', {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToast('All items deleted successfully', 'success');
                        fetchStagedInventory();
                    } else {
                        throw new Error(data.message || 'Failed to delete items');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('Error deleting items: ' + error.message, 'error');
                })
                .finally(() => {
                    hideLoading();
                });
            }
        });
    }

    function handleDragStart(e) {
        if (selectedRows.size === 0) {
            e.currentTarget.classList.add('selected');
            selectedRows.add(e.currentTarget);
        }
        
        const selectedItems = Array.from(selectedRows).map(row => {
            try {
                const itemData = JSON.parse(row.dataset.item);
                const quantityInput = row.querySelector('.quantity-input');
                if (quantityInput) {
                    itemData.quantity = parseInt(quantityInput.value, 10);
                }
                return itemData;
            } catch (error) {
                console.error('Error parsing row data:', error);
                return null;
            }
        }).filter(item => item !== null);

        e.dataTransfer.setData('application/json', JSON.stringify(selectedItems));
        selectedRows.forEach(row => row.classList.add('dragging'));
    }

    function handleDragEnd(e) {
        selectedRows.forEach(row => row.classList.remove('dragging'));
    }

    function enableDropzones() {
        const dropzones = document.querySelectorAll('.dropzone');
        dropzones.forEach(dropzone => {
            dropzone.addEventListener('dragover', handleDragOver);
            dropzone.addEventListener('dragleave', handleDragLeave);
            dropzone.addEventListener('drop', handleDrop);
        });
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        e.currentTarget.classList.add('drag-over');
    }

    function handleDragLeave(e) {
        e.currentTarget.classList.remove('drag-over');
    }

    function handleDrop(e) {
        e.preventDefault();
        const dropzone = e.currentTarget;
        dropzone.classList.remove('drag-over');

        try {
            const itemsData = JSON.parse(e.dataTransfer.getData('application/json'));
            console.log('Dropped items data:', itemsData);
            
            if (!Array.isArray(itemsData) || itemsData.length === 0) {
                throw new Error('Invalid drop data');
            }

            const dropzoneBody = dropzone.querySelector('tbody');
            if (!dropzoneBody) {
                throw new Error('Dropzone table body not found');
            }

            itemsData.forEach(async (item) => {
                console.log('Processing dropped item:', item);
                // Create new row for the dropzone
                const tr = document.createElement('tr');
                tr.dataset.skuId = item.skuId;

                try {
                    // Check Shopify status for the item
                    const response = await fetch('/warehouse/check_shopify_status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(item)
                    });
                    
                    if (!response.ok) {
                        console.error(`HTTP error! status: ${response.status}`);
                        showToast(`Error checking Shopify status: ${response.status}`, 'error');
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    // Check if response is empty
                    const responseText = await response.text();
                    if (!responseText || responseText.trim() === '') {
                        console.error('Empty response from server');
                        showToast('Empty response from server', 'error');
                        throw new Error('Empty response from server');
                    }
                    
                    // Parse the JSON response
                    const statusData = JSON.parse(responseText);
                    console.log('Shopify status response:', statusData);
                    
                    // Set the status attribute (new or update)
                    const itemStatus = statusData.exists ? 'update' : 'new';
                    tr.dataset.status = itemStatus;
                    
                    // Merge Shopify details with item data
                    const enrichedItem = {
                        ...item,
                        shopifyProductId: statusData.shopifyProductId,
                        shopifyVariantId: statusData.shopifyVariantId,
                        shopifyInventoryItemId: statusData.shopifyInventoryItemId,
                        variantTitle: statusData.exists ? statusData.variantTitle : statusData.catalogInfo?.variantTitle,
                        price: statusData.exists ? statusData.price : statusData.catalogInfo?.price,
                        status: itemStatus,
                        message: statusData.message // Preserve the message field from the API response
                    };
                    
                    console.log('Enriched item data to be stored:', enrichedItem);
                    tr.dataset.item = JSON.stringify(enrichedItem);
                    
                    // Create status badge
                    const statusBadge = `<span class="badge ${statusData.exists ? 'bg-info' : 'bg-success'}" style="margin-right: 5px;">${statusData.exists ? 'Update' : 'New'}</span>`;
                    
                    tr.innerHTML = `
                        <td>${item.item || 'N/A'}</td>
                        <td>${item.quantity || 0}</td>
                        <td class="variant-cell">${enrichedItem.variantTitle || 'N/A'}</td>
                        <td class="price-cell">${statusData.exists ? statusData.price : (statusData.catalogInfo?.price || 'N/A')}</td>
                        <td class="location-cell">${statusData.locationId || 'N/A'}</td>
                        <td class="variant-id-cell">${enrichedItem.shopifyVariantId || 'N/A'}</td>
                        <td class="status-cell">${statusBadge}${statusData.message || 'Ready'}</td>
                        <td>
                            <button class="btn btn-link btn-sm remove-btn p-0" 
                                    style="color: red; font-size: 1.2em;">❌</button>
                        </td>
                    `;

                    // Add remove button handler
                    const removeBtn = tr.querySelector('.remove-btn');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', () => {
                            tr.remove();
                            // Move item back to staged inventory
                            const stagedInventoryBody = document.getElementById('stagedInventoryBody');
                            if (stagedInventoryBody) {
                                const newRow = createTableRow(item);
                                if (newRow) {
                                    stagedInventoryBody.appendChild(newRow);
                                }
                            }
                        });
                    }

                    // Add the row to the dropzone
                    dropzoneBody.appendChild(tr);
                    
                } catch (itemError) {
                    console.error(`Error processing item ${item.skuId}:`, itemError);
                    showToast(`Error processing item: ${itemError.message}`, 'error');
                    
                    // Return the item to staged inventory since it failed
                    const stagedInventoryBody = document.getElementById('stagedInventoryBody');
                    if (stagedInventoryBody) {
                        const newRow = createTableRow(item);
                        if (newRow) {
                            stagedInventoryBody.appendChild(newRow);
                        }
                    }
                }
            });

            // Remove selected rows from staged inventory
            selectedRows.forEach(row => row.remove());
            selectedRows.clear();

        } catch (error) {
            console.error('Error handling drop:', error);
            showToast('Error handling dropped items: ' + error.message, 'error');
        }
    }

    function makeRowsDraggable() {
        const tables = document.querySelectorAll('#stagedInventoryTable');
        tables.forEach(table => {
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.draggable = true;
                row.addEventListener('dragstart', handleDragStart);
                row.addEventListener('dragend', handleDragEnd);
            });
        });
    }

    async function updateQuantity(skuId, newQuantity) {
        try {
            const response = await fetch('/warehouse/update_quantity', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    skuId: skuId,
                    quantity: parseInt(newQuantity, 10)
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.status === 'success') {
                showToast('Quantity updated successfully', 'success');
                // Update the item data in the row
                const row = document.querySelector(`tr[data-sku-id="${skuId}"]`);
                if (row) {
                    const itemData = JSON.parse(row.dataset.item);
                    itemData.quantity = parseInt(newQuantity, 10);
                    row.dataset.item = JSON.stringify(itemData);
                }
            } else {
                throw new Error(data.message || 'Failed to update quantity');
            }
        } catch (error) {
            console.error('Error updating quantity:', error);
            showToast('Error updating quantity: ' + error.message, 'error');
            // Reset the input to its previous value
            const row = document.querySelector(`tr[data-sku-id="${skuId}"]`);
            if (row) {
                const itemData = JSON.parse(row.dataset.item);
                const quantityInput = row.querySelector('.quantity-input');
                if (quantityInput) {
                    quantityInput.value = itemData.quantity;
                }
            }
        }
    }

    async function deleteItem(skuId, row) {
        if (confirm('Are you sure you want to delete this item?')) {
            try {
                const response = await fetch(`/warehouse/delete_item?skuId=${encodeURIComponent(skuId)}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (data.status === 'success') {
                    row.remove();
                    showToast('Item deleted successfully', 'success');
                    // Update counter
                    const counter = document.getElementById('stagedInventoryCounter');
                    if (counter) {
                        const currentCount = parseInt(counter.textContent, 10) - 1;
                        counter.textContent = currentCount;
                        counter.style.color = currentCount > 0 ? 'red' : 'green';
                    }
                } else {
                    throw new Error(data.message || 'Failed to delete item');
                }
            } catch (error) {
                console.error('Error deleting item:', error);
                showToast('Error deleting item: ' + error.message, 'error');
            }
        }
    }

    function showLocationSelectionPopup(locations) {
        return new Promise((resolve, reject) => {
            const popup = document.createElement('div');
            popup.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background-color: #2c3e50;
                padding: 30px;
                border-radius: 15px;
                max-width: 600px;
                width: 90%;
                color: #ffffff;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            `;

            content.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 10px 0; font-size: 1.8rem; color: #2ecc71;">Select Shopify Location</h3>
                    <p style="color: #95a5a6; margin: 0;">Click a location to proceed</p>
                </div>
                <div style="max-height: 400px; overflow-y: auto; margin: 20px 0;">
                    ${locations.map(loc => `
                        <div class="location-card" data-location-id="${loc.id}" style="
                            border: 2px solid #2ecc71;
                            border-radius: 10px;
                            padding: 15px;
                            margin: 10px 0;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            background-color: #34495e;
                        ">
                            <h4 style="margin: 0 0 10px 0; color: #2ecc71;">${loc.name}</h4>
                            <p style="margin: 0; font-size: 0.9em; color: #bdc3c7;">${loc.address}</p>
                        </div>
                    `).join('')}
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button id="cancelLocationSelection" style="
                        padding: 10px 20px;
                        border: none;
                        border-radius: 5px;
                        background-color: #e74c3c;
                        color: #ffffff;
                        cursor: pointer;
                        font-size: 1rem;
                    ">Cancel</button>
                </div>
            `;

            popup.appendChild(content);
            document.body.appendChild(popup);

            // Add hover effect to location cards
            const cards = content.querySelectorAll('.location-card');
            cards.forEach(card => {
                card.addEventListener('mouseover', () => {
                    card.style.transform = 'translateY(-2px)';
                    card.style.boxShadow = '0 5px 15px rgba(46, 204, 113, 0.3)';
                });
                card.addEventListener('mouseout', () => {
                    card.style.transform = 'translateY(0)';
                    card.style.boxShadow = 'none';
                });
                card.addEventListener('click', () => {
                    const locationId = card.dataset.locationId;
                    document.body.removeChild(popup);
                    resolve(locationId);
                });
            });

            // Cancel button handler
            document.getElementById('cancelLocationSelection').addEventListener('click', () => {
                document.body.removeChild(popup);
                resolve(null);
            });

            // Click outside to cancel
            popup.addEventListener('click', (event) => {
                if (event.target === popup) {
                    document.body.removeChild(popup);
                    resolve(null);
                }
            });
        });
    }

    function handleRowClick(e) {
        if (e.ctrlKey || e.metaKey) {
            // Multi-select with Ctrl/Cmd key
            e.currentTarget.classList.toggle('selected');
            if (selectedRows.has(e.currentTarget)) {
                selectedRows.delete(e.currentTarget);
            } else {
                selectedRows.add(e.currentTarget);
            }
        } else {
            // Single select without Ctrl/Cmd key
            selectedRows.forEach(row => row.classList.remove('selected'));
            selectedRows.clear();
            e.currentTarget.classList.add('selected');
            selectedRows.add(e.currentTarget);
        }
    }

    function createTableRow(item) {
        const tr = document.createElement('tr');
        tr.dataset.skuId = item.skuId;
        tr.dataset.item = JSON.stringify(item);
        
        tr.innerHTML = `
            <td class="item-name">${item.item || 'N/A'}</td>
            <td class="expansion-name">${item.expansionName || 'N/A'}</td>
            <td class="condition">${item.condAbbr || 'N/A'}</td>
            <td class="print-type">${item.printingName || 'Normal'}</td>
            <td>
                <input type="number" class="form-control form-control-sm quantity-input" 
                       min="1" value="${item.quantity || 0}">
            </td>
            <td>
                <button class="btn btn-link btn-sm delete-btn p-0" 
                        style="color: red; font-size: 1.2em;">❌</button>
            </td>
        `;
        
        const quantityInput = tr.querySelector('.quantity-input');
        if (quantityInput) {
            quantityInput.addEventListener('change', (e) => {
                updateQuantity(item.skuId, e.target.value);
            });
        }
        
        const deleteBtn = tr.querySelector('.delete-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                deleteItem(item.skuId, tr);
            });
        }
        
        tr.draggable = true;
        tr.addEventListener('dragstart', handleDragStart);
        tr.addEventListener('dragend', handleDragEnd);
        tr.addEventListener('click', handleRowClick);
        
        return tr;
    }

    function updateFilterOptions(data) {
        const setFilter = document.getElementById('setFilter');
        
        // Get unique sets
        const sets = [...new Set(data.map(item => item.expansionName))].filter(Boolean).sort();
        
        // Update set filter options
        const currentSet = setFilter.value;
        setFilter.innerHTML = '<option value="">All Sets</option>';
        sets.forEach(set => {
            const option = document.createElement('option');
            option.value = set;
            option.textContent = set;
            if (set === currentSet) {
                option.selected = true;
            }
            setFilter.appendChild(option);
        });
    }

    function filterInventoryData(data) {
        const setFilter = document.getElementById('setFilter');
        const searchFilter = document.getElementById('stagedInventorySearch');
        
        return data.filter(item => {
            // Set filter
            if (setFilter.value && item.expansionName !== setFilter.value) {
                return false;
            }
            
            // Search filter
            if (searchFilter.value) {
                const searchTerm = searchFilter.value.toLowerCase();
                return (
                    (item.item && item.item.toLowerCase().includes(searchTerm)) ||
                    (item.expansionName && item.expansionName.toLowerCase().includes(searchTerm))
                );
            }
            
            return true;
        });
    }

    // Initialize the page
    enableDropzones();
    makeRowsDraggable();

    // Set up filter event listeners
    const setFilter = document.getElementById('setFilter');
    const searchFilter = document.getElementById('stagedInventorySearch');
    const shopifyStatusFilter = document.getElementById('shopifyStatusFilter');

    setFilter.addEventListener('change', () => {
        displayStagedInventory(allInventoryData);
    });

    searchFilter.addEventListener('input', () => {
        displayStagedInventory(allInventoryData);
    });
    
    // Add event listener for Shopify status filter
    if (shopifyStatusFilter) {
        shopifyStatusFilter.addEventListener('change', function() {
            const filterValue = this.value;
            const rows = document.querySelectorAll('#shopifyBody tr');
            
            rows.forEach(row => {
                if (filterValue === 'all') {
                    row.style.display = '';
                } else {
                    const rowStatus = row.dataset.status;
                    row.style.display = (rowStatus === filterValue) ? '' : 'none';
                }
            });
        });
    }
});
</script>
{% endblock %}
