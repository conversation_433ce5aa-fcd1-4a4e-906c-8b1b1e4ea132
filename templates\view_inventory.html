{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-info">
                <div class="card-body">
                    <h5 class="card-title text-white">Unique Items</h5>
                    <h2 class="mb-0 text-white" id="uniqueItems">0</h2>
                    <small class="text-white-50">Waiting to be pushed</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success">
                <div class="card-body">
                    <h5 class="card-title text-white">Total Cards</h5>
                    <h2 class="mb-0 text-white" id="totalStock">0</h2>
                    <small class="text-white-50">Waiting to be pushed</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-primary">
                <div class="card-body">
                    <h5 class="card-title text-white">Total Value</h5>
                    <h2 class="mb-0 text-white" id="totalValue">0.00</h2>
                    <small class="text-white-50">Waiting to be pushed</small>
                </div>
            </div>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-body">
            <!-- Top Actions Row -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex gap-2">
                    <a href="{{ url_for('manual_inventory.manual_inventory_ui.manual_inventory') }}" class="btn btn-primary" data-bs-toggle="tooltip" title="Add new items manually">
                        <i class="fas fa-plus me-2"></i>Add Items
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <div class="btn-group">
                        <button class="btn btn-success" id="linkAllBtn" onclick="startBulkLinking()" data-bs-toggle="tooltip" title="Step 1: Link selected unlinked items to Shopify">
                            <i class="fas fa-link me-2"></i>Link Items
                            <span class="badge bg-light text-success ms-1">1</span>
                        </button>
                        <button class="btn btn-warning" id="selectNeedsSyncBtn" onclick="selectNeedsSync()" data-bs-toggle="tooltip" title="Auto-select and link all unlinked items">
                            <i class="fas fa-exclamation-triangle"></i>
                        </button>
                    </div>
                    <button class="btn btn-primary" id="pushToShopifyBtn" onclick="startShopifyPush()" data-bs-toggle="tooltip" title="Step 2: Push selected items to Shopify">
                        <i class="fas fa-upload me-2"></i>Push to Shopify
                        <span class="badge bg-light text-primary ms-1">2</span>
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" data-bs-toggle="tooltip" title="Export inventory">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('view_inventory.export_inventory_csv') }}">Export as CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="form-floating">
                        <input type="text" class="form-control bg-dark text-white" id="searchInput" placeholder="Search cards...">
                        <label for="searchInput">Search by Name</label>
                        <button class="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3" type="button" id="clearSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="gameSelect">
                            <!-- Games will be loaded here -->
                        </select>
                        <label for="gameSelect">Filter by Game</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="expansionSelect" disabled>
                            <!-- Expansions will be loaded here -->
                        </select>
                        <label for="expansionSelect">Filter by Expansion</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="foilSelect">
                            <option value="">All Cards</option>
                            <option value="foil">Foil Only</option>
                            <option value="non-foil">Non-Foil Only</option>
                        </select>
                        <label for="foilSelect">Foil Type</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="sortSelect">
                            <option value="name">A to Z</option>
                            <option value="price_asc">Price (Low to High)</option>
                            <option value="price_desc">Price (High to Low)</option>
                            <option value="quantity_asc">Quantity (Low to High)</option>
                            <option value="quantity_desc">Quantity (High to Low)</option>
                            <option value="shopify_synced">Shopify (Synced First)</option>
                            <option value="shopify_unsynced">Shopify (Unsynced First)</option>
                        </select>
                        <label for="sortSelect">Sort By</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card" id="tableView">
        <div class="card-header bg-dark d-flex justify-content-between align-items-center py-2">
            <h5 class="mb-0 text-white">Inventory Items</h5>
            <button class="btn btn-outline-light btn-sm" id="viewToggle" data-bs-toggle="tooltip" title="Switch between table and grid view">
                <i class="fas fa-table me-1"></i>View
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table" id="inventoryTable">
                    <thead>
                        <tr>
                            <th class="col-select">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th class="col-image">Image</th>
                            <th class="col-name">Name</th>
                            <th class="col-number">Number</th>
                            <th class="col-expansion">Expansion</th>
                            <th class="col-print-type">Print Type</th>
                            <th class="col-condition">Cond</th>
                            <th class="col-language">Lang</th>
                            <th class="col-price">Price</th>
                            <th class="col-stock">Stock</th>
                            <th class="col-shopify">Shopify</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="11" class="text-center py-4">
                                <div class="mb-3">
                                    <i class="fas fa-search fa-2x text-muted"></i>
                                </div>
                                <h5 class="text-muted">Select a game or search by card name to view inventory</h5>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card d-none" id="gridView">
        <div class="card-header bg-dark d-flex justify-content-between align-items-center py-2">
            <h5 class="mb-0 text-white">Inventory Items</h5>
            <button class="btn btn-outline-light btn-sm" id="viewToggleGrid" data-bs-toggle="tooltip" title="Switch between table and grid view">
                <i class="fas fa-grip-horizontal me-1"></i>View
            </button>
        </div>
        <div class="card-body">
            <div class="inventory-grid" id="inventoryGrid">
                <div class="text-center w-100 py-4">
                    <div class="mb-3">
                        <i class="fas fa-search fa-2x text-muted"></i>
                    </div>
                    <h5 class="text-muted">Select a game or search by card name to view inventory</h5>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Shopify Location Modal -->
<div class="modal fade" id="shopifyLocationModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title">Select Shopify Location</h5>
            </div>
            <div class="modal-body">
                <div id="locationCards" class="mb-3">
                    <!-- Location cards will be loaded here -->
                </div>
                <div class="text-center">
                    <button id="confirmLocation" class="btn btn-success me-2">Confirm Selection</button>
                    <button id="cancelLocation" class="btn btn-danger">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Shopify Push Modal -->
<div class="modal fade" id="shopifyPushModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title">Pushing Inventory to Shopify</h5>
            </div>
            <div class="modal-body">
                <div class="progress mb-3">
                    <div class="progress-bar" id="pushProgress" role="progressbar" style="width: 0%"></div>
                </div>
                <div id="pushStatus" class="text-center">
                    Preparing to push inventory...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Linking Modal -->
<div class="modal fade" id="bulkLinkingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title">Linking Items to Shopify</h5>
            </div>
            <div class="modal-body">
                <div class="progress mb-3">
                    <div class="progress-bar" id="linkingProgress" role="progressbar" style="width: 0%"></div>
                </div>
                <div id="linkingStatus" class="text-center">
                    Preparing to link items...
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="/static/css/view_inventory.css">

<style>
.sync-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 0.8rem;
}

.sync-status .status-icon {
    margin-bottom: 0.25rem;
}

.sync-status .status-text {
    text-align: center;
}

.sync-status .sync-time {
    font-size: 0.7rem;
    color: #6c757d;
}

.sync-status.needs-sync {
    color: #ffc107;
}

.sync-status.sync-error {
    color: #dc3545;
}

.sync-status.sync-success {
    color: #28a745;
}

.location-card {
    border: 2px solid #2ecc71;
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #2c3e50;
    box-shadow: 0 0 15px #2ecc71;
}

.location-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 20px #2ecc71;
}

.location-card.selected {
    background-color: #27ae60;
}

.location-card h4 {
    margin: 0 0 10px 0;
    color: #2ecc71;
}

.location-card p {
    margin: 0;
    font-size: 0.9em;
    color: #bdc3c7;
}

/* Modern button styles */
.btn {
    border-radius: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn i {
    transition: transform 0.2s ease;
}

.btn:hover i {
    transform: scale(1.1);
}

.btn-group {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Badge styles */
.badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    border-radius: 4px;
}

/* Form control styles */
.form-control, .form-select {
    border-radius: 6px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.15);
}

.form-floating label {
    padding: 0.5rem 0.75rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
    color: #6c757d;
}

/* Table styles */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: rgba(0, 0, 0, 0.2);
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Card styles */
.card {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 1.5rem;
}

/* Tooltip styles */
.tooltip-inner {
    max-width: 300px;
    text-align: left;
    padding: 8px 12px;
    line-height: 1.4;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Table header view toggle */
.table thead tr:first-child th {
    border-bottom: none;
    padding: 0.75rem 1rem;
    background-color: transparent;
}

.table thead tr:first-child .btn-sm {
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
}

/* Add some spacing between the view toggle and the table header */
.table thead tr:last-child th {
    padding-top: 1rem;
}

/* Input group styles */
.input-group {
    border-radius: 6px;
    overflow: hidden;
}

.input-group-text {
    background-color: rgba(0, 0, 0, 0.2);
    border: none;
    color: #fff;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Sync status styles */
.sync-status {
    padding: 0.75rem;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.sync-status:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sync-status .status-icon {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.sync-status .status-text {
    font-weight: 500;
    font-size: 0.9rem;
}

.sync-status .sync-time {
    margin-top: 0.35rem;
    opacity: 0.8;
    font-size: 0.75rem;
}

/* Table styles */
.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.table td {
    vertical-align: middle;
    padding: 0.75rem;
}

.table .col-image img {
    border-radius: 4px;
    transition: transform 0.2s ease;
}

.table .col-image img:hover {
    transform: scale(1.1);
}

/* Grid view styles */
.inventory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.inventory-grid-item {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.inventory-grid-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.inventory-grid-item img {
    width: 100%;
    height: 200px;
    object-fit: contain;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.inventory-grid-item .name {
    font-weight: 500;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.inventory-grid-item .details {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

/* Form check styles */
.form-check-input {
    cursor: pointer;
    width: 1.2rem;
    height: 1.2rem;
    margin-top: 0.25rem;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Progress bar styles */
.progress {
    height: 0.75rem;
    border-radius: 6px;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.1);
}

.progress-bar {
    transition: width 0.3s ease;
    background-color: #0d6efd;
}

/* Modal styles */
.modal-content {
    border-radius: 8px;
    border: none;
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-title {
    font-weight: 500;
    font-size: 1.25rem;
}
</style>

<script>
async function startShopifyPush() {
    try {
        // Get selected synced items with quantity > 0
        const items = Array.from(document.querySelectorAll('#inventoryTable tbody tr'))
            .filter(row => {
                            const checkbox = row.querySelector('.row-select');
                            const shopifyCell = row.querySelector('.col-shopify');
                            const quantityInput = row.querySelector('input[data-type="quantity"]');
                            return checkbox.checked && 
                                   parseInt(quantityInput.value) > 0;
            })
            .map(row => ({
                id: row.querySelector('input[data-type="quantity"]').dataset.id,
                quantity: parseInt(row.querySelector('input[data-type="quantity"]').value)
            }));

        if (items.length === 0) {
            alert('Please select items with quantity greater than 0');
            return;
        }

        // Get Shopify locations
        const locationsResponse = await fetch('/api/inventory/get-shopify-locations');
        const locationsData = await locationsResponse.json();

        if (!locationsData.locations || locationsData.locations.length === 0) {
            alert('No Shopify locations found');
            return;
        }

        // Show location selection modal
        const locationModal = new bootstrap.Modal(document.getElementById('shopifyLocationModal'));
        const locationCards = document.getElementById('locationCards');
        locationCards.innerHTML = '';

        // Create location cards
        locationsData.locations.forEach((loc, index) => {
            const card = document.createElement('div');
            card.className = `location-card${index === 0 ? ' selected' : ''}`;
            card.dataset.locationId = loc.id;
            card.innerHTML = `
                <h4>${loc.name}</h4>
                <p>${loc.address}</p>
            `;
            card.addEventListener('click', () => {
                document.querySelectorAll('.location-card').forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
            });
            locationCards.appendChild(card);
        });

        // Show the modal
        locationModal.show();

        // Handle location selection
        return new Promise((resolve, reject) => {
            document.getElementById('confirmLocation').onclick = async () => {
                const selectedCard = document.querySelector('.location-card.selected');
                if (!selectedCard) {
                    alert('Please select a location');
                    return;
                }

                locationModal.hide();

                // Show push progress modal
                const pushModal = new bootstrap.Modal(document.getElementById('shopifyPushModal'));
                const progressBar = document.getElementById('pushProgress');
                const statusText = document.getElementById('pushStatus');
                pushModal.show();

                try {
                    // Get full inventory items with Shopify data
                            const itemsWithShopifyData = Array.from(document.querySelectorAll('#inventoryTable tbody tr'))
                        .filter(row => {
                            const checkbox = row.querySelector('.row-select');
                            return checkbox.checked;
                        })
                        .map(row => {
                            const customFields = row.dataset.customFields ? JSON.parse(row.dataset.customFields) : {};
                            return {
                                id: row.querySelector('input[data-type="quantity"]').dataset.id,
                                quantity: parseInt(row.querySelector('input[data-type="quantity"]').value),
                                variant_id: customFields.shopify_variant_id,
                                inventory_item_id: customFields.shopify_inventory_item_id
                            };
                        });

                    // Process items individually with delay
                    let processedItems = 0;
                    let successCount = 0;
                    let failCount = 0;
                    const results = [];

                    // Helper function to delay execution
                    const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

                    // Process each item
                    for (const item of itemsWithShopifyData) {
                        statusText.textContent = `Processing item ${processedItems + 1}/${itemsWithShopifyData.length}`;

                        try {
                            const pushResponse = await fetch('/api/inventory/push-to-shopify', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    items: [item], // Send single item
                                    locationId: selectedCard.dataset.locationId
                                })
                            });

                            const data = await pushResponse.json();
                            
                            if (data.error) {
                                throw new Error(data.error);
                            }

                            results.push(data);
                            
                            // Update counts
                            if (data.results && data.results[0].status === 'success') {
                                successCount++;
                            } else {
                                failCount++;
                                console.error('Failed item:', item, data.results?.[0]?.error || 'Unknown error');
                            }

                            processedItems++;
                            
                            // Update progress
                            const progress = Math.round((processedItems / itemsWithShopifyData.length) * 100);
                            progressBar.style.width = `${progress}%`;
                            statusText.textContent = `Processed ${processedItems}/${itemsWithShopifyData.length} items (${successCount} successful, ${failCount} failed)`;

                            // Delay between requests (500ms = 2 requests per second)
                            await delay(500);

                        } catch (error) {
                            console.error('Error processing item:', item, error);
                            failCount++;
                            processedItems++;
                            
                            // Update progress even on error
                            const progress = Math.round((processedItems / itemsWithShopifyData.length) * 100);
                            progressBar.style.width = `${progress}%`;
                            statusText.textContent = `Processed ${processedItems}/${itemsWithShopifyData.length} items (${successCount} successful, ${failCount} failed)`;
                            
                            // Still delay between requests even if there was an error
                            await delay(500);
                        }
                    }

                    // Update final status
                    progressBar.style.width = '100%';
                    statusText.textContent = `Complete! Successfully pushed ${successCount} items, ${failCount} failed`;

                    // Reload inventory after a short delay
                    setTimeout(() => {
                        loadInventory();
                        pushModal.hide();
                    }, 2000);

                } catch (error) {
                    console.error('Error:', error);
                    statusText.textContent = `Error: ${error.message}`;
                    setTimeout(() => pushModal.hide(), 2000);
                }
            };

            document.getElementById('cancelLocation').onclick = () => {
                locationModal.hide();
                reject(new Error('User cancelled'));
            };
        });

    } catch (error) {
        console.error('Error:', error);
        alert('Error: ' + error.message);
    }
}

window.startBulkLinking = async function() {
    try {
        // Get selected unlinked items
        const selectedItems = Array.from(document.querySelectorAll('#inventoryTable tbody tr'))
            .filter(row => {
                const checkbox = row.querySelector('.row-select');
                const shopifyCell = row.querySelector('.col-shopify');
                return checkbox.checked && !shopifyCell.querySelector('.fa-check');
            })
            .map(row => ({
                id: row.querySelector('.row-select').dataset.id,
                name: row.querySelector('.col-name').textContent
            }));
        
        if (selectedItems.length === 0) {
            alert('No unlinked items selected');
            return;
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('bulkLinkingModal'));
        modal.show();

        const progressBar = document.getElementById('linkingProgress');
        const statusText = document.getElementById('linkingStatus');
        let successCount = 0;
        let failCount = 0;

        // Show initial status
        statusText.textContent = `Linking ${selectedItems.length} items to Shopify...`;
        progressBar.style.width = '0%';
        
        try {
            // Process all items at once
            const result = await fetch('/api/inventory/bulk-link-shopify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    item_ids: selectedItems.map(item => item.id)
                })
            });
            
            const data = await result.json();
            
            if (data.error) {
                throw new Error(data.error);
            }

            // Update counts
            successCount = data.success_count;
            failCount = data.failed_count;
            
            // Update progress
            progressBar.style.width = '100%';
            statusText.textContent = `Processed ${selectedItems.length} items (${successCount} successful, ${failCount} failed)`;

            if (data.failed_items && data.failed_items.length > 0) {
                console.error('Failed items:', data.failed_items);
                // Update status text to show specific error for the first failed item
                if (data.failed_items[0].error) {
                    statusText.textContent = `Error: ${data.failed_items[0].error}`;
                }
            }
        } catch (error) {
            console.error('Error:', error);
            failCount = selectedItems.length;
            progressBar.style.width = '100%';
            statusText.textContent = `Error processing items: ${error.message}`;
        }

        // Reload inventory after a short delay
        setTimeout(() => {
            loadInventory();
            modal.hide();
        }, 2000);
    } catch (error) {
        console.error('Error:', error);
        alert('Error: ' + error.message);
    }
};
</script>

<script>
function formatPrice(price, currencySymbol) {
    if (price === null || price === undefined) return '-';
    return `${currencySymbol}${parseFloat(price).toFixed(2)}`;
}

function isFoilCard(item) {
    const name = item.name.toLowerCase();
    const printType = (item.printType || '').toLowerCase();
    return name.includes('foil') || printType.includes('foil');
}

let currentView = 'table';

// Debounce function to limit API calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function loadInventory() {
    const gameSelect = document.getElementById('gameSelect');
    const expansionSelect = document.getElementById('expansionSelect');
    const sortSelect = document.getElementById('sortSelect');
    const foilSelect = document.getElementById('foilSelect');
    const searchInput = document.getElementById('searchInput');
    const inventoryTable = document.getElementById('inventoryTable').getElementsByTagName('tbody')[0];
    const inventoryGrid = document.getElementById('inventoryGrid');
    
    const params = new URLSearchParams({
        game: gameSelect.value,
        expansion: expansionSelect.value,
        sort: sortSelect.value,
        foil: foilSelect.value,
        search: searchInput.value.trim()
    });
    
    inventoryTable.innerHTML = '<tr class="loading"><td colspan="10" class="text-center py-4">Loading inventory...</td></tr>';
    inventoryGrid.innerHTML = '<div class="loading-grid"><div class="loading-card"></div><div class="loading-card"></div><div class="loading-card"></div><div class="loading-card"></div></div>';
    
    fetch(`/api/inventory?${params}`)
        .then(response => response.json())
        .then(items => {
            inventoryTable.innerHTML = '';
            inventoryGrid.innerHTML = '';
            
            // Filter items based on foil selection
            if (foilSelect.value) {
                const wantFoil = foilSelect.value === 'foil';
                items = items.filter(item => isFoilCard(item) === wantFoil);
            }
            
            // Calculate total value and stock
            let totalValue = 0;
            let totalStock = 0;
            items.forEach(item => {
                if (item.price && item.quantity) {
                    totalValue += parseFloat(item.price) * parseInt(item.quantity);
                    totalStock += parseInt(item.quantity);
                }
            });
            document.getElementById('totalValue').textContent = formatPrice(totalValue, items[0]?.currency_symbol || '$');
            document.getElementById('totalStock').textContent = totalStock.toLocaleString();
            document.getElementById('uniqueItems').textContent = items.length.toLocaleString();
            
            if (items.length === 0) {
                document.getElementById('totalValue').textContent = formatPrice(0, '$');
                document.getElementById('totalStock').textContent = '0';
                document.getElementById('uniqueItems').textContent = '0';
                let message = 'No items found';
                if (gameSelect.value && expansionSelect.value) {
                    message += ` in ${expansionSelect.value} (${gameSelect.value})`;
                } else if (gameSelect.value) {
                    message += ` in ${gameSelect.value}`;
                }
                inventoryTable.innerHTML = `<tr><td colspan="10" class="text-center">${message}</td></tr>`;
                inventoryGrid.innerHTML = `<div class="text-center w-100">${message}</div>`;
                return;
            }
            
            // Populate both views
            items.forEach(item => {
                const isFoil = isFoilCard(item);
                const row = document.createElement('tr');
                if (isFoil) row.classList.add('foil-card');
                const customFields = item.customFields || {};
                row.dataset.customFields = JSON.stringify(customFields);
                row.innerHTML = `
                    <td class="col-select">
                        <input type="checkbox" class="form-check-input row-select" data-id="${item.id}">
                    </td>
                    <td class="col-image">
                        <img src="${item.imageUrl || '/static/placeholder.jpg'}" 
                             alt="${item.name}" 
                             style="max-width: 50px; max-height: 70px;"
                             onerror="this.onerror=null; this.src='/static/placeholder.jpg';">
                    </td>
                    <td class="col-name">${item.name}</td>
                    <td class="col-number">${item.number || 'N/A'}</td>
                    <td class="col-expansion">${item.expansionName || 'N/A'}</td>
                    <td class="col-print-type">${item.printType || 'N/A'}</td>
                    <td class="col-condition">${item.condition || 'N/A'}</td>
                    <td class="col-language">${item.language || 'N/A'}</td>
                    <td class="col-price">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">${item.currency_symbol}</span>
                            <input type="number" class="form-control bg-dark text-white" 
                                   value="${item.price ? parseFloat(item.price).toFixed(2) : '0.00'}" 
                                   step="0.01" 
                                   min="0"
                                   data-id="${item.id}"
                                   data-type="price">
                            <button class="btn btn-success btn-sm save-stock" onclick="updateValue(this.previousElementSibling)">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </td>
                    <td class="col-stock">
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control bg-dark text-white" 
                                   value="${item.quantity}" 
                                   min="0"
                                   data-id="${item.id}"
                                   data-type="quantity">
                            <button class="btn btn-success btn-sm save-stock" onclick="updateValue(this.previousElementSibling)">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </td>
                    <td class="col-shopify">
                        ${item.shopify_synced ? 
                            `<div class="sync-status ${item.needs_shopify_sync ? 'needs-sync' : item.last_sync_status === 'success' ? 'sync-success' : 'sync-error'}">
                                <div class="status-icon">
                                    ${item.needs_shopify_sync ? 
                                        '<i class="fas fa-exclamation-triangle"></i>' :
                                        item.last_sync_status === 'success' ? 
                                            '<i class="fas fa-check"></i>' : 
                                            '<i class="fas fa-times"></i>'
                                    }
                                </div>
                                <div class="status-text">
                                    ${item.needs_shopify_sync ? 
                                        'Needs Sync' :
                                        item.last_sync_status === 'success' ? 
                                            'Synced' : 
                            `Error: ${item.last_sync_error || 'No error details available'}`
                                    }
                                </div>
                                ${item.shopify_sync_timestamp ? 
                                    `<div class="sync-time">Last sync: ${new Date(item.shopify_sync_timestamp).toLocaleString()}</div>` : 
                                    ''
                                }
                            </div>` : 
                            `<div class="d-flex align-items-center gap-2">
                                <i class="fas fa-times text-danger"></i>
                                <button class="btn btn-sm btn-outline-primary" onclick="linkShopify('${item.id}')">
                                    <i class="fas fa-link"></i>
                                </button>
                            </div>`
                        }
                    </td>
                `;
                inventoryTable.appendChild(row);

                // Grid view item
                const gridItem = document.createElement('div');
                gridItem.className = `inventory-grid-item${isFoil ? ' foil-card' : ''}`;
                gridItem.innerHTML = `
                    <img src="${item.imageUrl || '/static/placeholder.jpg'}" 
                         alt="${item.name}"
                         onerror="this.onerror=null; this.src='/static/placeholder.jpg';">
                    <div class="name">${item.name}</div>
                    <div class="details">
                        ${item.expansionName || 'N/A'}<br>
                        ${item.condition || 'N/A'} | ${item.language || 'N/A'}
                    </div>
                    <div class="controls-stack">
                        <div class="input-group input-group-sm mb-2">
                            <span class="input-group-text">${item.currency_symbol}</span>
                            <input type="number" class="form-control bg-dark text-white" 
                                   value="${item.price ? parseFloat(item.price).toFixed(2) : '0.00'}" 
                                   step="0.01" 
                                   min="0"
                                   data-id="${item.id}"
                                   data-type="price">
                            <button class="btn btn-success btn-sm save-stock" onclick="updateValue(this.previousElementSibling)">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control bg-dark text-white" 
                                   value="${item.quantity}" 
                                   min="0"
                                   data-id="${item.id}"
                                   data-type="quantity">
                            <button class="btn btn-success btn-sm save-stock" onclick="updateValue(this.previousElementSibling)">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </div>
                    <div class="shopify-status text-center mt-2">
                        ${item.shopify_synced ? 
                            `<div class="sync-status ${item.needs_shopify_sync ? 'needs-sync' : item.last_sync_status === 'success' ? 'sync-success' : 'sync-error'}">
                                <div class="status-icon">
                                    ${item.needs_shopify_sync ? 
                                        '<i class="fas fa-exclamation-triangle"></i>' :
                                        item.last_sync_status === 'success' ? 
                                            '<i class="fas fa-check"></i>' : 
                                            '<i class="fas fa-times"></i>'
                                    }
                                </div>
                                <div class="status-text">
                                    ${item.needs_shopify_sync ? 
                                        'Needs Sync' :
                                        item.last_sync_status === 'success' ? 
                                            'Synced' : 
                                            `Error: ${item.last_sync_error || 'Unknown'}`
                                    }
                                </div>
                                ${item.shopify_sync_timestamp ? 
                                    `<div class="sync-time">Last sync: ${new Date(item.shopify_sync_timestamp).toLocaleString()}</div>` : 
                                    ''
                                }
                            </div>` : 
                            `<div class="d-flex align-items-center justify-content-center gap-2">
                                <div><i class="fas fa-times text-danger"></i><small class="ms-1">Not Synced</small></div>
                                <button class="btn btn-sm btn-outline-primary" onclick="linkShopify('${item.id}')">
                                    <i class="fas fa-link"></i>
                                </button>
                            </div>`
                        }
                    </div>
                `;
                inventoryGrid.appendChild(gridItem);
            });
        })
        .catch(error => {
            console.error('Error:', error);
            inventoryTable.innerHTML = '<tr><td colspan="10" class="text-center">Error loading inventory</td></tr>';
            inventoryGrid.innerHTML = '<div class="text-center w-100">Error loading inventory</div>';
        });
}

function setView(view) {
    const tableView = document.getElementById('tableView');
    const gridView = document.getElementById('gridView');
    const tableToggleBtn = document.getElementById('viewToggle');
    const gridToggleBtn = document.getElementById('viewToggleGrid');
    
    currentView = view;
    localStorage.setItem('inventoryViewPreference', view);
    
    if (view === 'grid') {
        tableView.classList.add('d-none');
        gridView.classList.remove('d-none');
        tableToggleBtn.innerHTML = '<i class="fas fa-grip-horizontal me-1"></i>View';
        gridToggleBtn.innerHTML = '<i class="fas fa-table me-1"></i>View';
    } else {
        gridView.classList.add('d-none');
        tableView.classList.remove('d-none');
        tableToggleBtn.innerHTML = '<i class="fas fa-table me-1"></i>View';
        gridToggleBtn.innerHTML = '<i class="fas fa-grip-horizontal me-1"></i>View';
    }
}

function toggleView() {
    setView(currentView === 'table' ? 'grid' : 'table');
}

// Add event listeners for both view toggle buttons
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('viewToggle').addEventListener('click', toggleView);
    document.getElementById('viewToggleGrid').addEventListener('click', toggleView);
});

function updateSelectAllState() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.row-select');
    const checkedBoxes = document.querySelectorAll('.row-select:checked');
    
    if (checkboxes.length === 0) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    } else if (checkedBoxes.length === 0) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    } else if (checkedBoxes.length === checkboxes.length) {
        selectAll.checked = true;
        selectAll.indeterminate = false;
    } else {
        selectAll.checked = false;
        selectAll.indeterminate = true;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Load saved view preference
    const savedView = localStorage.getItem('inventoryViewPreference');
    if (savedView) {
        setView(savedView);
    }
    
    // Add Font Awesome for icons
    const fontAwesome = document.createElement('link');
    fontAwesome.rel = 'stylesheet';
    fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
    document.head.appendChild(fontAwesome);
    
    const gameSelect = document.getElementById('gameSelect');
    const expansionSelect = document.getElementById('expansionSelect');
    const sortSelect = document.getElementById('sortSelect');
    const foilSelect = document.getElementById('foilSelect');
    const searchInput = document.getElementById('searchInput');
    const clearSearch = document.getElementById('clearSearch');

    // Add search input handler with debounce
    searchInput.addEventListener('input', debounce(() => {
        loadInventory();
    }, 300));

    // Add clear search handler
    clearSearch.addEventListener('click', () => {
        searchInput.value = '';
        loadInventory();
    });

    // Load games on page load
    fetch('/api/inventory/games')
        .then(response => response.json())
        .then(response => {
            const games = response.games || [];
            gameSelect.innerHTML = ''; // Clear existing options
            games.forEach(game => {
                const option = document.createElement('option');
                option.value = game;
                option.textContent = game;
                gameSelect.appendChild(option);
            });
        });

    // Load expansions when game is selected
    gameSelect.addEventListener('change', function() {
        if (!this.value) return; // Skip if no game selected
        
        expansionSelect.innerHTML = ''; // Clear existing options
        expansionSelect.disabled = false; // Always enabled since we have "All Expansions"
        
        fetch(`/api/inventory/expansions/${encodeURIComponent(this.value)}`)
            .then(response => response.json())
            .then(response => {
                const expansions = response.expansions || [];
                expansions.forEach(expansion => {
                    const option = document.createElement('option');
                    option.value = expansion;
                    option.textContent = expansion;
                    expansionSelect.appendChild(option);
                });
                loadInventory(); // Only load inventory after selecting a game
            });
    });

    // Load inventory when filters change
    expansionSelect.addEventListener('change', loadInventory);
    sortSelect.addEventListener('change', loadInventory);
    foilSelect.addEventListener('change', loadInventory);
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle select all checkbox
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.row-select');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Handle individual checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('row-select')) {
            updateSelectAllState();
        }
    });
    
    // Don't load inventory initially - wait for user to select a game or search
});

function linkShopify(itemId) {
    const btn = event.target.closest('button');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    fetch(`/api/inventory/link-shopify/${itemId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        // Refresh the inventory to show updated status
        loadInventory();
    })
    .catch(error => {
        console.error('Error:', error);
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-link"></i>';
        alert('Failed to link with Shopify: ' + error.message);
    });
}

function selectNeedsSync() {
    // Show loading state
    const btn = document.getElementById('selectNeedsSyncBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Get all items that need sync from a dedicated endpoint
    fetch('/api/inventory/needs-sync')
        .then(response => response.json())
        .then(data => {
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('bulkLinkingModal'));
            modal.show();

            const progressBar = document.getElementById('linkingProgress');
            const statusText = document.getElementById('linkingStatus');
            let successCount = 0;
            let failCount = 0;

            // Show initial status
            statusText.textContent = `Linking ${data.items.length} items to Shopify...`;
            progressBar.style.width = '0%';

            // Process each item
            const processItems = async () => {
                let processedItems = 0;

                for (const item of data.items) {
                    try {
                        const result = await fetch('/api/inventory/bulk-link-shopify', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                item_ids: [item._id]
                            })
                        });
                        
                        const resultData = await result.json();
                        
                        if (resultData.error) {
                            throw new Error(resultData.error);
                        }

                        // Update counts
                        if (resultData.success_count > 0) {
                            successCount++;
                        } else {
                            failCount++;
                        }

                        processedItems++;
                        
                        // Update progress
                        const progress = Math.round((processedItems / data.items.length) * 100);
                        progressBar.style.width = `${progress}%`;
                        statusText.textContent = `Processed ${processedItems}/${data.items.length} items (${successCount} successful, ${failCount} failed)`;

                        // Delay between requests
                        await new Promise(resolve => setTimeout(resolve, 500));

                    } catch (error) {
                        console.error('Error processing item:', item._id, error);
                        failCount++;
                        processedItems++;
                        
                        // Update progress even on error
                        const progress = Math.round((processedItems / data.items.length) * 100);
                        progressBar.style.width = `${progress}%`;
                        statusText.textContent = `Processed ${processedItems}/${data.items.length} items (${successCount} successful, ${failCount} failed)`;
                    }
                }

                // Update final status
                progressBar.style.width = '100%';
                statusText.textContent = `Complete! Successfully linked ${successCount} items, ${failCount} failed`;

                // Reload inventory after a short delay
                setTimeout(() => {
                    loadInventory();
                    modal.hide();
                }, 2000);

                // Restore button state
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            };

            processItems().catch(error => {
                console.error('Error:', error);
                statusText.textContent = `Error: ${error.message}`;
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            });
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to get items: ' + error.message);
            // Restore button state
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
        });
}

function updateValue(input) {
    const id = input.dataset.id;
    const type = input.dataset.type;
    const value = input.value;
    
    // Show loading state
    input.disabled = true;
    const saveBtn = input.nextElementSibling;
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    }
    const originalValue = input.value;

    // Get the Shopify status cell for this item
    const row = input.closest('tr');
    const shopifyCell = row.querySelector('.col-shopify');
    const syncStatus = shopifyCell.querySelector('.sync-status');
    
    fetch(`/api/inventory/update/${id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            [type]: value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        // Update was successful, enable input and button
        input.disabled = false;
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i>';
        }
        
        // Update all instances of this item's value (in both views)
        document.querySelectorAll(`input[data-id="${id}"][data-type="${type}"]`).forEach(el => {
            el.value = type === 'price' ? (data.price ? parseFloat(data.price).toFixed(2) : '0.00') : data.quantity;
        });
        
        // Update Shopify sync status if available
        if (syncStatus && data.shopify_synced) {
            const statusClass = data.last_sync_status === 'success' ? 'sync-success' : 'sync-error';
            const statusIcon = data.last_sync_status === 'success' ? 
                '<i class="fas fa-check"></i>' : 
                '<i class="fas fa-times"></i>';
            const statusText = data.last_sync_status === 'success' ? 
                'Synced' : 
                `Error: ${data.last_sync_error || 'Unknown'}`;
            
            syncStatus.className = `sync-status ${statusClass}`;
            syncStatus.innerHTML = `
                <div class="status-icon">${statusIcon}</div>
                <div class="status-text">${statusText}</div>
                ${data.shopify_sync_timestamp ? 
                    `<div class="sync-time">Last sync: ${new Date(data.shopify_sync_timestamp).toLocaleString()}</div>` : 
                    ''}
            `;
        }
        
        // Recalculate total value and stock
        let totalValue = 0;
        let totalStock = 0;
        document.querySelectorAll('input[data-type="price"]').forEach(priceInput => {
            const itemId = priceInput.dataset.id;
            const quantityInput = document.querySelector(`input[data-id="${itemId}"][data-type="quantity"]`);
            if (priceInput.value && quantityInput.value) {
                const quantity = parseInt(quantityInput.value);
                totalValue += parseFloat(priceInput.value) * quantity;
                totalStock += quantity;
            }
        });
        const currencySymbol = document.querySelector('.input-group-text')?.textContent || '$';
        document.getElementById('totalValue').textContent = formatPrice(totalValue, currencySymbol);
        document.getElementById('totalStock').textContent = totalStock.toLocaleString();
    })
    .catch(error => {
        console.error('Error:', error);
        // Revert to original value on error and re-enable controls
        input.value = originalValue;
        input.disabled = false;
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i>';
        }
        alert('Failed to update value: ' + error.message);
    });
}
</script>
{% endblock %}
