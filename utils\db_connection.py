from config.config import Config
import os
import logging
from pymongo import MongoClient, ReadPreference
from pymongo.server_api import ServerApi
from flask import current_app

logger = logging.getLogger(__name__)

class DatabaseConnection:
    _instance = None
    _client = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = DatabaseConnection()
        return cls._instance

    def __init__(self):
        if DatabaseConnection._instance is not None:
            raise Exception("DatabaseConnection is a singleton. Use get_instance() instead.")
        DatabaseConnection._instance = self

    def get_client(self):
        """Get MongoDB client with environment-specific configuration."""
        if self._client is None:
            try:
                # Get environment-specific URI
                env = os.environ.get('FLASK_ENV', 'development')
                if env == 'production':
                    mongo_uri = os.environ.get('MONGO_URI_PRODUCTION') or current_app.config.get('MONGO_URI_PRODUCTION')
                else:
                    mongo_uri = os.environ.get('MONGO_URI_LOCAL') or current_app.config.get('MONGO_URI_LOCAL')

                # If no environment-specific URI is found, fall back to default
                if not mongo_uri:
                    mongo_uri = os.environ.get('MONGO_URI') or current_app.config.get('MONGO_URI')

                # Create client with optimized settings
                self._client = MongoClient(
                    mongo_uri,
                    server_api=ServerApi('1'),
                    serverSelectionTimeoutMS=30000,
                    connectTimeoutMS=20000,
                    maxPoolSize=50,
                    retryWrites=True,
                    read_preference=ReadPreference.PRIMARY_PREFERRED,
                    w='majority'
                )

                # Test connection
                self._client.server_info()
                logger.info(f"Successfully connected to MongoDB cluster in {env} environment")
                
            except Exception as e:
                logger.error(f"Failed to connect to MongoDB cluster: {str(e)}")
                raise

        return self._client

    def get_db(self, db_name=None):
        """Get database instance."""
        if db_name is None:
            db_name = os.environ.get('MONGO_DBNAME') or current_app.config.get('MONGO_DBNAME', 'test')
        return self.get_client()[db_name]

    def close(self):
        """Close the MongoDB connection."""
        if self._client:
            self._client.close()
            self._client = None
            logger.info("Closed MongoDB connection")

# Global instance
db_connection = DatabaseConnection.get_instance()

