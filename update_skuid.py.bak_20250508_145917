from pymongo import MongoClient
import logging
import time
from bson import ObjectId

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_client = MongoClient(
    mongo_uri,
    connectTimeoutMS=30000,
    socketTimeoutMS=60000,
    serverSelectionTimeoutMS=30000
)
db = mongo_client['test']
shopify_collection = db['shProducts']
catalog_collection = db['catalog']

# Language mapping
language_code_to_id = {
    "EN": 1, "JP": 2, "DE": 3, "FR": 4, "IT": 5,
    "ES": 6, "PT": 7, "RU": 8, "KO": 9, "CS": 10, "CT": 11
}

# Printing type mapping
printing_code_to_id = {
    "NF": 1, "FO": 2, "UL": 7, "1E": 8, "NH": 10,
    "HF": 11, "LI": 23, "RF": 77, "1HF": 79, "PF": 81,
    "CF": 112, "1ENO": 105, "1ECF": 106, "1ERF": 107,
    "UNO": 110, "URF": 111, "1EFO": 121
}

# Condition mapping
condition_code_to_id = {
    "NM": 1, "LP": 2, "MP": 3, "HP": 4, "DM": 5
}

def get_catalog_record(product_id):
    """Get catalog record"""
    try:
        product_id_int = int(product_id)
        catalog_record = catalog_collection.find_one(
            {"productId": product_id_int},
            projection={"skus": 1}  # Only retrieve the skus field
        )
        return catalog_record
    except (ValueError, TypeError):
        logger.error(f"Invalid product ID: {product_id}")
        return None

def match_variant_to_skuid(variant, product_id):
    """Match a variant to a TCGPlayer SKU ID"""
    try:
        # If we don't have a skuId, try to match using SKU parts
        if 'sku' in variant and variant['sku']:
            sku = variant['sku']
            sku_parts = sku.split('-')
            
            # We need at least 3 parts for language, printing, and condition
            if len(sku_parts) >= 3:
                # Extract the last three parts (language, printing, condition)
                # regardless of how many parts are in the SKU
                lang_abbr = sku_parts[-3] if len(sku_parts) >= 3 else None
                printing_code = sku_parts[-2] if len(sku_parts) >= 2 else None
                condition_id = sku_parts[-1] if len(sku_parts) >= 1 else None
                
                # Convert language code to language ID
                lang_id = language_code_to_id.get(lang_abbr.upper()) if lang_abbr else None
                
                # Convert printing code to printing ID
                printing_id = None
                if printing_code:
                    printing_code_upper = printing_code.upper()
                    printing_id = printing_code_to_id.get(printing_code_upper)
                
                # Convert condition to condition ID
                condition_id_int = None
                if condition_id:
                    try:
                        condition_id_int = int(condition_id) if condition_id.isdigit() else condition_code_to_id.get(condition_id.upper())
                    except (ValueError, TypeError):
                        pass
                
                if lang_id is not None and printing_id is not None and condition_id_int is not None:
                    # Get catalog record
                    catalog_record = get_catalog_record(product_id)
                    
                    if catalog_record:
                        # Find matching sku_info
                        for s in catalog_record.get("skus", []):
                            # Check for direct match
                            if ((s.get("langAbbr") == lang_abbr.upper() or s.get("languageId") == lang_id) and
                                str(s.get("printingId")) == str(printing_id) and
                                s.get("conditionId") == condition_id_int and
                                'skuId' in s):
                                return s['skuId']
        
        return None
    except Exception as e:
        logger.error(f"Error in match_variant_to_skuid: {str(e)}")
        return None

def update_variant_skuid(product_id, variant_id, sku_id):
    """Update a variant with a skuId"""
    try:
        # Convert product_id to ObjectId if it's a string
        if isinstance(product_id, str):
            product_id = ObjectId(product_id)
        
        # Extract the actual ID value from the variant_id object if it's a dict
        if isinstance(variant_id, dict) and '$numberLong' in variant_id:
            variant_id = int(variant_id['$numberLong'])
        
        print(f"Using variant_id: {variant_id} (type: {type(variant_id)})")
        
        # Update the variant with the skuId
        # First, try to update using the variant_id directly
        result = shopify_collection.update_one(
            {'_id': product_id, 'variants.id': variant_id},
            {'$set': {'variants.$.skuId': sku_id}}
        )
        
        if result.modified_count == 0:
            # If that didn't work, try with the variant_id as a nested object
            result = shopify_collection.update_one(
                {'_id': product_id, 'variants.id.$numberLong': str(variant_id)},
                {'$set': {'variants.$.skuId': sku_id}}
            )
        
        return result.modified_count
    except Exception as e:
        logger.error(f"Error updating variant {variant_id}: {str(e)}")
        print(f"Error updating variant {variant_id}: {str(e)}")
        return 0

def process_product(product_id):
    """Process a product by ID"""
    try:
        start_time = time.time()
        
        # Find the product by productId
        product = shopify_collection.find_one({'productId': product_id})
        
        if not product:
            print(f"Product with ID {product_id} not found")
            return
        
        print(f"Found product: {product.get('title')}")
        print(f"MongoDB _id: {product.get('_id')}")
        
        variants = product.get('variants', [])
        
        # Skip if no variants
        if not variants:
            print("No variants found for this product")
            return
        
        # Process each variant
        variants_updated = 0
        
        # Create a copy of the variants list to modify
        updated_variants = []
        
        for variant in variants:
            # Create a copy of the variant to modify
            updated_variant = variant.copy()
            
            # Print variant details
            print(f"\nProcessing variant: {variant.get('title')}")
            print(f"SKU: {variant.get('sku')}")
            
            # Match variant to skuId
            sku_id = match_variant_to_skuid(variant, product.get('productId'))
            
            if sku_id:
                print(f"Found matching skuId: {sku_id}")
                
                # Add skuId directly to the variant
                updated_variant['skuId'] = sku_id
                variants_updated += 1
                print(f"Added skuId {sku_id} to variant {variant.get('title')}")
            else:
                print(f"No matching skuId found for variant {variant.get('title')}")
            
            # Add the updated variant to the list
            updated_variants.append(updated_variant)
        
        # Update the entire product document with all variants at once
        if variants_updated > 0:
            result = shopify_collection.update_one(
                {'_id': product.get('_id')},
                {'$set': {
                    'variants': updated_variants,
                    'skusMatched': True
                }}
            )
            
            print(f"\nUpdated product with {variants_updated} variants in {time.time() - start_time:.2f} seconds")
            print(f"Modified count: {result.modified_count}")
            
            # Verify the update by retrieving the product again
            updated_product = shopify_collection.find_one({'_id': product.get('_id')})
            if updated_product:
                print("\nVerifying update:")
                for variant in updated_product.get('variants', []):
                    print(f"Variant: {variant.get('title')}, skuId: {variant.get('skuId', 'Not assigned')}")
            else:
                print("Failed to retrieve updated product")
        else:
            print("\nNo variants were updated")
        
    except Exception as e:
        logger.error(f"Error processing product {product_id}: {str(e)}")
        print(f"Error processing product {product_id}: {str(e)}")

def main():
    """Main function to process a specific product"""
    try:
        # Specific product ID to process
        product_id = 21876
        
        print(f"\n=== UPDATING SKUID FOR PRODUCT ID: {product_id} ===")
        
        # Process the specific product
        process_product(product_id)
        
        print("\n=== UPDATE COMPLETE ===")
        
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        print(f"Error in main: {str(e)}")
    finally:
        # Clean up resources
        mongo_client.close()

if __name__ == "__main__":
    main()
