{% extends "base.html" %}

{% block title %}Add Attendee - {{ event.title }}{% endblock %}

{% block styles %}
{{ super() }}
{% include "events/event_styles.html" %}
{% endblock %}

{% block content %}
<div class="container-fluid event-page-content">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Add Attendee - {{ event.title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('event.event_attendees', event_id=event.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Attendees
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if event.is_full() %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> This event is full. You cannot add more attendees.
                    </div>
                    {% else %}
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Basic Information</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="name">Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="email">Email <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            {% if event.custom_fields %}
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Custom Fields</h4>
                                    </div>
                                    <div class="card-body">
                                        {% for field in event.custom_fields %}
                                        <div class="form-group">
                                            <label for="custom_{{ field.name }}">
                                                {{ field.name }}
                                                {% if field.required %}
                                                <span class="text-danger">*</span>
                                                {% endif %}
                                            </label>
                                            
                                            {% if field.type == 'text' %}
                                            <input type="text" class="form-control" id="custom_{{ field.name }}" name="custom_{{ field.name }}" {% if field.required %}required{% endif %}>
                                            
                                            {% elif field.type == 'number' %}
                                            <input type="number" class="form-control" id="custom_{{ field.name }}" name="custom_{{ field.name }}" {% if field.required %}required{% endif %}>
                                            
                                            {% elif field.type == 'email' %}
                                            <input type="email" class="form-control" id="custom_{{ field.name }}" name="custom_{{ field.name }}" {% if field.required %}required{% endif %}>
                                            
                                            {% elif field.type == 'url' %}
                                            <input type="url" class="form-control" id="custom_{{ field.name }}" name="custom_{{ field.name }}" {% if field.required %}required{% endif %}>
                                            
                                            {% elif field.type == 'textarea' %}
                                            <textarea class="form-control" id="custom_{{ field.name }}" name="custom_{{ field.name }}" rows="3" {% if field.required %}required{% endif %}></textarea>
                                            
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary btn-lg">Add Attendee</button>
                                <a href="{{ url_for('event.event_attendees', event_id=event.id) }}" class="btn btn-secondary btn-lg ml-2">Cancel</a>
                            </div>
                        </div>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
