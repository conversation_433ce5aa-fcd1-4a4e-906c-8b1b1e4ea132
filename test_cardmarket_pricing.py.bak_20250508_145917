# Test script to run CardMarket pricing on real products
from pymongo import MongoClient
import random
import sys
import logging
from datetime import datetime, timezone

# Import the actual functions from cmaautopricing.py
sys.path.append('.')  # Ensure the current directory is in the path
from cmaautopricing import (
    PricingCalculator, 
    prepare_cardmarket_settings, 
    fetch_cardmarket_pricing_data, 
    determine_printing_type, 
    extract_condition
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
db = client['test']

# Try different users to find one with valid CardMarket mappings
usernames = ['admintcg', 'khaoz', 'fantasytower', 'supercollectiblesmx']
products = []

for username in usernames:
    user_products = list(db.shProducts.find({
        'username': username,
        'productId': {'$exists': True, '$ne': None}  # Ensure productId exists and is not null
    }).limit(100))  # Get 100 products per user
    
    logger.info(f"Found {len(user_products)} products for username '{username}' with valid productId")
    products.extend(user_products)

logger.info(f"Found {len(products)} total products across all users with valid productId")

# Check if priceguides collection exists and how many documents it has
if 'priceguides' in db.list_collection_names():
    pg_count = db.priceguides.count_documents({})
    logger.info(f"Found priceguides collection with {pg_count} documents")
    
    # Check a few sample documents
    sample_docs = list(db.priceguides.find().limit(3))
    logger.info("Sample documents from priceguides collection:")
    for doc in sample_docs:
        logger.info(f"  Document: {doc}")
else:
    logger.info("priceguides collection not found in test database")

# Create a mapping from TCGPlayer ID to CardMarket ID using the cardtrader.matchedIds collection
cardtrader_db = client['cardtrader']
tcg_to_cm_mapping = {}

# Check if the matchedIds collection exists
if 'matchedIds' in cardtrader_db.list_collection_names():
    logger.info("Found matchedIds collection in cardtrader database")
    
    # Create the mapping
    for doc in cardtrader_db.matchedIds.find({}, {'tcg_player_id': 1, 'card_market_id': 1}):
        if 'tcg_player_id' in doc and 'card_market_id' in doc:
            tcg_to_cm_mapping[doc['tcg_player_id']] = doc['card_market_id']
    
    logger.info(f"Created mapping for {len(tcg_to_cm_mapping)} products from TCGPlayer ID to CardMarket ID")
else:
    logger.info("matchedIds collection not found in cardtrader database")

# Print some sample mappings
logger.info("Sample TCGPlayer ID to CardMarket ID mappings:")
sample_keys = list(tcg_to_cm_mapping.keys())[:5]
for key in sample_keys:
    logger.info(f"  TCGPlayer ID: {key} -> CardMarket ID: {tcg_to_cm_mapping[key]}")

# Filter products that have a mapping to CardMarket ID
products_with_mapping = []
for product in products:
    product_id = product.get('productId')
    if product_id in tcg_to_cm_mapping:
        # Add the CardMarket ID to the product for later use
        product['cardMarketId'] = tcg_to_cm_mapping[product_id]
        logger.info(f"Product: {product.get('title')} (TCGPlayer ID: {product_id}) -> CardMarket ID: {product['cardMarketId']}")
        products_with_mapping.append(product)
    else:
        logger.info(f"No CardMarket ID mapping found for product: {product.get('title')} (TCGPlayer ID: {product_id})")

logger.info(f"Found {len(products_with_mapping)} products with valid CardMarket ID mapping")

if not products_with_mapping:
    logger.info("No products found with valid CardMarket ID mapping")
    sys.exit(1)

# Select 5 random products from the results
selected_products = random.sample(products_with_mapping, min(5, len(products_with_mapping)))
logger.info(f"Selected {len(selected_products)} random products for testing")

# Get user profile for admintcg
user_profile = db.user.find_one({'username': 'admintcg'})
if not user_profile:
    logger.info("User profile not found for admintcg")
    sys.exit(1)

# Prepare settings for the pricing calculator
settings = prepare_cardmarket_settings(user_profile)
calculator = PricingCalculator(settings, user_profile.get('currency', 'EUR'))

# Process each product
for product in selected_products:
    logger.info("\n" + "="*80)
    logger.info(f"PRODUCT: {product.get('title')} (ID: {product.get('productId')})")
    logger.info(f"Game: {product.get('gameName')}")
    logger.info(f"Product Type: {product.get('product_type')}")
    logger.info("-"*80)
    
    # Custom function to fetch pricing data directly from priceguides collection
    def fetch_pricing_from_priceguides(product_id, card_market_id):
        pricing_data = {}
        
        try:
            # Query the priceguides collection for the CardMarket ID
            logger.info(f"Querying priceguides for CardMarket ID: {card_market_id}")
            doc = db.priceguides.find_one({"idProduct": card_market_id})
            
            if doc:
                logger.info(f"Found pricing data with idProduct: {doc.get('idProduct')}")
                
                # Use the original product ID as the key in our pricing data
                if product_id not in pricing_data:
                    pricing_data[product_id] = []
                
                # Add normal pricing
                normal_pricing = {
                    'idProduct': doc.get('idProduct'),
                    'subTypeName': 'Normal',
                    'trend': doc.get('trend'),
                    'low': doc.get('low'),
                    'avg': doc.get('avg'),
                    'avg1': doc.get('avg1'),
                    'avg7': doc.get('avg7'),
                    'avg30': doc.get('avg30')
                }
                pricing_data[product_id].append(normal_pricing)
                
                # Add foil pricing if available
                if doc.get('trendFoil') is not None:
                    foil_pricing = {
                        'idProduct': doc.get('idProduct'),
                        'subTypeName': 'Foil',
                        'trend': doc.get('trendFoil'),
                        'low': doc.get('lowFoil'),
                        'avg': doc.get('avgFoil'),
                        'avg1': doc.get('avg1Foil'),
                        'avg7': doc.get('avg7Foil'),
                        'avg30': doc.get('avg30Foil')
                    }
                    pricing_data[product_id].append(foil_pricing)
                
                # Add reverse holofoil pricing if available
                if doc.get('trendReverseHolo') is not None:
                    reverse_holo_pricing = {
                        'idProduct': doc.get('idProduct'),
                        'subTypeName': 'Reverse Holofoil',
                        'trend': doc.get('trendReverseHolo'),
                        'low': doc.get('lowReverseHolo'),
                        'avg': doc.get('avgReverseHolo'),
                        'avg1': doc.get('avg1ReverseHolo'),
                        'avg7': doc.get('avg7ReverseHolo'),
                        'avg30': doc.get('avg30ReverseHolo')
                    }
                    pricing_data[product_id].append(reverse_holo_pricing)
                
                # Add etched foil pricing if available
                if doc.get('trendEtched') is not None:
                    etched_pricing = {
                        'idProduct': doc.get('idProduct'),
                        'subTypeName': 'Etched Foil',
                        'trend': doc.get('trendEtched'),
                        'low': doc.get('lowEtched'),
                        'avg': doc.get('avgEtched'),
                        'avg1': doc.get('avg1Etched'),
                        'avg7': doc.get('avg7Etched'),
                        'avg30': doc.get('avg30Etched')
                    }
                    pricing_data[product_id].append(etched_pricing)
            else:
                logger.info(f"No pricing data found in priceguides collection for CardMarket ID: {card_market_id}")
            
            return pricing_data
        except Exception as e:
            logger.error(f"Error fetching CardMarket prices: {str(e)}")
            return {}
    
    # Fetch pricing data using our custom function
    product_id = str(product.get('productId'))
    card_market_id = product.get('cardMarketId')
    
    logger.info(f"Fetching CardMarket pricing data for product ID: {product_id} (CardMarket ID: {card_market_id})")
    pricing_data = fetch_pricing_from_priceguides(product_id, card_market_id)
    
    if not pricing_data or product_id not in pricing_data:
        logger.info("No CardMarket pricing data found for this product")
        continue
    
    product_pricing = pricing_data[product_id]
    valid_subtypes = [p.get('subTypeName') for p in product_pricing 
                      if p.get('subTypeName') and (p.get('trend') is not None or 
                                                  p.get('low') is not None or 
                                                  p.get('avg') is not None)]
    
    logger.info(f"Found pricing data with subtypes: {valid_subtypes}")
    
    # Process each variant
    for variant in product.get('variants', []):
        logger.info("\nVARIANT: " + variant.get('title', 'Unknown'))
        logger.info(f"Current Price: ${float(variant.get('price', 0)):.2f}")
        
        # Determine printing type and condition using the actual functions
        printing_type = determine_printing_type(variant.get('title', ''), valid_subtypes)
        condition = extract_condition(variant.get('title', ''))
        
        logger.info(f"Determined Printing Type: {printing_type}")
        logger.info(f"Determined Condition: {condition}")
        
        # Find matching price data
        matched_price = None
        for p in product_pricing:
            if p.get('subTypeName', '').lower() == printing_type.lower():
                matched_price = p
                break
        
        if not matched_price:
            logger.info("No matching price data found for this variant")
            continue
        
        # Extract pricing info
        pricing_info = {}
        for price_type in ['trend', 'low', 'avg', 'avg1', 'avg7', 'avg30']:
            price = matched_price.get(price_type)
            if price is not None:
                pricing_info[price_type] = float(price)
        
        logger.info("CardMarket Prices:")
        for price_type, price in pricing_info.items():
            logger.info(f"  {price_type}: €{price:.2f}")
        
        # Create sku_info for price calculation
        sku_info = {
            'pricingInfo': pricing_info,
            'condName': condition,
            'printingName': printing_type,
            'skuId': variant.get('id'),
            'variantTitle': variant.get('title')
        }
        
        # Calculate new price using the actual calculator
        new_price, is_missing, price_history = calculator.calculate_final_price(sku_info, product)
        
        if not is_missing and new_price is not None:
            old_price = float(variant.get('price', 0))
            logger.info(f"\nPrice Calculation Result:")
            logger.info(f"  Old Price: ${old_price:.2f}")
            logger.info(f"  New Price: ${new_price:.2f}")
            logger.info(f"  Difference: ${new_price - old_price:.2f} ({((new_price/old_price)-1)*100 if old_price else 0:.2f}%)")
            
            logger.info("\nPrice Calculation Steps:")
            for step in price_history:
                logger.info(f"  {step['step']}: ${step['old_price']:.2f} -> ${step['new_price']:.2f} ({step['details']})")
        else:
            logger.info("Could not calculate a new price for this variant")

logger.info("\n" + "="*80)
logger.info("Test completed")
