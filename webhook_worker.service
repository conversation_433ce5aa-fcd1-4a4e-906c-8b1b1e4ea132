[Unit]
Description=Shopify Webhook Processing Worker
After=network.target redis.service mongodb.service

[Service]
Type=simple
User=apache
Group=apache
WorkingDirectory=/var/www/html/cfc.tcgsync.com
Environment=PYTHONPATH=/var/www/html/cfc.tcgsync.com
# Webhook worker is intentionally disabled to prevent webhook records from being deleted
ExecStart=/bin/echo "Webhook worker is disabled to preserve webhook records"
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=full
ProtectHome=read-only

# SELinux settings
SELinuxContext=system_u:system_r:redis_t:s0

# Resource limits
CPUQuota=50%
MemoryLimit=512M
LimitNOFILE=65535

[Install]
WantedBy=multi-user.target
