// Tab switching
document.querySelectorAll('.auth-tab').forEach(tab => {
    tab.addEventListener('click', () => {
        // Update tabs
        document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        
        // Update content
        document.querySelectorAll('.auth-content').forEach(content => {
            content.style.opacity = '0';
            content.style.transform = 'translateY(10px)';
            setTimeout(() => {
                content.classList.remove('active');
                if (content.id === `${tab.dataset.tab}-tab`) {
                    content.classList.add('active');
                    requestAnimationFrame(() => {
                        content.style.opacity = '1';
                        content.style.transform = 'translateY(0)';
                    });
                }
            }, 300);
        });
    });
});

// Feature rotation with enhanced transitions
const slides = document.querySelectorAll('.feature-slide');
let currentSlide = 0;
let slideInterval;

function showSlide(index) {
    // Prevent transitions from stacking
    if (document.querySelector('.feature-slide.transitioning')) {
        return;
    }
    
    const currentActive = document.querySelector('.feature-slide.active');
    
    // Mark that we're in a transition
    if (currentActive) {
        currentActive.classList.add('transitioning');
        
        // Start fade out
        currentActive.style.opacity = '0';
        currentActive.style.transform = 'translateY(40px)';
        
        // After fade out completes
        setTimeout(() => {
            // Remove active and transitioning classes from all slides
            slides.forEach(slide => {
                slide.classList.remove('active', 'transitioning');
            });
            
            // Prepare the new slide
            slides[index].classList.add('active');
            
            // Trigger the transition in
            requestAnimationFrame(() => {
                slides[index].style.opacity = '1';
                slides[index].style.transform = 'translateY(0)';
            });
        }, 600); // Slightly shorter transition out time for more responsive feel
    } else {
        // Initial slide (no active slide yet)
        // Remove active class from all slides first
        slides.forEach(slide => {
            slide.classList.remove('active');
        });
        
        slides[index].classList.add('active');
        requestAnimationFrame(() => {
            slides[index].style.opacity = '1';
            slides[index].style.transform = 'translateY(0)';
        });
    }
}

function nextSlide() {
    currentSlide = (currentSlide + 1) % slides.length;
    showSlide(currentSlide);
}

function startSlideRotation() {
    // Clear any existing interval
    if (slideInterval) clearInterval(slideInterval);
    
    // Set new interval - change slide every 5 seconds (slightly faster for better engagement)
    slideInterval = setInterval(nextSlide, 5000);
}

// Start the rotation after a short delay
setTimeout(() => {
    startSlideRotation();
    
    // Pause rotation when user hovers over the feature section
    const featureSection = document.querySelector('.features-section');
    
    featureSection.addEventListener('mouseenter', () => {
        clearInterval(slideInterval);
    });
    
    featureSection.addEventListener('mouseleave', () => {
        startSlideRotation();
    });
}, 1000);

// Password reset modal
function showResetModal() {
    const modal = document.getElementById('reset-password-modal');
    modal.style.display = 'block';
    requestAnimationFrame(() => {
        modal.classList.add('active');
    });
}

function closeResetModal() {
    const modal = document.getElementById('reset-password-modal');
    modal.classList.remove('active');
    setTimeout(() => {
        modal.style.display = 'none';
        // Reset form and success message
        const form = document.getElementById('reset-password-form');
        form.style.display = 'block';
        form.style.opacity = '1';
        form.style.transform = 'none';
        document.getElementById('reset-success').classList.remove('active');
    }, 300);
}

// Magic link login
function showEmailPrompt() {
    // This function is now just a placeholder for backward compatibility
    // The magic link form is now directly embedded in the page
    document.getElementById('magic-email').focus();
}

function showMagicLinkSentModal() {
    const modal = document.getElementById('magic-link-sent-modal');
    modal.style.display = 'block';
    requestAnimationFrame(() => {
        modal.classList.add('active');
    });
}

function closeMagicLinkSentModal() {
    const modal = document.getElementById('magic-link-sent-modal');
    modal.classList.remove('active');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// Function to get URL parameters
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    var results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Toggle between magic link and password login
document.addEventListener('DOMContentLoaded', function() {
    const showPasswordLoginBtn = document.getElementById('show-password-login');
    const showStaffLoginBtn = document.getElementById('show-staff-login');
    const backToMagicLinkBtn = document.getElementById('back-to-magic-link');
    const backFromStaffLoginBtn = document.getElementById('back-from-staff-login');
    const magicLinkSection = document.querySelector('.magic-link-section');
    const passwordLoginSection = document.getElementById('password-login-section');
    const staffLoginSection = document.getElementById('staff-login-section');
    const loginToggle = document.querySelector('.login-toggle');
    
    // Check if staff parameter is present in URL
    const staffParam = getUrlParameter('staff');
    if (staffParam === 'true') {
        // Show staff login section
        magicLinkSection.style.display = 'none';
        loginToggle.style.display = 'none';
        passwordLoginSection.style.display = 'none';
        staffLoginSection.style.display = 'block';
    }
    
    // Show password login
    if (showPasswordLoginBtn) {
        showPasswordLoginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            magicLinkSection.style.display = 'none';
            loginToggle.style.display = 'none';
            passwordLoginSection.style.display = 'block';
            staffLoginSection.style.display = 'none';
        });
    }
    
    // Show staff login
    if (showStaffLoginBtn) {
        showStaffLoginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            magicLinkSection.style.display = 'none';
            loginToggle.style.display = 'none';
            passwordLoginSection.style.display = 'none';
            staffLoginSection.style.display = 'block';
        });
    }
    
    // Back to magic link from password login
    if (backToMagicLinkBtn) {
        backToMagicLinkBtn.addEventListener('click', function(e) {
            e.preventDefault();
            passwordLoginSection.style.display = 'none';
            staffLoginSection.style.display = 'none';
            magicLinkSection.style.display = 'block';
            loginToggle.style.display = 'block';
        });
    }
    
    // Back to magic link from staff login
    if (backFromStaffLoginBtn) {
        backFromStaffLoginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            staffLoginSection.style.display = 'none';
            magicLinkSection.style.display = 'block';
            loginToggle.style.display = 'block';
        });
    }
    
    // Handle magic link form submission
    const magicLinkForm = document.getElementById('magic-link-form');
    if (magicLinkForm) {
        magicLinkForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('magic-email').value;
            
            fetch(magicLinkForm.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ email: email }),
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    document.getElementById('magic-link-success').style.display = 'flex';
                    // Show modal
                    showMagicLinkSentModal();
                    // Clear the form
                    document.getElementById('magic-email').value = '';
                } else {
                    showErrorPopup(data.message || 'Failed to send magic link. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorPopup('An error occurred. Please try again.');
            });
        });
    }
});

function showSuccessPopup(message) {
    const popup = document.createElement('div');
    popup.className = 'success-popup';
    popup.innerHTML = `<div class="success-message">${message}</div>`;
    document.body.appendChild(popup);
    
    setTimeout(function() {
        popup.style.opacity = '0';
        setTimeout(function() {
            document.body.removeChild(popup);
        }, 300);
    }, 3000);
}

// Close modals when clicking outside
const resetModal = document.getElementById('reset-password-modal');
if (resetModal) {
    resetModal.addEventListener('click', function(e) {
        if (e.target === this) {
            closeResetModal();
        }
    });
}

const magicLinkModal = document.getElementById('magic-link-sent-modal');
if (magicLinkModal) {
    magicLinkModal.addEventListener('click', function(e) {
        if (e.target === this) {
            closeMagicLinkSentModal();
        }
    });
}

// Form submissions
document.addEventListener('DOMContentLoaded', function() {
    // Login form submission
const loginFormElement = document.getElementById('login-form');
if (loginFormElement) {
    loginFormElement.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            formData.forEach((value, key) => data[key] = value);
            
            fetch(this.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data),
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('Login response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Login response data:', data);
                if (data.success) {
                    console.log('Login successful, redirecting to:', data.redirect);
                    // Use window.location.href for more reliable redirection
                    window.location.href = data.redirect;
                } else if (data.suspended) {
                    console.log('Account suspended, redirecting to:', data.redirect);
                    window.location.href = data.redirect;
                } else if (data.magic_link_sent) {
                    console.log('Magic link sent due to password failure');
                    showSuccessPopup(data.message);
                } else {
                    console.log('Login failed:', data.message);
                    showErrorPopup(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorPopup('An error occurred. Please try again.');
            });
        });
    }

    // Staff login form submission
const staffLoginFormElement = document.getElementById('staff-login-form');
if (staffLoginFormElement) {
    staffLoginFormElement.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            formData.forEach((value, key) => data[key] = value);
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;
            submitButton.textContent = 'Logging in...';
            submitButton.disabled = true;
            
            console.log('Staff login form submitted', data);
            console.log('Form action:', this.action);
            
            fetch(this.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data),
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    console.log('Login successful, redirecting to:', data.redirect);
                    window.location.href = data.redirect;
                } else {
                    submitButton.textContent = originalButtonText;
                    submitButton.disabled = false;
                    showErrorPopup(data.message || 'Invalid email or PIN. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                submitButton.textContent = originalButtonText;
                submitButton.disabled = false;
                showErrorPopup('An error occurred. Please try again.');
            });
        });
    }

    // Register form submission
const registerFormElement = document.getElementById('register-form');
if (registerFormElement) {
    registerFormElement.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            formData.forEach((value, key) => data[key] = value);
            
            fetch(this.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data),
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.redirect) {
                        // Redirect to the specified URL (activating page)
                        window.location.href = data.redirect;
                    } else {
                        // Fallback to old behavior if no redirect is provided
                        const form = document.getElementById('register-form');
                        const successMessage = document.getElementById('registration-success');
                        
                        form.style.opacity = '0';
                        form.style.transform = 'translateY(-10px)';
                        
                        setTimeout(() => {
                            form.style.display = 'none';
                            successMessage.classList.add('active');
                        }, 300);
                    }
                } else {
                    showErrorPopup(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorPopup('An error occurred. Please try again.');
            });
        });
    }

    // Reset password form submission
const resetPasswordFormElement = document.getElementById('reset-password-form');
if (resetPasswordFormElement) {
    resetPasswordFormElement.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            formData.forEach((value, key) => data[key] = value);
            
            fetch(this.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data),
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const form = document.getElementById('reset-password-form');
                    form.style.opacity = '0';
                    form.style.transform = 'translateY(-10px)';
                    
                    setTimeout(() => {
                        form.style.display = 'none';
                        document.getElementById('reset-success').classList.add('active');
                    }, 300);
                } else {
                    showErrorPopup(data.message || 'Failed to send reset email. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorPopup('An error occurred. Please try again.');
            });
        });
    }
});


function showErrorPopup(message, persistent = false) {
    const popup = document.getElementById('error-popup');
    const errorMessage = document.getElementById('error-message');
    errorMessage.innerHTML = message;
    popup.style.display = 'block';
    
    if (!persistent) {
        setTimeout(function() {
            popup.style.display = 'none';
        }, 3000);
    }
}
