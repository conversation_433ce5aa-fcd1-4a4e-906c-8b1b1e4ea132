from config.config import Config
import requests
import base64
from datetime import datetime, timedelta
from flask import current_app
from models.ebay_model import EbayAccount
from urllib.parse import urlencode, quote

class EbayAPI:
    def __init__(self):
        self.app_id = current_app.config['EBAY_APP_ID']
        self.cert_id = current_app.config['EBAY_CERT_ID']
        self.ru_name = current_app.config['EBAY_RU_NAME']
        self.oauth_url = current_app.config['EBAY_OAUTH_URL']
        self.token_url = current_app.config['EBAY_TOKEN_URL']
        self.api_url = current_app.config['EBAY_API_URL']
        
        # Only request the scopes we need for orders
        self.scopes = [
            'https://api.ebay.com/oauth/api_scope',
            'https://api.ebay.com/oauth/api_scope/sell.fulfillment'
        ]

    def get_auth_url(self, state=None):
        """Generate the eBay OAuth authorization URL."""
        scope_str = ' '.join(self.scopes)
        current_app.logger.info(f"Using scopes: {scope_str}")
        
        # Build authorization URL according to eBay's specifications
        params = {
            'client_id': self.app_id,
            'response_type': 'code',
            'redirect_uri': 'https://login.tcgsync.com/ebay/callback',
            'scope': scope_str,
            'prompt': 'login',
            'RuName': self.ru_name  # Add RuName directly in the params
        }
        
        if state:
            params['state'] = state
            
        auth_url = f"{self.oauth_url}?{urlencode(params)}"
        current_app.logger.info(f"Generated auth URL: {auth_url}")
        return auth_url

    def _get_basic_auth_header(self):
        """Generate Basic Auth header for eBay API calls."""
        credentials = f"{self.app_id}:{self.cert_id}"
        encoded = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded}"

    def get_access_token(self, auth_code):
        """Exchange authorization code for access token."""
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': self._get_basic_auth_header()
        }
        
        # Build token request according to eBay's specifications
        data = {
            'grant_type': 'authorization_code',
            'code': auth_code,
            'redirect_uri': 'https://login.tcgsync.com/ebay/callback',
            'RuName': self.ru_name  # Add RuName in the form data
        }
        
        current_app.logger.info(f"Sending token request to {self.token_url}")
        current_app.logger.debug(f"Token request headers: {headers}")
        current_app.logger.debug(f"Token request data: {data}")
        
        response = requests.post(self.token_url, headers=headers, data=data)
        current_app.logger.info(f"Token response status: {response.status_code}")
        current_app.logger.debug(f"Token response content: {response.text}")
        
        if response.status_code != 200:
            current_app.logger.error(f"Token request failed: {response.text}")
            raise Exception(f"Token request failed with status {response.status_code}: {response.text}")
            
        response_data = response.json()
        current_app.logger.info("Successfully obtained token response")
        return response_data

    def refresh_access_token(self, refresh_token):
        """Refresh an expired access token."""
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': self._get_basic_auth_header()
        }
        
        data = {
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token,
            'scope': ' '.join(self.scopes),
            'RuName': self.ru_name  # Add RuName in the form data
        }
        
        current_app.logger.info("Refreshing access token")
        current_app.logger.debug(f"Refresh token request data: {data}")
        
        response = requests.post(self.token_url, headers=headers, data=data)
        current_app.logger.info(f"Refresh token response status: {response.status_code}")
        
        if response.status_code != 200:
            current_app.logger.error(f"Refresh token request failed: {response.text}")
            raise Exception(f"Refresh token request failed with status {response.status_code}: {response.text}")
            
        response_data = response.json()
        current_app.logger.info("Successfully refreshed access token")
        return response_data

    def _get_auth_header(self, access_token):
        """Generate Bearer Auth header for eBay API calls."""
        return {'Authorization': f'Bearer {access_token}'}

    def get_orders(self, access_token, last_modified_date=None):
        """Fetch orders from eBay."""
        headers = self._get_auth_header(access_token)
        params = {
            'limit': 50,
            'filter': 'lastmodifieddate:[{date}]'.format(
                date=last_modified_date.isoformat() if last_modified_date else 
                (datetime.utcnow() - timedelta(days=30)).isoformat()
            )
        }
        
        response = requests.get(
            f"{self.api_url}/order",
            headers=headers,
            params=params
        )
        response.raise_for_status()
        return response.json()

    def get_order_details(self, access_token, order_id):
        """Fetch specific order details from eBay."""
        headers = self._get_auth_header(access_token)
        response = requests.get(
            f"{self.api_url}/order/{order_id}",
            headers=headers
        )
        response.raise_for_status()
        return response.json()

    @staticmethod
    def ensure_valid_token(ebay_account):
        """Ensure the access token is valid, refresh if needed."""
        if not ebay_account:
            raise ValueError("No eBay account provided")
            
        # Check if token is expired or will expire in next 5 minutes
        if datetime.utcnow() + timedelta(minutes=5) >= ebay_account.token_expiry:
            ebay_api = EbayAPI()
            token_data = ebay_api.refresh_access_token(ebay_account.refresh_token)
            
            # Update account with new tokens
            ebay_account.access_token = token_data['access_token']
            ebay_account.refresh_token = token_data.get('refresh_token', ebay_account.refresh_token)
            ebay_account.token_expiry = datetime.utcnow() + timedelta(seconds=token_data['expires_in'])
            ebay_account.save()
            
        return ebay_account.access_token

