{% block event_styles %}
<style>
    /* Base text color for all event pages */
    .event-page-content {
        color: white !important;
        background-color: #1a2530 !important;
    }
    
    /* Override for specific elements */
    .event-page-content h1, 
    .event-page-content h2, 
    .event-page-content h3, 
    .event-page-content h4, 
    .event-page-content h5, 
    .event-page-content h6,
    .event-page-content p,
    .event-page-content span,
    .event-page-content div,
    .event-page-content label,
    .event-page-content a:not(.btn),
    .event-page-content li,
    .event-page-content td,
    .event-page-content th,
    .event-page-content small,
    .event-page-content strong,
    .event-page-content em,
    .event-page-content b,
    .event-page-content i {
        color: white !important;
    }
    
    /* Links */
    .event-page-content a {
        color: #3498db !important;
    }
    
    .event-page-content a:hover {
        color: #2980b9 !important;
    }
    
    /* Text muted */
    .event-page-content .text-muted {
        color: rgba(255, 255, 255, 0.7) !important;
    }
    
    /* Card styles */
    .event-page-content .card {
        background-color: #2c3e50;
        border-color: #34495e;
    }
    
    .event-page-content .card-header {
        background-color: #34495e;
        border-color: #2c3e50;
        color: white;
    }
    
    .event-page-content .card-body {
        background-color: #2c3e50;
        color: white;
    }
    
    /* Form controls */
    .event-page-content .form-control {
        background-color: #34495e;
        border-color: #2c3e50;
        color: white !important;
    }
    
    .event-page-content .form-control:focus {
        background-color: #34495e;
        color: white !important;
    }
    
    .event-page-content .form-control::placeholder {
        color: #aaa;
    }
    
    .event-page-content input, 
    .event-page-content select, 
    .event-page-content textarea,
    .event-page-content option {
        color: white !important;
        background-color: #34495e !important;
    }
    
    /* Tables */
    .event-page-content .table {
        color: white !important;
    }
    
    .event-page-content .table th,
    .event-page-content .table td,
    .event-page-content .table thead th {
        color: white !important;
        border-color: #34495e;
    }
    
    .event-page-content .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(255, 255, 255, 0.05);
    }
    
    .event-page-content .table-bordered {
        border-color: #34495e;
    }
    
    .event-page-content .table-bordered td, 
    .event-page-content .table-bordered th {
        border-color: #34495e;
    }
    
    /* Modals */
    .event-page-content .modal-content {
        background-color: #2c3e50;
        color: white;
    }
    
    .event-page-content .modal-header {
        border-bottom-color: #34495e;
    }
    
    .event-page-content .modal-footer {
        border-top-color: #34495e;
    }
    
    /* Custom fields */
    .event-page-content .custom-field {
        background-color: #34495e;
        border-color: #2c3e50;
    }
    
    /* Info boxes */
    .event-page-content .info-box {
        background-color: #34495e;
        color: white;
    }
    
    .event-page-content .info-box-content {
        color: white;
    }
    
    /* Alerts */
    .event-page-content .alert-info {
        background-color: #17a2b8;
        border-color: #148a9c;
        color: white;
    }
    
    /* Select boxes */
    .event-page-content select option {
        background-color: #34495e;
        color: white;
    }
    
    /* Calendar specific */
    .fc-view {
        background-color: #2c3e50;
    }
    
    .fc-day-header {
        background-color: #34495e;
        color: white !important;
    }
    
    .fc-day {
        background-color: #2c3e50;
    }
    
    .fc-day-number {
        color: white !important;
    }
    
    .fc-list-heading td {
        background-color: #34495e !important;
    }
    
    .fc-list-item:hover td {
        background-color: #3e5771 !important;
    }
    
    /* Chart backgrounds */
    .chart-container {
        background-color: #2c3e50;
    }
    
    /* Stat cards */
    .stat-card {
        color: white !important;
    }
    
    .stat-card .stat-label {
        color: rgba(255, 255, 255, 0.8) !important;
    }
    
    /* Badges */
    .event-page-content .badge {
        color: white !important;
    }
    
    /* Buttons */
    .event-page-content .btn {
        border-color: transparent;
    }
    
    .event-page-content .btn-primary {
        background-color: #3498db;
        color: white !important;
    }
    
    .event-page-content .btn-secondary {
        background-color: #95a5a6;
        color: white !important;
    }
    
    .event-page-content .btn-success {
        background-color: #2ecc71;
        color: white !important;
    }
    
    .event-page-content .btn-danger {
        background-color: #e74c3c;
        color: white !important;
    }
    
    .event-page-content .btn-warning {
        background-color: #f39c12;
        color: white !important;
    }
    
    .event-page-content .btn-info {
        background-color: #3498db;
        color: white !important;
    }
    
    /* Form check */
    .event-page-content .form-check-input {
        background-color: #34495e;
        border-color: #2c3e50;
    }
</style>
{% endblock %}
