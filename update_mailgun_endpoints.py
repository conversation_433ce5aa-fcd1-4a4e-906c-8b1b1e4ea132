from config.config import Config
import os
import re
from pathlib import Path

def update_mailgun_endpoints(directory):
    # Pattern to match Mailgun API URLs without 'eu'
    pattern = r'https://api\.mailgun\.net/v3/'
    replacement = 'https://api.eu.mailgun.net/v3/'
    
    # File extensions to check
    extensions = ('.py',)
    
    # Specific files we want to update
    target_files = [
        'routes/admin_routes.py',
        'routes/buylist_routes.py',
        'routes/invoice_routes.py',
        'routes/view_order_routes.py',
        'utils/email_utils.py',
        'email_sender.py'
    ]
    
    # Walk through all files in the directory
    for filepath in target_files:
        full_path = Path(directory) / filepath
        if full_path.exists() and full_path.suffix == '.py':
            try:
                # Try different encodings
                encodings = ['utf-8', 'latin-1', 'cp1252']
                content = None
                
                for encoding in encodings:
                    try:
                        with open(full_path, 'r', encoding=encoding) as f:
                            content = f.read()
                            break
                    except UnicodeDecodeError:
                        continue
                
                if content is None:
                    print(f"Could not read {filepath} with any supported encoding")
                    continue
                
                # Check if the file contains any Mailgun API URLs
                if pattern in content:
                    print(f"Found Mailgun API URL in: {filepath}")
                    
                    # Replace the URLs
                    new_content = content.replace(pattern, replacement)
                    
                    # Write the updated content back to the file
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    print(f"Updated Mailgun API URL in: {filepath}")
                else:
                    print(f"No Mailgun API URLs found in: {filepath}")
            
            except Exception as e:
                print(f"Error processing {filepath}: {str(e)}")
        else:
            print(f"File not found: {filepath}")

if __name__ == "__main__":
    # Update files in the current directory
    current_dir = os.getcwd()
    print(f"Scanning directory: {current_dir}")
    update_mailgun_endpoints(current_dir)
    print("Finished updating Mailgun endpoints")

