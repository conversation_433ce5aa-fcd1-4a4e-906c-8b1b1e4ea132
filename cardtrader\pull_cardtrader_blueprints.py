# Script to pull all blueprints from CardTrader API and save them to MongoDB
import requests
import time
import random
from pymongo import MongoClient
from datetime import datetime
import logging
import sys
import threading
import queue
import argparse
from concurrent.futures import ThreadPoolExecutor
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

# CardTrader API configuration
API_BASE_URL = "https://api.cardtrader.com/api/v2"
# CardTrader API token
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

# Create a session with retry capabilities
def create_session():
    """Create a requests session with retry capabilities"""
    session = requests.Session()
    retry_strategy = Retry(
        total=3,  # Maximum number of retries
        backoff_factor=1,  # Time factor between retries
        status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry on
        allowed_methods=["GET"]  # Only retry on GET requests
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    return session

# Thread-safe rate limiter
class RateLimiter:
    def __init__(self, rate=1):
        """Initialize rate limiter with rate in requests per second"""
        self.rate = rate  # requests per second
        self.last_request_time = 0
        self.lock = threading.Lock()
    
    def wait(self):
        """Wait if necessary to maintain the rate limit"""
        with self.lock:
            # Since there are no rate limits, we'll just update the last request time
            # without waiting
            self.last_request_time = time.time()

# Global rate limiter - CardTrader API is limited to 1 call per second
rate_limiter = RateLimiter(rate=1)  # 1 request per second

def fetch_games(session=None):
    """Fetch all games from CardTrader API"""
    url = f"{API_BASE_URL}/games"
    
    # Use the provided session or create a new one
    if session is None:
        session = create_session()
    
    try:
        logger.info("Fetching games from CardTrader API")
        rate_limiter.wait()  # Respect rate limit
        response = session.get(url, headers=HEADERS, timeout=10)
        
        if response.status_code == 200:
            games = response.json()
            logger.info(f"Found {len(games)} games")
            return games
        else:
            logger.error(f"API request failed with status code {response.status_code}: {response.text}")
            return []
    except Exception as e:
        logger.error(f"Error fetching games: {str(e)}")
        return []

def fetch_expansions(game_id, session=None):
    """Fetch all expansions for a specific game from CardTrader API"""
    url = f"{API_BASE_URL}/expansions"
    params = {}
    if game_id is not None:
        params["game_id"] = game_id
    
    # Use the provided session or create a new one
    if session is None:
        session = create_session()
    
    try:
        logger.info(f"Fetching expansions for game ID: {game_id}")
        rate_limiter.wait()  # Respect rate limit
        response = session.get(url, headers=HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            expansions = response.json()
            logger.info(f"Found {len(expansions)} expansions for game ID: {game_id}")
            return expansions
        else:
            logger.error(f"API request failed with status code {response.status_code}: {response.text}")
            return []
    except Exception as e:
        logger.error(f"Error fetching expansions for game ID {game_id}: {str(e)}")
        return []

def fetch_blueprints(expansion_id, session=None):
    """Fetch all blueprints for a specific expansion from CardTrader API"""
    url = f"{API_BASE_URL}/blueprints/export"
    params = {"expansion_id": expansion_id}
    
    # Use the provided session or create a new one
    if session is None:
        session = create_session()
    
    try:
        logger.info(f"Fetching blueprints for expansion ID: {expansion_id}")
        rate_limiter.wait()  # Respect rate limit
        response = session.get(url, headers=HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            blueprints = response.json()
            logger.info(f"Found {len(blueprints)} blueprints for expansion ID: {expansion_id}")
            return blueprints
        elif response.status_code == 404:
            logger.warning(f"No blueprints found for expansion ID: {expansion_id}")
            return []
        else:
            logger.error(f"API request failed with status code {response.status_code}: {response.text}")
            return []
    except Exception as e:
        logger.error(f"Error fetching blueprints for expansion ID {expansion_id}: {str(e)}")
        return []

def save_blueprints_to_mongodb(blueprints, expansion_id):
    """Save blueprints to MongoDB"""
    if not blueprints:
        logger.info(f"No blueprints to save for expansion ID: {expansion_id}")
        return 0
    
    try:
        # Create blueprints collection if it doesn't exist
        if 'blueprints' not in cardtrader_db.list_collection_names():
            logger.info("Creating blueprints collection")
            cardtrader_db.create_collection('blueprints')
        
        # Add fetched_at timestamp to each blueprint
        for blueprint in blueprints:
            blueprint['fetched_at'] = datetime.now()
        
        # Use bulk operations for better performance
        from pymongo.operations import ReplaceOne
        bulk_operations = []
        for blueprint in blueprints:
            # Use upsert to update if exists or insert if not
            bulk_operations.append(
                ReplaceOne(
                    {'id': blueprint['id']},
                    blueprint,
                    upsert=True
                )
            )
        
        if bulk_operations:
            result = cardtrader_db.blueprints.bulk_write(bulk_operations)
            logger.info(f"Saved {len(blueprints)} blueprints for expansion ID: {expansion_id}")
            logger.info(f"Inserted: {result.upserted_count}, Modified: {result.modified_count}")
            return len(blueprints)
        return 0
    except Exception as e:
        logger.error(f"Error saving blueprints to MongoDB: {str(e)}")
        return 0

def process_expansion(expansion, session, result_queue):
    """Process a single expansion in a thread"""
    expansion_id = expansion.get('id')
    expansion_name = expansion.get('name', 'Unknown')
    
    logger.info(f"Processing expansion: {expansion_name} (ID: {expansion_id})")
    
    # Fetch blueprints for this expansion
    blueprints = fetch_blueprints(expansion_id, session)
    
    # Save blueprints to MongoDB
    saved_count = save_blueprints_to_mongodb(blueprints, expansion_id)
    
    # Add result to queue
    result_queue.put((expansion_id, len(blueprints), saved_count))
    
    logger.info(f"Completed expansion: {expansion_name} (ID: {expansion_id})")

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Pull all blueprints from CardTrader API and save them to MongoDB')
    parser.add_argument('--game', type=int, default=None,
                        help='Filter by game_id (default: None, fetch all games)')
    parser.add_argument('--expansion', type=int, default=None,
                        help='Filter by expansion_id (default: None, fetch all expansions)')
    parser.add_argument('--threads', type=int, default=20,
                        help='Number of threads to use (default: 20)')
    parser.add_argument('--rate', type=float, default=1.0,
                        help='API request rate limit in requests per second (default: 1.0)')
    return parser.parse_args()

def main():
    # Parse command line arguments
    args = parse_arguments()
    
    # Update rate limiter based on command line argument
    global rate_limiter
    rate_limiter = RateLimiter(rate=args.rate)
    
    start_time = time.time()
    logger.info(f"Starting CardTrader blueprints collection")
    logger.info(f"Configuration: game_id: {args.game}, expansion_id: {args.expansion}, {args.threads} threads, {args.rate} req/sec")
    
    # Create a session for API requests
    session = create_session()
    
    # If expansion_id is provided, only fetch blueprints for that expansion
    if args.expansion is not None:
        logger.info(f"Fetching blueprints for expansion ID: {args.expansion}")
        blueprints = fetch_blueprints(args.expansion, session)
        saved_count = save_blueprints_to_mongodb(blueprints, args.expansion)
        logger.info(f"Saved {saved_count} blueprints for expansion ID: {args.expansion}")
        end_time = time.time()
        logger.info(f"Completed in {end_time - start_time:.2f} seconds")
        return
    
    # If game_id is provided, only fetch expansions for that game
    if args.game is not None:
        logger.info(f"Fetching expansions for game ID: {args.game}")
        expansions = fetch_expansions(args.game, session)
    else:
        # Fetch all games
        games = fetch_games(session)
        if not games:
            logger.error("No games found. Exiting.")
            return
        
        # Fetch expansions for all games
        expansions = []
        for game in games:
            game_id = game.get('id')
            game_expansions = fetch_expansions(game_id, session)
            expansions.extend(game_expansions)
    
    if not expansions:
        logger.error("No expansions found. Exiting.")
        return
    
    # Create a queue for results
    result_queue = queue.Queue()
    
    # Determine number of threads (adjust based on your system capabilities)
    num_threads = min(args.threads, len(expansions))
    logger.info(f"Using {num_threads} threads to process {len(expansions)} expansions")
    
    # Process expansions using thread pool
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # Submit all tasks
        futures = []
        for expansion in expansions:
            futures.append(executor.submit(process_expansion, expansion, session, result_queue))
    
    # Process all results from the queue
    processed_count = 0
    blueprints_count = 0
    saved_count = 0
    
    while not result_queue.empty():
        expansion_id, blueprint_count, saved = result_queue.get()
        blueprints_count += blueprint_count
        saved_count += saved
        processed_count += 1
        
        # Print progress
        if processed_count % 10 == 0 or processed_count == len(expansions):
            logger.info(f"Progress: {processed_count}/{len(expansions)} expansions processed")
    
    end_time = time.time()
    duration = end_time - start_time
    logger.info(f"Completed in {duration:.2f} seconds")
    logger.info(f"Processed {processed_count} expansions")
    logger.info(f"Found {blueprints_count} blueprints")
    logger.info(f"Saved {saved_count} blueprints to MongoDB")
    logger.info(f"Average processing time: {duration/processed_count:.2f} seconds per expansion")
    
    # Count documents in blueprints collection
    blueprints_count = cardtrader_db.blueprints.count_documents({})
    logger.info(f"Total documents in blueprints collection: {blueprints_count}")

if __name__ == "__main__":
    main()
