from config.config import Config
import logging
from skupricer import process_user, mongo_client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_specific_product():
    try:
        # Get TCGPlayer API key
        tcgplayer_key_doc = mongo_client['test']['tcgplayerKey'].find_one({})
        if not tcgplayer_key_doc:
            logger.error("TCGPlayer key not found")
            return
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']

        # Create test config for specific user
        test_config = {
            'username': 'tcgosaurus',
            'selectedProductTypes': ['Yugioh Single']  # Match the product's type
        }

        # Find specific product by productId
        db = mongo_client['test']
        product = db.shProducts.find_one({
            'username': 'tcgosaurus',
            'productId': 115826
        })
        
        if not product:
            logger.error("Product not found")
            return

        logger.info("\n=== Product Before Processing ===")
        logger.info(f"ID: {product['_id']}")
        logger.info(f"Title: {product.get('title')}")
        logger.info(f"SKUs Matched: {product.get('skusMatched')}")
        logger.info(f"Product Type: {product.get('product_type')}")
        logger.info("\nVariants:")
        for variant in product.get('variants', []):
            logger.info(f"\nVariant: {variant.get('title')}")
            logger.info(f"SKU ID: {variant.get('skuId')}")
            logger.info(f"Price: {variant.get('price')}")

        # Force process specific product by updating its last_repriced
        result = db.shProducts.update_one(
            {'_id': product['_id']},
            {'$unset': {'last_repriced': ""}}
        )
        logger.info(f"\nReset last_repriced for product: {result.modified_count} document modified")

        # Process the user which will include our target product
        logger.info("\nProcessing user with config:")
        logger.info(f"Username: {test_config['username']}")
        logger.info(f"Selected Product Types: {test_config['selectedProductTypes']}")
        logger.info(f"TCGPlayer API Key: {tcgplayer_api_key[:10]}...")

        # Add debug logging to skupricer
        logging.getLogger('skupricer').setLevel(logging.DEBUG)
        
        process_user(test_config, tcgplayer_api_key)

        # Check the result and compare with original
        updated_product = db.shProducts.find_one({'productId': 115826})
        if updated_product:
            logger.info("\n=== Product After Processing ===")
            logger.info(f"Title: {updated_product.get('title')}")
            logger.info(f"SKUs Matched: {updated_product.get('skusMatched')}")
            logger.info(f"Needs Pushing: {updated_product.get('needsPushing')}")
            logger.info(f"Last Repriced: {updated_product.get('last_repriced')}")
            logger.info("\nVariants Changes:")
            
            original_variants = {v.get('title'): v for v in product.get('variants', [])}
            updated_variants = {v.get('title'): v for v in updated_product.get('variants', [])}
            
            for title in original_variants.keys():
                orig = original_variants.get(title, {})
                updated = updated_variants.get(title, {})
                logger.info(f"\nVariant: {title}")
                logger.info(f"Original SKU ID: {orig.get('skuId')} -> Updated SKU ID: {updated.get('skuId')}")
                logger.info(f"Original Price: {orig.get('price')} -> Updated Price: {updated.get('price')}")
                
            logger.info("\nSummary:")
            logger.info(updated_product.get('summary', 'No summary available'))
        else:
            logger.error("Product not found after processing")

    except Exception as e:
        logger.error(f"Error in test: {str(e)}")
    finally:
        mongo_client.close()

if __name__ == "__main__":
    test_specific_product()

