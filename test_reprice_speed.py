from config.config import Config
#!/usr/bin/env python3
"""
Script to test the speed of repricing the entire inventory.

This script tests the speed of repricing the entire inventory for a specific user.
It starts a repricing job and monitors its progress, reporting on the time taken
and the number of products processed.

Usage:
    python test_reprice_speed.py [--url URL] [--api-key API_KEY] [--username USERNAME]

Options:
    --url URL          The URL of the repricing service [default: https://webhooks.tcgsync.com]
    --api-key API_KEY  The API key for the repricing service [default: IPBRMIwlm5a7DWoZqPnxyamQSIPMyG19]
    --username USERNAME The username to test repricing with [default: admintcg]
"""

import argparse
import sys
import time
import json
import requests
from datetime import datetime, timedelta

try:
    from colorama import init, Fore, Style
    init()  # Initialize colorama
except ImportError:
    print("Required packages not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "colorama"])
    from colorama import init, Fore, Style
    init()  # Initialize colorama

def print_success(message):
    """Print a success message in green."""
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

def print_error(message):
    """Print an error message in red."""
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

def print_info(message):
    """Print an info message in blue."""
    print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")

def print_warning(message):
    """Print a warning message in yellow."""
    print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

def print_header(message):
    """Print a header message in cyan."""
    print(f"\n{Fore.CYAN}=== {message} ==={Style.RESET_ALL}")

def print_json(data):
    """Print JSON data in a formatted way."""
    print(json.dumps(data, indent=2))

def format_time(seconds):
    """Format time in seconds to a human-readable format."""
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} hours"

def test_reprice_speed(url, api_key, username):
    """Test the speed of repricing the entire inventory."""
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    # Test health endpoint
    print_header("Testing Health Endpoint")
    try:
        health_url = f"{url}/api/health"
        print_info(f"GET {health_url}")
        response = requests.get(health_url, headers=headers)
        
        if response.status_code == 200:
            print_success(f"Health check successful (Status: {response.status_code})")
            print_json(response.json())
        else:
            print_error(f"Health check failed (Status: {response.status_code})")
            print(response.text)
            return
    except Exception as e:
        print_error(f"Error testing health endpoint: {str(e)}")
        return

    # Start repricing job
    print_header(f"Starting Repricing Job for User {username}")
    try:
        reprice_url = f"{url}/api/reprice"
        data = {
            'username': username
        }
        print_info(f"POST {reprice_url}")
        print_info(f"Data: {data}")
        
        response = requests.post(reprice_url, headers=headers, json=data)
        
        if response.status_code in [200, 201]:
            print_success(f"Start repricing successful (Status: {response.status_code})")
            result = response.json()
            print_json(result)
            
            if 'job_id' in result:
                job_id = result['job_id']
                print_success(f"Job ID: {job_id}")
            else:
                print_error("No job ID returned")
                return
        else:
            print_error(f"Start repricing failed (Status: {response.status_code})")
            print(response.text)
            return
    except Exception as e:
        print_error(f"Error starting repricing job: {str(e)}")
        return

    # Check job status
    print_header("Monitoring Job Progress")
    try:
        status_url = f"{url}/api/status/{job_id}"
        print_info(f"GET {status_url}")
        
        start_time = datetime.now()
        last_progress_time = start_time
        last_processed = 0
        
        # Check status multiple times to see progress
        check_count = 0
        while True:
            check_count += 1
            response = requests.get(status_url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status', 'unknown')
                processed = result.get('processed_products', 0)
                total = result.get('total_products', 0)
                progress = result.get('progress_percent', 0)
                
                # Calculate time elapsed
                current_time = datetime.now()
                elapsed = (current_time - start_time).total_seconds()
                
                # Calculate processing rate
                if processed > 0 and elapsed > 0:
                    rate = processed / elapsed
                    
                    # Calculate time since last progress update
                    time_since_last = (current_time - last_progress_time).total_seconds()
                    
                    # Calculate products processed since last update
                    products_since_last = processed - last_processed
                    
                    # Calculate current rate (products per second)
                    current_rate = products_since_last / time_since_last if time_since_last > 0 else 0
                    
                    # Update last progress time and processed count
                    last_progress_time = current_time
                    last_processed = processed
                    
                    # Estimate time remaining
                    if rate > 0 and total > processed:
                        remaining_products = total - processed
                        estimated_seconds = remaining_products / rate
                        estimated_completion = current_time + timedelta(seconds=estimated_seconds)
                        
                        print_info(f"Check #{check_count}: Status is '{status}' - {processed} of {total} products processed ({progress:.1f}%)")
                        print_info(f"  Time elapsed: {format_time(elapsed)}")
                        print_info(f"  Processing rate: {rate:.2f} products/second (current: {current_rate:.2f} products/second)")
                        print_info(f"  Estimated time remaining: {format_time(estimated_seconds)}")
                        print_info(f"  Estimated completion time: {estimated_completion.strftime('%Y-%m-%d %H:%M:%S')}")
                    else:
                        print_info(f"Check #{check_count}: Status is '{status}' - {processed} of {total} products processed ({progress:.1f}%)")
                        print_info(f"  Time elapsed: {format_time(elapsed)}")
                        print_info(f"  Processing rate: {rate:.2f} products/second (current: {current_rate:.2f} products/second)")
                else:
                    print_info(f"Check #{check_count}: Status is '{status}' - {processed} of {total} products processed ({progress:.1f}%)")
                    print_info(f"  Time elapsed: {format_time(elapsed)}")
                
                # If the job is complete or has an error, stop checking
                if status in ['complete', 'error', 'cancelled']:
                    if status == 'complete':
                        print_success(f"Job completed in {format_time(elapsed)}")
                        print_success(f"Final result: {result.get('result', 'No result provided')}")
                    elif status == 'error':
                        print_error(f"Job failed with error: {result.get('error', 'No error provided')}")
                    elif status == 'cancelled':
                        print_warning(f"Job was cancelled after {format_time(elapsed)}")
                    
                    break
            else:
                print_error(f"Check status failed (Status: {response.status_code})")
                print(response.text)
                break
                
            # Wait before checking again
            time.sleep(5)
    except Exception as e:
        print_error(f"Error checking job status: {str(e)}")
        
    # Print final summary
    print_header("Repricing Speed Test Summary")
    try:
        # Get the final job status
        response = requests.get(status_url, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            status = result.get('status', 'unknown')
            processed = result.get('processed_products', 0)
            total = result.get('total_products', 0)
            
            # Calculate total time
            if 'start_time' in result and 'completion_time' in result:
                start_time_str = result.get('start_time')
                completion_time_str = result.get('completion_time')
                
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                completion_time = datetime.fromisoformat(completion_time_str.replace('Z', '+00:00'))
                
                total_time = (completion_time - start_time).total_seconds()
                
                print_info(f"Total time: {format_time(total_time)}")
                print_info(f"Total products processed: {processed} of {total}")
                
                if total_time > 0 and processed > 0:
                    rate = processed / total_time
                    print_info(f"Average processing rate: {rate:.2f} products/second")
                    
                    # Calculate time per product
                    time_per_product = total_time / processed
                    print_info(f"Average time per product: {time_per_product:.4f} seconds")
            else:
                print_warning("Could not calculate total time (missing start_time or completion_time)")
        else:
            print_error(f"Failed to get final job status (Status: {response.status_code})")
    except Exception as e:
        print_error(f"Error getting final job status: {str(e)}")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test the speed of repricing the entire inventory.')
    parser.add_argument('--url', default='https://webhooks.tcgsync.com',
                        help='The URL of the repricing service')
    parser.add_argument('--api-key', default='IPBRMIwlm5a7DWoZqPnxyamQSIPMyG19',
                        help='The API key for the repricing service')
    parser.add_argument('--username', default='admintcg',
                        help='The username to test repricing with')
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    print_header("Starting Repricing Speed Test")
    print_info(f"Base URL: {args.url}")
    print_info(f"API Key: {'*' * len(args.api_key)}")
    print_info(f"Username: {args.username}")
    print_info(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_reprice_speed(args.url, args.api_key, args.username)

if __name__ == '__main__':
    main()

