import motor.motor_asyncio
import asyncio
from datetime import datetime, timedelta
import multiprocessing
from multiprocessing import Pool, Manager
import tqdm
import logging
from typing import Dict, Any
from dataclasses import dataclass

# Configuration
@dataclass
class Config:
    MONGO_URI: str = "*******************************************************************"
    DB_NAME: str = 'test'
    SYNC_INTERVAL: int = 15  # minutes
    BATCH_SIZE: int = 1000
    MAX_RETRIES: int = 3
    RETRY_DELAY: int = 5  # seconds

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('sync_script')

class DatabaseClient:
    def __init__(self, config: Config):
        self.config = config
        self.client = motor.motor_asyncio.AsyncIOMotorClient(self.config.MONGO_URI)
        self.db = self.client[self.config.DB_NAME]

class ProductSynchronizer:
    def __init__(self, config: Config):
        self.config = config
        self.db_client = DatabaseClient(config)
        
    async def process_record(self, record: Dict[str, Any]) -> bool:
        shopify_id = record["_id"]

        # Process only if tcgItem is true
        if not record.get("tcgItem"):
            try:
                result = await self.db_client.db.shProducts.update_one(
                    {"_id": shopify_id},
                    {"$set": {
                        "lastUpdated": datetime.utcnow()
                    }}
                )
                return result.modified_count > 0
            except Exception as e:
                logger.error(f"Error updating non-TCG item {shopify_id}: {e}")
                return False

        # Continue with processing for TCG items
        product_id = record.get("productId")
        if not product_id:
            return False

        try:
            product_id_int = int(product_id)
        except (ValueError, TypeError):
            logger.warning(f"Invalid productId: {product_id} for shopify_id: {shopify_id}")
            return False

        for attempt in range(self.config.MAX_RETRIES):
            try:
                catalog_record = await self.db_client.db.catalog.find_one({"productId": product_id_int})
                if not catalog_record:
                    return False

                update_fields = {
                    "abbreviation": catalog_record.get("abbreviation"),
                    "expansionName": catalog_record.get("expansionName"),
                    "rarity": catalog_record.get("rarity"),
                    "number": catalog_record.get("number"),
                    "groupId": catalog_record.get("groupId"),
                    "gameName": catalog_record.get("gameName"),
                    "categoryId": catalog_record.get("categoryId"),
                    "lastUpdated": datetime.utcnow()
                }

                result = await self.db_client.db.shProducts.update_one(
                    {"_id": shopify_id},
                    {"$set": update_fields}
                )
                return result.modified_count > 0

            except Exception as e:
                if attempt == self.config.MAX_RETRIES - 1:
                    logger.error(f"Failed to process record {shopify_id} after {self.config.MAX_RETRIES} attempts: {e}")
                    return False
                await asyncio.sleep(self.config.RETRY_DELAY)

    async def process_records_for_username(self, username: str, total_records: int) -> int:
        # Modified query to only check for username
        cursor = self.db_client.db.shProducts.find(
            {"username": username}
        ).batch_size(self.config.BATCH_SIZE)
        
        updated_count = 0
        position = multiprocessing.current_process()._identity[0] - 1
        
        with tqdm.tqdm(total=total_records, desc=f"User: {username}", 
                      position=position, leave=True) as pbar:
            async for record in cursor:
                if await self.process_record(record):
                    updated_count += 1
                pbar.update(1)
        
        return updated_count

def worker_process(username: str, total_records: int, return_dict: Dict[str, int]):
    async def async_worker():
        try:
            config = Config()
            synchronizer = ProductSynchronizer(config)
            updated_count = await synchronizer.process_records_for_username(
                username, total_records
            )
            return_dict[username] = updated_count
        except Exception as e:
            logger.error(f"Error processing records for {username}: {e}")
            return_dict[username] = 0

    asyncio.run(async_worker())

class SyncManager:
    def __init__(self):
        self.config = Config()
        self.db_client = DatabaseClient(self.config)
        
    async def get_work_distribution(self) -> Dict[str, int]:
        # Modified to get all distinct usernames without needsMatching filter
        distinct_usernames = await self.db_client.db.shProducts.distinct("username")
        
        total_records_per_user = {}
        for username in distinct_usernames:
            # Count all records for each user
            total_records_per_user[username] = await self.db_client.db.shProducts.count_documents(
                {"username": username}
            )
        
        return total_records_per_user

    async def run_sync_cycle(self) -> int:
        total_records_per_user = await self.get_work_distribution()
        
        if not total_records_per_user:
            logger.info("No records found")
            return 0
            
        logger.info(f"Processing {len(total_records_per_user)} unique usernames")
        
        manager = Manager()
        return_dict = manager.dict()
        
        with Pool(processes=min(len(total_records_per_user), 
                              multiprocessing.cpu_count())) as pool:
            pool.starmap(
                worker_process,
                [(username, count, return_dict) 
                 for username, count in total_records_per_user.items()]
            )
        
        return sum(return_dict.values())

    async def run(self):
        while True:
            start_time = datetime.now()
            logger.info(f"Starting synchronization at {start_time}")
            
            try:
                total_updated = await self.run_sync_cycle()
                logger.info(f"Synchronization completed. Updated {total_updated} records")
            except Exception as e:
                logger.error(f"Sync cycle failed: {e}")
                await asyncio.sleep(30)  # Wait before retry on failure
                continue
                
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(f"Sync completed at {end_time}. Duration: {duration}")
            
            sleep_time = timedelta(minutes=self.config.SYNC_INTERVAL) - duration
            if sleep_time.total_seconds() > 0:
                logger.info(f"Sleeping for {sleep_time} until next sync")
                await asyncio.sleep(sleep_time.total_seconds())

def main():
    multiprocessing.freeze_support()  # For Windows support
    sync_manager = SyncManager()
    asyncio.run(sync_manager.run())

if __name__ == "__main__":
    main()
