from config.config import Config
#!/usr/bin/env python3
"""
Script to verify price changes in the database and fix any discrepancies.
This script should be run periodically to ensure that price changes are properly saved.
"""

import logging
import time
import sys
from datetime import datetime, timedelta, timezone
from pymongo import MongoClient
from pymongo.errors import PyMongoError
from functools import wraps

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("price_verification.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = '*******************************************************************'
mongo_dbname = 'test'
mongo_client = MongoClient(mongo_uri)
db = mongo_client[mongo_dbname]
shProducts_collection = db['shProducts']
verification_queue_collection = db['verificationQueue']
pricing_transactions_collection = db['pricingTransactions']

# Retry decorator for database operations
def retry_database_operation(max_attempts=3, initial_backoff=1):
    """
    Decorator to retry database operations with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        initial_backoff: Initial backoff time in seconds
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            backoff = initial_backoff
            last_error = None
            
            while attempt < max_attempts:
                try:
                    result = func(*args, **kwargs)
                    # Verify the result
                    if hasattr(result, 'acknowledged') and not result.acknowledged:
                        raise PyMongoError("Operation not acknowledged by MongoDB")
                    return result
                except Exception as e:
                    last_error = e
                    attempt += 1
                    if attempt >= max_attempts:
                        logger.error(f"DATABASE: Failed after {max_attempts} attempts: {str(e)}")
                        break
                    
                    logger.warning(f"DATABASE: Attempt {attempt} failed: {str(e)}. Retrying in {backoff} seconds...")
                    time.sleep(backoff)
                    backoff *= 2  # Exponential backoff
            
            raise last_error
        return wrapper
    return decorator

@retry_database_operation(max_attempts=3, initial_backoff=1)
def verified_update_one(collection, filter_dict, update_dict):
    """
    Perform an update_one operation with verification and retry logic.
    
    Args:
        collection: MongoDB collection
        filter_dict: Filter criteria
        update_dict: Update operations
        
    Returns:
        UpdateResult object
    """
    result = collection.update_one(filter_dict, update_dict)
    
    # Verify the result
    if not result.acknowledged:
        raise PyMongoError("Update not acknowledged by MongoDB")
    return result

@retry_database_operation(max_attempts=3, initial_backoff=1)
def verified_update_many(collection, filter_dict, update_dict):
    """
    Perform an update_many operation with verification and retry logic.
    
    Args:
        collection: MongoDB collection
        filter_dict: Filter criteria
        update_dict: Update operations
        
    Returns:
        UpdateResult object
    """
    result = collection.update_many(filter_dict, update_dict)
    
    # Verify the result
    if not result.acknowledged:
        raise PyMongoError("Update many not acknowledged by MongoDB")
    return result

def verify_pending_changes():
    """
    Process the verification queue to ensure changes were applied.
    """
    try:
        logger.info("VERIFICATION: Starting verification of pending changes")
        
        # Find pending verifications
        pending_verifications = verification_queue_collection.find({
            "status": "pending",
            "attempts": {"$lt": 3},
            "created_at": {"$gt": datetime.now(timezone.utc) - timedelta(hours=24)}
        })
        
        verification_count = 0
        fixed_count = 0
        error_count = 0
        
        for verification in pending_verifications:
            verification_count += 1
            try:
                product = shProducts_collection.find_one({"_id": verification["product_id"]})
                if not product:
                    logger.error(f"VERIFICATION: Product {verification['product_id']} not found")
                    continue
                    
                mismatches = []
                for variant in product["variants"]:
                    variant_id = variant["id"]
                    # Convert to string to match the string keys in expected_prices
                    str_variant_id = str(variant_id)
                    if str_variant_id in verification["expected_prices"]:
                        expected_price = verification["expected_prices"][str_variant_id]
                        actual_price = float(variant["price"])
                        
                        if abs(expected_price - actual_price) > 0.01:
                            mismatches.append({
                                "variant_id": variant_id,
                                "expected": expected_price,
                                "actual": actual_price
                            })
                
                if mismatches:
                    logger.warning(f"VERIFICATION: Found {len(mismatches)} price mismatches for product {verification['product_id']}")
                    
                    # Update verification record
                    verified_update_one(
                        verification_queue_collection,
                        {"_id": verification["_id"]},
                        {"$inc": {"attempts": 1}, "$set": {"mismatches": mismatches}}
                    )
                    
                    # If this is the last attempt, try to fix the mismatches
                    if verification["attempts"] + 1 >= 3:
                        logger.info(f"VERIFICATION: Final attempt to fix mismatches for product {verification['product_id']}")
                        
                        # Create update operations to fix mismatches
                        for variant in product["variants"]:
                            variant_id = variant["id"]
                            for mismatch in mismatches:
                                # Direct comparison is fine here since mismatch["variant_id"] contains the original numeric ID
                                if mismatch["variant_id"] == variant_id:
                                    variant["price"] = str(mismatch["expected"])
                        
                        # Update the product with corrected prices
                        verified_update_one(
                            shProducts_collection,
                            {"_id": verification["product_id"]},
                            {"$set": {"variants": product["variants"], "needsPushing": True}}
                        )
                        
                        # Mark as fixed
                        verified_update_one(
                            verification_queue_collection,
                            {"_id": verification["_id"]},
                            {"$set": {"status": "fixed"}}
                        )
                        
                        fixed_count += 1
                else:
                    logger.info(f"VERIFICATION: No mismatches found for product {verification['product_id']}")
                    
                    # Mark as verified
                    verified_update_one(
                        verification_queue_collection,
                        {"_id": verification["_id"]},
                        {"$set": {"status": "verified"}}
                    )
                    
                    # Also update the pricing transactions
                    verified_update_many(
                        pricing_transactions_collection,
                        {"product_id": verification["product_id"]},
                        {"$set": {"verified": True}}
                    )
                    
            except Exception as e:
                error_count += 1
                logger.error(f"VERIFICATION: Error verifying changes for product {verification.get('product_id')}: {str(e)}")
                try:
                    verified_update_one(
                        verification_queue_collection,
                        {"_id": verification["_id"]},
                        {"$inc": {"attempts": 1}, "$set": {"last_error": str(e)}}
                    )
                except Exception as update_error:
                    logger.error(f"VERIFICATION: Error updating verification record: {str(update_error)}")
        
        logger.info(f"VERIFICATION: Summary: {verification_count} processed, {fixed_count} fixed, {error_count} errors")
        
    except Exception as e:
        logger.error(f"VERIFICATION: Error in verify_pending_changes: {str(e)}")

def check_unverified_transactions():
    """
    Check for unverified pricing transactions and schedule verification if needed.
    """
    try:
        logger.info("SCHEDULING: Checking for unverified pricing transactions")
        
        # Find unverified transactions from the last 24 hours
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
        unverified_transactions = pricing_transactions_collection.find({
            "verified": False,
            "status": "success",
            "timestamp": {"$gt": cutoff_time}
        })
        
        transaction_count = 0
        scheduled_count = 0
        
        for transaction in unverified_transactions:
            transaction_count += 1
            
            # Skip batch jobs and already scheduled verifications
            if transaction.get("product_id") == "batch_job":
                continue
                
            # Check if verification is already scheduled
            existing_verification = verification_queue_collection.find_one({
                "product_id": transaction["product_id"],
                "status": "pending"
            })
            
            if not existing_verification:
                # Get the product to extract expected prices
                product = shProducts_collection.find_one({"_id": transaction["product_id"]})
                if not product:
                    logger.warning(f"SCHEDULING: Product {transaction['product_id']} not found")
                    continue
                
                # Create expected prices dictionary
                expected_prices = {}
                for variant in product.get("variants", []):
                    if "id" in variant and "price" in variant:
                        # Convert variant ID to string to ensure MongoDB compatibility
                        expected_prices[str(variant["id"])] = float(variant["price"])
                
                if expected_prices:
                    # Schedule verification
                    verification_queue_collection.insert_one({
                        "product_id": transaction["product_id"],
                        "expected_prices": expected_prices,
                        "created_at": datetime.now(timezone.utc),
                        "status": "pending",
                        "attempts": 0,
                        "max_attempts": 3
                    })
                    scheduled_count += 1
        
        logger.info(f"SCHEDULING: Unverified transactions summary: {transaction_count} found, {scheduled_count} scheduled for verification")
        
    except Exception as e:
        logger.error(f"SCHEDULING: Error in check_unverified_transactions: {str(e)}")

def main():
    """
    Main function to run the verification process.
    """
    try:
        logger.info("PRICE VERIFICATION: Starting process")
        
        # First, check for unverified transactions
        check_unverified_transactions()
        
        # Then, verify pending changes
        verify_pending_changes()
        
        logger.info("PRICE VERIFICATION: Process completed")
        
    except Exception as e:
        logger.error(f"PRICE VERIFICATION: Error in main process: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
