{% extends "base.html" %}

{% block title %}Shopify Update Catalog{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Success notification area at the top -->
    <div id="topNotificationArea" class="mb-3" style="display: none;"></div>

    <!-- Previous header content remains the same -->
    <div class="dashboard-header d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Shopify Update Catalog</h1>
        <div class="d-flex align-items-center">
            <button id="viewQueuedBtn" class="btn btn-secondary me-2">
                <i class="fas fa-list me-2"></i>View Queued Files
            </button>
            <button id="pushToUpdatesBtn" class="btn btn-primary">
                <i class="fas fa-cloud-upload-alt me-2"></i>Push Selected to Updates
            </button>
        </div>
    </div>

    <!-- Settings Accordion -->
    <div class="accordion mb-4" id="settingsAccordion">
        <div class="accordion-item bg-dark border-secondary">
            <h2 class="accordion-header" id="headingSettings">
                <button class="accordion-button collapsed bg-dark text-light border-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSettings" aria-expanded="false" aria-controls="collapseSettings">
                    <i class="fas fa-cog me-2"></i>Settings
                </button>
            </h2>
            <div id="collapseSettings" class="accordion-collapse collapse" aria-labelledby="headingSettings" data-bs-parent="#settingsAccordion">
                <div class="accordion-body">
                    <!-- Title Format Settings -->
                    <h5 class="mb-3 text-light">Title Format Settings</h5>
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-light">Include in Title</label>
                                <div class="d-flex flex-wrap gap-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="includeName" checked disabled>
                                        <label class="form-check-label text-light" for="includeName">Card Name (Required)</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="includeNumber">
                                        <label class="form-check-label text-light" for="includeNumber">Card Number</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="includeExpansion">
                                        <label class="form-check-label text-light" for="includeExpansion">Expansion Name</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-light">Component Order (Drag to reorder)</label>
                            <div class="title-components-container p-3 border border-secondary rounded">
                                <div id="titleComponentOrder" class="d-flex gap-2">
                                    <div class="badge bg-primary p-2 draggable cursor-move" data-component="name">Card Name</div>
                                    <div class="badge bg-secondary p-2 draggable cursor-move" data-component="expansion">Expansion Name</div>
                                    <div class="badge bg-secondary p-2 draggable cursor-move" data-component="number">Card Number</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <strong>Preview:</strong> <span id="titlePreview" class="ms-2">Opal Palace (352) (CMR)</span>
                            </div>
                        </div>
                        <div class="col-12 text-end">
                             <button id="saveTitleFormatBtn" class="btn btn-primary btn-sm">
                                <i class="fas fa-save me-2"></i>Save Title Settings
                            </button>
                        </div>
                    </div>

                    <hr class="border-secondary">

                    <!-- Condition Selection -->
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0 text-light">Conditions to Include</h5>
                            <button id="saveConditionsBtn" class="btn btn-primary btn-sm">
                                <i class="fas fa-save me-2"></i>Save Condition Settings
                            </button>
                        </div>
                        <div class="d-flex flex-wrap gap-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-NM" value="Near Mint" checked>
                                <label class="form-check-label text-light" for="condition-NM">Near Mint (NM)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-LP" value="Lightly Played" checked>
                                <label class="form-check-label text-light" for="condition-LP">Lightly Played (LP)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-MP" value="Moderately Played" checked>
                                <label class="form-check-label text-light" for="condition-MP">Moderately Played (MP)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-HP" value="Heavily Played" checked>
                                <label class="form-check-label text-light" for="condition-HP">Heavily Played (HP)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-DM" value="Damaged" checked>
                                <label class="form-check-label text-light" for="condition-DM">Damaged (DM)</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Card -->
    <div class="card bg-dark border-secondary mb-4">
        <div class="card-header bg-dark border-secondary">
            <h5 class="mb-0 text-light">Filters</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-2">
                    <label for="gameSelect" class="form-label text-light">Game</label>
                    <select id="gameSelect" class="form-select bg-dark text-light border-secondary">
                        <option value="">Select Game</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="expansionSelect" class="form-label text-light">Expansion</label>
                    <select id="expansionSelect" class="form-select bg-dark text-light border-secondary" disabled>
                        <option value="">Select Expansion</option>
                    </select>
                </div>
                <!-- Filter by Expansion moved to Results Card Header -->
                <div class="col-md-2">
                    <label for="filterSelect" class="form-label text-light">Type</label>
                    <select id="filterSelect" class="form-select bg-dark text-light border-secondary">
                        <option value="all">Show All</option>
                        <option value="singles">Singles Only</option>
                        <option value="sealed">Sealed Only</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="searchInput" class="form-label text-light">Search</label>
                    <input type="text" id="searchInput" class="form-control bg-dark text-light border-secondary" placeholder="Search by name...">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button id="applyFilterBtn" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-2"></i>Apply Filters
                    </button>
                </div>
            </div>
            <!-- Condition Selection moved to Settings Accordion -->
        </div>
    </div>

    <!-- Tab Navigation -->
    <ul class="nav nav-tabs mb-4" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active bg-dark text-light" id="catalog-tab" data-bs-toggle="tab" data-bs-target="#catalog-content" type="button" role="tab">
                Catalog Items
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link bg-dark text-light" id="staged-tab" data-bs-toggle="tab" data-bs-target="#staged-content" type="button" role="tab">
                Staged Items
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link bg-dark text-light" id="excluded-sets-tab" data-bs-toggle="tab" data-bs-target="#excluded-sets-content" type="button" role="tab">
                Excluded Sets
                <span id="excludedSetsCount" class="badge bg-info ms-2" style="display: none;">0</span>
            </button>
        </li>
        {% if current_user.username == "Xavier" %}
        <li class="nav-item" role="presentation">
            <button class="nav-link bg-dark text-light" id="smoshey-tab" data-bs-toggle="tab" data-bs-target="#smoshey-content" type="button" role="tab">
                Smoshey Records
                <span id="smosheyCount" class="badge bg-danger ms-2" style="display: none;">0</span>
            </button>
        </li>
        {% endif %}
    </ul>

    <!-- Tab Content -->
    <div class="tab-content">
        {% if current_user.username == "Xavier" %}
        <!-- Smoshey Records Tab -->
        <div class="tab-pane fade" id="smoshey-content" role="tabpanel">
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 text-light me-3">Smoshey Records</h5>
                    </div>
                    <button id="selectAllSmosheyBtn" class="btn btn-outline-light btn-sm" style="display:none;">
                        <i class="fas fa-check-square me-2"></i>Select All Smoshey
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-dark table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="border-secondary" style="width: 50px;">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="selectAllSmosheyCheckbox">
                                        </div>
                                    </th>
                            <th class="border-secondary">Name</th>
                            <th class="border-secondary">Product ID</th>
                                </tr>
                            </thead>
                            <tbody id="smosheyRecordsTableBody">
                                <!-- Records will be dynamically inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        <!-- Catalog Items Tab -->
        <div class="tab-pane fade show active" id="catalog-content" role="tabpanel">
            <!-- Results Card -->
            <div class="card bg-dark border-secondary">
        <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center flex-grow-1">
                <h5 class="mb-0 text-light me-3">Results</h5>
                <p id="unmatchedCount" class="mb-0 badge bg-danger me-3" style="display: none;"></p>
                <!-- View Mode Toggle -->
                <div class="btn-group me-3">
                    <button id="viewModeExpansions" class="btn btn-sm btn-primary active">Expansions</button>
                    <button id="viewModeCards" class="btn btn-sm btn-outline-primary">Cards</button>
                </div>
                <button id="selectAllExpansionsBtn" class="btn btn-outline-light btn-sm me-3">
                    <i class="fas fa-check-square me-2"></i>Select All Expansions
                </button>
                <!-- Sort by Missing Records Dropdown -->
                <div class="me-3" style="max-width: 200px;">
                    <label for="sortExpansionsBy" class="form-label text-light visually-hidden">Sort Expansions</label>
                    <select id="sortExpansionsBy" class="form-select form-select-sm bg-dark text-light border-secondary">
                        <option value="releaseDate">Sort by Release Date (Newest First)</option>
                        <option value="missingHigh">Sort by Missing Records (High to Low)</option>
                        <option value="missingLow">Sort by Missing Records (Low to High)</option>
                    </select>
                </div>
                <!-- Filter by Expansion Dropdown -->
                <div class="ms-auto me-3" style="max-width: 200px;">
                     <label for="resultExpansionFilter" class="form-label text-light visually-hidden">Filter by Expansion</label>
                     <select id="resultExpansionFilter" class="form-select form-select-sm bg-dark text-light border-secondary">
                         <option value="all">Filter by Expansion</option>
                     </select>
                 </div>
                <button id="selectAllBtn" class="btn btn-outline-light btn-sm" style="display:none;">
                    <i class="fas fa-check-square me-2"></i>Select All
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <!-- Expansions View -->
            <div id="expansionsTableContainer">
                <!-- Expansion Name Filter -->
                <div class="p-3 border-bottom border-secondary">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label for="expansionNameFilter" class="form-label text-light mb-1">Filter by Expansion Name</label>
                            <input type="text" id="expansionNameFilter" class="form-control bg-dark text-light border-secondary"
                                   placeholder="Type to filter expansions..." autocomplete="off">
                        </div>
                        <div class="col-md-8 d-flex align-items-end justify-content-end">
                            <button id="clearExpansionFilter" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear Filter
                            </button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-dark table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="border-secondary" style="width: 50px;">
                                    <i class="fas fa-check-square text-muted"></i>
                                </th>
                                <th class="border-secondary">Expansion Name</th>
                                <th class="border-secondary">Release Date</th>
                                <th class="border-secondary">Missing Records</th>
                                <th class="border-secondary">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="expansionsTableBody">
                            <!-- Expansions dynamically inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Cards View -->
            <div id="unmatchedItemsTableContainer" style="display:none;">
                <div class="table-responsive">
                    <table class="table table-dark table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="border-secondary" style="width: 50px;">
                                    <i class="fas fa-check-square text-muted"></i>
                                </th>
                                <th class="border-secondary">Name</th>
                                <th class="border-secondary">Expansion</th>
                                <th class="border-secondary">Release Date</th>
                                <th class="border-secondary">Product ID</th>
                            </tr>
                        </thead>
                        <tbody id="unmatchedItemsTableBody">
                            <!-- Items dynamically inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div id="paginationContainer" class="d-flex justify-content-center mt-4"></div>

    <!-- Loading Status -->
    <div id="loadingStatus" class="mt-4"></div>
        </div>

        <!-- Staged Items Tab -->
        <div class="tab-pane fade" id="staged-content" role="tabpanel">
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 text-light me-3">Staged Items</h5>
                        <p id="stagedCount" class="mb-0 badge bg-primary" style="display: none;"></p>
                    </div>
                    <button id="selectAllStagedBtn" class="btn btn-outline-light btn-sm" style="display:none;">
                        <i class="fas fa-check-square me-2"></i>Select All Staged
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-dark table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="border-secondary" style="width: 50px;">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="selectAllStagedCheckbox">
                                        </div>
                                    </th>
                                    <th class="border-secondary">Name</th>
                                    <th class="border-secondary">Product ID</th>
                                    <th class="border-secondary">Condition</th>
                                </tr>
                            </thead>
                            <tbody id="stagedItemsTableBody">
                                <!-- Items will be dynamically inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Excluded Sets Tab -->
        <div class="tab-pane fade" id="excluded-sets-content" role="tabpanel">
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark border-secondary">
                    <h5 class="mb-0 text-light">Excluded Sets</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-dark table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="border-secondary">Expansion Name</th>
                                    <th class="border-secondary">Group ID</th>
                                    <th class="border-secondary">Excluded On</th>
                                    <th class="border-secondary">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="excludedSetsTableBody">
                                <!-- Excluded sets will be dynamically inserted here -->
                            </tbody>
                        </table>
                    </div>
                    <div id="noExcludedSets" class="text-center text-light p-4" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>No sets currently excluded
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Queued Files Modal -->
<div class="modal fade" id="queuedFilesModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                    <i class="fas fa-list me-2"></i>Queued Files
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-dark table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="border-secondary">File Name</th>
                                <th class="border-secondary">Size</th>
                                <th class="border-secondary">Created</th>
                                <th class="border-secondary">Modified</th>
                            </tr>
                        </thead>
                        <tbody id="queuedFilesTableBody">
                            <!-- Files dynamically inserted here -->
                        </tbody>
                    </table>
                </div>
                <div id="noQueuedFiles" class="text-center text-light p-4" style="display: none;">
                    <i class="fas fa-info-circle me-2"></i>No queued files found
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">Feedback</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="feedbackModalBody">
                <!-- Feedback message inserted here -->
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(0,0,0,0.7); z-index: 9999;">
    <div class="position-absolute top-50 start-50 translate-middle text-center text-light">
        <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5 id="loadingOverlayText" class="mb-0">Generating Files...</h5>
        <p class="text-muted">This may take a few moments</p>
    </div>
</div>

<!-- Language Selection Modal -->
<div class="modal fade" id="languageSelectionModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                    <i class="fas fa-language me-2"></i>Select Languages
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="languageCheckboxes" class="d-flex flex-column gap-2">
                    <!-- Language checkboxes will be dynamically inserted here -->
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmLanguagesBtn">Continue</button>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Error
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="errorModalBody">
                <!-- Error message inserted here -->
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                     <i class="fas fa-question-circle me-2"></i>Confirm Action
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light" id="confirmationModalBody">
                <!-- Confirmation details will be inserted here -->
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmPushBtn">Confirm Push</button>
            </div>
        </div>
    </div>
</div>




<!-- Include SortableJS before other scripts -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>

<style>
.cursor-move {
    cursor: move !important;
}

.title-components-container {
    min-height: 50px;
    background-color: rgba(255, 255, 255, 0.05);
}

.draggable {
    user-select: none;
    touch-action: none;
}

.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    opacity: 0.8;
    background-color: #0d6efd !important;
}

.sortable-drag {
    opacity: 0.8;
}

#titleComponentOrder {
    min-height: 38px;
}

.badge.draggable {
    font-size: 0.9rem;
    padding: 8px 12px;
}


</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize subscription check variables
    let userSubscriptionType = null;
    let isLimitedSubscription = false;
    const MAX_SELECTIONS_FOR_LIMITED_USERS = 10;

    // Store the current expansions data for re-sorting
    let currentExpansions = [];

    // Function to format release date
    function formatReleaseDate(dateString) {
        if (!dateString) return 'N/A';

        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'N/A';

            // Format date as MM/DD/YYYY
            return date.toLocaleDateString();
        } catch (error) {
            console.error('Error formatting date:', error);
            return 'N/A';
        }
    }

    // Load saved conditions
    loadSavedConditions();

    // Save conditions button click handler
    document.getElementById('saveConditionsBtn').addEventListener('click', function() {
        saveConditions();
    });

    function loadSavedConditions() {
        fetch('/shopify/update-catalog/api/conditions')
            .then(response => response.json())
            .then(settings => {
                if (settings && settings.selected_conditions) {
                    // Uncheck all conditions first
                    document.querySelectorAll('.condition-checkbox').forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // Check the saved conditions
                    settings.selected_conditions.forEach(condition => {
                        const checkbox = document.querySelector(`.condition-checkbox[value="${condition}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });

                    console.log('Loaded saved conditions:', settings.selected_conditions);
                }
            })
            .catch(error => {
                console.error('Error loading saved conditions:', error);
            });
    }

    function saveConditions() {
        const selectedConditions = Array.from(document.querySelectorAll('.condition-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedConditions.length === 0) {
            showErrorMessage('Please select at least one condition.');
            return;
        }

        const settings = {
            selected_conditions: selectedConditions
        };

        fetch('/shopify/update-catalog/api/conditions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showFeedbackMessage('Conditions settings saved successfully', 'success');
            } else {
                showFeedbackMessage('Failed to save conditions settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving conditions settings:', error);
            showFeedbackMessage('Failed to save conditions settings', 'error');
        });
    }

    // Fetch user subscription info
    function fetchUserSubscriptionInfo() {
        fetch('/api/user/subscription-info')
            .then(response => response.json())
            .then(data => {
                userSubscriptionType = data.subscriptionName;
                // Apply limit only if the user is on a 'Free' subscription (case-insensitive)
                // Make sure we're not limiting users on Monthly, Annual, or Lifetime packages
                isLimitedSubscription = userSubscriptionType && userSubscriptionType.toLowerCase() === 'free';

                // Fix for Monthly Basic users - they should not be limited
                if (userSubscriptionType &&
                    (userSubscriptionType.toLowerCase().includes('monthly') ||
                     userSubscriptionType.toLowerCase().includes('annual') ||
                     userSubscriptionType.toLowerCase().includes('lifetime'))) {
                    isLimitedSubscription = false;
                }

                console.log("User subscription type:", userSubscriptionType);
                console.log("Is limited subscription:", isLimitedSubscription);

                // Display subscription warning if needed
                if (isLimitedSubscription) {
                    const warningDiv = document.createElement('div');
                    warningDiv.className = 'alert alert-warning mb-4';
                    warningDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Subscription Notice:</strong> Your current subscription (${userSubscriptionType})
                        only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time.
                        <a href="/activating" class="alert-link">Upgrade to a Monthly, Annual, or Lifetime package</a> to select more items.
                    `;

                    // Insert after the header
                    const header = document.querySelector('.dashboard-header');
                    header.parentNode.insertBefore(warningDiv, header.nextSibling);
                }
            })
            .catch(error => {
                console.error('Error fetching subscription info:', error);
            });
    }

    // Call the function to fetch subscription info
    fetchUserSubscriptionInfo();
    // Fetch smoshey records when tab is shown
    document.getElementById('smoshey-tab')?.addEventListener('click', function() {
        fetchSmosheyRecords();
    });

    function fetchSmosheyRecords() {
        fetch('/shopify/update-catalog/api/smoshey-records')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('smosheyRecordsTableBody');
                const smosheyCount = document.getElementById('smosheyCount');
                tableBody.innerHTML = '';

                if (data.records && data.records.length > 0) {
                    data.records.forEach(record => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td class="border-secondary">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input smoshey-checkbox"
                                           data-product-id="${record.productId}">
                                </div>
                            </td>
                            <td class="border-secondary">${record['Card Name'] || ''}</td>
                            <td class="border-secondary">${record.productId}</td>
                        `;
                        tableBody.appendChild(row);

                        // Add event listener to enforce selection limit
                        const checkbox = row.querySelector('.smoshey-checkbox');
                        checkbox.addEventListener('change', function() {
                            enforceSelectionLimit(this);
                        });
                    });

                    smosheyCount.textContent = data.count;
                    smosheyCount.style.display = 'inline-block';
                    document.getElementById('selectAllSmosheyBtn').style.display = 'inline-block';
                } else {
                    smosheyCount.textContent = '0';
                    smosheyCount.style.display = 'inline-block';
                    document.getElementById('selectAllSmosheyBtn').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error fetching smoshey records:', error);
                showErrorMessage('Failed to load smoshey records. Please try again.');
            });
    }

    // Handle select all checkbox for smoshey records
    document.getElementById('selectAllSmosheyCheckbox')?.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.smoshey-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Handle select all button for smoshey records
    document.getElementById('selectAllSmosheyBtn')?.addEventListener('click', function() {
        const allChecked = this.innerHTML.includes('Deselect');
        const checkboxes = document.querySelectorAll('.smoshey-checkbox');

        if (!allChecked && isLimitedSubscription) {
            // If selecting all and user has limited subscription, check if it would exceed the limit
            const catalogSelected = document.querySelectorAll('.productCheckbox:checked').length;
            const stagedSelected = document.querySelectorAll('.staged-checkbox:checked').length;
            const currentlySelected = catalogSelected + stagedSelected;

            if (checkboxes.length + currentlySelected > MAX_SELECTIONS_FOR_LIMITED_USERS) {
                // If selecting all would exceed the limit, only select up to the limit
                showErrorMessage(`Your current subscription (${userSubscriptionType}) only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time. Only the first ${MAX_SELECTIONS_FOR_LIMITED_USERS - currentlySelected} smoshey records will be selected. <a href="/activating" class="alert-link">Upgrade to a Monthly, Annual, or Lifetime package</a> to select more items.`);

                // Uncheck all first
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Then check only up to the limit
                const remainingSlots = MAX_SELECTIONS_FOR_LIMITED_USERS - currentlySelected;
                for (let i = 0; i < Math.min(remainingSlots, checkboxes.length); i++) {
                    checkboxes[i].checked = true;
                }

                // Update button text
                this.innerHTML = '<i class="fas fa-square me-2"></i>Deselect All Smoshey';
                return;
            }
        }

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        this.innerHTML = allChecked ?
            '<i class="fas fa-check-square me-2"></i>Select All Smoshey' :
            '<i class="fas fa-square me-2"></i>Deselect All Smoshey';
    });

    // Initialize missing product IDs
    let missingProductIds = [];
    let allProducts = [];

    // Fetch staged inventory items when tab is shown
    document.getElementById('staged-tab').addEventListener('click', function() {
        fetchStagedItems();
    });

    function fetchStagedItems() {
        fetch('/shopify/update-catalog/api/staged-items')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('stagedItemsTableBody');
                const stagedCount = document.getElementById('stagedCount');
                tableBody.innerHTML = '';

                if (data.items && data.items.length > 0) {
                    data.items.forEach(item => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td class="border-secondary">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input staged-checkbox"
                                           data-product-id="${item.productId}">
                                </div>
                            </td>
                            <td class="border-secondary">${item.name}</td>
                            <td class="border-secondary">${item.productId}</td>
                            <td class="border-secondary">${item.condition}</td>
                        `;
                        tableBody.appendChild(row);

                        // Add event listener to enforce selection limit
                        const checkbox = row.querySelector('.staged-checkbox');
                        checkbox.addEventListener('change', function() {
                            enforceSelectionLimit(this);
                        });
                    });

                    stagedCount.textContent = `${data.items.length} items`;
                    stagedCount.style.display = 'inline-block';
                    document.getElementById('selectAllStagedBtn').style.display = 'inline-block';
                } else {
                    stagedCount.textContent = '0 items';
                    stagedCount.style.display = 'inline-block';
                    document.getElementById('selectAllStagedBtn').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error fetching staged items:', error);
                showErrorMessage('Failed to load staged items. Please try again.');
            });
    }

    // Handle select all checkbox for staged items
    document.getElementById('selectAllStagedCheckbox').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.staged-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Handle select all button for staged items
    document.getElementById('selectAllStagedBtn').addEventListener('click', function() {
        const allChecked = this.innerHTML.includes('Deselect');
        const checkboxes = document.querySelectorAll('.staged-checkbox');

        if (!allChecked && isLimitedSubscription) {
            // If selecting all and user has limited subscription, check if it would exceed the limit
            const catalogSelected = document.querySelectorAll('.productCheckbox:checked').length;
            const smosheySelected = document.querySelectorAll('.smoshey-checkbox:checked').length;
            const currentlySelected = catalogSelected + smosheySelected;

            if (checkboxes.length + currentlySelected > MAX_SELECTIONS_FOR_LIMITED_USERS) {
                // If selecting all would exceed the limit, only select up to the limit
                showErrorMessage(`Your current subscription (${userSubscriptionType}) only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time. Only the first ${MAX_SELECTIONS_FOR_LIMITED_USERS - currentlySelected} staged items will be selected. <a href="/activating" class="alert-link">Upgrade to a Monthly, Annual, or Lifetime package</a> to select more items.`);

                // Uncheck all first
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Then check only up to the limit
                const remainingSlots = MAX_SELECTIONS_FOR_LIMITED_USERS - currentlySelected;
                for (let i = 0; i < Math.min(remainingSlots, checkboxes.length); i++) {
                    checkboxes[i].checked = true;
                }

                // Update button text
                this.innerHTML = '<i class="fas fa-square me-2"></i>Deselect All Staged';
                return;
            }
        }

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        this.innerHTML = allChecked ?
            '<i class="fas fa-check-square me-2"></i>Select All Staged' :
            '<i class="fas fa-square me-2"></i>Deselect All Staged';
    });

    // Excluded Sets Tab functionality
    document.getElementById('excluded-sets-tab').addEventListener('click', function() {
        fetchExcludedSets();
    });

    function fetchExcludedSets() {
        fetch('/shopify/update-catalog/api/excluded-sets')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('excludedSetsTableBody');
                const excludedSetsCount = document.getElementById('excludedSetsCount');
                const noExcludedSets = document.getElementById('noExcludedSets');
                tableBody.innerHTML = '';

                if (data.excludedSets && data.excludedSets.length > 0) {
                    data.excludedSets.forEach(set => {
                        const row = document.createElement('tr');
                        const excludedDate = set.excludedAt ? new Date(set.excludedAt).toLocaleString() : 'N/A';
                        row.innerHTML = `
                            <td class="border-secondary">${set.expansionName || 'N/A'}</td>
                            <td class="border-secondary">${set.groupId || 'N/A'}</td>
                            <td class="border-secondary">${excludedDate}</td>
                            <td class="border-secondary">
                                <button class="btn btn-sm btn-outline-info include-expansion-btn"
                                        data-expansion-name="${set.expansionName}" data-group-id="${set.groupId}">
                                    <i class="fas fa-plus me-1"></i>Include
                                </button>
                            </td>
                        `;
                        tableBody.appendChild(row);

                        // Add event listener for the include button
                        row.querySelector('.include-expansion-btn').addEventListener('click', function() {
                            const expansionName = this.getAttribute('data-expansion-name');
                            const groupId = this.getAttribute('data-group-id');
                            includeExpansion(expansionName, groupId);
                        });
                    });
                    excludedSetsCount.textContent = data.excludedSets.length;
                    excludedSetsCount.style.display = 'inline-block';
                    noExcludedSets.style.display = 'none';
                } else {
                    excludedSetsCount.textContent = '0';
                    excludedSetsCount.style.display = 'inline-block';
                    noExcludedSets.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error fetching excluded sets:', error);
                showErrorMessage('Failed to load excluded sets. Please try again.');
            });
    }

    // Sample card data for preview
    const previewCard = {
        name: 'Opal Palace',
        expansionName: 'Commander Legends',
        number: '352'
    };

    // Call updateTitlePreview immediately to ensure the preview is updated
    setTimeout(updateTitlePreview, 100);

    // Initialize Sortable with animation and proper handling
    new Sortable(document.getElementById('titleComponentOrder'), {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        forceFallback: true,
        fallbackClass: 'sortable-fallback',
        onEnd: function(evt) {
            updateTitlePreview();
            // Update visual state of badges
            updateBadgeStates();
        }
    });

    // Load saved title format settings
    fetch('/shopify/update-catalog/api/title-format')
        .then(response => response.json())
        .then(settings => {
            document.getElementById('includeNumber').checked = settings.includeNumber;
            document.getElementById('includeExpansion').checked = settings.includeExpansion;

        // Update component order
        const orderContainer = document.getElementById('titleComponentOrder');
        orderContainer.innerHTML = '';
        settings.order.forEach(component => {
            // Map 'abbreviation' to 'expansion' for backwards compatibility
            const mappedComponent = component === 'abbreviation' ? 'expansion' : component;

            const div = document.createElement('div');
            const isEnabled = mappedComponent === 'name' ||
                            (mappedComponent === 'number' && settings.includeNumber) ||
                            (mappedComponent === 'expansion' && settings.includeExpansion);

            div.className = `badge ${isEnabled ? 'bg-primary' : 'bg-secondary'} p-2 draggable cursor-move`;
            div.dataset.component = mappedComponent;
            div.textContent = mappedComponent === 'name' ? 'Card Name' :
                            mappedComponent === 'number' ? 'Card Number' : 'Expansion Name';
            orderContainer.appendChild(div);
        });

            updateTitlePreview();
        })
        .catch(error => {
            console.error('Error loading title format settings:', error);
            updateTitlePreview(); // Still show preview with defaults
        });

    function updateBadgeStates() {
        document.querySelectorAll('#titleComponentOrder .draggable').forEach(badge => {
            const type = badge.dataset.component;
            const isEnabled = type === 'name' ||
                            (type === 'number' && document.getElementById('includeNumber').checked) ||
                            (type === 'expansion' && document.getElementById('includeExpansion').checked);
            badge.className = `badge ${isEnabled ? 'bg-primary' : 'bg-secondary'} p-2 draggable cursor-move`;
        });
    }

    // Handle checkbox changes
    document.getElementById('includeNumber').addEventListener('change', function() {
        updateBadgeStates();
        updateTitlePreview();
    });

    document.getElementById('includeExpansion').addEventListener('change', function() {
        updateBadgeStates();
        updateTitlePreview();
    });

    // Save title format settings
    document.getElementById('saveTitleFormatBtn').addEventListener('click', function() {
        const settings = {
            includeName: true,
            includeNumber: document.getElementById('includeNumber').checked,
            includeExpansion: document.getElementById('includeExpansion').checked,
            order: Array.from(document.querySelectorAll('#titleComponentOrder .draggable'))
                      .map(el => el.dataset.component)
        };

        fetch('/shopify/update-catalog/api/title-format', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showFeedbackMessage('Title format settings saved successfully', 'success');
            } else {
                showFeedbackMessage('Failed to save title format settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving title format settings:', error);
            showFeedbackMessage('Failed to save title format settings', 'error');
        });
    });

    function updateTitlePreview() {
        const preview = [];
        const components = document.querySelectorAll('#titleComponentOrder .draggable');

        components.forEach(component => {
            const type = component.dataset.component;
            const isEnabled = type === 'name' ||
                            (type === 'number' && document.getElementById('includeNumber').checked) ||
                            (type === 'expansion' && document.getElementById('includeExpansion').checked);

            if (isEnabled) {
                switch (type) {
                    case 'name':
                        preview.push(previewCard.name);
                        break;
                    case 'number':
                        preview.push(`(${previewCard.number})`);
                        break;
                    case 'expansion':
                        preview.push(`(${previewCard.expansionName})`);
                        break;
                }
            }
        });

        document.getElementById('titlePreview').textContent = preview.join(' ');

        // Log for debugging
        console.log('Preview components:', preview);
        console.log('Include Number:', document.getElementById('includeNumber').checked);
        console.log('Include Expansion:', document.getElementById('includeExpansion').checked);
        console.log('Component order:', Array.from(components).map(c => c.dataset.component));
    }

    function showFeedbackMessage(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';

        const feedbackModal = new bootstrap.Modal(document.getElementById('feedbackModal'));
        document.getElementById('feedbackModalBody').innerHTML = `
            <div class="alert ${alertClass} mb-0">
                <i class="fas fa-${icon} me-2"></i>${message}
            </div>
        `;
        feedbackModal.show();
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
        new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize queued files modal
    const queuedFilesModal = new bootstrap.Modal(document.getElementById('queuedFilesModal'));

    document.getElementById('viewQueuedBtn').addEventListener('click', function() {
        fetchQueuedFiles();
    });

    function fetchQueuedFiles() {
        fetch('/shopify/update-catalog/api/queued-files')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('queuedFilesTableBody');
                const noFiles = document.getElementById('noQueuedFiles');

                tableBody.innerHTML = '';

                if (data.files && data.files.length > 0) {
                    data.files.forEach(file => {
                        const row = document.createElement('tr');
                        const size = formatFileSize(file.size);
                        const created = new Date(file.created).toLocaleString();
                        const modified = new Date(file.modified).toLocaleString();

                        row.innerHTML = `
                            <td class="border-secondary">${file.name}</td>
                            <td class="border-secondary">${size}</td>
                            <td class="border-secondary">${created}</td>
                            <td class="border-secondary">${modified}</td>
                        `;
                        tableBody.appendChild(row);
                    });
                    tableBody.closest('.table-responsive').style.display = 'block';
                    noFiles.style.display = 'none';
                } else {
                    tableBody.closest('.table-responsive').style.display = 'none';
                    noFiles.style.display = 'block';
                }

                queuedFilesModal.show();
            })
            .catch(error => {
                console.error('Error fetching queued files:', error);
                showErrorMessage('Failed to load queued files. Please try again.');
            });
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    fetch('/shopify/update-catalog/api/games')
        .then(response => response.json())
        .then(games => {
            const gameSelect = document.getElementById('gameSelect');
            games.forEach(game => {
                const option = document.createElement('option');
                option.value = game;
                option.textContent = game;
                gameSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error fetching games:', error);
            showErrorMessage('Failed to load games. Please refresh the page or contact support.');
        });

    function fetchAndPopulateFilters() {
        fetch('/shopify/update-catalog/api/unmatched-items')
            .then(response => response.json())
            .then(data => {
                if (data && data.filters) {
                    const expansionSelect = document.getElementById('expansionSelect');
                    expansionSelect.innerHTML = '<option value="all">All Expansions</option>';
                    data.filters.expansionNames.forEach(expansion => {
                        const option = document.createElement('option');
                        option.value = expansion;
                        option.textContent = expansion;
                        expansionSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching filters:', error);
                showErrorMessage('Failed to load filters. Please refresh the page or contact support.');
            });
    }

    fetchAndPopulateFilters();

    document.getElementById('gameSelect').addEventListener('change', function() {
        const gameName = this.value;
        const expansionSelect = document.getElementById('expansionSelect');
        expansionSelect.innerHTML = '<option value="">Select Expansion</option>';
        document.getElementById('unmatchedCount').style.display = 'none';
        document.getElementById('unmatchedItemsTableContainer').style.display = 'none';

        if (gameName) {
            expansionSelect.disabled = true;
            expansionSelect.innerHTML = '<option value="">Loading...</option>';

            fetch(`/shopify/update-catalog/api/expansions?gameName=${encodeURIComponent(gameName)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(expansions => {
                    expansionSelect.innerHTML = '<option value="">Select Expansion</option>';
                    expansions.forEach(expansion => {
                        const option = document.createElement('option');
                        option.value = expansion;
                        option.textContent = expansion;
                        expansionSelect.appendChild(option);
                    });
                    expansionSelect.disabled = false;
                })
                .catch(error => {
                    console.error('Error fetching expansions:', error);
                    expansionSelect.innerHTML = '<option value="">Error loading expansions</option>';
                    showErrorMessage('Failed to load expansions. Please try again.');
                });
        } else {
            expansionSelect.disabled = true;
        }
    });



    document.getElementById('searchInput').addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('applyFilterBtn').click();
        }
    });

document.getElementById('applyFilterBtn').addEventListener('click', function() {
        const gameName = document.getElementById('gameSelect').value;
        const expansionName = document.getElementById('expansionSelect').value;
        const filter = getSelectedFilter();
        const searchTerm = document.getElementById('searchInput').value.trim();
        const viewMode = document.getElementById('viewModeExpansions').classList.contains('active') ? 'expansions' : 'cards';

        // Show loading overlay with appropriate text
        document.getElementById('loadingOverlayText').textContent = "Processing Filters...";
        showLoadingOverlay();

        if (viewMode === 'expansions') {
            fetchExpansionStats(gameName, filter, searchTerm);
        } else {
            fetchUnmatchedItems(gameName, expansionName, filter, null, null, 1, searchTerm);
        }
    });

    // Sort expansions dropdown handler
    document.getElementById('sortExpansionsBy').addEventListener('change', function() {
        // Re-sort and display the expansions without making a new API call
        if (currentExpansions.length > 0) {
            displayExpansionStats([...currentExpansions]);
        }
    });

    // View mode toggle handlers
    document.getElementById('viewModeExpansions').addEventListener('click', function() {
        if (!this.classList.contains('active')) {
            // Switch to expansions view
            this.classList.add('active');
            this.classList.remove('btn-outline-primary');
            this.classList.add('btn-primary');

            document.getElementById('viewModeCards').classList.remove('active');
            document.getElementById('viewModeCards').classList.remove('btn-primary');
            document.getElementById('viewModeCards').classList.add('btn-outline-primary');

            document.getElementById('expansionsTableContainer').style.display = 'block';
            document.getElementById('unmatchedItemsTableContainer').style.display = 'none';
            document.getElementById('paginationContainer').style.display = 'none';

            // Fetch expansion stats
            const gameName = document.getElementById('gameSelect').value;
            const filter = getSelectedFilter();
            const searchTerm = document.getElementById('searchInput').value.trim();

            if (gameName) {
                fetchExpansionStats(gameName, filter, searchTerm);
            }
        }
    });

    document.getElementById('viewModeCards').addEventListener('click', function() {
        if (!this.classList.contains('active')) {
            // Switch to cards view
            this.classList.add('active');
            this.classList.remove('btn-outline-primary');
            this.classList.add('btn-primary');

            document.getElementById('viewModeExpansions').classList.remove('active');
            document.getElementById('viewModeExpansions').classList.remove('btn-primary');
            document.getElementById('viewModeExpansions').classList.add('btn-outline-primary');

            document.getElementById('expansionsTableContainer').style.display = 'none';
            document.getElementById('unmatchedItemsTableContainer').style.display = 'block';
            document.getElementById('paginationContainer').style.display = 'flex';

            // Fetch cards
            const gameName = document.getElementById('gameSelect').value;
            const expansionName = document.getElementById('expansionSelect').value;
            const filter = getSelectedFilter();
            const searchTerm = document.getElementById('searchInput').value.trim();

            if (gameName) {
                fetchUnmatchedItems(gameName, expansionName, filter, null, null, 1, searchTerm);
            }
        }
    });

    let currentPage = 1;
    let totalPages = 1;

    function updateResultExpansionFilter(products) {
        const expansionFilter = document.getElementById('resultExpansionFilter');
        const currentValue = expansionFilter.value;

        const expansions = [...new Set(products.map(p => p.expansionName))].filter(e => e).sort();

        expansionFilter.innerHTML = '<option value="all">All Expansions</option>';
        expansions.forEach(expansion => {
            const option = document.createElement('option');
            option.value = expansion;
            option.textContent = expansion;
            expansionFilter.appendChild(option);
        });

        if (expansions.includes(currentValue)) {
            expansionFilter.value = currentValue;
        }
    }

    function filterAndDisplayProducts(products) {
        const expansionFilter = document.getElementById('resultExpansionFilter');
        const selectedExpansion = expansionFilter.value;

        let filteredProducts = products;
        if (selectedExpansion !== 'all') {
            filteredProducts = products.filter(p => p.expansionName === selectedExpansion);
        }

        displayProducts(filteredProducts);
    }

    // Function to check if selection limit is reached for limited subscription users
    function checkSelectionLimit() {
        if (!isLimitedSubscription) return true; // No limit for premium subscriptions

        const catalogProducts = document.querySelectorAll('.productCheckbox:checked').length;
        const stagedProducts = document.querySelectorAll('.staged-checkbox:checked').length;
        const smosheyProducts = document.querySelectorAll('.smoshey-checkbox:checked').length;

        const totalSelected = catalogProducts + stagedProducts + smosheyProducts;
        return totalSelected <= MAX_SELECTIONS_FOR_LIMITED_USERS;
    }

    // Function to enforce selection limit
    function enforceSelectionLimit(checkbox) {
        if (isLimitedSubscription && checkbox.checked) {
            const catalogProducts = document.querySelectorAll('.productCheckbox:checked').length;
            const stagedProducts = document.querySelectorAll('.staged-checkbox:checked').length;
            const smosheyProducts = document.querySelectorAll('.smoshey-checkbox:checked').length;

            const totalSelected = catalogProducts + stagedProducts + smosheyProducts;

            if (totalSelected > MAX_SELECTIONS_FOR_LIMITED_USERS) {
                checkbox.checked = false;
                showErrorMessage(`Your current subscription (${userSubscriptionType}) only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time. <a href="/activating" class="alert-link">Upgrade to a Monthly, Annual, or Lifetime package</a> to select more items.`);
                return false;
            }
        }
        return true;
    }

    function displayProducts(products) {
        const unmatchedCount = document.getElementById('unmatchedCount');
        const unmatchedItemsTableBody = document.getElementById('unmatchedItemsTableBody');
        unmatchedItemsTableBody.innerHTML = '';

        if (products.length > 0) {
            unmatchedCount.textContent = `${products.length} items`;
            unmatchedCount.style.display = 'inline-block';

            // First sort by release date (newest first)
            products.sort((a, b) => {
                // Handle missing products first
                const aIsMissing = missingProductIds.includes(a.productId);
                const bIsMissing = missingProductIds.includes(b.productId);
                if (aIsMissing && !bIsMissing) return -1;
                if (!aIsMissing && bIsMissing) return 1;

                // Then sort by release date (newest first)
                // Handle cases where release date might be missing
                if (!a.releasedOn && !b.releasedOn) return 0;
                if (!a.releasedOn) return 1; // Items without dates go last
                if (!b.releasedOn) return -1;

                // Compare dates - future dates should be considered "newest"
                const dateA = new Date(a.releasedOn);
                const dateB = new Date(b.releasedOn);

                // Check if dates are valid
                const isValidDateA = !isNaN(dateA.getTime());
                const isValidDateB = !isNaN(dateB.getTime());

                if (!isValidDateA && !isValidDateB) return 0;
                if (!isValidDateA) return 1;
                if (!isValidDateB) return -1;

                // Sort newest first
                return dateB - dateA;
            });

            products.forEach(product => {
                const row = document.createElement('tr');
                const isMissing = missingProductIds.includes(product.productId);
                row.innerHTML = `
                    <td class="align-middle border-secondary">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input productCheckbox" id="checkbox-${product.productId}"
                                   data-product-id="${product.productId}" ${isMissing ? 'checked' : ''}>
                            <label class="form-check-label" for="checkbox-${product.productId}"></label>
                        </div>
                    </td>
                    <td class="align-middle border-secondary">
                        ${product.name}
                        ${isMissing ? '<span class="badge bg-warning text-dark missing-badge">Missing</span>' : ''}
                    </td>
                    <td class="align-middle border-secondary">${product.expansionName || 'N/A'}</td>
                    <td class="align-middle border-secondary">${formatReleaseDate(product.releasedOn)}</td>
                    <td class="align-middle border-secondary">${product.productId}</td>
                `;
                unmatchedItemsTableBody.appendChild(row);

                // Add event listener to enforce selection limit
                const checkbox = row.querySelector('.productCheckbox');
                checkbox.addEventListener('change', function() {
                    enforceSelectionLimit(this);
                });
            });

            document.getElementById('unmatchedItemsTableContainer').style.display = 'block';
            document.getElementById('selectAllBtn').style.display = 'inline-block';
        } else {
            unmatchedCount.textContent = '0 items';
            unmatchedCount.classList.remove('bg-danger');
            unmatchedCount.classList.add('bg-success');
            unmatchedCount.style.display = 'inline-block';
            document.getElementById('unmatchedItemsTableContainer').style.display = 'none';
            document.getElementById('selectAllBtn').style.display = 'none';
        }
    }

    function fetchExpansionStats(gameName, filter, searchTerm = '') {
        let url = `/shopify/update-catalog/api/unmatched-items?gameName=${encodeURIComponent(gameName)}&filter=${filter}&groupByExpansion=true`;
        if (searchTerm) {
            url += `&search=${encodeURIComponent(searchTerm)}`;
        }

        // Show loading status
        const loadingStatus = document.getElementById('loadingStatus');
        loadingStatus.innerHTML = `
            <div class="alert alert-info mt-3">
                <i class="fas fa-spinner fa-spin me-2"></i>Loading expansion statistics... This may take a moment.
            </div>
        `;

        const fetchTimeout = 120000;
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timed out')), fetchTimeout);
        });

        Promise.race([
            fetch(url),
            timeoutPromise
        ])
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Network response was not ok');
                });
            }
            return response.json();
        })
        .then(data => {
            // Hide loading overlay
            hideLoadingOverlay();

            // Clear loading status
            loadingStatus.innerHTML = '';

            // Check for warning message
            if (data.warning) {
                loadingStatus.innerHTML = `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>${data.warning}
                    </div>
                `;
            }

            if (data.expansions && data.expansions.length > 0) {
                // Store the expansions data for re-sorting
                currentExpansions = data.expansions;

                // Display the expansions
                displayExpansionStats(currentExpansions);

                // Update the expansion filter dropdown
                const expansionFilter = document.getElementById('resultExpansionFilter');
                expansionFilter.innerHTML = '<option value="all">All Expansions</option>';
                data.filters.expansionNames.forEach(expansion => {
                    const option = document.createElement('option');
                    option.value = expansion;
                    option.textContent = expansion;
                    expansionFilter.appendChild(option);
                });
            } else {
                currentExpansions = [];
                displayExpansionStats([]);

                // If there's no data but we have a warning, it might be due to too many results
                if (data.warning) {
                    loadingStatus.innerHTML = `
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Too many results:</strong> ${data.warning}
                        </div>
                        <div class="alert alert-info mt-2">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Suggestion:</strong> Please use the search box to narrow down your results.
                        </div>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Hide loading overlay
            hideLoadingOverlay();

            // Clear and update loading status
            loadingStatus.innerHTML = '';

            let errorMessage = 'An unexpected error occurred. Please try again.';
            if (error.message === 'Request timed out') {
                errorMessage = 'The request timed out. Please try again with more specific filters.';

                // Add more helpful information
                loadingStatus.innerHTML = `
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Request timed out:</strong> There are too many results to process.
                    </div>
                    <div class="alert alert-info mt-2">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Suggestions:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Use the search box to find specific cards</li>
                            <li>Filter by singles or sealed products only</li>
                            <li>Try a different game with fewer expansions</li>
                        </ul>
                    </div>
                `;
            } else if (error.message.includes('too many results')) {
                errorMessage = error.message;

                // Add more helpful information
                loadingStatus.innerHTML = `
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Error:</strong> ${error.message}
                    </div>
                    <div class="alert alert-info mt-2">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Suggestions:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Use the search box to find specific cards</li>
                            <li>Filter by singles or sealed products only</li>
                            <li>Try a different game with fewer expansions</li>
                        </ul>
                    </div>
                `;
            }

            showErrorMessage(errorMessage);
        });
    }

    function displayExpansionStats(expansions) {
        const unmatchedCount = document.getElementById('unmatchedCount');
        const expansionsTableBody = document.getElementById('expansionsTableBody');
        expansionsTableBody.innerHTML = '';

        if (expansions.length > 0) {
            // Sort expansions based on selected sort option
            const sortBy = document.getElementById('sortExpansionsBy').value;

            if (sortBy === 'missingHigh') {
                // Sort by missing records count (high to low)
                expansions.sort((a, b) => b.count - a.count);
            } else if (sortBy === 'missingLow') {
                // Sort by missing records count (low to high)
                expansions.sort((a, b) => a.count - b.count);
            }
            // Default sorting is by release date (newest first), which is already done in the backend

            // Store the expansions data for filtering
            allExpansionsData = expansions;

            // Clear any existing filter and render all expansions
            document.getElementById('expansionNameFilter').value = '';
            renderExpansions(expansions);

            // Calculate and display total missing records
            const totalMissing = expansions.reduce((total, exp) => total + exp.count, 0);
            unmatchedCount.textContent = `${totalMissing} items across ${expansions.length} expansions`;
            unmatchedCount.style.display = 'inline-block';

            document.getElementById('expansionsTableContainer').style.display = 'block';
        } else {
            // Clear stored data
            allExpansionsData = [];
            unmatchedCount.textContent = '0 items';
            unmatchedCount.style.display = 'inline-block';
            document.getElementById('expansionsTableContainer').style.display = 'none';
        }
    }

    function fetchAndSelectExpansionItems(gameName, expansionName) {
        fetch(`/shopify/update-catalog/api/expansion-items?gameName=${encodeURIComponent(gameName)}&expansionName=${encodeURIComponent(expansionName)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                hideLoadingOverlay();

                if (data.products && data.products.length > 0) {
                    // Store the product IDs to be selected
                    const productIds = data.products.map(product => product.productId);

                    // Switch to cards view to show the selected items
                    document.getElementById('viewModeCards').click();

                    // Set the expansion in the dropdown
                    document.getElementById('expansionSelect').value = expansionName;

                    // Fetch the cards and then select them
                    fetchUnmatchedItems(gameName, expansionName, getSelectedFilter(), null, null, 1, '', () => {
                        // After fetching, select all the products from this expansion
                        selectProductsByIds(productIds);
                    });
                } else {
                    showErrorMessage('No cards found for this expansion.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                hideLoadingOverlay();
                showErrorMessage('Failed to load expansion items. Please try again.');
            });
    }

    function selectProductsByIds(productIds) {
        // First uncheck all checkboxes
        document.querySelectorAll('.productCheckbox').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Then check only the ones with matching product IDs
        productIds.forEach(productId => {
            const checkbox = document.querySelector(`.productCheckbox[data-product-id="${productId}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });

        // Update the select all button text
        const selectAllBtn = document.getElementById('selectAllBtn');
        selectAllBtn.innerHTML = '<i class="fas fa-square me-2"></i>Deselect All';

        // Show a success message
        showFeedbackMessage(`Selected ${productIds.length} cards from expansion`, 'success');
    }

    function fetchUnmatchedItems(gameName, expansionName, filter, set, rarity, page = 1, searchTerm = '', callback) {
        // Add per_page parameter with a reasonable default
        const per_page = 100;

        let url = `/shopify/update-catalog/api/unmatched-items?gameName=${encodeURIComponent(gameName)}&filter=${filter}&page=${page}&per_page=${per_page}`;
        if (expansionName) {
            url += `&expansionName=${encodeURIComponent(expansionName)}`;
        }
        if (searchTerm) {
            url += `&search=${encodeURIComponent(searchTerm)}`;
        }

        // Show loading status
        const loadingStatus = document.getElementById('loadingStatus');
        loadingStatus.innerHTML = `
            <div class="alert alert-info mt-3">
                <i class="fas fa-spinner fa-spin me-2"></i>Loading products... This may take a moment.
            </div>
        `;

        const fetchTimeout = 120000;
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timed out')), fetchTimeout);
        });

        Promise.race([
            fetch(url),
            timeoutPromise
        ])
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Network response was not ok');
                });
            }
            return response.json();
        })
        .then(data => {
            // Hide loading overlay
            hideLoadingOverlay();

            // Clear loading status
            loadingStatus.innerHTML = '';

            // Check for warning message
            if (data.warning) {
                loadingStatus.innerHTML = `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>${data.warning}
                    </div>
                `;
            }

            if (data.count > 0) {
                allProducts = data.products;
                updateResultExpansionFilter(data.products);
                filterAndDisplayProducts(data.products);

                currentPage = data.page;
                totalPages = data.total_pages;
                updatePagination();

                // Add per-page selector if there are multiple pages
                if (totalPages > 1) {
                    const perPageSelector = document.createElement('div');
                    perPageSelector.className = 'mt-3 text-center';
                    perPageSelector.innerHTML = `
                        <div class="d-inline-block">
                            <label for="perPageSelect" class="form-label text-light me-2">Items per page:</label>
                            <select id="perPageSelect" class="form-select form-select-sm d-inline-block" style="width: auto;">
                                <option value="50" ${per_page === 50 ? 'selected' : ''}>50</option>
                                <option value="100" ${per_page === 100 ? 'selected' : ''}>100</option>
                                <option value="200" ${per_page === 200 ? 'selected' : ''}>200</option>
                                <option value="500" ${per_page === 500 ? 'selected' : ''}>500</option>
                            </select>
                        </div>
                    `;
                    document.getElementById('paginationContainer').appendChild(perPageSelector);

                    // Add event listener to per page selector
                    document.getElementById('perPageSelect').addEventListener('change', function() {
                        const newPerPage = parseInt(this.value);
                        fetchUnmatchedItems(gameName, expansionName, filter, set, rarity, 1, searchTerm, callback);
                    });
                }

                // Call the callback function if provided
                if (typeof callback === 'function') {
                    callback();
                }
            } else {
                displayProducts([]);

                // If there's no data but we have a warning, it might be due to too many results
                if (data.warning && !expansionName) {
                    loadingStatus.innerHTML = `
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Too many results:</strong> ${data.warning}
                        </div>
                        <div class="alert alert-info mt-2">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Suggestion:</strong> Please select a specific expansion from the dropdown to narrow down your search.
                        </div>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Hide loading overlay
            hideLoadingOverlay();

            // Clear and update loading status
            loadingStatus.innerHTML = '';

            let errorMessage = 'An unexpected error occurred. Please try again.';
            if (error.message === 'Request timed out') {
                errorMessage = 'The request timed out. Please try again with more specific filters.';

                // Add more helpful information
                loadingStatus.innerHTML = `
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Request timed out:</strong> There are too many results to process.
                    </div>
                    <div class="alert alert-info mt-2">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Suggestions:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Select a specific expansion from the dropdown</li>
                            <li>Use the search box to find specific cards</li>
                            <li>Filter by singles or sealed products only</li>
                        </ul>
                    </div>
                `;
            } else if (error.message.includes('too many results')) {
                errorMessage = error.message;

                // Add more helpful information
                loadingStatus.innerHTML = `
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Error:</strong> ${error.message}
                    </div>
                    <div class="alert alert-info mt-2">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Suggestions:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Select a specific expansion from the dropdown</li>
                            <li>Use the search box to find specific cards</li>
                            <li>Filter by singles or sealed products only</li>
                        </ul>
                    </div>
                `;
            }

            showErrorMessage(errorMessage);
        });
    }

    function updatePagination() {
        const paginationContainer = document.getElementById('paginationContainer');
        paginationContainer.innerHTML = '';

        if (totalPages > 1) {
            const nav = document.createElement('nav');
            const ul = document.createElement('ul');
            ul.className = 'pagination';

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `
                <button class="page-link bg-dark text-light border-secondary" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
            if (currentPage !== 1) {
                prevLi.querySelector('button').addEventListener('click', () => changePage(currentPage - 1));
            }
            ul.appendChild(prevLi);

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `
                    <button class="page-link bg-dark text-light border-secondary">
                        ${i}
                    </button>
                `;
                if (i !== currentPage) {
                    li.querySelector('button').addEventListener('click', () => changePage(i));
                }
                ul.appendChild(li);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `
                <button class="page-link bg-dark text-light border-secondary" ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
            if (currentPage !== totalPages) {
                nextLi.querySelector('button').addEventListener('click', () => changePage(currentPage + 1));
            }
            ul.appendChild(nextLi);

            nav.appendChild(ul);
            paginationContainer.appendChild(nav);
        }
    }

    function changePage(page) {
        const searchTerm = document.getElementById('searchInput').value.trim();
        const viewMode = document.getElementById('viewModeExpansions').classList.contains('active') ? 'expansions' : 'cards';

        // Show loading overlay with appropriate text
        document.getElementById('loadingOverlayText').textContent = "Loading Page...";
        showLoadingOverlay();

        if (viewMode === 'expansions') {
            fetchExpansionStats(
                document.getElementById('gameSelect').value,
                getSelectedFilter(),
                searchTerm
            );
        } else {
            fetchUnmatchedItems(
                document.getElementById('gameSelect').value,
                document.getElementById('expansionSelect').value,
                getSelectedFilter(),
                null,
                null,
                page,
                searchTerm
            );
        }
    }

    function showErrorMessage(message) {
        const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
        document.getElementById('errorModalBody').innerHTML = `
            <div class="alert alert-danger mb-0">
                ${message}
            </div>
        `;
        errorModal.show();
    }


    function getSelectedFilter() {
        return document.getElementById('filterSelect').value;
    }

    document.getElementById('selectAllBtn').addEventListener('click', function() {
        const allChecked = this.innerHTML.includes('Deselect');
        const checkboxes = document.querySelectorAll('.productCheckbox');

        if (!allChecked && isLimitedSubscription) {
            // If selecting all and user has limited subscription, check if it would exceed the limit
            const currentlySelected = document.querySelectorAll('.staged-checkbox:checked').length +
                                     document.querySelectorAll('.smoshey-checkbox:checked').length;

            if (checkboxes.length + currentlySelected > MAX_SELECTIONS_FOR_LIMITED_USERS) {
                // If selecting all would exceed the limit, only select up to the limit
                showErrorMessage(`Your current subscription (${userSubscriptionType}) only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time. Only the first ${MAX_SELECTIONS_FOR_LIMITED_USERS - currentlySelected} items will be selected. <a href="/activating" class="alert-link">Upgrade to a Monthly, Annual, or Lifetime package</a> to select more items.`);

                // Uncheck all first
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Then check only up to the limit
                const remainingSlots = MAX_SELECTIONS_FOR_LIMITED_USERS - currentlySelected;
                for (let i = 0; i < Math.min(remainingSlots, checkboxes.length); i++) {
                    checkboxes[i].checked = true;
                }

                // Update button text
                this.innerHTML = '<i class="fas fa-square me-2"></i>Deselect All';
                return;
            }
        }

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        this.innerHTML = allChecked ?
            '<i class="fas fa-check-square me-2"></i>Select All' :
            '<i class="fas fa-square me-2"></i>Deselect All';
    });

    function showLoadingOverlay() {
        document.getElementById('loadingOverlay').classList.remove('d-none');
    }

    function hideLoadingOverlay() {
        document.getElementById('loadingOverlay').classList.add('d-none');
    }

    // Function to show language selection modal
    function showLanguageSelectionModal(callback) { // Removed 'languages' parameter
        const languageCheckboxes = document.getElementById('languageCheckboxes');
        languageCheckboxes.innerHTML = '';

        // Hardcoded language options
        const hardcodedLanguages = ["CS", "CT", "DE", "EN", "FR", "IT", "JP", "KR", "PT", "RU", "SP"];

        hardcodedLanguages.forEach(lang => {
            const div = document.createElement('div');
            div.className = 'form-check';
            div.innerHTML = `
                <input type="checkbox" class="form-check-input language-checkbox" id="lang-${lang}" value="${lang}" ${lang === 'EN' ? 'checked' : ''}>
                <label class="form-check-label text-light" for="lang-${lang}">${lang}</label>
            `;
            languageCheckboxes.appendChild(div);
        });

        const modal = new bootstrap.Modal(document.getElementById('languageSelectionModal'));

        // Remove any existing event listener
        const confirmBtn = document.getElementById('confirmLanguagesBtn');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new event listener
        newConfirmBtn.addEventListener('click', () => {
            const selectedLanguages = Array.from(document.querySelectorAll('.language-checkbox:checked'))
                .map(checkbox => checkbox.value);
            modal.hide();
            callback(selectedLanguages);
        });

        modal.show();
    }

    // Function to fetch products for multiple expansions
    async function fetchProductsForExpansions(expansionNames, existingProducts = []) {
        const gameName = document.getElementById('gameSelect').value;
        let allProducts = [...existingProducts]; // Start with existing products

        try {
            // Show a message that we're loading expansion data
            const loadingStatus = document.getElementById('loadingStatus');
            loadingStatus.innerHTML = `
                <div class="alert alert-info mt-3">
                    <i class="fas fa-spinner fa-spin me-2"></i>Loading products from ${expansionNames.length} selected expansion(s)...
                </div>
            `;

            // Fetch products for each expansion sequentially
            for (const expansionName of expansionNames) {
                // Update loading message
                loadingStatus.innerHTML += `
                    <div class="alert alert-info mt-2">
                        <i class="fas fa-spinner fa-spin me-2"></i>Loading products from expansion: ${expansionName}...
                    </div>
                `;

                const response = await fetch(`/shopify/update-catalog/api/expansion-items?gameName=${encodeURIComponent(gameName)}&expansionName=${encodeURIComponent(expansionName)}`);

                if (!response.ok) {
                    throw new Error(`Failed to fetch items for expansion: ${expansionName}`);
                }

                const data = await response.json();
                if (data.products && data.products.length > 0) {
                    // Add product IDs to the list
                    const productIds = data.products.map(product => product.productId);
                    allProducts = [...allProducts, ...productIds];

                    // Update loading message with success
                    loadingStatus.innerHTML += `
                        <div class="alert alert-success mt-2">
                            <i class="fas fa-check-circle me-2"></i>Found ${productIds.length} products in expansion: ${expansionName}
                        </div>
                    `;
                } else {
                    // Update loading message with warning
                    loadingStatus.innerHTML += `
                        <div class="alert alert-warning mt-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>No products found in expansion: ${expansionName}
                        </div>
                    `;
                }
            }

            hideLoadingOverlay();

            // Show summary message
            loadingStatus.innerHTML += `
                <div class="alert alert-success mt-3">
                    <i class="fas fa-check-circle me-2"></i>Total products selected: ${allProducts.length}
                </div>
            `;

            // Continue with the normal process using the combined product list
            if (allProducts.length > 0) {
                continueWithProductSelection(allProducts);
            } else {
                showErrorMessage('No products found in the selected expansions. Please select different expansions or individual products.');
            }

        } catch (error) {
            console.error('Error fetching expansion products:', error);
            hideLoadingOverlay();
            showErrorMessage(`Failed to load expansion items: ${error.message}`);
        }
    }

    document.getElementById('pushToUpdatesBtn').addEventListener('click', async function() {

        // Get selected products from catalog, staged items, and smoshey records
        const catalogProducts = Array.from(document.querySelectorAll('.productCheckbox:checked'))
            .map(checkbox => parseInt(checkbox.dataset.productId));
        const stagedProducts = Array.from(document.querySelectorAll('.staged-checkbox:checked'))
            .map(checkbox => parseInt(checkbox.dataset.productId));
        const smosheyProducts = Array.from(document.querySelectorAll('.smoshey-checkbox:checked'))
            .map(checkbox => parseInt(checkbox.dataset.productId));

        // Get selected expansions
        const selectedExpansions = Array.from(document.querySelectorAll('.expansion-checkbox:checked'))
            .map(checkbox => checkbox.getAttribute('data-expansion-name'));

        const selectedProducts = [...catalogProducts, ...stagedProducts, ...smosheyProducts];
        const missingCount = selectedProducts.filter(id => missingProductIds.includes(id)).length;
        const totalCount = selectedProducts.length;

        if (totalCount === 0 && selectedExpansions.length === 0) {
            showErrorMessage('Please select at least one product or expansion to push to updates.');
            return;
        }

        // If expansions are selected, fetch their products first
        if (selectedExpansions.length > 0) {
            // Show loading overlay
            document.getElementById('loadingOverlayText').textContent = "Loading Expansion Cards...";
            showLoadingOverlay();

            // Fetch products for all selected expansions
            fetchProductsForExpansions(selectedExpansions, selectedProducts);
            return;
        }

        // If no expansions selected, continue with the normal process
        continueWithProductSelection(selectedProducts);
    });

    // Function to continue with product selection after gathering all products
    async function continueWithProductSelection(selectedProducts) {
        // Make sure all products are integers
        const productIds = selectedProducts.map(id => typeof id === 'string' ? parseInt(id) : id);
        const missingCount = productIds.filter(id => missingProductIds.includes(id)).length;
        const totalCount = productIds.length;

        // Check if user has a limited subscription and has selected too many items
        console.log("Checking selection limit - Subscription type:", userSubscriptionType);
        console.log("Is limited subscription:", isLimitedSubscription);
        console.log("Total selected items:", totalCount);

        if (isLimitedSubscription && totalCount > MAX_SELECTIONS_FOR_LIMITED_USERS) {
            showErrorMessage(`Your current subscription (${userSubscriptionType}) only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time. <a href="/activating" class="alert-link">Upgrade to a Monthly, Annual, or Lifetime package</a> to select more items.`);
            return;
        }
        const selectedConditions = Array.from(document.querySelectorAll('.condition-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedConditions.length === 0) {
            showErrorMessage('Please select at least one condition.');
            return;
        }

        try {
            // No longer fetching unique languages, use hardcoded list
            // const languages = await fetchUniqueLanguages(productIds); // Removed this line

            // Show language selection modal with hardcoded languages
            showLanguageSelectionModal((selectedLanguages) => { // Removed 'languages' parameter
                if (selectedLanguages.length === 0) {
                    showErrorMessage('Please select at least one language.');
                    return;
                }

                // Show custom confirmation modal instead of confirm()
                showConfirmationModal(totalCount, missingCount, selectedConditions, selectedLanguages, (confirmed) => {
                    if (confirmed) {
                        showLoadingOverlay();
                        const gameName = document.getElementById('gameSelect').value;
                        processUpdate({
                            gameName: gameName,
                            selectedProductIds: productIds,
                            selectedConditions: selectedConditions,
                            selectedLanguages: selectedLanguages
                        });
                    }
                });
            });
        } catch (error) {
            showErrorMessage('Failed to fetch languages. Please try again.'); // This error should ideally not happen now
        }
    }

    // Function to show the custom confirmation modal
    function showConfirmationModal(totalCount, missingCount, conditions, languages, callback) {
        const modalBody = document.getElementById('confirmationModalBody');
        const confirmBtn = document.getElementById('confirmPushBtn');
        const confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));

        let languageText = languages.length > 0 ? languages.join(', ') : 'Default (EN)';

        modalBody.innerHTML = `
            <p>You are about to push <strong>${totalCount} product(s)</strong> to updates.</p>
            ${missingCount > 0 ? `<p class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i> Includes ${missingCount} missing item(s).</p>` : ''}
            <hr class="border-secondary">
            <p><strong>Conditions to include:</strong><br>${conditions.join(', ')}</p>
            <p><strong>Languages to include:</strong><br>${languageText}</p>
            <hr class="border-secondary">
            <p>Do you want to proceed?</p>
        `;

        // Remove previous listener to avoid duplicates
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new listener
        newConfirmBtn.addEventListener('click', () => {
            confirmationModal.hide();
            callback(true); // User confirmed
        }, { once: true }); // Ensure listener only fires once

        // Handle modal close/cancel
        const modalElement = document.getElementById('confirmationModal');
        modalElement.addEventListener('hidden.bs.modal', () => {
             // Check if the callback was already called by the confirm button
             // This simple check might not be foolproof in complex scenarios
             // but works here as we only care if confirm was clicked.
             if (!newConfirmBtn.dataset.confirmed) {
                 //callback(false); // User cancelled (removed this line as it caused issues if confirm was clicked quickly)
             }
             // Reset state if needed
             delete newConfirmBtn.dataset.confirmed;
        }, { once: true });

        // Mark confirm button state when clicked
         newConfirmBtn.addEventListener('click', () => {
             newConfirmBtn.dataset.confirmed = 'true';
         }, { once: true });


        confirmationModal.show();
    }


    function processUpdate(data) {
        fetch('/shopify/update-catalog/api/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                selectedProductIds: data.selectedProductIds,
                selectedConditions: data.selectedConditions,
                selectedLanguages: data.selectedLanguages
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            const loadingStatus = document.getElementById('loadingStatus');

            if (data.type === 'error') {
                loadingStatus.innerHTML += `
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                    </div>
                `;
            } else if (data.type === 'info') {
                // Show info message at the top
                const topNotificationArea = document.getElementById('topNotificationArea');
                topNotificationArea.innerHTML = `
                    <div class="alert alert-warning alert-dismissible fade show">
                        <i class="fas fa-info-circle me-2"></i>${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                topNotificationArea.style.display = 'block';

                // Also add to loading status for history
                loadingStatus.innerHTML += `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-info-circle me-2"></i>${data.message}
                    </div>
                `;
            } else {
                // Show success message at the top
                const fileCount = data.fileCount || 0;
                const topNotificationArea = document.getElementById('topNotificationArea');
                topNotificationArea.innerHTML = `
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>${data.message}
                        <button class="btn btn-sm btn-outline-success ms-3" onclick="document.getElementById('viewQueuedBtn').click()">
                            <i class="fas fa-eye me-1"></i>View Files
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                topNotificationArea.style.display = 'block';

                // Cooldown has been removed

                // Also add to loading status for history
                loadingStatus.innerHTML += `
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle me-2"></i>${data.message}
                    </div>
                `;

                // Automatically open the queued files modal if files were created
                if (fileCount > 0) {
                    setTimeout(() => {
                        document.getElementById('viewQueuedBtn').click();
                    }, 1000);
                }
            }

            if (data.missingProducts?.length > 0) {
                // Add missing products warning to loading status
                loadingStatus.innerHTML += `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>Missing products: ${data.missingProducts.join(', ')}
                    </div>
                `;
                missingProductIds = data.missingProducts.map(id => parseInt(id));
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Show error at the top
            const topNotificationArea = document.getElementById('topNotificationArea');
            topNotificationArea.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle me-2"></i>${error.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            topNotificationArea.style.display = 'block';

            // Also add to loading status
            document.getElementById('loadingStatus').innerHTML += `
                <div class="alert alert-danger mt-3">
                    <i class="fas fa-exclamation-circle me-2"></i>${error.message}
                </div>
            `;
        })
        .finally(() => {
            hideLoadingOverlay();
            setTimeout(() => {
                const gameName = document.getElementById('gameSelect').value;
                const expansionName = document.getElementById('expansionSelect').value;
                const filter = getSelectedFilter();
                const searchTerm = document.getElementById('searchInput').value.trim();
                fetchUnmatchedItems(gameName, expansionName, filter, null, null, 1, searchTerm);
            }, 3000);
        });
    }

    document.getElementById('resultExpansionFilter').addEventListener('change', function() {
        filterAndDisplayProducts(allProducts);
    });

    // Handle Select All Expansions button
    document.getElementById('selectAllExpansionsBtn').addEventListener('click', function() {
        const allChecked = this.innerHTML.includes('Deselect');
        const checkboxes = document.querySelectorAll('.expansion-checkbox:not(:disabled)');

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        this.innerHTML = allChecked ?
            '<i class="fas fa-check-square me-2"></i>Select All Expansions' :
            '<i class="fas fa-square me-2"></i>Deselect All Expansions';

        // If selecting all expansions, show a success message
        if (!allChecked) {
            showFeedbackMessage(`Selected ${checkboxes.length} expansions`, 'success');
        }
    });

    // Function to exclude an expansion
    function excludeExpansion(expansionName, groupId) {
        if (!groupId) {
            showErrorMessage('Cannot exclude expansion: Group ID not found');
            return;
        }

        fetch('/shopify/update-catalog/api/exclude-expansion', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                expansionName: expansionName,
                groupId: groupId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showFeedbackMessage(`Excluded expansion: ${expansionName}`, 'success');
                // Refresh the expansions view
                refreshExpansions();
            } else {
                showErrorMessage(data.message || 'Failed to exclude expansion');
            }
        })
        .catch(error => {
            console.error('Error excluding expansion:', error);
            showErrorMessage('Failed to exclude expansion');
        });
    }

    // Function to include an expansion
    function includeExpansion(expansionName, groupId) {
        if (!groupId) {
            showErrorMessage('Cannot include expansion: Group ID not found');
            return;
        }

        fetch('/shopify/update-catalog/api/include-expansion', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                expansionName: expansionName,
                groupId: groupId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showFeedbackMessage(`Included expansion: ${expansionName}`, 'success');
                // Refresh the expansions view
                refreshExpansions();
            } else {
                showErrorMessage(data.message || 'Failed to include expansion');
            }
        })
        .catch(error => {
            console.error('Error including expansion:', error);
            showErrorMessage('Failed to include expansion');
        });
    }

    // Function to refresh the expansions view
    function refreshExpansions() {
        const gameName = document.getElementById('gameSelect').value;
        const filter = getSelectedFilter();
        const searchTerm = document.getElementById('searchInput').value.trim();

        if (gameName) {
            fetchExpansionStats(gameName, filter, searchTerm);
        }
    }

    // Store all expansions for client-side filtering
    let allExpansionsData = [];

    // Function to filter expansions by name
    function filterExpansionsByName() {
        const filterValue = document.getElementById('expansionNameFilter').value.toLowerCase().trim();
        const expansionsTableBody = document.getElementById('expansionsTableBody');

        if (!allExpansionsData.length) {
            return; // No data to filter
        }

        // Filter the expansions based on the filter value
        const filteredExpansions = filterValue === '' ?
            allExpansionsData :
            allExpansionsData.filter(expansion =>
                expansion.expansionName.toLowerCase().includes(filterValue)
            );

        // Clear the table body
        expansionsTableBody.innerHTML = '';

        // Re-render the filtered expansions
        renderExpansions(filteredExpansions);

        // Update the count display
        const unmatchedCount = document.getElementById('unmatchedCount');
        if (filteredExpansions.length > 0) {
            const totalMissing = filteredExpansions.reduce((total, exp) => total + exp.count, 0);
            const filterText = filterValue ? ` (filtered from ${allExpansionsData.length})` : '';
            unmatchedCount.textContent = `${totalMissing} items across ${filteredExpansions.length} expansions${filterText}`;
        } else {
            const filterText = filterValue ? ' (no matches found)' : '';
            unmatchedCount.textContent = `0 items${filterText}`;
        }
    }

    // Function to render expansions (extracted from displayExpansionStats)
    function renderExpansions(expansions) {
        const expansionsTableBody = document.getElementById('expansionsTableBody');

        expansions.forEach(expansion => {
            const row = document.createElement('tr');
            // Format the release date if available
            const releaseDate = expansion.releaseDate ? new Date(expansion.releaseDate).toLocaleDateString() : 'Unknown';

            row.innerHTML = `
                <td class="align-middle border-secondary">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input expansion-checkbox" id="expansion-${expansion.expansionName}"
                               data-expansion-name="${expansion.expansionName}">
                        <label class="form-check-label" for="expansion-${expansion.expansionName}"></label>
                    </div>
                </td>
                <td class="align-middle border-secondary">${expansion.expansionName}</td>
                <td class="align-middle border-secondary">${releaseDate}</td>
                <td class="align-middle border-secondary">
                    <span class="badge bg-danger">${expansion.count}</span>
                </td>
                <td class="align-middle border-secondary">
                    <button class="btn btn-sm btn-outline-primary view-expansion-btn" data-expansion-name="${expansion.expansionName}">
                        <i class="fas fa-eye me-1"></i>View Cards
                    </button>
                    <button class="btn btn-sm btn-outline-success select-all-expansion-btn" data-expansion-name="${expansion.expansionName}">
                        <i class="fas fa-check-square me-1"></i>Select All
                    </button>
                    <button class="btn btn-sm btn-outline-warning exclude-expansion-btn"
                            data-expansion-name="${expansion.expansionName}" data-group-id="${expansion.groupId}">
                        <i class="fas fa-minus me-1"></i>Exclude
                    </button>
                </td>
            `;
            expansionsTableBody.appendChild(row);

            // Add event listeners for the buttons (same as before)
            addExpansionRowEventListeners(row);
        });
    }

    // Function to add event listeners to expansion row buttons
    function addExpansionRowEventListeners(row) {
        const viewBtn = row.querySelector('.view-expansion-btn');
        viewBtn.addEventListener('click', function() {
            const expansionName = this.getAttribute('data-expansion-name');
            const gameName = document.getElementById('gameSelect').value;

            // Switch to cards view
            document.getElementById('viewModeCards').click();

            // Set the expansion in the dropdown
            document.getElementById('expansionSelect').value = expansionName;

            // Fetch the cards for this expansion
            fetchUnmatchedItems(gameName, expansionName, getSelectedFilter(), null, null, 1, '');
        });

        const selectAllBtn = row.querySelector('.select-all-expansion-btn');
        selectAllBtn.addEventListener('click', function() {
            const expansionName = this.getAttribute('data-expansion-name');
            const gameName = document.getElementById('gameSelect').value;

            // Show loading overlay
            document.getElementById('loadingOverlayText').textContent = "Loading Expansion Cards...";
            showLoadingOverlay();

            // Fetch all cards for this expansion and select them
            fetchAndSelectExpansionItems(gameName, expansionName);
        });

        // Add event listener for exclude button
        const excludeBtn = row.querySelector('.exclude-expansion-btn');
        if (excludeBtn) {
            excludeBtn.addEventListener('click', function() {
                const expansionName = this.getAttribute('data-expansion-name');
                const groupId = this.getAttribute('data-group-id');
                excludeExpansion(expansionName, groupId);
            });
        }
    }

    // Add event listeners for the expansion name filter
    document.getElementById('expansionNameFilter').addEventListener('input', function() {
        filterExpansionsByName();
    });

    document.getElementById('clearExpansionFilter').addEventListener('click', function() {
        document.getElementById('expansionNameFilter').value = '';
        filterExpansionsByName();
    });
});
</script>
{% endblock %}
