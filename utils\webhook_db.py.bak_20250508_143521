"""
Simplified MongoDB Connection for Webhook Worker
"""
import os
from pymongo import MongoClient
import logging

logger = logging.getLogger(__name__)

class WebhookDB:
    _instance = None
    _client = None
    _db = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(WebhookDB, cls).__new__(cls)
            cls._instance._connect()
        return cls._instance

    def _connect(self):
        """Initialize MongoDB connection with optimized settings"""
        try:
            # Get MongoDB URI from environment or use default
            mongo_uri = os.getenv('MONGO_URI', '*******************************************************************')
            db_name = os.getenv('MONGO_DBNAME', 'test')

            # Add optimization parameters
            optimized_uri = (f"{mongo_uri}"
                           f"&maxPoolSize=100"
                           f"&minPoolSize=20"
                           f"&maxIdleTimeMS=60000"
                           f"&heartbeatFrequencyMS=5000"
                           f"&serverSelectionTimeoutMS=10000"
                           f"&localThresholdMS=15"
                           f"&retryWrites=true"
                           f"&retryReads=true"
                           f"&w=majority"
                           f"&readPreference=primaryPreferred"
                           f"&connectTimeoutMS=30000"
                           f"&socketTimeoutMS=60000")

            # Initialize PyMongo client
            self._client = MongoClient(optimized_uri)
            self._db = self._client[db_name]

            # Test connection
            self._db.command('ping')
            logger.info(f"MongoDB connection initialized for database: {db_name}")

        except Exception as e:
            logger.error(f"Failed to initialize MongoDB connection: {str(e)}")
            raise

    @property
    def db(self):
        """Get MongoDB database connection"""
        if not self._db:
            raise RuntimeError("Database not initialized")
        return self._db

# Create singleton instance
mongo = WebhookDB()
