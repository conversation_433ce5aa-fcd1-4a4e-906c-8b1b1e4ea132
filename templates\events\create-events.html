{% extends "base.html" %}
{% block title %}Create Events{% endblock %}
{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }
    
    /* Form styling */
    .form-card {
        border-radius: 12px; 
        background-color: rgba(25, 25, 39, 0.8); 
        border: 1px solid rgba(255, 255, 255, 0.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .form-section {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-bottom: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .form-section:last-child {
        border-bottom: none;
    }
    
    .form-control, .form-select {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
    }
    
    /* Dark mode dropdown styling */
    .form-select option {
        background-color: #1e293b;
        color: white;
    }
    
    /* For Firefox */
    .form-select:focus {
        color: white;
        background-color: rgba(255, 255, 255, 0.15);
    }
    
    /* For Chrome/Safari/Edge */
    @media screen and (-webkit-min-device-pixel-ratio:0) {
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
        }
    }
    
    /* Global styles for select dropdowns */
    select option,
    .dropdown-menu,
    .dropdown-item {
        background-color: #1e293b !important;
        color: white !important;
    }
    
    /* For webkit browsers */
    select::-webkit-scrollbar {
        width: 8px;
    }
    
    select::-webkit-scrollbar-track {
        background: #16213e;
    }
    
    select::-webkit-scrollbar-thumb {
        background-color: #6b21a8;
        border-radius: 20px;
    }
    
    /* Section styling with appropriate colors */
    .section-basic { border-left: 4px solid #2ecc71; }
    .section-datetime { border-left: 4px solid #3498db; }
    .section-capacity { border-left: 4px solid #e74c3c; }
    .section-details { border-left: 4px solid #9b59b6; }
    
    /* Form check styling */
    .form-check-input {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .form-check-input:checked {
        background-color: #2ecc71;
        border-color: #2ecc71;
    }
    
    /* Day selector styling */
    .day-selector {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .day-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .day-btn.selected {
        background-color: #3498db;
        border-color: #3498db;
    }
    
    /* Alert styling */
    .alert-info {
        background-color: rgba(52, 152, 219, 0.1);
        border-color: rgba(52, 152, 219, 0.2);
        color: rgba(255, 255, 255, 0.9);
    }
</style>

<div class="container mt-5">
    <div class="card form-card">
        <div class="card-header d-flex align-items-center" style="background-color: rgba(46, 204, 113, 0.2); border-bottom: 1px solid rgba(255, 255, 255, 0.05);">
            <div class="icon-wrapper me-3" style="width: 40px; height: 40px; border-radius: 10px; background-color: rgba(46, 204, 113, 0.3); display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-plus-circle" style="color: #2ecc71; font-size: 20px;"></i>
            </div>
            <h3 class="text-white mb-0">Create New Event</h3>
        </div>
        <div class="card-body text-white">
            <form id="createEventForm" method="post">
                <!-- Basic Info Section -->
                <div class="form-section section-basic p-3">
                    <h4 class="mb-3">Basic Information</h4>
                    
                    <div class="mb-3">
                        <label for="eventTitle" class="form-label">Event Title*</label>
                        <input type="text" class="form-control" id="eventTitle" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="eventDescription" class="form-label">Event Description*</label>
                        <textarea class="form-control" id="eventDescription" name="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="gameSelection" class="form-label">Game</label>
                            <select class="form-select" id="gameSelection" name="game">
                                <option value="" selected>-- Select Game --</option>
                                {% for game in games %}
                                <option value="{{ game }}">{{ game }}</option>
                                {% endfor %}
                                <option value="custom">Other (Custom Event)</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3" id="customGameContainer" style="display: none;">
                            <label for="customGame" class="form-label">Custom Game Name</label>
                            <input type="text" class="form-control" id="customGame" name="custom_game_name">
                        </div>
                    </div>
                </div>
                
                <!-- Date & Time Section -->
                <div class="form-section section-datetime p-3">
                    <h4 class="mb-3">Date & Time</h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="eventDate" class="form-label">Event Date*</label>
                            <input type="date" class="form-control" id="eventDate" name="event_date" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="eventTime" class="form-label">Start Time*</label>
                            <input type="time" class="form-control" id="eventTime" name="event_time" required>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="isRecurring" name="is_recurring">
                        <label class="form-check-label" for="isRecurring">This is a recurring event</label>
                    </div>
                    
                    <div id="recurrenceOptions" style="display: none;">
                        <div class="mb-3">
                            <label for="recurrenceType" class="form-label">Recurrence Pattern</label>
                            <select class="form-select" id="recurrenceType" name="recurrence_type">
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="custom">Custom Dates</option>
                            </select>
                        </div>
                        
                        <!-- Weekly options -->
                        <div id="weeklyOptions" class="recurr-option">
                            <div class="mb-3">
                                <label class="form-label">Repeat on</label>
                                <div class="day-selector">
                                    <div class="day-btn" data-day="0">M</div>
                                    <div class="day-btn" data-day="1">T</div>
                                    <div class="day-btn" data-day="2">W</div>
                                    <div class="day-btn" data-day="3">T</div>
                                    <div class="day-btn" data-day="4">F</div>
                                    <div class="day-btn" data-day="5">S</div>
                                    <div class="day-btn" data-day="6">S</div>
                                </div>
                                <!-- Hidden inputs for selected days -->
                                <div id="weeklyDaysInputs"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="weeklyWeeks" class="form-label">For how many weeks?</label>
                                <input type="number" class="form-control" id="weeklyWeeks" name="weekly_weeks" value="12" min="1" max="52">
                                <small class="form-text text-white-50">Maximum 52 weeks (1 year)</small>
                            </div>
                        </div>
                        
                        <!-- Monthly options -->
                        <div id="monthlyOptions" class="recurr-option" style="display: none;">
                            <div class="mb-3">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="monthly_option" id="dayOfMonthOption" value="day_of_month" checked>
                                    <label class="form-check-label" for="dayOfMonthOption">
                                        On day <span id="dayOfMonth">15</span> of every month
                                    </label>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="monthly_option" id="dayOfWeekOption" value="day_of_week">
                                    <label class="form-check-label" for="dayOfWeekOption">
                                        On the 
                                        <select name="week_number" class="form-select-sm mx-1 d-inline-block" style="width: auto; background-color: rgba(255, 255, 255, 0.1);">
                                            <option value="1">first</option>
                                            <option value="2">second</option>
                                            <option value="3">third</option>
                                            <option value="4">fourth</option>
                                            <option value="5">last</option>
                                        </select>
                                        <select name="day_of_week" class="form-select-sm mx-1 d-inline-block" style="width: auto; background-color: rgba(255, 255, 255, 0.1);">
                                            <option value="0">Monday</option>
                                            <option value="1">Tuesday</option>
                                            <option value="2">Wednesday</option>
                                            <option value="3">Thursday</option>
                                            <option value="4">Friday</option>
                                            <option value="5">Saturday</option>
                                            <option value="6">Sunday</option>
                                        </select>
                                        of every month
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="monthlyMonths" class="form-label">For how many months?</label>
                                <input type="number" class="form-control" id="monthlyMonths" name="monthly_months" value="6" min="1" max="24">
                                <small class="form-text text-white-50">Maximum 24 months (2 years)</small>
                            </div>
                        </div>
                        
                        <!-- Custom dates option -->
                        <div id="customDatesOptions" class="recurr-option" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">Add specific dates</label>
                                <div id="customDates">
                                    <div class="input-group mb-2">
                                        <input type="date" class="form-control" name="custom_dates[]">
                                        <button type="button" class="btn btn-outline-danger remove-date">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-light btn-sm mt-2" id="addDate">
                                    <i class="fas fa-plus"></i> Add Another Date
                                </button>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>Each recurring event will have its own separate ticket product in Shopify.</span>
                        </div>
                    </div>
                </div>
                
                <!-- Capacity & Tickets Section -->
                <div class="form-section section-capacity p-3">
                    <h4 class="mb-3">Capacity & Pricing</h4>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="maxAttendees" class="form-label">Maximum Attendees*</label>
                            <input type="number" class="form-control" id="maxAttendees" name="max_attendees" min="1" required>
                            <small class="form-text text-white-50">This will determine the number of tickets available for purchase.</small>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="ticketPrice" class="form-label">Ticket Price (£)*</label>
                            <input type="number" class="form-control" id="ticketPrice" name="ticket_price" min="0" step="0.01" required>
                            <small class="form-text text-white-50">Amount charged to customers</small>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="costPerAttendee" class="form-label">Cost Per Attendee (£)</label>
                            <input type="number" class="form-control" id="costPerAttendee" name="cost_per_attendee" min="0" step="0.01" value="0">
                            <small class="form-text text-white-50">Your cost for running this event</small>
                        </div>
                    </div>
                    
                    <div class="mb-3 p-3 rounded" style="background-color: rgba(46, 204, 113, 0.1);">
                        <h5>Profit Calculation</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <p class="mb-1">Profit per ticket:</p>
                                <h4 id="profitPerTicket">£0.00</h4>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-1">Total potential revenue:</p>
                                <h4 id="totalRevenue">£0.00</h4>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-1">Total potential profit:</p>
                                <h4 id="totalProfit">£0.00</h4>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Event Details Section -->
                <div class="form-section section-details p-3">
                    <h4 class="mb-3">Event Details</h4>
                    
                    <div class="mb-3">
                        <label for="eventType" class="form-label">Event Type*</label>
                        <select class="form-select" id="eventType" name="event_type" required>
                            <option value="" selected disabled>-- Select Event Type --</option>
                            <option value="tournament">Tournament</option>
                            <option value="pre-release">Pre-Release</option>
                            <option value="draft">Draft</option>
                            <option value="sealed">Sealed</option>
                            <option value="casual">Casual Play</option>
                            <option value="championship">Championship</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="prizeDetails" class="form-label">Prize Details</label>
                        <textarea class="form-control" id="prizeDetails" name="prize_details" rows="3" placeholder="Describe prizes, structure, qualification criteria, etc."></textarea>
                    </div>
                </div>
                
                <div class="text-end mt-4">
                    <a href="{{ url_for('events.events_dashboard') }}" class="btn btn-outline-light me-2">Cancel</a>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus-circle me-2"></i>Create Event
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <!-- Toasts will be added here dynamically -->
</div>

<script>
    // Toast notification function
    function showToast(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container');
        
        // Create toast element
        const toastEl = document.createElement('div');
        toastEl.className = `toast align-items-center text-white border-0`;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        // Set background color based on type
        let bgColor, icon;
        switch(type) {
            case 'success':
                bgColor = '#2ecc71';
                icon = 'fa-check-circle';
                break;
            case 'error':
                bgColor = '#e74c3c';
                icon = 'fa-exclamation-circle';
                break;
            case 'warning':
                bgColor = '#f1c40f';
                icon = 'fa-exclamation-triangle';
                break;
            default: // info
                bgColor = '#3498db';
                icon = 'fa-info-circle';
        }
        
        toastEl.style.backgroundColor = bgColor;
        
        // Create toast content
        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${icon} me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        // Add toast to container
        toastContainer.appendChild(toastEl);
        
        // Initialize and show toast
        const toast = new bootstrap.Toast(toastEl, {
            animation: true,
            autohide: true,
            delay: 5000
        });
        toast.show();
        
        // Remove toast after it's hidden
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    }
    document.addEventListener('DOMContentLoaded', function() {
        // Game selection handling
        const gameSelection = document.getElementById('gameSelection');
        const customGameContainer = document.getElementById('customGameContainer');
        
        gameSelection.addEventListener('change', function() {
            if (this.value === 'custom') {
                customGameContainer.style.display = 'block';
                document.getElementById('customGame').setAttribute('required', 'required');
            } else {
                customGameContainer.style.display = 'none';
                document.getElementById('customGame').removeAttribute('required');
            }
        });
        
        // Recurring event options
        const isRecurringCheckbox = document.getElementById('isRecurring');
        const recurrenceOptions = document.getElementById('recurrenceOptions');
        
        isRecurringCheckbox.addEventListener('change', function() {
            recurrenceOptions.style.display = this.checked ? 'block' : 'none';
        });
        
        // Recurrence type options
        const recurrenceType = document.getElementById('recurrenceType');
        const weeklyOptions = document.getElementById('weeklyOptions');
        const monthlyOptions = document.getElementById('monthlyOptions');
        const customDatesOptions = document.getElementById('customDatesOptions');
        
        recurrenceType.addEventListener('change', function() {
            // Hide all options first
            weeklyOptions.style.display = 'none';
            monthlyOptions.style.display = 'none';
            customDatesOptions.style.display = 'none';
            
            // Show the selected option
            if (this.value === 'weekly') {
                weeklyOptions.style.display = 'block';
            } else if (this.value === 'monthly') {
                monthlyOptions.style.display = 'block';
            } else if (this.value === 'custom') {
                customDatesOptions.style.display = 'block';
            }
        });
        
        // Day of month display
        const eventDate = document.getElementById('eventDate');
        const dayOfMonth = document.getElementById('dayOfMonth');
        
        eventDate.addEventListener('change', function() {
            if (this.value) {
                const date = new Date(this.value);
                dayOfMonth.textContent = date.getDate();
            }
        });
        
        // Weekly day selection
        const dayButtons = document.querySelectorAll('.day-btn');
        const weeklyDaysInputs = document.getElementById('weeklyDaysInputs');
        
        dayButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                this.classList.toggle('selected');
                updateWeeklyDaysInputs();
            });
        });
        
        function updateWeeklyDaysInputs() {
            // Clear previous inputs
            weeklyDaysInputs.innerHTML = '';
            
            // Add hidden input for each selected day
            document.querySelectorAll('.day-btn.selected').forEach(btn => {
                const day = btn.getAttribute('data-day');
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'weekly_days';
                input.value = day;
                weeklyDaysInputs.appendChild(input);
            });
        }
        
        // Custom dates
        const addDateBtn = document.getElementById('addDate');
        const customDatesContainer = document.getElementById('customDates');
        
        addDateBtn.addEventListener('click', function() {
            const newDateGroup = document.createElement('div');
            newDateGroup.className = 'input-group mb-2';
            newDateGroup.innerHTML = `
                <input type="date" class="form-control" name="custom_dates[]">
                <button type="button" class="btn btn-outline-danger remove-date">
                    <i class="fas fa-times"></i>
                </button>
            `;
            customDatesContainer.appendChild(newDateGroup);
            
            // Add event listener to the remove button
            newDateGroup.querySelector('.remove-date').addEventListener('click', function() {
                customDatesContainer.removeChild(newDateGroup);
            });
        });
        
        // Add event listeners to existing remove buttons
        document.querySelectorAll('.remove-date').forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.input-group').remove();
            });
        });
        
        // Profit calculation
        const ticketPrice = document.getElementById('ticketPrice');
        const costPerAttendee = document.getElementById('costPerAttendee');
        const maxAttendees = document.getElementById('maxAttendees');
        const profitPerTicket = document.getElementById('profitPerTicket');
        const totalRevenue = document.getElementById('totalRevenue');
        const totalProfit = document.getElementById('totalProfit');
        
        function updateProfitCalculation() {
            const price = parseFloat(ticketPrice.value) || 0;
            const cost = parseFloat(costPerAttendee.value) || 0;
            const attendees = parseInt(maxAttendees.value) || 0;
            
            const profit = price - cost;
            const revenue = price * attendees;
            const totalProfitValue = profit * attendees;
            
            profitPerTicket.textContent = `£${profit.toFixed(2)}`;
            totalRevenue.textContent = `£${revenue.toFixed(2)}`;
            totalProfit.textContent = `£${totalProfitValue.toFixed(2)}`;
            
            // Change color based on profit
            if (profit > 0) {
                profitPerTicket.style.color = '#2ecc71';
                totalProfit.style.color = '#2ecc71';
            } else if (profit < 0) {
                profitPerTicket.style.color = '#e74c3c';
                totalProfit.style.color = '#e74c3c';
            } else {
                profitPerTicket.style.color = '#ffffff';
                totalProfit.style.color = '#ffffff';
            }
        }
        
        ticketPrice.addEventListener('input', updateProfitCalculation);
        costPerAttendee.addEventListener('input', updateProfitCalculation);
        maxAttendees.addEventListener('input', updateProfitCalculation);
        
        // Form submission
        const form = document.getElementById('createEventForm');
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            // Submit form via AJAX
            const formData = new FormData(form);
            
            fetch('{{ url_for("events.create_events") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showToast(data.message, 'success');
                    // Redirect to events dashboard after a short delay
                    setTimeout(() => {
                        window.location.href = '{{ url_for("events.events_dashboard") }}';
                    }, 1000);
                } else {
                    // Show error message
                    showToast('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred while creating the event. Please try again.', 'error');
            });
        });
    });
</script>
{% endblock %}
