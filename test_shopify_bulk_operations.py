from config.config import Config
import os
import json
import time
import logging
from routes.reprice_logs_routes import upload_file_to_shopify, check_bulk_operation_status

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_jsonl_file(username="test_user"):
    """Create a test JSONL file with variant updates in the correct format"""
    # Create user-specific directories relative to the app
    downloads_dir = os.path.join(os.getcwd(), "Downloads")
    base_path = os.path.join(downloads_dir, username)
    pending_path = os.path.join(base_path, "pending")
    os.makedirs(pending_path, exist_ok=True)
    
    test_file_path = os.path.join(pending_path, "test_bulk_operation.jsonl")
    
    # Sample variant updates - only include price, not inventory
    variants = [
        {
            "input": {
                "id": "gid://shopify/ProductVariant/123456789",
                "price": "10.99"
            }
        },
        {
            "input": {
                "id": "gid://shopify/ProductVariant/987654321",
                "price": "15.99"
            }
        },
        {
            "input": {
                "id": "gid://shopify/ProductVariant/456789123",
                "price": "20.99"
            }
        }
    ]
    
    # Write to JSONL file
    with open(test_file_path, 'w') as f:
        for variant in variants:
            f.write(json.dumps(variant) + '\n')
    
    logger.info(f"Created test file at {test_file_path}")
    return test_file_path

def test_shopify_bulk_operations(api_key, store_name, username="test_user"):
    """Test the Shopify bulk operations integration"""
    # Create test file
    test_file_path = create_test_jsonl_file(username)
    
    # Upload file to Shopify
    logger.info("Uploading file to Shopify...")
    operation_id = upload_file_to_shopify(test_file_path, api_key, store_name)
    
    if not operation_id:
        logger.error("Failed to start bulk operation")
        return False
    
    logger.info(f"Bulk operation started with ID: {operation_id}")
    
    # Monitor operation status
    max_wait = 120  # seconds
    wait_time = 0
    check_interval = 5  # seconds
    
    while wait_time < max_wait:
        status = check_bulk_operation_status(operation_id, api_key, store_name)
        logger.info(f"Operation status: {status.get('status', 'UNKNOWN')}")
        
        if status.get('status') in ['COMPLETED', 'FAILED', 'CANCELED']:
            logger.info(f"Operation completed with status: {status.get('status')}")
            return status.get('status') == 'COMPLETED'
        
        time.sleep(check_interval)
        wait_time += check_interval
    
    logger.error("Timeout waiting for operation to complete")
    return False

if __name__ == "__main__":
    # Replace with your Shopify API key, store name, and username
    api_key = input("Enter your Shopify API key: ")
    store_name = input("Enter your Shopify store name (without .myshopify.com): ")
    username = input("Enter your username (or press Enter to use 'test_user'): ") or "test_user"
    
    result = test_shopify_bulk_operations(api_key, store_name, username)
    
    if result:
        print("Test successful! The Shopify bulk operations integration is working correctly.")
    else:
        print("Test failed. Please check the logs for more information.")

