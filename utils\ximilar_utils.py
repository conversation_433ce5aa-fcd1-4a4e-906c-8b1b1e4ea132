from config.config import Config
import requests

def analyze_image(image_url):
    headers = {
        'Authorization': f'Token {current_app.config["XIMILAR_API_KEY"]}',
        'Content-Type': 'application/json'
    }
    data = {
        "records": [
            {
                "_url": image_url
            }
        ]
    }
    response = requests.post(
        'https://api.ximilar.com/tagging/tagging/classify/',
        headers=headers,
        json=data
    )
    return response.json()

