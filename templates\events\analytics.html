{% extends "base.html" %}

{% block title %}Event Analytics{% endblock %}

{% block styles %}
{{ super() }}
{% include "events/event_styles.html" %}
<style>
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    .stat-card {
        text-align: center;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .stat-card .stat-value {
        font-size: 2.5rem;
        font-weight: bold;
    }
    .stat-card .stat-label {
        font-size: 1.2rem;
        color: #6c757d;
    }
    .bg-purple {
        background-color: #6f42c1;
        color: white;
    }
    .bg-teal {
        background-color: #20c997;
        color: white;
    }
    .bg-orange {
        background-color: #fd7e14;
        color: white;
    }
    .bg-pink {
        background-color: #e83e8c;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid event-page-content">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Event Analytics</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('event.events') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Events
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Summary Stats -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card bg-primary">
                                <div class="stat-value">{{ total_events }}</div>
                                <div class="stat-label">Total Events</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-success">
                                <div class="stat-value">{{ total_attendees }}</div>
                                <div class="stat-label">Total Attendees</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-info">
                                <div class="stat-value">${{ "%.2f"|format(total_revenue) }}</div>
                                <div class="stat-label">Total Revenue</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-warning">
                                <div class="stat-value">${{ "%.2f"|format(total_profit) }}</div>
                                <div class="stat-label">Total Profit</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <!-- Game Statistics -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Game Statistics</h4>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="gameChart"></canvas>
                                    </div>
                                    <div class="table-responsive mt-3">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Game</th>
                                                    <th>Events</th>
                                                    <th>Attendees</th>
                                                    <th>Revenue</th>
                                                    <th>Profit</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for game, stats in game_stats.items() %}
                                                <tr>
                                                    <td>{{ game }}</td>
                                                    <td>{{ stats.count }}</td>
                                                    <td>{{ stats.attendees }}</td>
                                                    <td>${{ "%.2f"|format(stats.revenue) }}</td>
                                                    <td>${{ "%.2f"|format(stats.profit) }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Event Type Statistics -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Event Type Statistics</h4>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="typeChart"></canvas>
                                    </div>
                                    <div class="table-responsive mt-3">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Type</th>
                                                    <th>Events</th>
                                                    <th>Attendees</th>
                                                    <th>Revenue</th>
                                                    <th>Profit</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for type, stats in type_stats.items() %}
                                                <tr>
                                                    <td>{{ type|title }}</td>
                                                    <td>{{ stats.count }}</td>
                                                    <td>{{ stats.attendees }}</td>
                                                    <td>${{ "%.2f"|format(stats.revenue) }}</td>
                                                    <td>${{ "%.2f"|format(stats.profit) }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <!-- Format Statistics -->
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Format Statistics</h4>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="formatChart"></canvas>
                                    </div>
                                    <div class="table-responsive mt-3">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Format</th>
                                                    <th>Events</th>
                                                    <th>Attendees</th>
                                                    <th>Revenue</th>
                                                    <th>Profit</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for format, stats in format_stats.items() %}
                                                <tr>
                                                    <td>{{ format }}</td>
                                                    <td>{{ stats.count }}</td>
                                                    <td>{{ stats.attendees }}</td>
                                                    <td>${{ "%.2f"|format(stats.revenue) }}</td>
                                                    <td>${{ "%.2f"|format(stats.profit) }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>
<script>
    $(document).ready(function() {
        // Game Chart
        var gameCtx = document.getElementById('gameChart').getContext('2d');
        var gameChart = new Chart(gameCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for game in game_stats %}
                    '{{ game }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: 'Attendees',
                    data: [
                        {% for game, stats in game_stats.items() %}
                        {{ stats.attendees }},
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true
                        }
                    }]
                }
            }
        });
        
        // Event Type Chart
        var typeCtx = document.getElementById('typeChart').getContext('2d');
        var typeChart = new Chart(typeCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for type in type_stats %}
                    '{{ type|title }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for type, stats in type_stats.items() %}
                        {{ stats.count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.5)',
                        'rgba(54, 162, 235, 0.5)',
                        'rgba(255, 206, 86, 0.5)',
                        'rgba(75, 192, 192, 0.5)',
                        'rgba(153, 102, 255, 0.5)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'right'
                }
            }
        });
        
        // Format Chart
        var formatCtx = document.getElementById('formatChart').getContext('2d');
        var formatChart = new Chart(formatCtx, {
            type: 'horizontalBar',
            data: {
                labels: [
                    {% for format in format_stats %}
                    '{{ format }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: 'Events',
                    data: [
                        {% for format, stats in format_stats.items() %}
                        {{ stats.count }},
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(75, 192, 192, 0.5)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    xAxes: [{
                        ticks: {
                            beginAtZero: true
                        }
                    }]
                }
            }
        });
    });
</script>
{% endblock %}
