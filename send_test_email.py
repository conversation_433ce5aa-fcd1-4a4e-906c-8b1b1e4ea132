from config.config import Config
import requests
import time
from typing import List

def get_email_list() -> List[str]:
    return [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]

from config import Config

def send_email(email: str) -> dict:
    MAILGUN_API_KEY = Config.MAILGUN_API_KEY
    MAILGUN_DOMAIN = Config.MAILGUN_DOMAIN

    if not MAILGUN_API_KEY or not MAILGUN_DOMAIN:
        return {
            "email": email,
            "status": "error",
            "response": "Mailgun API key or domain not set in config"
        }
    
    try:
        response = requests.post(
            f"https://api.eu.mailgun.net/v3/{MAILGUN_DOMAIN}/messages",
            auth=("api", MAILGUN_API_KEY),
            timeout=10,
            data={
                "from": f"TCGSync <noreply@{MAILGUN_DOMAIN}>",
                "to": [email],
                "subject": "⏰ Last Chance: 50% Off TCGSync + Free Guided Setup Ends at Midnight",
                "html": """
                <h2>Time is Running Out! 🎯</h2>

                <p>Your exclusive 50% off TCGSync offer expires at midnight tonight. Don't miss out on transforming your card business with:</p>

                <ul>
                    <li>✅ Instant Card Recognition</li>
                    <li>✅ Automated Pricing</li>
                    <li>✅ Comprehensive Catalogs</li>
                    <li>✅ Zero Downtime Reliability</li>
                </ul>

                <h3>Two Powerful Benefits:</h3>

                <h4>1. Quick Implementation</h4>
                <p>Get up and running in just 2 hours - start optimizing your business today.</p>

                <h4>2. FREE Personal Onboarding Call</h4>
                <p>Schedule your complimentary Zoom onboarding session within 72 hours - we'll guide you through every feature to ensure your success.</p>

                <p style="font-size: 18px; margin: 20px 0;">
                    <a href="https://login.tcgsync.com/login" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                        ➡️ Login now to claim your 50% discount
                    </a>
                </p>

                <h3>Why TCGSync?</h3>
                <ul>
                    <li>• Seamless integration</li>
                    <li>• Reliable performance with zero downtime</li>
                    <li>• Complete catalog access</li>
                    <li>• Smart autopricing technology</li>
                    <li>• Advanced card recognition</li>
                    <li>• Expert guidance included</li>
                </ul>

                <p><strong>Don't let this opportunity slip away</strong> - upgrade your business today at half the price and get free personalized setup support!</p>

                <p style="margin-top: 30px;">
                    <a href="https://login.tcgsync.com/login" style="background-color: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;">
                        Login Now
                    </a>
                </p>
                """
            }
        )
        return {
            "email": email,
            "status": response.status_code,
            "response": response.text
        }
    except Exception as e:
        return {
            "email": email,
            "status": "error",
            "response": str(e)
        }

def send_bulk_emails():
    emails = get_email_list()
    total = len(emails)
    successful = 0
    failed = 0
    results = []

    print(f"Starting to send {total} emails...")

    for i, email in enumerate(emails, 1):
        print(f"\nSending to {email} ({i}/{total})...")
        result = send_email(email)
        results.append(result)
        
        if result["status"] == 200:
            successful += 1
            print(f"✓ Success: {email}")
        else:
            failed += 1
            print(f"✗ Failed: {email} - {result['response']}")
        
        # Rate limiting: 1 email per minute
        if i < total:  # Don't sleep after the last email
            print(f"Rate limiting: Waiting 60 seconds before sending next email...")
            time.sleep(60)  # Mailgun's free tier has a rate limit

    print(f"\nEmail Campaign Complete!")
    print(f"Total: {total}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")

if __name__ == "__main__":
    send_bulk_emails()

