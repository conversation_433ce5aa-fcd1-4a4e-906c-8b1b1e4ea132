<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ username }}'s Events</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css" rel="stylesheet">
    <style>
        body {
            background-color: white;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        /* Calendar styling */
        .calendar-container {
            margin-top: 20px;
        }
        
        /* Fix for squashed calendar */
        .fc-view-harness {
            height: 600px !important;
        }
        
        .fc-scrollgrid-sync-table {
            width: 100% !important;
            height: 100% !important;
        }
        
        .fc-daygrid-body {
            width: 100% !important;
        }
        
        .fc-daygrid-body-balanced {
            width: 100% !important;
        }
        
        .fc-col-header, .fc-daygrid-body, .fc-scrollgrid-sync-table {
            width: 100% !important;
        }
        
        .fc-daygrid-day {
            min-height: 80px;
        }
        
        /* Game colors */
        .game-magic {
            background-color: #e74c3c !important;
            border-color: #e74c3c !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-yugioh {
            background-color: #9b59b6 !important;
            border-color: #9b59b6 !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-pokemon {
            background-color: #f1c40f !important;
            border-color: #f1c40f !important;
            color: #2c3e50 !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-starwars {
            background-color: #3498db !important;
            border-color: #3498db !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-lorcana {
            background-color: #2ecc71 !important;
            border-color: #2ecc71 !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-digimon {
            background-color: #e67e22 !important;
            border-color: #e67e22 !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-dragonball {
            background-color: #f39c12 !important;
            border-color: #f39c12 !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-sorcery {
            background-color: #8e44ad !important;
            border-color: #8e44ad !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-metazoo {
            background-color: #16a085 !important;
            border-color: #16a085 !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        .game-custom {
            background-color: #95a5a6 !important;
            border-color: #95a5a6 !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        /* Event styling */
        .fc-event {
            padding: 3px 5px !important;
            margin-bottom: 2px !important;
            border: none !important;
        }
        
        .fc-event-title {
            font-weight: 500 !important;
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: clip !important;
            line-height: 1.3 !important;
        }
        
        .fc-event-time {
            font-weight: 600 !important;
            font-size: 0.85em !important;
        }
        
        /* Tooltip styling */
        .event-tooltip {
            position: absolute;
            z-index: 10000;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            padding: 10px;
            max-width: 300px;
            font-size: 14px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .event-tooltip.visible {
            opacity: 1;
        }
        
        .event-tooltip-title {
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 16px;
        }
        
        .event-tooltip-game {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            margin-bottom: 5px;
        }
        
        .event-tooltip-time,
        .event-tooltip-price,
        .event-tooltip-spots {
            margin-bottom: 3px;
            font-size: 13px;
        }
        
        .event-tooltip-spots {
            font-weight: 500;
        }
        
        /* Legend styling */
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 5px;
        }
        
        /* Custom toolbar elements */
        .fc-toolbar-chunk {
            display: flex;
            align-items: center;
        }
        
        #gameFilterContainer {
            margin-left: 10px;
            width: 150px;
        }
        
        #legendContainer {
            margin-right: 10px;
        }
        
        .fc-legend-button {
            margin-right: 10px !important;
        }
    </style>
</head>
<body>
    <!-- Game filter dropdown (will be moved to calendar toolbar) -->
    <div id="gameFilterContainer" style="display: none;">
        <select id="gameFilter" class="form-select form-select-sm">
            <option value="all">All Games</option>
            {% if 'Magic The Gathering' in games_with_events %}
            <option value="game-magic">Magic The Gathering</option>
            {% endif %}
            {% if 'YuGiOh' in games_with_events %}
            <option value="game-yugioh">YuGiOh</option>
            {% endif %}
            {% if 'Pokemon' in games_with_events %}
            <option value="game-pokemon">Pokemon</option>
            {% endif %}
            {% if 'Star Wars' in games_with_events %}
            <option value="game-starwars">Star Wars</option>
            {% endif %}
            {% if 'Lorcana' in games_with_events %}
            <option value="game-lorcana">Lorcana</option>
            {% endif %}
            {% if 'Digimon' in games_with_events %}
            <option value="game-digimon">Digimon</option>
            {% endif %}
            {% if 'Dragonball' in games_with_events %}
            <option value="game-dragonball">Dragonball</option>
            {% endif %}
            {% if 'Sorcery' in games_with_events %}
            <option value="game-sorcery">Sorcery</option>
            {% endif %}
            {% if 'Metazoo' in games_with_events %}
            <option value="game-metazoo">Metazoo</option>
            {% endif %}
            {% if has_custom_events %}
            <option value="game-custom">Custom Events</option>
            {% endif %}
        </select>
    </div>
    
    <!-- Legend for game colors (will be moved to calendar toolbar) -->
    <div id="legendContainer" style="display: none;">
        <div class="legend" style="margin: 0; gap: 5px;">
            {% if 'Magic The Gathering' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-magic"></div>
                <span>MTG</span>
            </div>
            {% endif %}
            {% if 'YuGiOh' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-yugioh"></div>
                <span>YuGiOh</span>
            </div>
            {% endif %}
            {% if 'Pokemon' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-pokemon"></div>
                <span>Pokemon</span>
            </div>
            {% endif %}
            {% if 'Star Wars' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-starwars"></div>
                <span>Star Wars</span>
            </div>
            {% endif %}
            {% if 'Lorcana' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-lorcana"></div>
                <span>Lorcana</span>
            </div>
            {% endif %}
            {% if 'Digimon' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-digimon"></div>
                <span>Digimon</span>
            </div>
            {% endif %}
            {% if 'Dragonball' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-dragonball"></div>
                <span>Dragonball</span>
            </div>
            {% endif %}
            {% if 'Sorcery' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-sorcery"></div>
                <span>Sorcery</span>
            </div>
            {% endif %}
            {% if 'Metazoo' in games_with_events %}
            <div class="legend-item">
                <div class="legend-color game-metazoo"></div>
                <span>Metazoo</span>
            </div>
            {% endif %}
            {% if has_custom_events %}
            <div class="legend-item">
                <div class="legend-color game-custom"></div>
                <span>Custom</span>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Calendar View -->
    <div class="calendar-container">
        <div id="calendar"></div>
    </div>
    
    <!-- Event Details Modal -->
    <div class="modal fade" id="eventDetailsModal" tabindex="-1" aria-labelledby="eventDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border-radius: 12px; border: none; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="border-bottom: 1px solid rgba(0, 0, 0, 0.1); padding: 15px 20px;">
                    <h5 class="modal-title" id="eventDetailsModalLabel" style="font-weight: 600;"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 20px;">
                    <div id="eventDetailsContent">
                        <!-- Event details will be populated here by JavaScript -->
                    </div>
                    
                    <form id="attendeeForm" style="margin-top: 20px;">
                        <input type="hidden" id="eventId" name="eventId">
                        <input type="hidden" id="shopifyUrl" name="shopifyUrl">
                        
                        <div class="mb-3">
                            <label for="fullName" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="fullName" name="fullName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes (optional)</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="futureEvents" name="futureEvents" checked>
                            <label class="form-check-label" for="futureEvents">Send me details about future events</label>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="submitAttendeeForm">
                                Continue to Purchase Ticket
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS (for modal) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
    
    <!-- Tooltip container -->
    <div class="event-tooltip" id="eventTooltip">
        <div class="event-tooltip-title"></div>
        <div class="event-tooltip-game"></div>
        <div class="event-tooltip-time"></div>
        <div class="event-tooltip-price"></div>
        <div class="event-tooltip-spots"></div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize FullCalendar
            const calendarEl = document.getElementById('calendar');
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today gameFilter',
                    center: 'title',
                    right: 'legend dayGridMonth,timeGridWeek,listMonth'
                },
                customButtons: {
                    gameFilter: {
                        text: 'Filter',
                        click: function() {
                            // This is just a placeholder, the actual filter element will be moved here
                        }
                    },
                    legend: {
                        text: 'Legend',
                        click: function() {
                            // This is just a placeholder, the actual legend element will be moved here
                        }
                    }
                },
                height: 'auto',
                events: {{ events_json|safe }},
                eventClick: function(info) {
                    showEventDetails(info.event);
                },
                eventMouseEnter: function(info) {
                    showTooltip(info.event, info.el);
                },
                eventMouseLeave: function() {
                    hideTooltip();
                },
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                displayEventEnd: true
            });
            calendar.render();
            
            // Move the game filter and legend to the toolbar
            setTimeout(function() {
                // Move game filter to toolbar
                const gameFilterButton = document.querySelector('.fc-gameFilter-button');
                if (gameFilterButton) {
                    const gameFilterContainer = document.getElementById('gameFilterContainer');
                    gameFilterContainer.style.display = 'block';
                    gameFilterButton.parentNode.replaceChild(gameFilterContainer, gameFilterButton);
                }
                
                // Move legend to toolbar
                const legendButton = document.querySelector('.fc-legend-button');
                if (legendButton) {
                    const legendContainer = document.getElementById('legendContainer');
                    legendContainer.style.display = 'block';
                    legendButton.parentNode.replaceChild(legendContainer, legendButton);
                }
            }, 100);
            
            // Tooltip functions
            const tooltip = document.getElementById('eventTooltip');
            
            function showTooltip(event, element) {
                // Set tooltip content
                tooltip.querySelector('.event-tooltip-title').textContent = event.extendedProps.originalTitle;
                
                const gameElement = tooltip.querySelector('.event-tooltip-game');
                gameElement.textContent = event.extendedProps.game;
                
                // Set game badge color
                gameElement.className = 'event-tooltip-game';
                if (event.classNames[0]) {
                    const gameClass = event.classNames[0];
                    const bgColor = window.getComputedStyle(document.querySelector('.' + gameClass)).backgroundColor;
                    gameElement.style.backgroundColor = bgColor;
                }
                
                tooltip.querySelector('.event-tooltip-time').textContent = `Time: ${event.extendedProps.time}`;
                tooltip.querySelector('.event-tooltip-price').textContent = `Price: ${event.extendedProps.formattedPrice}`;
                tooltip.querySelector('.event-tooltip-spots').textContent = `Availability: ${event.extendedProps.availability}`;
                
                // Position tooltip near the event
                const rect = element.getBoundingClientRect();
                tooltip.style.top = (rect.top + window.scrollY - tooltip.offsetHeight - 10) + 'px';
                tooltip.style.left = (rect.left + window.scrollX + (rect.width / 2) - (tooltip.offsetWidth / 2)) + 'px';
                
                // Show tooltip
                tooltip.classList.add('visible');
            }
            
            function hideTooltip() {
                tooltip.classList.remove('visible');
            }
            
            // Show event details function
            function showEventDetails(event) {
                // Get modal elements
                const modal = new bootstrap.Modal(document.getElementById('eventDetailsModal'));
                const modalTitle = document.getElementById('eventDetailsModalLabel');
                const modalContent = document.getElementById('eventDetailsContent');
                const eventIdInput = document.getElementById('eventId');
                const shopifyUrlInput = document.getElementById('shopifyUrl');
                
                // Set modal title
                modalTitle.textContent = event.title;
                
                // Set event ID and Shopify URL in form
                eventIdInput.value = event.id;
                shopifyUrlInput.value = event.extendedProps.shopifyProductUrl || '';
                
                // Format price
                const formattedPrice = new Intl.NumberFormat('en-GB', { 
                    style: 'currency', 
                    currency: 'GBP' 
                }).format(event.extendedProps.ticketPrice);
                
                // Set modal content
                modalContent.innerHTML = `
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">
                                ${event.extendedProps.eventType}
                            </span>
                            <span class="badge bg-info">
                                ${event.extendedProps.game}
                            </span>
                        </div>
                        <p><strong>Date & Time:</strong> ${new Date(event.start).toLocaleString()}</p>
                        <p><strong>Price:</strong> ${formattedPrice}</p>
                        <p><strong>Availability:</strong> ${event.extendedProps.currentAttendees}/${event.extendedProps.maxAttendees} spots filled</p>
                        <p><strong>Description:</strong> ${event.extendedProps.description || 'No description available.'}</p>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="alert alert-info mb-0 flex-grow-1 me-2">
                            <i class="fas fa-info-circle me-2"></i>
                            Please provide your details below to continue to ticket purchase.
                        </div>
                        <button type="button" class="btn btn-outline-primary" id="addToCalendarBtn">
                            <i class="fas fa-calendar-plus me-1"></i> Add to Calendar
                        </button>
                    </div>
                `;
                
                // Show modal
                modal.show();
            }
            
            // Add event listener for the "Add to Calendar" button
            document.addEventListener('click', function(e) {
                if (e.target && (e.target.id === 'addToCalendarBtn' || e.target.parentElement.id === 'addToCalendarBtn')) {
                    // Get the event details from the form
                    const eventId = document.getElementById('eventId').value;
                    const eventTitle = document.getElementById('eventDetailsModalLabel').textContent;
                    const eventDetails = document.getElementById('eventDetailsContent');
                    
                    // Extract event information
                    const eventDateText = eventDetails.querySelector('p:nth-child(2)').textContent;
                    const eventDate = eventDateText.replace('Date & Time:', '').trim();
                    const eventDescription = eventDetails.querySelector('p:nth-child(5)').textContent.replace('Description:', '').trim();
                    const eventGame = eventDetails.querySelector('.badge.bg-info').textContent.trim();
                    const eventType = eventDetails.querySelector('.badge.bg-secondary').textContent.trim();
                    
                    // Create start and end date objects
                    const startDate = new Date(eventDate);
                    const endDate = new Date(startDate.getTime() + (3 * 60 * 60 * 1000)); // Default to 3 hours duration
                    
                    // Format dates for iCalendar
                    const formatDate = (date) => {
                        return date.toISOString().replace(/-|:|\.\d+/g, '');
                    };
                    
                    // Create iCalendar content
                    const icsContent = 
                        'BEGIN:VCALENDAR\n' +
                        'VERSION:2.0\n' +
                        'CALSCALE:GREGORIAN\n' +
                        'BEGIN:VEVENT\n' +
                        'SUMMARY:' + eventTitle + '\n' +
                        'DESCRIPTION:' + eventGame + ' ' + eventType + ' - ' + eventDescription + '\n' +
                        'DTSTART:' + formatDate(startDate) + '\n' +
                        'DTEND:' + formatDate(endDate) + '\n' +
                        'LOCATION:{{ username }}\'s Store\n' +
                        'STATUS:CONFIRMED\n' +
                        'SEQUENCE:0\n' +
                        'END:VEVENT\n' +
                        'END:VCALENDAR';
                    
                    // Create a blob with the iCalendar content
                    const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
                    
                    // Create a download link
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = eventTitle.replace(/\s+/g, '_') + '.ics';
                    
                    // Trigger the download
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            });
            
            // Game filter functionality
            document.getElementById('gameFilter').addEventListener('change', function() {
                const selectedGame = this.value;
                
                if (selectedGame === 'all') {
                    // Show all events
                    calendar.getEvents().forEach(event => {
                        event.setProp('display', 'auto');
                    });
                } else {
                    // Filter events by game class
                    calendar.getEvents().forEach(event => {
                        if (event.classNames.includes(selectedGame)) {
                            event.setProp('display', 'auto');
                        } else {
                            event.setProp('display', 'none');
                        }
                    });
                }
            });
            
            // Handle form submission
            document.getElementById('attendeeForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form data
                const eventId = document.getElementById('eventId').value;
                const shopifyUrl = document.getElementById('shopifyUrl').value;
                const fullName = document.getElementById('fullName').value;
                const email = document.getElementById('email').value;
                const notes = document.getElementById('notes').value;
                const futureEvents = document.getElementById('futureEvents').checked;
                
                // Create attendee data
                const attendeeData = {
                    event_id: eventId,
                    full_name: fullName,
                    email: email,
                    notes: notes,
                    future_events: futureEvents
                };
                
                // Send data to server
                fetch('/api/public/register-attendee', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(attendeeData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Redirect to Shopify product page
                        if (shopifyUrl) {
                            window.location.href = shopifyUrl;
                        } else {
                            alert('Registration successful! However, no ticket purchase link is available.');
                        }
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('There was an error processing your registration. Please try again.');
                });
            });
        });
    </script>
</body>
</html>
