{% extends "base.html" %}

{% block title %}TCG Land{% endblock %}

{% block content %}
<style>
    .table {
        color: white;
        background-color: black;
    }
    .table th, .table td {
        border-color: white;
    }
    #loadingIndicator, #aiResponse {
        display: none;
        color: white;
    }
</style>

<div class="container mt-5">
    <h1>TCG Land</h1>
    <p>Welcome to TCG Land. Build your deck, get price estimates, or use our AI to suggest a deck for you!</p>
    
    {% if cards %}
    <table class="table mt-4">
        <thead>
            <tr>
                <th>Total Market Price</th>
                <th>Total Lowest Price</th>
                <th>Total Saved</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>${{ total_market_price }}</td>
                <td>${{ total_lowest_price }}</td>
                <td style="color: green;">${{ total_saved }}</td>
            </tr>
        </tbody>
    </table>
    {% endif %}
    
    <h2 class="mt-4">Suggest a Deck</h2>
    <form id="deckSuggestionForm" class="mb-4">
        <div class="form-group">
            <label for="deckType">Deck Type:</label>
            <input type="text" class="form-control" id="deckType" name="deckType" placeholder="e.g., standard, modern, commander, pauper">
        </div>
        <div class="form-group">
            <label for="budget">Budget ($):</label>
            <input type="number" class="form-control" id="budget" name="budget" value="100" min="1" step="0.01">
        </div>
        <button type="submit" class="btn btn-secondary mt-2">Suggest Deck</button>
    </form>
    <div id="loadingIndicator">Generating deck suggestion... Please wait.</div>
    <div id="aiResponse" class="mt-3 mb-3"></div>

    <form id="cardForm" method="POST" action="{{ url_for('tcgland.process_cards') }}">
        <div class="form-group">
            <label for="cardList">Card List:</label>
            <textarea class="form-control" id="cardList" name="cardList" rows="10" placeholder="Your card list will appear here after suggestion, or you can paste your own list.
Format: Quantity Card Name Number
Example:
4 Lightning Bolt 146
3 Mountain 383"></textarea>
        </div>
        {% for abbreviation in abbreviations %}
            <input type="hidden" name="abbreviations" value="{{ abbreviation }}">
        {% endfor %}
        <button type="submit" class="btn btn-primary mt-2">Process Cards</button>
    </form>

    {% if cards %}
    <table class="table mt-4">
        <thead>
            <tr>
                <th>Quantity</th>
                <th>Name</th>
                <th>Number</th>
                <th>Product ID</th>
                <th>High Price</th>
                <th>Market Price</th>
                <th>Direct Low Price</th>
                <th>Sellers Found</th>
                <th>Lowest Price</th>
            </tr>
        </thead>
        <tbody>
            {% for card in cards %}
            <tr>
                <td>{{ card.qty }}</td>
                <td>{{ card.name }}</td>
                <td>{{ card.number }}</td>
                <td>{{ card.productId }}</td>
                <td>{{ card.highPrice }}</td>
                <td>{{ card.marketPrice }}</td>
                <td>{{ card.directLowPrice }}</td>
                <td>{{ card.sellersFound }}</td>
                <td>{{ card.lowestPrice }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}
</div>

<script>
document.getElementById('deckSuggestionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const budget = document.getElementById('budget').value;
    const deckType = document.getElementById('deckType').value;
    const loadingIndicator = document.getElementById('loadingIndicator');
    const aiResponse = document.getElementById('aiResponse');
    const cardList = document.getElementById('cardList');
    
    loadingIndicator.style.display = 'block';
    aiResponse.style.display = 'none';
    cardList.value = '';  // Clear existing content
    
    fetch('/tcgland/suggest_deck', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({budget: parseFloat(budget), deckType: deckType}),
    })
    .then(response => response.json())
    .then(data => {
        loadingIndicator.style.display = 'none';
        if (data.error) {
            throw new Error(data.error);
        }
        aiResponse.textContent = `Here is a $${budget} budget ${deckType} deck list:`;
        aiResponse.style.display = 'block';
        cardList.value = data.deck.join('\n');
    })
    .catch((error) => {
        loadingIndicator.style.display = 'none';
        console.error('Error:', error);
        alert('Error suggesting deck: ' + error.message);
    });
});
</script>
{% endblock %}
