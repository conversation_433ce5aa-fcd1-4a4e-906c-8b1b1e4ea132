import requests
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

def create_session():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=0.1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["GET"]
    )
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=100,
        pool_maxsize=100,
        pool_block=False
    )
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    return session

API_BASE_URL = "https://api.cardtrader.com/api/v2"
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

def main():
    session = create_session()
    url = f"{API_BASE_URL}/games"
    response = session.get(url, headers=HEADERS, timeout=10)
    games = response.json()
    
    print(f"Games response type: {type(games)}")
    print(f"Games response content: {games}")
    
    if isinstance(games, list):
        for i, game in enumerate(games):
            print(f"Game {i} type: {type(game)}")
            print(f"Game {i} content: {game}")
    
if __name__ == "__main__":
    main()
