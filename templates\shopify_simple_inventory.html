{% extends "base.html" %}

{% block title %}My Products{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <h1 class="mb-4">My Products</h1>

    <!-- Top Navigation Bar -->
    <div class="navbar mb-3" style="background-color: #1a3c40;">
        <div class="container-fluid d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center w-100">
                <div class="flex-grow-1 me-2">
                    <label class="text-white me-1 w-100" for="productTitle">Search:</label>
                    <input type="text" class="form-control form-control-sm w-100" id="productTitle" placeholder="Enter product title">
                </div>
                <div class="me-2" style="flex: 0 0 200px;">
                    <label class="text-white me-1 w-100" for="vendorSelect">Vendor:</label>
                    <select class="form-select form-select-sm w-100" id="vendorSelect">
                        <option value="">Select vendors</option>
                    </select>
                </div>
                <div class="me-2" style="flex: 0 0 200px;">
                    <label class="text-white me-1 w-100" for="productTypeSelect">Product type:</label>
                    <select class="form-select form-select-sm w-100" id="productTypeSelect">
                        <option value="">Select product types</option>
                    </select>
                </div>
                <div style="flex: 0 0 200px;">
                    <label class="text-white me-1 w-100" for="expansionNameSelect">Expansion:</label>
                    <select class="form-select form-select-sm w-100" id="expansionNameSelect">
                        <option value="">Select expansion</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Filter Form -->
    <div class="filter-container">
        <!-- Secondary Filters Section -->
        <div class="filter-section mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="filter-label">Tagged with:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-tags"></i></span>
                        <select class="form-select" id="taggedWith">
                            <option value="">Select...</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="filter-label">Product barcode:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                        <input type="text" class="form-control" id="productBarcode" placeholder="Enter barcode">
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="filter-label">Product sku:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-box"></i></span>
                        <input type="text" class="form-control" id="productSku" placeholder="Enter SKU">
                    </div>
                </div>
            </div>
        </div>

        <!-- Price and Quantity Filters Section -->
        <div class="filter-section mb-4">
            <div class="row g-3">
                <div class="col-md-2">
                    <label class="filter-label">Price greater than:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-dollar-sign"></i></span>
                        <input type="number" class="form-control" id="priceGreaterThan" step="0.01">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="filter-label">Price less than:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-dollar-sign"></i></span>
                        <input type="number" class="form-control" id="priceLessThan" step="0.01">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="filter-label">Overall qty greater than:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-boxes"></i></span>
                        <input type="number" class="form-control" id="overallQtyGreaterThan">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="filter-label">Overall qty less than:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-boxes"></i></span>
                        <input type="number" class="form-control" id="overallQtyLessThan">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="filter-label">Variant qty greater than:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-box"></i></span>
                        <input type="number" class="form-control" id="variantQtyGreaterThan">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="filter-label">Variant qty less than:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-box"></i></span>
                        <input type="number" class="form-control" id="variantQtyLessThan">
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Filters Section -->
        <div class="filter-section mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="filter-label">Price override type:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-percentage"></i></span>
                        <select class="form-select" id="priceOverrideType">
                            <option value="">Select...</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <label class="filter-label">My custom filter</label>
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Enter custom filter">
                        <button class="btn btn-outline-primary" type="button">
                            <i class="fas fa-save me-1"></i>
                            Save filter
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Section -->
        <div class="filter-section">
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="filter-label">Action when updating inventory level:</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-edit"></i></span>
                        <select class="form-select" id="inventoryAction">
                            <option value="">Set inventory level</option>
                            <option value="adjust">Adjust inventory level</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6 d-flex align-items-end justify-content-end">
                    <button class="btn btn-primary btn-lg apply-filters-btn">
                        <i class="fas fa-filter me-2"></i>
                        APPLY FILTERS
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div id="results" class="mt-4">
        <!-- Results will be dynamically inserted here -->
    </div>
</div>

<style>
    /* Color Scheme */
    :root {
        --primary-color: #1a3c40;
        --secondary-color: #2c5d63;
        --accent-color: #bbdfc8;
        --light-accent: #e8f3f1;
    }

    /* Navbar Styling */
    .navbar {
        padding: 0.75rem 1rem;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .btn-group .btn {
        margin-right: 2px;
        border-radius: 4px;
    }

    /* Filter Sections */
    .filter-container {
        background-color: var(--light-accent);
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .filter-section {
        background-color: white;
        padding: 1.5rem;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .filter-label {
        color: var(--primary-color);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Form Controls */
    .input-group-text {
        background-color: white;
        border-right: none;
        color: var(--secondary-color);
    }

    .form-control, .form-select {
        border-left: none;
        border-color: #ced4da;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--secondary-color);
        box-shadow: none;
    }

    /* Apply Filters Button */
    .apply-filters-btn {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        padding: 0.75rem 2rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    .apply-filters-btn:hover {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        transform: translateY(-1px);
    }

    /* Products Table */
    .product-thumbnail {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 4px;
    }

    .variant-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
    }

    .variant-row:last-child {
        border-bottom: none;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quantity-input {
        width: 60px;
        text-align: center;
    }

    .table-dark {
        background-color: var(--primary-color);
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: var(--light-accent);
    }

    /* Toast Notifications */
    .toast-container {
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1050;
    }

    .toast {
        background-color: var(--primary-color);
        color: white;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .filter-section {
            padding: 1rem;
        }
        
        .apply-filters-btn {
            width: 100%;
            margin-top: 1rem;
        }

        .quantity-controls {
            flex-direction: column;
        }
    }
</style>

<script src="https://kit.fontawesome.com/your-font-awesome-kit.js"></script>
<script>
    let currentPage = 1;
    let fetchTimeout;
    let totalProducts = 0;

    document.addEventListener('DOMContentLoaded', async function() {
        // Initialize all dropdowns
        await Promise.all([
            loadVendors(),
            loadProductTypes(),
            loadTags(),
            loadPriceOverrideTypes(),
            loadExpansionNames()
        ]);

        // Add event listeners for all filters
        setupFilterEventListeners();
        fetchProducts(1);
    });

    function setupFilterEventListeners() {
        // Vendor select changes
        document.getElementById('vendorSelect').addEventListener('change', async function() {
            await loadProductTypes();
            fetchProducts(1);
        });

        // Product type changes
        document.getElementById('productTypeSelect').addEventListener('change', function() {
            fetchProducts(1);
        });

        document.getElementById('expansionNameSelect').addEventListener('change', function() {
            fetchProducts(1);
        });

        // Text input debouncing
        const textInputs = ['productTitle', 'productBarcode', 'productSku'];
        textInputs.forEach(id => {
            document.getElementById(id).addEventListener('input', debounce(() => fetchProducts(1), 500));
        });

        // Number input debouncing
        const numberInputs = [
            'priceGreaterThan', 'priceLessThan',
            'overallQtyGreaterThan', 'overallQtyLessThan',
            'variantQtyGreaterThan', 'variantQtyLessThan'
        ];
        numberInputs.forEach(id => {
            document.getElementById(id).addEventListener('input', debounce(() => fetchProducts(1), 500));
        });

        // Select changes
        const selects = ['taggedWith', 'priceOverrideType', 'inventoryAction'];
        selects.forEach(id => {
            document.getElementById(id).addEventListener('change', () => fetchProducts(1));
        });

        // Apply filters button
        document.querySelector('.apply-filters-btn').addEventListener('click', () => fetchProducts(1));
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    async function loadVendors() {
        try {
            const response = await fetch(`/shopify/products/api/vendors`);
            if (!response.ok) throw new Error('Failed to fetch vendors');
            const data = await response.json();
            
            const select = document.getElementById('vendorSelect');
            select.innerHTML = '<option value="">Select vendors</option>';
            data.vendors.forEach(vendor => {
                if (vendor) {
                    const option = document.createElement('option');
                    option.value = vendor;
                    option.textContent = vendor;
                    select.appendChild(option);
                }
            });
            select.disabled = false;
        } catch (error) {
            console.error('Error loading vendors:', error);
            showNotification('Error', 'Failed to load vendors', 'danger');
        }
    }

    async function loadProductTypes() {
        try {
            const vendor = document.getElementById('vendorSelect').value;
            const url = new URL('/shopify/products/api/product-types', window.location.origin);
            if (vendor) url.searchParams.append('vendor', vendor);
            
            const response = await fetch(url);
            if (!response.ok) throw new Error('Failed to fetch product types');
            const data = await response.json();
            
            const select = document.getElementById('productTypeSelect');
            select.innerHTML = '<option value="">Select product types</option>';
            data.product_types.forEach(type => {
                if (type) {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                }
            });
            select.disabled = false;
        } catch (error) {
            console.error('Error loading product types:', error);
            showNotification('Error', 'Failed to load product types', 'danger');
        }
    }

    async function loadTags() {
        try {
            const response = await fetch('/shopify/products/api/tags');
            if (!response.ok) throw new Error('Failed to fetch tags');
            const data = await response.json();
            
            const select = document.getElementById('taggedWith');
            select.innerHTML = '<option value="">Select...</option>';
            data.tags.forEach(tag => {
                if (tag) {
                    const option = document.createElement('option');
                    option.value = tag;
                    option.textContent = tag;
                    select.appendChild(option);
                }
            });
        } catch (error) {
            console.error('Error loading tags:', error);
            showNotification('Error', 'Failed to load tags', 'danger');
        }
    }

    async function loadPriceOverrideTypes() {
        try {
            const response = await fetch('/shopify/products/api/price-override-types');
            if (!response.ok) throw new Error('Failed to fetch price override types');
            const data = await response.json();
            
            const select = document.getElementById('priceOverrideType');
            select.innerHTML = '<option value="">Select...</option>';
            data.price_override_types.forEach(type => {
                if (type) {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                }
            });
        } catch (error) {
            console.error('Error loading price override types:', error);
            showNotification('Error', 'Failed to load price override types', 'danger');
        }
    }

    async function loadExpansionNames() {
        try {
            const response = await fetch('/shopify/products/api/expansion-names');
            if (!response.ok) throw new Error('Failed to fetch expansion names');
            const data = await response.json();
            
            const select = document.getElementById('expansionNameSelect');
            select.innerHTML = '<option value="">Select expansion</option>';
            data.forEach(name => {
                if (name) {
                    const option = document.createElement('option');
                    option.value = name;
                    option.textContent = name;
                    select.appendChild(option);
                }
            });
        } catch (error) {
            console.error('Error loading expansion names:', error);
            showNotification('Error', 'Failed to load expansion names', 'danger');
        }
    }

    async function fetchProducts(page = 1) {
        showLoading();
        try {
            const params = new URLSearchParams({
                page: page,
                vendor: document.getElementById('vendorSelect').value,
                productType: document.getElementById('productTypeSelect').value,
                title: document.getElementById('productTitle').value,
                barcode: document.getElementById('productBarcode').value,
                sku: document.getElementById('productSku').value,
                'tags[]': Array.from(document.getElementById('taggedWith').selectedOptions).map(option => option.value),
                priceGreaterThan: document.getElementById('priceGreaterThan').value,
                priceLessThan: document.getElementById('priceLessThan').value,
                overallQtyGreaterThan: document.getElementById('overallQtyGreaterThan').value,
                overallQtyLessThan: document.getElementById('overallQtyLessThan').value,
                variantQtyGreaterThan: document.getElementById('variantQtyGreaterThan').value,
                variantQtyLessThan: document.getElementById('variantQtyLessThan').value,
                priceOverrideType: document.getElementById('priceOverrideType').value,
                expansionName: document.getElementById('expansionNameSelect').value
            });

            const response = await fetch(`/shopify/products/api/products?${params.toString()}`);
            if (!response.ok) throw new Error('Failed to fetch products');
            const data = await response.json();
            
            displayProducts(data.products);
            updatePagination(data.page, data.pages);
            totalProducts = data.total;
            
            document.getElementById('resultCounter').textContent = 
                `Showing ${data.products.length} of ${data.total} products`;
        } catch (error) {
            console.error('Error fetching products:', error);
            showNotification('Error', 'Failed to fetch products', 'danger');
        } finally {
            hideLoading();
        }
    }

    function displayProducts(products) {
        const resultsDiv = document.getElementById('results');
        if (!products || products.length === 0) {
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    No products found matching your criteria
                </div>
            `;
            return;
        }

        const table = document.createElement('table');
        table.className = 'table table-striped table-hover';
        table.innerHTML = `
            <thead class="table-dark">
                <tr>
                    <th>Image</th>
                    <th>Title</th>
                    <th>Vendor</th>
                    <th>Product Type</th>
                    <th>Variants</th>
                    <th>Total Stock</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${products.map(product => `
                    <tr>
                        <td>
                            <img src="${product.image || '/static/images/placeholder.png'}" 
                                alt="${product.title}" 
                                class="product-thumbnail">
                        </td>
                        <td>${product.title}</td>
                        <td>${product.vendor}</td>
                        <td>${product.product_type}</td>
                        <td>
                            ${product.variants.map(variant => `
                                <div class="variant-row">
                                    <span>${variant.title}</span>
                                    <div class="quantity-controls">
                                        <button class="btn btn-sm btn-outline-secondary decrease-qty" 
                                                data-variant-id="${variant.id}">-</button>
                                        <input type="number" class="form-control form-control-sm quantity-input" 
                                               value="${variant.inventory_quantity}"
                                               data-variant-id="${variant.id}"
                                               data-original-quantity="${variant.inventory_quantity}">
                                        <button class="btn btn-sm btn-outline-secondary increase-qty"
                                                data-variant-id="${variant.id}">+</button>
                                    </div>
                                </div>
                            `).join('')}
                        </td>
                        <td>${product.total_quantity}</td>
                        <td>
                            <button class="btn btn-sm btn-primary save-changes" 
                                    data-product-id="${product._id}" 
                                    style="display: none;">
                                Save Changes
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        `;

        resultsDiv.innerHTML = '';
        resultsDiv.appendChild(table);

        // Add event listeners for quantity controls
        addQuantityControlListeners();
    }

    function addQuantityControlListeners() {
        // Decrease quantity buttons
        document.querySelectorAll('.decrease-qty').forEach(button => {
            button.addEventListener('click', function() {
                const input = this.parentElement.querySelector('.quantity-input');
                input.value = Math.max(0, parseInt(input.value) - 1);
                checkQuantityChange(input);
            });
        });

        // Increase quantity buttons
        document.querySelectorAll('.increase-qty').forEach(button => {
            button.addEventListener('click', function() {
                const input = this.parentElement.querySelector('.quantity-input');
                input.value = parseInt(input.value) + 1;
                checkQuantityChange(input);
            });
        });

        // Quantity input changes
        document.querySelectorAll('.quantity-input').forEach(input => {
            input.addEventListener('input', function() {
                checkQuantityChange(this);
            });
        });

        // Save changes buttons
        document.querySelectorAll('.save-changes').forEach(button => {
            button.addEventListener('click', async function() {
                const productRow = this.closest('tr');
                const variants = productRow.querySelectorAll('.quantity-input');
                const updates = [];

                variants.forEach(input => {
                    if (input.value !== input.dataset.originalQuantity) {
                        updates.push({
                            variant_id: input.dataset.variantId,
                            quantity: parseInt(input.value)
                        });
                    }
                });

                if (updates.length > 0) {
                    await updateInventory(updates);
                }
            });
        });
    }

    function checkQuantityChange(input) {
        const originalQty = parseInt(input.dataset.originalQuantity);
        const currentQty = parseInt(input.value);
        const saveButton = input.closest('tr').querySelector('.save-changes');
        
        if (originalQty !== currentQty) {
            saveButton.style.display = 'inline-block';
        } else {
            // Check if any other variants in the same product have changes
            const otherVariants = input.closest('tr').querySelectorAll('.quantity-input');
            const hasChanges = Array.from(otherVariants).some(variant => 
                parseInt(variant.value) !== parseInt(variant.dataset.originalQuantity)
            );
            saveButton.style.display = hasChanges ? 'inline-block' : 'none';
        }
    }

    async function updateInventory(updates) {
        try {
            const action = document.getElementById('inventoryAction').value || 'set';
            const response = await fetch('/shopify/products/api/update-inventory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    products: updates
                })
            });

            if (!response.ok) throw new Error('Failed to update inventory');
            const result = await response.json();

            if (result.success) {
                showNotification('Success', 'Inventory updated successfully', 'success');
                // Refresh the products list
                fetchProducts(currentPage);
            } else {
                throw new Error(result.error || 'Failed to update inventory');
            }
        } catch (error) {
            console.error('Error updating inventory:', error);
            showNotification('Error', error.message, 'danger');
        }
    }

    function updatePagination(currentPage, totalPages) {
        const pagination = document.createElement('nav');
        pagination.setAttribute('aria-label', 'Product pagination');
        pagination.innerHTML = `
            <ul class="pagination justify-content-center">
                ${Array.from({ length: totalPages }, (_, i) => i + 1).map(page => `
                    <li class="page-item ${page === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="fetchProducts(${page}); return false;">
                            ${page}
                        </a>
                    </li>
                `).join('')}
            </ul>
        `;
        
        const existingPagination = document.querySelector('nav[aria-label="Product pagination"]');
        if (existingPagination) {
            existingPagination.replaceWith(pagination);
        } else {
            document.getElementById('results').appendChild(pagination);
        }
    }

    function showLoading() {
        const resultsDiv = document.getElementById('results');
        resultsDiv.innerHTML = `
            <div class="text-center my-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading products...</p>
            </div>
        `;
    }

    function hideLoading() {
        // Loading will be hidden when content is displayed
    }

    function showNotification(title, message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}:</strong> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        const container = document.getElementById('toast-container') || createToastContainer();
        container.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 5000
        });
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
            if (container.children.length === 0) {
                container.remove();
            }
        });
    }

    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1050';
        document.body.appendChild(container);
        return container;
    }
</script>
{% endblock %}
