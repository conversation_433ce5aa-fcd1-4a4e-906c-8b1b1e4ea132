{% extends "base.html" %}

{% block title %}Manage Attendees - {{ event.title }}{% endblock %}

{% block styles %}
{{ super() }}
{% include "events/event_styles.html" %}
<style>
    .attendee-actions {
        white-space: nowrap;
    }
    .custom-field-value {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid event-page-content">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Manage Attendees - {{ event.title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('event.event_detail', event_id=event.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Event
                        </a>
                        <a href="{{ url_for('event.add_attendee', event_id=event.id) }}" class="btn btn-primary ml-2">
                            <i class="fas fa-plus"></i> Add Attendee
                        </a>
                        {% if event.attendees %}
                        <a href="{{ url_for('event.export_attendees', event_id=event.id) }}" class="btn btn-success ml-2">
                            <i class="fas fa-file-export"></i> Export to CSV
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Attendees</span>
                                    <span class="info-box-number">{{ event.get_attendee_count() }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Paid</span>
                                    <span class="info-box-number">
                                        {% set paid_count = 0 %}
                                        {% for attendee in event.attendees %}
                                            {% if attendee.paid %}
                                                {% set paid_count = paid_count + 1 %}
                                            {% endif %}
                                        {% endfor %}
                                        {{ paid_count }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Unpaid</span>
                                    <span class="info-box-number">
                                        {% set unpaid_count = 0 %}
                                        {% for attendee in event.attendees %}
                                            {% if not attendee.paid %}
                                                {% set unpaid_count = unpaid_count + 1 %}
                                            {% endif %}
                                        {% endfor %}
                                        {{ unpaid_count }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-ticket-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Remaining Spots</span>
                                    <span class="info-box-number">
                                        {% if event.max_attendees %}
                                            {{ event.max_attendees - event.get_attendee_count() }}
                                            {% if event.is_full() %}
                                                <span class="badge badge-danger">Full</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">Unlimited</span>
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if event.attendees %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Registration Date</th>
                                    <th>Paid</th>
                                    {% if event.custom_fields %}
                                        {% for field in event.custom_fields %}
                                        <th>{{ field.name }}</th>
                                        {% endfor %}
                                    {% endif %}
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendee in event.attendees %}
                                <tr>
                                    <td>{{ attendee.name }}</td>
                                    <td>{{ attendee.email }}</td>
                                    <td>{{ attendee.registration_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if attendee.paid %}
                                        <span class="badge badge-success">Yes</span>
                                        {% else %}
                                        <span class="badge badge-warning">No</span>
                                        <form action="{{ url_for('event.mark_attendee_paid', event_id=event.id, attendee_email=attendee.email) }}" method="POST" style="display: inline;">
                                            <button type="submit" class="btn btn-xs btn-success ml-1">
                                                <i class="fas fa-check"></i> Mark Paid
                                            </button>
                                        </form>
                                        {% endif %}
                                    </td>
                                    {% if event.custom_fields %}
                                        {% for field in event.custom_fields %}
                                        <td>
                                            <div class="custom-field-value" title="{{ attendee.custom_fields.get(field.name, '') }}">
                                                {{ attendee.custom_fields.get(field.name, '') }}
                                            </div>
                                        </td>
                                        {% endfor %}
                                    {% endif %}
                                    <td class="attendee-actions">
                                        <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#removeModal{{ loop.index }}">
                                            <i class="fas fa-trash"></i> Remove
                                        </button>
                                        
                                        <!-- Remove Modal -->
                                        <div class="modal fade" id="removeModal{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="removeModalLabel{{ loop.index }}" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="removeModalLabel{{ loop.index }}">Confirm Remove</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Are you sure you want to remove {{ attendee.name }} ({{ attendee.email }}) from this event?
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                        <form action="{{ url_for('event.remove_attendee', event_id=event.id, attendee_email=attendee.email) }}" method="POST" style="display: inline;">
                                                            <button type="submit" class="btn btn-danger">Remove</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No attendees yet. <a href="{{ url_for('event.add_attendee', event_id=event.id) }}">Add your first attendee</a>.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
