# Script: update_mongo_uris.ps1
# Recursively update all Python files to read the Mongo URI from config/config.py

Param(
    [string]$ConfigImport = 'from config.config import Config'
)

Write-Host "Scanning for .py files..."
Get-ChildItem -Path . -Filter *.py -Recurse | ForEach-Object {
    $filePath = $_.FullName
    $text = Get-Content -Path $filePath -Raw
    $updated = $text

    # Ensure import of Config exists at top
    if ($updated -notmatch 'from\s+config\.config\s+import\s+Config') {
        $updated = "$ConfigImport`r`n$updated"
    }

    # Replace any literal mongo URI assignments to use Config.MONGO_URI
    $pattern = '^(?<prefix>\s*(?:MONGO_URI|mongo_uri)\s*=\s*)(["''])(?:mongodb://.*?)(\2)'
    $updated = $updated -replace $pattern, '${prefix}Config.MONGO_URI'

    # Replace any other literal mongo URI string with Config.MONGO_URI
    $updated = $updated -replace "(['\""])(mongodb://.*?)(\1)", "Config.MONGO_URI"

    if ($updated -ne $text) {
        Write-Host "Updating $filePath"
        Set-Content -Path $filePath -Value $updated -Force
    }
}

Write-Host "Mongo URI update complete. All files now reference Config.MONGO_URI."
