{% extends "base.html" %}

{% block title %}{{ event.title }}{% endblock %}

{% block styles %}
{{ super() }}
{% include "events/event_styles.html" %}
<style>
    .event-banner {
        width: 100%;
        max-height: 300px;
        object-fit: cover;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .event-meta {
        margin-bottom: 20px;
    }
    .event-meta-item {
        margin-bottom: 10px;
    }
    .event-meta-item i {
        width: 20px;
        text-align: center;
        margin-right: 10px;
    }
    .badge-casual {
        background-color: #28a745;
    }
    .badge-competitive {
        background-color: #007bff;
    }
    .badge-learn-to-play {
        background-color: #ffc107;
        color: #212529;
    }
    .attendee-list {
        max-height: 400px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid event-page-content">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ event.title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('event.events') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Events
                        </a>
                        <a href="{{ url_for('event.edit_event', event_id=event.id) }}" class="btn btn-primary ml-2">
                            <i class="fas fa-edit"></i> Edit Event
                        </a>
                        <a href="{{ url_for('event.event_attendees', event_id=event.id) }}" class="btn btn-success ml-2">
                            <i class="fas fa-users"></i> Manage Attendees
                        </a>
                        {% if event.published %}
                        <form action="{{ url_for('event.unpublish_event', event_id=event.id) }}" method="POST" style="display: inline;">
                            <button type="submit" class="btn btn-warning ml-2">
                                <i class="fas fa-eye-slash"></i> Unpublish
                            </button>
                        </form>
                        {% else %}
                        <form action="{{ url_for('event.publish_event', event_id=event.id) }}" method="POST" style="display: inline;">
                            <button type="submit" class="btn btn-info ml-2">
                                <i class="fas fa-globe"></i> Publish
                            </button>
                        </form>
                        {% endif %}
                        <button type="button" class="btn btn-danger ml-2" data-toggle="modal" data-target="#deleteModal">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            {% if event.banner_image_url %}
                            <img src="{{ event.banner_image_url }}" alt="{{ event.title }}" class="event-banner">
                            {% endif %}
                            
                            <div class="event-meta">
                                <div class="event-meta-item">
                                    <i class="fas fa-gamepad"></i> <strong>Game:</strong> {{ event.game }}
                                </div>
                                <div class="event-meta-item">
                                    <i class="fas fa-tag"></i> <strong>Type:</strong> 
                                    <span class="badge badge-{{ event.event_type }}">{{ event.event_type|title }}</span>
                                </div>
                                {% if event.format %}
                                <div class="event-meta-item">
                                    <i class="fas fa-chess-board"></i> <strong>Format:</strong> {{ event.format }}
                                </div>
                                {% endif %}
                                <div class="event-meta-item">
                                    <i class="fas fa-calendar"></i> <strong>Date:</strong> {{ event.start_datetime.strftime('%A, %B %d, %Y') }}
                                </div>
                                <div class="event-meta-item">
                                    <i class="fas fa-clock"></i> <strong>Time:</strong> {{ event.start_datetime.strftime('%I:%M %p') }} - {{ event.end_datetime.strftime('%I:%M %p') }}
                                </div>
                                <div class="event-meta-item">
                                    <i class="fas fa-map-marker-alt"></i> <strong>Location:</strong> {{ event.location }}
                                </div>
                                {% if event.recurrence.type != 'single' %}
                                <div class="event-meta-item">
                                    <i class="fas fa-redo"></i> <strong>Recurrence:</strong> 
                                    {% if event.recurrence.type == 'daily' %}
                                        Daily
                                    {% elif event.recurrence.type == 'weekly' %}
                                        Weekly on 
                                        {% for day in event.recurrence.days_of_week %}
                                            {% if day == 0 %}Monday{% elif day == 1 %}Tuesday{% elif day == 2 %}Wednesday
                                            {% elif day == 3 %}Thursday{% elif day == 4 %}Friday{% elif day == 5 %}Saturday
                                            {% elif day == 6 %}Sunday{% endif %}{% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    {% elif event.recurrence.type == 'monthly' %}
                                        Monthly on day {{ event.recurrence.day_of_month }}
                                    {% endif %}
                                    {% if event.recurrence.end_date %}
                                        until {{ event.recurrence.end_date.strftime('%B %d, %Y') }}
                                    {% endif %}
                                </div>
                                {% endif %}
                                <div class="event-meta-item">
                                    <i class="fas fa-ticket-alt"></i> <strong>Price:</strong> 
                                    {% if event.is_free() %}
                                        <span class="badge badge-success">Free</span>
                                    {% else %}
                                        ${{ "%.2f"|format(event.ticket_price) }}
                                    {% endif %}
                                </div>
                                <div class="event-meta-item">
                                    <i class="fas fa-users"></i> <strong>Attendees:</strong> 
                                    {{ event.get_attendee_count() }}
                                    {% if event.max_attendees %}
                                        / {{ event.max_attendees }}
                                        {% if event.is_full() %}
                                            <span class="badge badge-danger">Full</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">(unlimited)</span>
                                    {% endif %}
                                </div>
                                <div class="event-meta-item">
                                    <i class="fas fa-globe"></i> <strong>Status:</strong> 
                                    {% if event.published %}
                                        <span class="badge badge-success">Published</span>
                                    {% else %}
                                        <span class="badge badge-secondary">Draft</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if event.description %}
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Description</h5>
                                </div>
                                <div class="card-body">
                                    {{ event.description|nl2br }}
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if event.prize_description %}
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title">Prize Support</h5>
                                </div>
                                <div class="card-body">
                                    {{ event.prize_description|nl2br }}
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if event.shopify_product_id %}
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title">Shopify Integration</h5>
                                </div>
                                <div class="card-body">
                                    <p>This event is linked to a Shopify product for ticket sales.</p>
                                    <ul>
                                        <li><strong>Product ID:</strong> {{ event.shopify_product_id }}</li>
                                        <li><strong>Variant ID:</strong> {{ event.shopify_variant_id }}</li>
                                    </ul>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Attendees</h5>
                                    <div class="card-tools">
                                        <a href="{{ url_for('event.add_attendee', event_id=event.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-plus"></i> Add
                                        </a>
                                        {% if event.attendees %}
                                        <a href="{{ url_for('event.export_attendees', event_id=event.id) }}" class="btn btn-sm btn-success ml-1">
                                            <i class="fas fa-file-export"></i> Export
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="card-body">
                                    {% if event.attendees %}
                                    <div class="attendee-list">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Email</th>
                                                    <th>Paid</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for attendee in event.attendees %}
                                                <tr>
                                                    <td>{{ attendee.name }}</td>
                                                    <td>{{ attendee.email }}</td>
                                                    <td>
                                                        {% if attendee.paid %}
                                                        <span class="badge badge-success">Yes</span>
                                                        {% else %}
                                                        <span class="badge badge-warning">No</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <p class="text-muted">No attendees yet.</p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if not event.is_free() %}
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title">Financial Summary</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <th>Ticket Price:</th>
                                            <td>${{ "%.2f"|format(event.ticket_price) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Store Cost:</th>
                                            <td>${{ "%.2f"|format(event.store_cost_per_ticket) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Profit per Ticket:</th>
                                            <td>${{ "%.2f"|format(event.get_profit_per_ticket()) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Total Revenue:</th>
                                            <td>${{ "%.2f"|format(event.get_total_revenue()) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Total Profit:</th>
                                            <td>${{ "%.2f"|format(event.get_total_profit()) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if event.custom_fields %}
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title">Custom Fields</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group">
                                        {% for field in event.custom_fields %}
                                        <li class="list-group-item">
                                            <strong>{{ field.name }}</strong>
                                            <span class="float-right">
                                                <span class="badge badge-info">{{ field.type }}</span>
                                                {% if field.required %}
                                                <span class="badge badge-danger">Required</span>
                                                {% endif %}
                                            </span>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the event "{{ event.title }}"? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form action="{{ url_for('event.delete_event', event_id=event.id) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
