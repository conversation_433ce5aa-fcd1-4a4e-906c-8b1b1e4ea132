from pymongo import MongoClient

def update_manual_override():
    # MongoDB Configuration
    mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
    client = MongoClient(mongo_uri)
    db = client['test']
    collection = db['shProducts']

    try:
        # Perform the update
        result = collection.update_many(
            {
                'tcgItem': True,
                'product_type': {'$not': {'$regex': 'single', '$options': 'i'}}
            },
            {'$set': {'manualOverride': True}}
        )

        print(f'Matched {result.matched_count} documents')
        print(f'Modified {result.modified_count} documents')

        # Print some example documents that were updated
        print('\nExample updated documents:')
        for doc in collection.find({
            'tcgItem': True, 
            'product_type': {'$not': {'$regex': 'single', '$options': 'i'}},
            'manualOverride': True
        }).limit(3):
            print(f'Title: {doc.get("title", "N/A")}')
            print(f'Product Type: {doc.get("product_type", "N/A")}')
            print('---')

    except Exception as e:
        print(f'Error occurred: {str(e)}')
    finally:
        client.close()

if __name__ == '__main__':
    update_manual_override()
