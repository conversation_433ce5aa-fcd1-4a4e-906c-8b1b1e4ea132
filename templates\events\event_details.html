{% extends "base.html" %}

{% block title %}{{ event.title }} - Event Details{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/events.css') }}">
<style>
    .event-banner {
        width: 100%;
        height: 300px;
        object-fit: cover;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .event-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .event-meta-item i {
        width: 24px;
        margin-right: 10px;
        color: var(--primary-color);
    }

    .registration-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .reward-section {
        background-color: rgba(231, 76, 60, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        border-left: 4px solid #e74c3c;
    }

    .reward-image {
        max-width: 100%;
        max-height: 200px;
        border-radius: 4px;
        margin: 10px 0;
    }

    .prize-item {
        background-color: rgba(0, 0, 0, 0.03);
        border-radius: 4px;
        padding: 10px;
        margin-bottom: 10px;
    }

    .prize-item-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }

    .prize-badge {
        font-size: 0.8rem;
        padding: 2px 8px;
        border-radius: 10px;
        background-color: rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="{{ url_for('events.events_dashboard') }}" class="btn btn-outline-secondary mb-2">
                        <i class="fas fa-arrow-left"></i> Back to Events
                    </a>
                    <h1 class="h3 mb-0">{{ event.title }}</h1>
                </div>
                <div>
                    {% if not event.is_cancelled %}
                        {% if not event.is_published %}
                            <a href="{{ url_for('events.publish_event', event_id=event.id) }}" class="btn btn-success">
                                <i class="fas fa-globe"></i> Publish Event
                            </a>
                        {% endif %}
                        <a href="{{ url_for('events.edit_event', event_id=event.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Event
                        </a>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#cancelEventModal">
                            <i class="fas fa-times"></i> Cancel Event
                        </button>
                    {% else %}
                        <span class="badge bg-danger p-2">Event Cancelled</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Event Banner -->
            {% if event.banner_image_url %}
                <img src="{{ event.banner_image_url }}" alt="{{ event.title }}" class="event-banner">
            {% endif %}

            <!-- Event Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-title mb-0">Event Details</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="event-meta-item">
                                <i class="fas fa-calendar-day"></i>
                                <span>{{ event.date.strftime('%A, %B %d, %Y') }}</span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>{{ event.start_time }} - {{ event.end_time }}</span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ event.location }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="event-meta-item">
                                <i class="fas fa-gamepad"></i>
                                <span>{{ event.game }}</span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-tag"></i>
                                <span>{{ event.format|capitalize }}</span>
                            </div>
                            <div class="event-meta-item">
                                <i class="fas fa-users"></i>
                                <span>
                                    {% if event.max_tickets == 0 %}
                                        {{ event.tickets_sold }} registered (unlimited capacity)
                                    {% else %}
                                        {{ event.tickets_sold }} / {{ event.max_tickets }} registered
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>

                    {% if event.description %}
                        <div class="mt-4">
                            <h5>Description</h5>
                            <p>{{ event.description }}</p>
                        </div>
                    {% endif %}

                    {% if event.prize_description %}
                        <div class="mt-4">
                            <h5>Prize Support</h5>
                            <p>{{ event.prize_description }}</p>
                        </div>
                    {% endif %}

                    <!-- Free Rewards Section -->
                    {% if event.has_free_rewards %}
                        <div class="reward-section mt-4">
                            <h5><i class="fas fa-gift text-danger me-2"></i> Free Rewards</h5>
                            <p>{{ event.reward_details }}</p>

                            <!-- Eligibility -->
                            <div class="mb-3">
                                <strong>Eligibility:</strong>
                                {% if event.reward_eligibility == 'all_attendees' %}
                                    <span>Available to all attendees</span>
                                {% elif event.reward_eligibility == 'top_performers' %}
                                    <span>Available to top performers only</span>
                                {% elif event.reward_eligibility == 'random_drawing' %}
                                    <span>Winners selected by random drawing</span>
                                {% endif %}
                            </div>

                            <!-- Reward Image -->
                            {% if event.reward_image_url %}
                                <div class="text-center">
                                    <img src="{{ event.reward_image_url }}" alt="Event Reward" class="reward-image">
                                </div>
                            {% endif %}

                            <!-- Prize List -->
                            {% if event.prizes and event.prizes|length > 0 %}
                                <h6 class="mt-3">Prize List:</h6>
                                <div class="prize-list">
                                    {% for prize in event.prizes %}
                                        <div class="prize-item">
                                            <div class="prize-item-header">
                                                <strong>{{ prize.name }}</strong>
                                                <span class="prize-badge">
                                                    {{ prize.quantity }} available
                                                </span>
                                            </div>
                                            <div>{{ prize.description }}</div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    {% endif %}

                    {% if event.is_published and not event.is_cancelled %}
                        <div class="mt-4 text-center">
                            {% if event.is_sold_out() %}
                                {% if event.enable_waitlist %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>This event is sold out!</strong>
                                    </div>
                                    <a href="{{ url_for('events.join_waitlist', event_id=event.id) }}" class="btn btn-warning btn-lg">
                                        <i class="fas fa-clipboard-list me-2"></i> Join Waitlist
                                    </a>
                                {% else %}
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <strong>This event is sold out!</strong>
                                    </div>
                                {% endif %}
                            {% elif event.shopify_product_id %}
                                <a href="https://{{ current_user.shopify_store_name }}.myshopify.com/products/event-ticket-{{ event.title|lower|replace(' ', '-') }}" target="_blank" class="btn btn-primary btn-lg">
                                    <i class="fas fa-ticket-alt me-2"></i> Purchase Tickets
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Registrations -->
            {% if registrations %}
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Registrations ({{ registrations|length }})</h4>
                    </div>
                    <div class="card-body">
                        <div class="registration-list">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Registered</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for registration in registrations %}
                                        <tr>
                                            <td>{{ registration.customer_name }}</td>
                                            <td>{{ registration.customer_email }}</td>
                                            <td>{{ registration.registered_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>
                                                {% if registration.is_checked_in %}
                                                    <span class="badge bg-success">Checked In</span>
                                                {% else %}
                                                    <span class="badge bg-warning">Not Checked In</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if not registration.is_checked_in %}
                                                    <button class="btn btn-sm btn-outline-success check-in-btn" data-registration-id="{{ registration.id }}">
                                                        <i class="fas fa-check"></i> Check In
                                                    </button>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <div class="col-md-4">
            <!-- Event Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-title mb-0">Event Status</h4>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Status:</span>
                        {% if event.is_cancelled %}
                            <span class="badge bg-danger">Cancelled</span>
                        {% elif event.is_published %}
                            <span class="badge bg-success">Published</span>
                        {% else %}
                            <span class="badge bg-warning">Draft</span>
                        {% endif %}
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Price:</span>
                        <span>
                            {% if event.is_free() %}
                                <span class="badge bg-success">Free</span>
                            {% else %}
                                ${{ event.ticket_price }}
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Tickets Sold:</span>
                        <span>{{ event.tickets_sold }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Capacity:</span>
                        <span>
                            {% if event.max_tickets == 0 %}
                                Unlimited
                            {% else %}
                                {{ event.max_tickets }}
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Profit Per Ticket:</span>
                        <span>${{ event.profit_per_ticket() }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-title mb-0">Quick Links</h4>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{{ url_for('events.event_registrations', event_id=event.id) }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-users me-2"></i> View All Registrations
                        </a>
                        {% if event.enable_waitlist %}
                            <a href="{{ url_for('events.manage_waitlist', event_id=event.id) }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-clipboard-list me-2"></i> Manage Waitlist
                                {% if event.waitlist_count > 0 %}
                                    <span class="badge bg-primary float-end">{{ event.waitlist_count }}</span>
                                {% endif %}
                            </a>
                        {% endif %}
                        {% if event.is_published %}
                            {% if event.shopify_product_id %}
                                <a href="https://{{ current_user.shopify_store_name }}.myshopify.com/admin/products/{{ event.shopify_product_id }}" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="fas fa-shopping-cart me-2"></i> View in Shopify
                                </a>
                            {% endif %}
                            <a href="{{ url_for('events.public_event_details', username=current_user.username, event_id=event.id) }}" target="_blank" class="list-group-item list-group-item-action">
                                <i class="fas fa-globe me-2"></i> View Public Page
                                <i class="fas fa-external-link-alt ms-1" style="font-size: 0.8em;"></i>
                            </a>
                        {% endif %}
                        <a href="{{ url_for('events.edit_event', event_id=event.id) }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-edit me-2"></i> Edit Event
                        </a>
                        {% if not event.is_cancelled %}
                            <button type="button" class="list-group-item list-group-item-action text-danger" data-bs-toggle="modal" data-bs-target="#cancelEventModal">
                                <i class="fas fa-times me-2"></i> Cancel Event
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Recurrence Info -->
            {% if event.recurrence %}
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Recurrence</h4>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Type:</span>
                            <span>{{ event.recurrence.type|capitalize }}</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>End Date:</span>
                            <span>{{ event.recurrence.end_date.strftime('%Y-%m-%d') }}</span>
                        </div>
                        {% if event.recurrence.type == 'weekly' and event.recurrence.days_of_week %}
                            <div class="mb-3">
                                <span>Days:</span>
                                <div class="mt-1">
                                    {% for day in event.recurrence.days_of_week %}
                                        <span class="badge bg-primary me-1">
                                            {{ ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][day] }}
                                        </span>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Cancel Event Modal -->
<div class="modal fade" id="cancelEventModal" tabindex="-1" aria-labelledby="cancelEventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelEventModalLabel">Cancel Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this event? This action cannot be undone.</p>
                <p>If tickets have been sold, you may need to issue refunds manually in Shopify.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <form action="{{ url_for('events.cancel_event', event_id=event.id) }}" method="post">
                    <button type="submit" class="btn btn-danger">Cancel Event</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Check-in functionality
        $('.check-in-btn').click(function() {
            const registrationId = $(this).data('registration-id');
            const button = $(this);

            $.ajax({
                url: "{{ url_for('events.check_in_attendee', event_id=event.id, registration_id='') }}" + registrationId,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        // Update UI
                        button.closest('tr').find('td:nth-child(4)').html('<span class="badge bg-success">Checked In</span>');
                        button.remove();

                        // Show success message
                        toastr.success('Attendee checked in successfully');
                    } else {
                        toastr.error('Error checking in attendee: ' + response.error);
                    }
                },
                error: function() {
                    toastr.error('Error checking in attendee');
                }
            });
        });
    });
</script>
{% endblock %}
