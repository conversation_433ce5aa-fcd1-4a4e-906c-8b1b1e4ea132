from config.config import Config
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from middlewares.auth_middleware import login_required
from flask_login import current_user
from models.user_model import User
from config import Config
import logging
import traceback
import requests
import pymongo
from datetime import datetime
from requests.exceptions import SSLError
from urllib.parse import urlparse

logger = logging.getLogger(__name__)
advanced = Blueprint('advanced', __name__)

# MongoDB connection
mongo_client = Config.get_mongo_client()
db = mongo_client[Config.MONGO_DBNAME]
webhooks_collection = db['webhooks']

# Shopify API version
SHOPIFY_API_VERSION = "2023-04"

@advanced.route('/advanced')
@login_required
def advanced_dashboard():
    return render_template('advanced.html')

@advanced.route('/advanced/save-tokens', methods=['POST'])
@login_required
def save_tokens():
    try:
        # Log the incoming request data
        logger.info(f"Saving tokens for user {current_user.username}")
        logger.info(f"Form data: {request.form.to_dict()}")
        
        # Get all possible token fields
        token_fields = ['shopifyAccessToken'] + [f'shopifyAccessToken{i}' for i in range(1, 11)]
        
        # Get a fresh copy of the user document
        try:
            user = User.objects.get(id=current_user.id)
            logger.info(f"Found user document for {user.username}")
        except Exception as e:
            logger.error(f"Failed to get user document: {str(e)}")
            raise Exception("Failed to retrieve user data")

        # Track if any changes were made
        changes_made = False

        try:
            # Update the user document directly
            for field in token_fields:
                if field in request.form:
                    value = request.form.get(field, '').strip()
                    current_value = getattr(user, field, '')
                    if current_value != value:
                        setattr(user, field, value)
                        changes_made = True
                        logger.info(f"Updated {field}: {value}")

            if not changes_made:
                logger.warning("No changes detected in token values")
                raise Exception("No changes were made to the tokens")

            # Save the user document
            user.save()
            logger.info("Successfully saved user document")

            # Update current_user object to reflect changes
            for field in token_fields:
                if field in request.form:
                    value = request.form.get(field, '').strip()
                    setattr(current_user, field, value)
            logger.info("Updated current_user object")

            # Verify the changes
            updated_user = User.objects.get(id=current_user.id)
            for field in token_fields:
                if field in request.form:
                    expected = request.form.get(field, '').strip()
                    actual = getattr(updated_user, field, '')
                    logger.info(f"Verification - Field: {field}, Expected: {expected}, Actual: {actual}")
                    if expected != actual:
                        logger.warning(f"Verification failed for {field}")

        except Exception as e:
            logger.error(f"Failed to save updates: {str(e)}")
            logger.error(traceback.format_exc())
            raise Exception(f"Failed to save tokens: {str(e)}")

        response = {
            'success': True,
            'message': 'Shopify access tokens updated successfully'
        }
        
        if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            flash('Shopify access tokens updated successfully', 'success')
        
        return jsonify(response)
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error in save_tokens: {error_msg}")
        logger.error(traceback.format_exc())
        
        error_response = {
            'success': False,
            'message': error_msg
        }
        
        if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            flash(error_msg, 'error')
        
        return jsonify(error_response), 400

def format_shop_domain(shop_domain):
    """Format shop domain to ensure it's in the correct format for API calls"""
    # Remove any protocol prefix if present
    if shop_domain.startswith(('http://', 'https://')):
        parsed_url = urlparse(shop_domain)
        shop_domain = parsed_url.netloc
    
    # Ensure domain ends with myshopify.com if not already
    if not shop_domain.endswith('myshopify.com') and '.' in shop_domain:
        # It's a domain but not a myshopify domain
        shop_name = shop_domain.split('.')[0]
        shop_domain = f"{shop_name}.myshopify.com"
    elif '.' not in shop_domain:
        # It's just a shop name without domain
        shop_domain = f"{shop_domain}.myshopify.com"
    
    return shop_domain

@advanced.route('/advanced/check-webhooks', methods=['POST'])
@login_required
def check_webhooks():
    try:
        # Get the shop and access token from the form or use the user's stored values
        shop_domain = request.form.get('shop_domain', '').strip()
        access_token = request.form.get('access_token', '').strip()
        
        # If not provided in the form, use the user's stored values
        if not shop_domain:
            shop_domain = current_user.shopifyStoreName if current_user.shopifyStoreName else ''
        
        if not access_token:
            access_token = current_user.shopifyAccessToken if current_user.shopifyAccessToken else ''
        
        if not shop_domain or not access_token:
            logger.warning("Missing shop domain or access token for webhook check")
            raise Exception("Shop domain and access token are required")
        
        # Ensure the shop domain is properly formatted
        shop_domain = format_shop_domain(shop_domain)
        
        # Format the URL properly with https protocol
        shop_url = f"https://{shop_domain}"
        
        # Remove trailing slash if present
        shop_url = shop_url.rstrip('/')
        
        logger.info(f"Formatted shop URL: {shop_url}")
        
        # Request webhooks from Shopify API
        url = f"{shop_url}/admin/api/{SHOPIFY_API_VERSION}/webhooks.json"
        headers = {"X-Shopify-Access-Token": access_token}
        
        logger.info(f"Checking webhooks for shop: {shop_url}")
        response = requests.get(url, headers=headers)
        
        if response.status_code != 200:
            logger.error(f"Shopify API error: {response.status_code} - {response.text}")
            raise Exception(f"Shopify API error: {response.status_code}")
        
        webhooks_data = response.json()
        logger.info(f"Found {len(webhooks_data.get('webhooks', []))} webhooks")
        
        return jsonify({
            'success': True,
            'webhooks': webhooks_data.get('webhooks', [])
        })
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error checking webhooks: {error_msg}")
        logger.error(traceback.format_exc())
        
        return jsonify({
            'success': False,
            'message': error_msg
        }), 400

def delete_webhooks(shop_domain, access_token):
    """Delete all webhooks for a shop"""
    if not shop_domain or not access_token:
        logger.warning(f"Shop name or access token is missing for {shop_domain}. Skipping...")
        return False, "Shop name or access token is missing"
    
    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json',
    }
    
    # Format shop domain
    shop_domain = format_shop_domain(shop_domain)
    
    # Get all webhooks
    webhooks_url = f"https://{shop_domain}/admin/api/{SHOPIFY_API_VERSION}/webhooks.json"
    logger.info(f"Getting webhooks from: {webhooks_url}")
    
    try:
        response = requests.get(webhooks_url, headers=headers)
        response.raise_for_status()
        webhooks = response.json().get('webhooks', [])
        
        deleted_count = 0
        for webhook in webhooks:
            delete_url = f"https://{shop_domain}/admin/api/{SHOPIFY_API_VERSION}/webhooks/{webhook['id']}.json"
            delete_response = requests.delete(delete_url, headers=headers)
            if delete_response.status_code == 200:
                deleted_count += 1
                logger.info(f"Deleted webhook {webhook['id']} for {shop_domain}")
            else:
                logger.warning(f"Failed to delete webhook {webhook['id']} for {shop_domain}: Status code {delete_response.status_code}")
        
        return True, f"Successfully deleted {deleted_count} webhooks"
    except requests.exceptions.RequestException as e:
        logger.error(f"Error deleting webhooks for {shop_domain}: {str(e)}")
        return False, f"Error deleting webhooks: {str(e)}"

def create_webhooks(shop_domain, access_token, username):
    """Create standard webhooks for a shop"""
    if not shop_domain or not access_token:
        logger.warning(f"Shop name or access token is missing for {shop_domain}. Skipping...")
        return False, "Shop name or access token is missing"
    
    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json',
    }
    
    # Format shop domain
    shop_domain = format_shop_domain(shop_domain)
    
    # Core webhook topics with complete field coverage
    webhook_topics = [
        # Products
        "products/create",
        "products/update",
        "products/delete",
        
        # Inventory
        "inventory_levels/update",
        
        # Customers
        "customers/create",
        "customers/update",
        "customers/delete",
        
        # Orders
        "orders/create",
        "orders/updated",
        "orders/cancelled",
        "orders/fulfilled",
        "orders/paid"
    ]
    
    created_count = 0
    failed_count = 0
    errors = []
    
    for topic in webhook_topics:
        payload = {
            "webhook": {
                "topic": topic,
                "address": f"https://login.tcgsync.com/shopify/webhook?username={username}",
                "format": "json"
            }
        }
        
        create_url = f"https://{shop_domain}/admin/api/{SHOPIFY_API_VERSION}/webhooks.json"
        logger.info(f"Creating webhook for {username} - topic {topic} at: {create_url}")
        
        try:
            response = requests.post(create_url, headers=headers, json=payload)
            response_data = response.json()
            
            webhook_record = {
                "username": username,
                "shop_name": shop_domain,
                "topic": topic,
                "creation_date": datetime.now(),
                "success": response.status_code == 201,
                "response": response_data
            }
            webhooks_collection.insert_one(webhook_record)
            
            if response.status_code == 201:
                created_count += 1
                logger.info(f"Webhook created successfully for {username} ({shop_domain}) on topic {topic}.")
            else:
                failed_count += 1
                error_msg = f"Failed to create webhook for topic {topic}. Status code: {response.status_code}"
                errors.append(error_msg)
                logger.warning(f"{error_msg}. Error response: {response_data}")
        
        except SSLError as e:
            failed_count += 1
            error_msg = f"SSL Error for topic {topic}: {str(e)}"
            errors.append(error_msg)
            logger.error(error_msg)
        except Exception as e:
            failed_count += 1
            error_msg = f"Error for topic {topic}: {str(e)}"
            errors.append(error_msg)
            logger.error(error_msg)
    
    if failed_count == 0:
        return True, f"Successfully created {created_count} webhooks"
    else:
        return created_count > 0, f"Created {created_count} webhooks with {failed_count} failures. Errors: {'; '.join(errors[:3])}"

@advanced.route('/advanced/manage-webhooks', methods=['POST'])
@login_required
def manage_webhooks():
    try:
        # Get the shop and access token from the form or use the user's stored values
        shop_domain = request.form.get('shop_domain', '').strip()
        access_token = request.form.get('access_token', '').strip()
        
        # If not provided in the form, use the user's stored values
        if not shop_domain:
            shop_domain = current_user.shopifyStoreName if current_user.shopifyStoreName else ''
        
        if not access_token:
            access_token = current_user.shopifyAccessToken if current_user.shopifyAccessToken else ''
        
        if not shop_domain or not access_token:
            logger.warning("Missing shop domain or access token for webhook management")
            raise Exception("Shop domain and access token are required")
        
        # Format the shop domain
        shop_domain = format_shop_domain(shop_domain)
        
        # Delete existing webhooks
        delete_success, delete_message = delete_webhooks(shop_domain, access_token)
        if not delete_success:
            logger.warning(f"Failed to delete webhooks: {delete_message}")
            # Continue anyway to try creating webhooks
        
        # Create new webhooks
        create_success, create_message = create_webhooks(shop_domain, access_token, current_user.username)
        
        # Set repull flag to true to trigger data sync
        try:
            current_user.repull = True
            current_user.save()
            logger.info(f"Set repull flag to true for user {current_user.username}")
        except Exception as e:
            logger.error(f"Failed to set repull flag: {str(e)}")
        
        # Check webhooks to verify
        shop_url = f"https://{shop_domain}"
        url = f"{shop_url}/admin/api/{SHOPIFY_API_VERSION}/webhooks.json"
        headers = {"X-Shopify-Access-Token": access_token}
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            webhooks_data = response.json()
            webhook_count = len(webhooks_data.get('webhooks', []))
        except Exception as e:
            logger.error(f"Error verifying webhooks: {str(e)}")
            webhook_count = "unknown"
        
        result_message = f"Webhook management completed. {create_message}. Current webhook count: {webhook_count}"
        
        return jsonify({
            'success': create_success,
            'message': result_message,
            'webhooks': webhooks_data.get('webhooks', []) if 'webhooks_data' in locals() else []
        })
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error managing webhooks: {error_msg}")
        logger.error(traceback.format_exc())
        
        return jsonify({
            'success': False,
            'message': error_msg
        }), 400

