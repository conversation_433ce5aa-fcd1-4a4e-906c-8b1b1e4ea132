from pymongo import MongoClient
import os
import time
import re
from datetime import datetime

# Connect to MongoDB
client = MongoClient(os.environ.get('MONGO_URI', '*******************************************************************'))
db = client[os.environ.get('MONGO_DBNAME', 'test')]

# Test barcode to search for
test_barcode = '820650853494'  # The barcode from the user's example
username = 'ShuffledLGS'  # Username specified by the user

# Create optimized indexes for barcode search if they don't exist
try:
    # Create compound index for username + barcode
    db.shProducts.create_index([
        ("username", 1),
        ("variants.barcode", 1)
    ], name="pos_search_barcode_idx")
    
    print("Created optimized index for barcode search")
except Exception as e:
    print(f"Warning: Failed to create index: {str(e)}")

# Function to perform the optimized barcode search
def optimized_barcode_search(query, username, in_stock_only=False, page=1, limit=10):
    # Start timing the barcode search
    barcode_search_start = datetime.now()
    
    # Calculate skip value for pagination
    skip = (page - 1) * limit
    
    # Try direct query first (most efficient)
    direct_query = {
        "username": username,
        "variants.barcode": query
    }
    
    # Use hint to force index usage
    direct_count = db.shProducts.count_documents(direct_query, 
                                               hint=[("username", 1), ("variants.barcode", 1)])
    
    if direct_count > 0:
        print("Using direct indexed query - exact match found")
        # If we have exact matches, use the direct query approach
        cursor = db.shProducts.find(
            direct_query,
            {
                "title": 1,
                "expansionName": 1,
                "rarity": 1,
                "image": 1,
                "variants.$": 1  # Return only the matching variant
            }
        ).sort("_id", 1).skip(skip).limit(limit)
        
        products = list(cursor)
        
        # Format the results to match the pipeline output structure
        formatted_products = []
        for product in products:
            formatted_products.append({
                "_id": product["_id"],
                "title": product.get("title", "No Title"),
                "expansionName": product.get("expansionName", ""),
                "rarity": product.get("rarity", ""),
                "image": product.get("image", ""),
                "variants": product.get("variants", [])
            })
        
        # Log performance
        barcode_search_time = (datetime.now() - barcode_search_start).total_seconds()
        print(f"Direct query barcode search completed in {barcode_search_time:.3f} seconds")
        print(f"Found {direct_count} products with exact barcode match")
        
        return formatted_products, direct_count, barcode_search_time
    
    # If no exact matches or we need more complex matching, use aggregation pipeline
    print("No exact matches found, using aggregation pipeline")
    pipeline = []
    
    # First match on username (using the index)
    pipeline.append({"$match": {"username": username}})
    
    # Unwind the variants array to search within each variant
    pipeline.append({"$unwind": "$variants"})
    
    # Match on barcode - try different formats
    barcode_conditions = []
    
    # Exact string match (most efficient)
    barcode_conditions.append({"variants.barcode": query})
    
    # Try numeric match if the barcode is a number
    try:
        barcode_as_int = int(query)
        barcode_conditions.append({"variants.barcode": barcode_as_int})
    except ValueError:
        pass
        
    # Add regex match as last resort (least efficient)
    if len(query) >= 3:  # Only use regex for queries with at least 3 characters
        barcode_conditions.append({"variants.barcode": {"$regex": "^" + re.escape(query), "$options": "i"}})
    
    # Add the barcode conditions to the pipeline
    pipeline.append({"$match": {"$or": barcode_conditions}})
    
    # If in_stock_only is true, filter for variants with inventory
    if in_stock_only:
        pipeline.append({"$match": {"variants.inventory_quantity": {"$gt": 0}}})
    
    # Group back to get complete products with matching variants
    pipeline.append({
        "$group": {
            "_id": "$_id",
            "product": {"$first": "$$ROOT"},
            "matching_variants": {"$push": "$variants"}
        }
    })
    
    # Project to restructure the output - only include fields we need
    pipeline.append({
        "$project": {
            "_id": 1,
            "title": "$product.title",
            "expansionName": "$product.expansionName",
            "rarity": "$product.rarity",
            "image": "$product.image",
            "variants": "$matching_variants"
        }
    })
    
    # Add count for pagination
    count_pipeline = pipeline.copy()
    count_pipeline.append({"$count": "total"})
    
    # Execute count pipeline
    count_result = list(db.shProducts.aggregate(count_pipeline, allowDiskUse=True))
    total_items = count_result[0]["total"] if count_result else 0
    
    # Add sort, skip and limit for pagination
    pipeline.append({"$sort": {"_id": 1}})
    pipeline.append({"$skip": skip})
    pipeline.append({"$limit": limit})
    
    # Execute main pipeline with allowDiskUse for large datasets
    products = list(db.shProducts.aggregate(pipeline, allowDiskUse=True))
    
    # Log barcode search performance
    barcode_search_time = (datetime.now() - barcode_search_start).total_seconds()
    print(f"Aggregation pipeline barcode search completed in {barcode_search_time:.3f} seconds")
    print(f"Found {len(products)} products out of {total_items} total matches")
    
    return products, total_items, barcode_search_time

# Run the test
print(f"Testing barcode search for: {test_barcode}")
products, total_items, search_time = optimized_barcode_search(test_barcode, username)

# Print results
print(f"Search time: {search_time:.3f} seconds")
print(f"Total matches: {total_items}")
print(f"Products returned: {len(products)}")

# Print product details if any found
if products:
    for product in products:
        print(f"Product: {product.get('title', 'No Title')}")
        print(f"Variants: {len(product.get('variants', []))}")
else:
    print("No products found")
