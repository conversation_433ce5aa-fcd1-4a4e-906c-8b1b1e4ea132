{% extends "base.html" %}

{% block content %}
<!-- Same styles section as before -->
<style>
/* Dark mode styles for modal */
.modal-content {
    background-color: #212529;
    color: #fff;
    position: relative;
}

/* Loading overlay styles */
.loading-overlay {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(33, 37, 41, 0.9);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.modal-header {
    border-bottom: 1px solid #373b3e;
}

.modal-footer {
    border-top: 1px solid #373b3e;
}

.form-control {
    background-color: #2b3035;
    border-color: #373b3e;
    color: #fff;
}

.form-control:focus {
    background-color: #2b3035;
    border-color: #0d6efd;
    color: #fff;
}

.form-label {
    color: #fff;
}

.btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Loading indicator for images */
.image-loading {
    position: relative;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #2b3035;
}

.image-loading::after {
    content: "Loading...";
    color: #fff;
}
</style>

<!-- Same HTML structure as before -->
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h1>Warhammer Products</h1>
            
            <!-- Search Box -->
            <div class="mb-4">
                <div class="row">
                    <div class="col-12">
                        <input type="text" id="searchInput" class="form-control" placeholder="Search by name...">
                    </div>
                </div>
            </div>
            
            <!-- Filter Dropdowns -->
            <div class="mb-4">
                <div class="row">
                    <div class="col-md-4">
                        <select id="gameSystemSelect" class="form-select">
                            <option value="">Select Game System</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select id="allianceSelect" class="form-select" disabled>
                            <option value="">Select Alliance</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select id="factionSelect" class="form-select" disabled>
                            <option value="">Select Faction</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button id="searchButton" class="btn btn-primary">Search</button>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="row" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination will be loaded here -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export to Shopify</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="loading-overlay">
                <div class="loading-spinner"></div>
                <div class="text-light">Exporting to Shopify...</div>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="price" class="form-label">Price (<span id="currencyLabel">GBP</span>)</label>
                        <input type="number" class="form-control" id="price" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="quantity" value="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="barcode" class="form-label">Barcode (Optional)</label>
                        <input type="text" class="form-control" id="barcode">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Product Images</label>
                        <div class="row" id="productImages">
                            <!-- Images will be loaded here -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="exportButton">Export</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentProductId = null;
let currentPage = 1;
let totalPages = 0;
let currentProducts = []; // Store current products for reference
let userCurrency = '{{ user_currency }}'; // Get user's currency from server

// Function to validate image URL
async function validateImage(url) {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = url;
    });
}

// Function to create image element with validation
async function createValidatedImage(imageUrl, product, isModal = false) {
    const container = document.createElement('div');
    container.className = isModal ? 'position-relative' : '';
    container.innerHTML = `<div class="image-loading"></div>`;

    const isValid = await validateImage(imageUrl);
    if (!isValid) {
        // Remove invalid image from product's images array
        const index = product.images.indexOf(imageUrl);
        if (index > -1) {
            product.images.splice(index, 1);
        }
        return null;
    }

    if (isModal) {
        container.innerHTML = `
            <img src="${imageUrl}" 
                 class="img-fluid" 
                 style="max-height: 150px; width: 100%; object-fit: contain;">
            <div class="form-check position-absolute top-0 start-0 m-2">
                <input class="form-check-input" type="checkbox" value="${imageUrl}" checked>
            </div>
        `;
    } else {
        container.innerHTML = `
            <img src="${imageUrl}" 
                 class="card-img-top" 
                 alt="${product.name}" 
                 style="height: 200px; object-fit: contain;">
        `;
    }

    return container;
}

// Function to create product card
async function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'col-md-4 col-lg-3 mb-4';

    const imageUrl = product.images && product.images.length > 0 
        ? product.images[0] 
        : '/static/images/placeholder.png';

    const imageContainer = await createValidatedImage(imageUrl, product);
    
    const price = product.converted_price || 0;
    const currencySymbol = userCurrency === 'GBP' ? '£' : 
                          userCurrency === 'EUR' ? '€' : 
                          userCurrency === 'USD' ? '$' : userCurrency;

    const cardContent = document.createElement('div');
    cardContent.className = 'card h-100';
    cardContent.innerHTML = `
        <div class="card-body">
            <h5 class="card-title">${product.name}</h5>
            <p class="card-text">
                <strong>System:</strong> ${product.game_system || 'N/A'}<br>
                <strong>Alliance:</strong> ${product.game_alliance || 'N/A'}<br>
                <strong>Faction:</strong> ${product.game_faction || 'N/A'}<br>
                <strong>Price:</strong> ${currencySymbol}${price.toFixed(2)}
            </p>
            <button class="btn btn-primary export-btn" data-id="${product._id}">
                Export to Shopify
            </button>
        </div>
    `;

    if (imageContainer) {
        cardContent.insertBefore(imageContainer.firstChild, cardContent.firstChild);
    }
    card.appendChild(cardContent);
    return card;
}

// Function to show images in modal
async function showImagesInModal(product) {
    const imagesContainer = document.getElementById('productImages');
    imagesContainer.innerHTML = '';

    if (product.images && product.images.length > 0) {
        for (const imageUrl of product.images) {
            const imageContainer = await createValidatedImage(imageUrl, product, true);
            if (imageContainer) {
                const col = document.createElement('div');
                col.className = 'col-6 mb-2';
                col.appendChild(imageContainer);
                imagesContainer.appendChild(col);
            }
        }
    }

    if (imagesContainer.children.length === 0) {
        imagesContainer.innerHTML = '<div class="col-12"><p>No images available</p></div>';
    }
}

// Rest of the code remains the same, but update loadProducts to use async/await
async function loadProducts(page = 1) {
    const gameSystem = document.getElementById('gameSystemSelect').value;
    const alliance = document.getElementById('allianceSelect').value;
    const faction = document.getElementById('factionSelect').value;
    const searchQuery = document.getElementById('searchInput').value;
    
    const params = new URLSearchParams({
        page: page,
        game_system: gameSystem,
        game_alliance: alliance,
        game_faction: faction,
        query: searchQuery
    });
    
    try {
        const response = await fetch(`/warhammer/search?${params}`);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        
        const grid = document.getElementById('productsGrid');
        grid.innerHTML = '';
        
        if (!data.products || data.products.length === 0) {
            grid.innerHTML = '<div class="col-12"><p class="text-center">No products found</p></div>';
            return;
        }
        
        currentProducts = data.products;
        userCurrency = data.user_currency;
        document.getElementById('currencyLabel').textContent = userCurrency;
        
        for (const product of data.products) {
            const card = await createProductCard(product);
            grid.appendChild(card);
        }
        
        // Add event listeners to export buttons
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', async () => {
                currentProductId = btn.dataset.id;
                const product = currentProducts.find(p => p._id === currentProductId);
                
                const price = product.converted_price || 0;
                document.getElementById('price').value = price.toFixed(2);
                document.getElementById('quantity').value = '0';
                
                await showImagesInModal(product);
                
                const modal = new bootstrap.Modal(document.getElementById('exportModal'));
                modal.show();
            });
        });
        
        currentPage = data.current_page;
        totalPages = data.total_pages;
        updatePagination();
        
    } catch (error) {
        console.error('Error loading products:', error);
        const grid = document.getElementById('productsGrid');
        grid.innerHTML = '<div class="col-12"><p class="text-center text-danger">Error loading products</p></div>';
    }
}

// Add search input event listener
let searchTimeout = null;
document.getElementById('searchInput').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        loadProducts(1);
    }, 500);
});

// Game system dropdown click handler
document.getElementById('gameSystemSelect').addEventListener('click', function() {
    if (this.options.length <= 1) {
        fetch('/warhammer/options/game_systems')
            .then(response => response.json())
            .then(systems => {
                this.innerHTML = '<option value="">Select Game System</option>';
                systems.forEach(system => {
                    this.innerHTML += `<option value="${system}">${system}</option>`;
                });
            })
            .catch(error => {
                console.error('Error loading game systems:', error);
                this.innerHTML = '<option value="">Error loading systems</option>';
            });
    }
});

// Alliance dropdown change handler
document.getElementById('gameSystemSelect').addEventListener('change', function() {
    const allianceSelect = document.getElementById('allianceSelect');
    const factionSelect = document.getElementById('factionSelect');
    
    allianceSelect.innerHTML = '<option value="">Select Alliance</option>';
    factionSelect.innerHTML = '<option value="">Select Faction</option>';
    
    if (this.value) {
        allianceSelect.disabled = false;
        fetch(`/warhammer/options/alliances?game_system=${encodeURIComponent(this.value)}`)
            .then(response => response.json())
            .then(alliances => {
                alliances.forEach(alliance => {
                    allianceSelect.innerHTML += `<option value="${alliance}">${alliance}</option>`;
                });
            })
            .catch(error => {
                console.error('Error loading alliances:', error);
                allianceSelect.innerHTML = '<option value="">Error loading alliances</option>';
            });
    } else {
        allianceSelect.disabled = true;
        factionSelect.disabled = true;
    }
});

// Faction dropdown change handler
document.getElementById('allianceSelect').addEventListener('change', function() {
    const gameSystem = document.getElementById('gameSystemSelect').value;
    const factionSelect = document.getElementById('factionSelect');
    
    factionSelect.innerHTML = '<option value="">Select Faction</option>';
    
    if (this.value) {
        factionSelect.disabled = false;
        fetch(`/warhammer/options/factions?game_system=${encodeURIComponent(gameSystem)}&game_alliance=${encodeURIComponent(this.value)}`)
            .then(response => response.json())
            .then(factions => {
                factions.forEach(faction => {
                    factionSelect.innerHTML += `<option value="${faction}">${faction}</option>`;
                });
            })
            .catch(error => {
                console.error('Error loading factions:', error);
                factionSelect.innerHTML = '<option value="">Error loading factions</option>';
            });
    } else {
        factionSelect.disabled = true;
    }
});

// Search button click handler
document.getElementById('searchButton').addEventListener('click', () => {
    loadProducts(1);
});

// Export button click handler
document.getElementById('exportButton').addEventListener('click', async () => {
    const price = document.getElementById('price').value;
    const quantity = document.getElementById('quantity').value;
    const barcode = document.getElementById('barcode').value;
    
    if (!price || quantity === '') {
        alert('Please fill in all required fields');
        return;
    }
    
    document.querySelector('.loading-overlay').style.display = 'flex';
    
    try {
        const response = await fetch(`/warhammer/export-to-shopify/${currentProductId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                price: parseFloat(price),
                quantity: parseInt(quantity),
                barcode: barcode,
                selected_images: Array.from(document.querySelectorAll('#productImages input[type="checkbox"]:checked'))
                    .map(cb => cb.value)
            })
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }

        alert('Product exported successfully!');
        bootstrap.Modal.getInstance(document.getElementById('exportModal')).hide();
    } catch (error) {
        alert('Error exporting product: ' + error.message);
    } finally {
        document.querySelector('.loading-overlay').style.display = 'none';
    }
});

// Pagination update function
function updatePagination() {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    if (totalPages <= 1) return;
    
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>`;
    pagination.appendChild(prevLi);
    
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }
    
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
    pagination.appendChild(nextLi);
    
    pagination.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = parseInt(e.target.dataset.page);
            if (page && page !== currentPage) {
                loadProducts(page);
            }
        });
    });
}

// Initial load
loadProducts(1);
</script>
{% endblock %}
