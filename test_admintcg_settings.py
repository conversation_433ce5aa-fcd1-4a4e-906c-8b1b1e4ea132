from config.config import Config
#!/usr/bin/env python3
"""
Test script to send a repricing request for admintcg and show the user settings and product types fetched.

This script:
1. Uses the RepriceClient to start a repricing job for admintcg
2. Waits for the job to complete
3. Checks the logs to see what settings and product types were fetched

Usage:
    python test_admintcg_settings.py
"""

import sys
import time
import requests
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import the RepriceClient
from reprice_client import RepriceClient

def main():
    # Create a RepriceClient instance
    reprice_client = RepriceClient(
        base_url="https://webhooks.tcgsync.com",
        api_key="IPBRMIwlm5a7DWoZqPnxyamQSIPMyG19"
    )
    
    username = "admintcg"
    
    logger.info(f"=== Starting test for user: {username} ===")
    
    try:
        # Check service health
        logger.info("Checking service health...")
        health = reprice_client.health_check()
        logger.info(f"Service health: {health}")
        
        # Start a repricing job
        logger.info(f"Starting repricing job for {username}...")
        
        try:
            job = reprice_client.start_repricing(username)
            logger.info(f"Started job: {job}")
            
            job_id = job['job_id']
            
            # Poll for status until complete or error
            logger.info("Waiting for job to complete...")
            max_wait_time = 60  # seconds
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                status = reprice_client.check_job_status(job_id)
                logger.info(f"Job status: {status.get('status')}, Progress: {status.get('progress_percent', 0)}%")
                
                if status.get('status') in ['complete', 'error']:
                    logger.info(f"Job finished with status: {status.get('status')}")
                    logger.info(f"Result: {status.get('result')}")
                    break
                
                time.sleep(5)
            
            # Check if we timed out
            if time.time() - start_time >= max_wait_time:
                logger.warning(f"Timed out waiting for job to complete after {max_wait_time} seconds")
                
                # Cancel the job
                logger.info("Cancelling job...")
                cancel_result = reprice_client.cancel_job(job_id)
                logger.info(f"Cancel result: {cancel_result}")
            
            # Check the logs on the server
            logger.info("\n=== User Settings and Product Types from Server Logs ===")
            logger.info("The server logs should show the user settings and product types that were fetched.")
            logger.info("Check the reprice_service.log file on the server for entries like:")
            logger.info("  - User settings for admintcg:")
            logger.info("  - Min Price: X.XX")
            logger.info("  - Price Preference Order: [...]")
            logger.info("  - Selected product types for admintcg: [...]")
            
        except requests.exceptions.HTTPError as http_error:
            # Check if this is a 409 Conflict error (job already in progress)
            if http_error.response.status_code == 409:
                logger.warning("A repricing job is already in progress for this user.")
                
                # Try to get the existing job ID from the error response
                try:
                    error_data = http_error.response.json()
                    existing_job_id = error_data.get('job_id')
                    
                    if existing_job_id:
                        logger.info(f"Existing job ID: {existing_job_id}")
                        
                        # Check the status of the existing job
                        status = reprice_client.check_job_status(existing_job_id)
                        logger.info(f"Existing job status: {status}")
                except Exception:
                    logger.error("Could not get existing job ID from error response")
            else:
                # Handle other HTTP errors
                logger.error(f"HTTP error: {http_error}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    logger.info("=== Test completed ===")

if __name__ == "__main__":
    main()

