@echo off
REM Script to send the Friday Flash Sale email campaign
REM This script will send emails to users listed in the specified file
REM with rate limiting (50 emails per hour)

setlocal enabledelayedexpansion

REM Default values
set USERS_FILE=sample_users.txt
set TEST_MODE=false

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :end_parse_args
if "%~1"=="--test" (
    set TEST_MODE=true
    shift
    goto :parse_args
)
if "%~1"=="--file" (
    set USERS_FILE=%~2
    shift
    shift
    goto :parse_args
)
echo Unknown option: %~1
echo Usage: %0 [--test] [--file FILENAME]
exit /b 1
:end_parse_args

REM Display banner
echo ==================================================
echo   TCGSync Friday Flash Sale Email Campaign
echo ==================================================
echo.

REM Run in test mode or with users file
if "%TEST_MODE%"=="true" (
    echo Running in TEST mode - sending only to admintcg
    python send_flash_sale_emails.py --test
) else (
    REM Check if users file exists
    if not exist "%USERS_FILE%" (
        echo Error: Users file '%USERS_FILE%' not found!
        exit /b 1
    )
    
    REM Count users in file
    for /f %%a in ('type "%USERS_FILE%" ^| find /c /v ""') do set USER_COUNT=%%a
    echo Sending emails to %USER_COUNT% users from file: %USERS_FILE%
    echo Rate limit: 50 emails per hour
    
    REM Calculate estimated completion time (rough calculation in batch)
    set /a HOURS_INT=%USER_COUNT% / 50
    set /a HOURS_DEC=(%USER_COUNT% * 100 / 50) %% 100
    echo Estimated completion time: %HOURS_INT%.%HOURS_DEC% hours
    
    REM Confirm before proceeding
    set /p CONFIRM=Do you want to proceed? (y/n): 
    if /i not "!CONFIRM!"=="y" (
        echo Operation cancelled.
        exit /b 0
    )
    
    REM Run the script
    echo Starting email campaign...
    python send_flash_sale_emails.py --file "%USERS_FILE%"
)

echo.
echo Email campaign completed!
echo Check bulk_email_send.log for details.
