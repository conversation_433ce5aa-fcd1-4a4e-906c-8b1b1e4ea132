from config.config import Config
#!/usr/bin/env python3
import sys
import logging
import argparse
from cmaautopricing import reprice_user_inventory, bulk_reprice, run_scheduled_task

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cm_repricer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description='Run CardMarket autopricing')
    parser.add_argument('--user', help='Username to reprice (optional, if not provided all users will be repriced)')
    parser.add_argument('--scheduled', action='store_true', help='Run as a scheduled task')
    
    args = parser.parse_args()
    
    try:
        if args.scheduled:
            logger.info("Starting scheduled CardMarket autopricing task")
            run_scheduled_task()
        elif args.user:
            logger.info(f"Repricing inventory for user: {args.user}")
            result = reprice_user_inventory(args.user)
            if isinstance(result, tuple) and len(result) == 2:
                logger.info(f"Result: {result[0]}")
            else:
                logger.info(f"Result: {result}")
        else:
            logger.info("Starting bulk CardMarket autopricing for all users")
            bulk_reprice()
            
        logger.info("CardMarket autopricing completed successfully")
        
    except Exception as e:
        logger.error(f"Error running CardMarket autopricing: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()

