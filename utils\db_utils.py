from config.config import Config
from flask import current_app, g
from pymongo import MongoClient

def get_db():
    """
    Get or create MongoDB database connection.
    Returns the database instance from the current application context.
    """
    if 'db' not in g:
        client = MongoClient(current_app.config['MONGO_URI'])
        g.db = client[current_app.config['MONGO_DBNAME']]
    return g.db

