from config.config import Config
#!/usr/bin/env python3
"""
Script to fix Nginx configuration issues.

This script connects to the server via SSH and fixes the Nginx configuration
to properly proxy requests to the repricing service.

Usage:
    python fix_nginx_config.py [--host HOST] [--username USERNAME] [--password PASSWORD]

Options:
    --host HOST          The hostname or IP address of the server [default: **************]
    --username USERNAME  The username for SSH authentication [default: ubuntu]
    --password PASSWORD  The password for SSH authentication [default: zI5zkxmubreicic]
"""

import argparse
import sys
import paramiko
import subprocess
import time

def print_step(message):
    """Print a step message."""
    print(f"\n\033[1;34m=== {message} ===\033[0m")

def print_success(message):
    """Print a success message."""
    print(f"\033[1;32m✓ {message}\033[0m")

def print_error(message):
    """Print an error message."""
    print(f"\033[1;31m✗ {message}\033[0m")

def print_command(command):
    """Print a command."""
    print(f"\033[1;33m$ {command}\033[0m")

def run_command(ssh, command, sudo=False):
    """Run a command on the remote server."""
    print_command(f"{'sudo ' if sudo else ''}{command}")
    
    if sudo:
        command = f"sudo {command}"
    
    stdin, stdout, stderr = ssh.exec_command(command)
    exit_status = stdout.channel.recv_exit_status()
    
    stdout_str = stdout.read().decode('utf-8')
    stderr_str = stderr.read().decode('utf-8')
    
    if stdout_str:
        print(stdout_str)
    
    if stderr_str and exit_status != 0:
        print(f"\033[1;31m{stderr_str}\033[0m")
    
    return exit_status, stdout_str, stderr_str

def fix_nginx_config(args):
    """Fix Nginx configuration issues."""
    print_step("Connecting to the server")
    try:
        # Create SSH client
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(args.host, port=22, username=args.username, password=args.password)
        print_success(f"Connected to {args.host} as {args.username}")
        
        # Check current Nginx configuration
        print_step("Checking current Nginx configuration")
        run_command(ssh, "ls -la /etc/nginx/sites-enabled/", sudo=True)
        
        # Backup current Nginx configuration
        print_step("Backing up current Nginx configuration")
        run_command(ssh, "mkdir -p ~/nginx-backup", sudo=False)
        run_command(ssh, "cp -r /etc/nginx/sites-enabled/* ~/nginx-backup/", sudo=True)
        print_success("Nginx configuration backed up to ~/nginx-backup/")
        
        # Remove conflicting server blocks
        print_step("Removing conflicting server blocks")
        run_command(ssh, "rm -f /etc/nginx/sites-enabled/reprice-service", sudo=True)
        print_success("Removed /etc/nginx/sites-enabled/reprice-service")
        
        # Update the HTTPS server block to proxy to port 5001
        print_step("Updating HTTPS server block to proxy to port 5001")
        
        # Create a temporary file with the updated configuration
        temp_config = """server {
    listen 80;
    server_name webhooks.tcgsync.com;

    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name webhooks.tcgsync.com;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/webhooks.tcgsync.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/webhooks.tcgsync.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # Proxy to the repricing service
    location / {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
"""
        
        # Write the temporary file
        run_command(ssh, f"echo '{temp_config}' > ~/webhooks.tcgsync.com", sudo=False)
        
        # Copy the temporary file to the Nginx configuration directory
        run_command(ssh, "cp ~/webhooks.tcgsync.com /etc/nginx/sites-available/webhooks.tcgsync.com", sudo=True)
        
        # Test the Nginx configuration
        print_step("Testing Nginx configuration")
        exit_status, stdout, stderr = run_command(ssh, "nginx -t", sudo=True)
        
        if exit_status != 0:
            print_error("Nginx configuration test failed")
            print_step("Restoring backup")
            run_command(ssh, "cp -r ~/nginx-backup/* /etc/nginx/sites-enabled/", sudo=True)
            run_command(ssh, "systemctl restart nginx", sudo=True)
            return
        
        # Reload Nginx
        print_step("Reloading Nginx")
        run_command(ssh, "systemctl reload nginx", sudo=True)
        
        # Wait for Nginx to reload
        print_step("Waiting for Nginx to reload")
        time.sleep(2)
        
        # Check if Nginx is running
        print_step("Checking if Nginx is running")
        run_command(ssh, "systemctl status nginx", sudo=True)
        
        # Test if the repricing service is accessible through Nginx
        print_step("Testing if the repricing service is accessible through Nginx")
        run_command(ssh, "curl -v http://localhost/api/health")
        
        # Test if the repricing service is accessible through HTTPS
        print_step("Testing if the repricing service is accessible through HTTPS")
        run_command(ssh, "curl -v -k https://localhost/api/health")
        
        print_step("Nginx configuration fixed")
        print_success("Nginx is now properly configured to proxy requests to the repricing service on port 5001")
        
    except Exception as e:
        print_error(f"Error fixing Nginx configuration: {str(e)}")
    finally:
        if 'ssh' in locals():
            ssh.close()

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Fix Nginx configuration issues.')
    parser.add_argument('--host', default='**************',
                        help='The hostname or IP address of the server')
    parser.add_argument('--username', default='ubuntu',
                        help='The username for SSH authentication')
    parser.add_argument('--password', default='zI5zkxmubreicic',
                        help='The password for SSH authentication')
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    # Check if paramiko is installed
    try:
        import paramiko
    except ImportError:
        print_error("Paramiko is not installed. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "paramiko"])
        print_success("Paramiko installed")
    
    # Fix Nginx configuration
    fix_nginx_config(args)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

