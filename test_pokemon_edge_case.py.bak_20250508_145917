import sys
import logging
import json
from pymongo import MongoClient
import re
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pokemon_edge_case_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = '*******************************************************************'
mongo_client = MongoClient(mongo_uri)
db = mongo_client['test']
shopify_collection = db['shProducts']
prices_collection = db['prices']
tcgplayer_key_collection = db['tcgplayerKey']

# Constants
PRODUCT_ID = 85724  # Giovanni's Persian
USERNAME = "tcgosaurus"

def has_valid_price(price_data):
    """Check if a price data object has any valid prices."""
    return (price_data.get('marketPrice') is not None or 
            price_data.get('lowPrice') is not None or 
            price_data.get('directLowPrice') is not None)

def get_all_products():
    """Get all products with the same productId for the user."""
    products = list(shopify_collection.find({
        "productId": PRODUCT_ID,
        "username": USERNAME
    }))
    
    if not products:
        logger.error(f"No products with ID {PRODUCT_ID} for user {USERNAME} found")
        return []
    
    logger.info(f"Found {len(products)} products with ID {PRODUCT_ID}")
    return products

def get_pricing_data():
    """Get the pricing data from MongoDB."""
    pricing_doc = prices_collection.find_one({"productId": PRODUCT_ID})
    
    if not pricing_doc:
        logger.error(f"Pricing data for product ID {PRODUCT_ID} not found")
        return None
        
    return pricing_doc

def print_product_info(product):
    """Print basic product information."""
    if not product:
        return
        
    logger.info("=== Product Information ===")
    logger.info(f"Product ID: {product.get('productId')}")
    logger.info(f"Title: {product.get('title')}")
    logger.info(f"Game Name: {product.get('gameName')}")
    logger.info(f"Product Type: {product.get('product_type')}")
    logger.info(f"Expansion Name: {product.get('expansionName')}")
    
    logger.info("\nVariants:")
    for variant in product.get('variants', []):
        logger.info(f"  - {variant.get('title')} (Price: ${variant.get('price')})")

def print_pricing_info(pricing_doc):
    """Print pricing information."""
    if not pricing_doc:
        return
        
    logger.info("\n=== Pricing Information ===")
    logger.info(f"Last Updated: {pricing_doc.get('last_updated')}")
    
    prices = pricing_doc.get('prices', {})
    logger.info(f"Available Subtypes: {list(prices.keys())}")
    
    logger.info("\nDetailed Pricing:")
    for subtype, price_data in prices.items():
        has_valid = any(price_data.get(key) is not None for key in ['marketPrice', 'lowPrice', 'directLowPrice'])
        valid_marker = "Valid" if has_valid else "Invalid"
        
        logger.info(f"  {subtype} ({valid_marker})")
        logger.info(f"    Market Price: ${price_data.get('marketPrice')}")
        logger.info(f"    Low Price: ${price_data.get('lowPrice')}")
        logger.info(f"    Direct Low Price: ${price_data.get('directLowPrice')}")
        logger.info(f"    Mid Price: ${price_data.get('midPrice')}")
        logger.info(f"    High Price: ${price_data.get('highPrice')}")

def test_edge_case_logic(product, pricing_doc):
    """Test the Pokemon edge case logic."""
    if not product or not pricing_doc:
        return
    
    logger.info("\n=== Testing Edge Case Logic ===")
    logger.info(f"Product Title: {product.get('title')}")
    
    # Extract valid subtypes from pricing data
    prices = pricing_doc.get('prices', {})
    valid_subtypes = []
    
    for subtype_name, price_data in prices.items():
        if (price_data.get('marketPrice') is not None or 
            price_data.get('lowPrice') is not None or 
            price_data.get('directLowPrice') is not None):
            valid_subtypes.append(subtype_name)
    
    logger.info(f"Valid Subtypes: {valid_subtypes}")
    
    # Test for each variant
    for variant in product.get('variants', []):
        variant_title = variant.get('title', '')
        logger.info(f"\nTesting variant: {variant_title}")
        
        # Original logic
        original_printing_type = determine_printing_type_original(variant_title, valid_subtypes)
        logger.info(f"Original Logic - Determined Printing Type: {original_printing_type}")
        
        # Apply edge case logic
        printing_type, debug_info = apply_edge_case_logic(variant_title, valid_subtypes, product)
        
        # Print debug info
        logger.info("Edge Case Debug Info:")
        for key, value in debug_info.items():
            logger.info(f"  {key}: {value}")
            
        logger.info(f"Final Printing Type: {printing_type}")
        
        # Check if matched price exists
        matched_price = find_matched_price(printing_type, prices)
        if matched_price:
            logger.info(f"Found matching price data for {printing_type}")
            logger.info(f"Market Price: ${matched_price.get('marketPrice')}")
            logger.info(f"Low Price: ${matched_price.get('lowPrice')}")
        else:
            logger.info(f"No matching price data found for {printing_type}")

def determine_printing_type_original(variant_title, valid_subtypes):
    """Original function to determine printing type."""
    if not variant_title:
        return 'Normal'

    # Clean up variant title
    variant_title = variant_title.lower()
    variant_title = re.sub(r'\s+', ' ', variant_title)
    
    # Remove condition prefixes
    for cond in ['near mint', 'nm', 'lightly played', 'lp', 'moderately played', 'mp', 
                'heavily played', 'hp', 'damaged', 'dmg']:
        variant_title = variant_title.replace(cond, '').strip()
    variant_title = variant_title.strip('- ').strip()
    
    # First try exact match against valid subtypes
    for subtype in valid_subtypes:
        if subtype and variant_title == subtype.lower():
            return subtype
    
    # Then try partial matches
    if 'cold foil' in variant_title:
        return 'Cold Foil'
    if 'rainbow foil' in variant_title:
        return 'Rainbow Foil'
    if 'reverse holofoil' in variant_title or 'reverse holo' in variant_title:
        return 'Reverse Holofoil'
    if 'etched foil' in variant_title:
        return 'Etched Foil'
    if 'etched' in variant_title:
        return 'Etched'
    if 'gilded' in variant_title:
        return 'Gilded'
    if any(s in variant_title for s in ['holofoil', 'holo foil']):
        return 'Holofoil'
    if '1st edition' in variant_title:
        return '1st Edition'
    if 'unlimited' in variant_title:
        return 'Unlimited'
    if 'foil' in variant_title:
        for subtype in valid_subtypes:
            if subtype and 'foil' in subtype.lower():
                return subtype
        return 'Foil'
    
    # If no specific match found, return Normal
    return 'Normal'

def apply_edge_case_logic(variant_title, valid_subtypes, product):
    """Apply the Pokemon edge case logic with detailed debugging."""
    debug_info = {}
    
    # First determine the printing type normally
    printing_type = determine_printing_type_original(variant_title, valid_subtypes)
    debug_info["original_printing_type"] = printing_type
    
    # Check if this is a Pokemon product
    game_name = product.get('gameName', '').lower()
    debug_info["game_name"] = game_name
    
    # Check if product title contains "Unlimited"
    product_title = product.get('title', '').lower()
    debug_info["product_title"] = product_title
    debug_info["contains_unlimited_in_title"] = 'unlimited' in product_title
    
    if game_name == 'pokemon' and valid_subtypes:
        # Normalize subtypes for the check
        normalized_subtypes = set()
        has_1st_ed = False
        has_unlimited_variation = False
        unlimited_subtype_name = None
        
        debug_info["valid_subtypes"] = valid_subtypes
        
        # Process each subtype
        for subtype in valid_subtypes:
            st_lower = subtype.lower()
            
            # Check for 1st edition variants (including "1st Edition Holofoil")
            if '1st edition' in st_lower:
                normalized_subtypes.add('1st edition')
                has_1st_ed = True
                debug_info["found_1st_edition"] = subtype
            # Treat 'unlimited', 'holofoil', 'reverse holo' etc. as 'unlimited' *if* 1st ed is also present
            elif 'unlimited' in st_lower or 'holo' in st_lower or 'foil' in st_lower or 'normal' in st_lower:
                normalized_subtypes.add('unlimited')
                has_unlimited_variation = True
                
                # Store the actual subtype name for later use
                # Prioritize explicit "Unlimited Holofoil" or "Unlimited" variants
                if unlimited_subtype_name is None:
                    unlimited_subtype_name = subtype
                if 'unlimited' in st_lower:
                    unlimited_subtype_name = subtype
                    debug_info["found_unlimited"] = subtype
        
        debug_info["normalized_subtypes"] = list(normalized_subtypes)
        debug_info["has_1st_ed"] = has_1st_ed
        debug_info["has_unlimited_variation"] = has_unlimited_variation
        debug_info["unlimited_subtype_name"] = unlimited_subtype_name
        
        # Check if the only normalized types are 1st ed and unlimited
        is_1st_ed_unlimited_only = (normalized_subtypes == {'1st edition', 'unlimited'})
        debug_info["is_1st_ed_unlimited_only"] = is_1st_ed_unlimited_only
        
        # Normalize the determined printing type
        normalized_determined_type = None
        pt_lower = printing_type.lower()
        if '1st edition' in pt_lower:
            normalized_determined_type = '1st edition'
        elif 'unlimited' in pt_lower or 'holo' in pt_lower or 'foil' in pt_lower or 'normal' in pt_lower:
            normalized_determined_type = 'unlimited'
        
        debug_info["normalized_determined_type"] = normalized_determined_type
        
        # Apply override if conditions met
        if is_1st_ed_unlimited_only and normalized_determined_type != '1st edition' and unlimited_subtype_name:
            debug_info["override_applied"] = True
            debug_info["override_from"] = printing_type
            debug_info["override_to"] = unlimited_subtype_name
            printing_type = unlimited_subtype_name  # Use the actual unlimited subtype name
        else:
            debug_info["override_applied"] = False
            
        # Special case: If product title contains "Unlimited" and we have an unlimited subtype, use it
        if 'unlimited' in product_title and unlimited_subtype_name:
            debug_info["title_override_applied"] = True
            debug_info["title_override_from"] = printing_type
            debug_info["title_override_to"] = unlimited_subtype_name
            printing_type = unlimited_subtype_name
    
    return printing_type, debug_info

def find_matched_price(printing_type, prices):
    """Find the matching price data for a printing type."""
    # First try exact match
    if printing_type in prices:
        return prices[printing_type]
    
    # Try case-insensitive match
    printing_type_lower = printing_type.lower()
    for subtype, price_data in prices.items():
        if subtype.lower() == printing_type_lower:
            return price_data
    
    # Try partial matches
    for subtype, price_data in prices.items():
        subtype_lower = subtype.lower()
        if ('holofoil' in subtype_lower and 'holofoil' in printing_type_lower) or \
           ('foil' in subtype_lower and 'foil' in printing_type_lower):
            return price_data
    
    return None

def main():
    """Main function to run the test."""
    logger.info("Starting Pokemon Edge Case Test")
    
    # Get all products with the same productId
    products = get_all_products()
    pricing_doc = get_pricing_data()
    
    if not products or not pricing_doc:
        logger.error("Cannot proceed with testing - missing data")
        return
    
    # Print pricing information once
    print_pricing_info(pricing_doc)
    
    # Test each product
    for product in products:
        logger.info("\n" + "="*50)
        print_product_info(product)
        test_edge_case_logic(product, pricing_doc)
    
    logger.info("\nTest completed")

if __name__ == "__main__":
    main()
