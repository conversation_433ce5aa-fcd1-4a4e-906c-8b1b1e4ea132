#!/usr/bin/env python3
"""
MongoDB URI Update Script (IP Address Version)

This script updates the MongoDB URI to use the IP address directly
instead of the hostname to resolve DNS resolution issues.
"""

import os
import sys
import logging
import re
import socket

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mongo_uri_update")

def update_mongo_uri():
    """Update MongoDB URI to use IP address."""
    logger.info("Updating MongoDB URI to use IP address...")
    
    # Read the config file
    try:
        with open('config/config.py', 'r') as f:
            content = f.read()
        
        # Extract the current MongoDB URI
        uri_match = re.search(r"MONGO_URI = os\.environ\.get\('MONGO_URI', '([^']+)'\)", content)
        if not uri_match:
            logger.error("Could not find MongoDB URI in config file")
            return False
        
        current_uri = uri_match.group(1)
        logger.info(f"Current MongoDB URI: {current_uri}")
        
        # Use the MongoDB replica set URI
        # This connects to the MongoDB cluster using the replica set configuration
        direct_uri = 'mongodb://admin:<EMAIL>,node2-594640eae489c6f0.database.cloud.ovh.net,node3-594640eae489c6f0.database.cloud.ovh.net/admin?replicaSet=replicaset&tls=true'
        logger.info(f"New MongoDB URI: {direct_uri}")
        
        # Update the config file
        updated_content = content.replace(current_uri, direct_uri)
        with open('config/config.py', 'w') as f:
            f.write(updated_content)
        
        logger.info("MongoDB URI updated successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error updating MongoDB URI: {str(e)}")
        return False

def update_env_files():
    """Update MongoDB URI in .env files."""
    env_files = ['.env.local', '.env.production']
    
    for env_file in env_files:
        try:
            if not os.path.exists(env_file):
                logger.info(f"Skipping {env_file} (file not found)")
                continue
                
            logger.info(f"Updating {env_file}...")
            
            with open(env_file, 'r') as f:
                content = f.read()
            
            # Look for MONGO_URI in the file
            uri_match = re.search(r'MONGO_URI=([^\n]+)', content)
            if not uri_match:
                logger.info(f"No MONGO_URI found in {env_file}")
                continue
            
            current_uri = uri_match.group(1)
            
            # Use the MongoDB replica set URI
            direct_uri = 'mongodb://admin:<EMAIL>,node2-594640eae489c6f0.database.cloud.ovh.net,node3-594640eae489c6f0.database.cloud.ovh.net/admin?replicaSet=replicaset&tls=true'
            
            # Update the file
            updated_content = content.replace(current_uri, direct_uri)
            with open(env_file, 'w') as f:
                f.write(updated_content)
            
            logger.info(f"MongoDB URI updated in {env_file}")
            
        except Exception as e:
            logger.error(f"Error updating {env_file}: {str(e)}")

if __name__ == "__main__":
    if update_mongo_uri():
        update_env_files()
        logger.info("MongoDB URI update completed successfully")
        logger.info("Please restart your application to apply the changes")
    else:
        logger.error("Failed to update MongoDB URI")
        sys.exit(1)
