from config.config import Config
import requests
import json

def create_shopify_product(store_name, access_token, product_data):
    """
    Create a new product in Shopify using the REST Admin API.
    
    Args:
        store_name (str): The Shopify store name
        access_token (str): The Shopify access token
        product_data (dict): The product data to create
        
    Returns:
        dict: The created product data from Shopify
    """
    url = f"https://{store_name}.myshopify.com/admin/api/2024-01/products.json"
    
    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps({'product': product_data}))
        response.raise_for_status()
        return response.json()['product']
    except requests.exceptions.RequestException as e:
        print(f"Error creating Shopify product: {e}")
        if hasattr(e.response, 'text'):
            print(f"Response: {e.response.text}")
        raise

def create_metafield(store_name, access_token, owner_id, key, value, type="single_line_text_field", namespace="custom"):
    """
    Create a metafield for a product in Shopify.
    
    Args:
        store_name (str): The Shopify store name
        access_token (str): The Shopify access token
        owner_id (str): The product ID to attach the metafield to
        key (str): The metafield key
        value (str): The metafield value
        type (str): The metafield type (default: single_line_text_field)
        namespace (str): The metafield namespace (default: custom)
        
    Returns:
        dict: The created metafield data from Shopify
    """
    url = f"https://{store_name}.myshopify.com/admin/api/2024-01/products/{owner_id}/metafields.json"
    
    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json'
    }
    
    metafield_data = {
        "metafield": {
            "namespace": namespace,
            "key": key,
            "value": value,
            "type": type
        }
    }
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(metafield_data))
        response.raise_for_status()
        return response.json()['metafield']
    except requests.exceptions.RequestException as e:
        print(f"Error creating metafield: {e}")
        if hasattr(e.response, 'text'):
            print(f"Response: {e.response.text}")
        raise

def format_board_game_description(game_data):
    """
    Format the board game data into a nice HTML description.
    
    Args:
        game_data (dict): The board game data
        
    Returns:
        str: Formatted HTML description
    """
    description = f"""
<div class="game-description">
    <h2>{game_data['name']}</h2>
    <p class="game-intro">A {game_data['year_published']} board game ranked #{game_data['rank']} on Board Game Geek!</p>

    <div class="game-details">
        <h3>🎮 Game Overview</h3>
        <ul>
            <li><strong>Players:</strong> {game_data['min_players']}-{game_data['max_players']} players</li>
            <li><strong>Playing Time:</strong> {game_data['playing_time']} minutes</li>
            <li><strong>Age:</strong> {game_data['min_age']}+</li>
            <li><strong>Complexity:</strong> {game_data['weight']:.2f}/5</li>
        </ul>

        <h3>🌟 Rankings & Ratings</h3>
        <ul>
            <li>Overall Rank: #{game_data['rank']}</li>
            <li>Strategy Games Rank: #{game_data['strategygames_rank']}</li>
            <li>{game_data['average']:.2f} Average Rating from {game_data['usersrated']:,}+ players</li>
        </ul>
    """

    if game_data.get('mechanics'):
        description += f"""
        <h3>🎲 Game Mechanics</h3>
        <ul>
            {''.join(f'<li>{mechanic}</li>' for mechanic in game_data['mechanics'])}
        </ul>
        """

    if game_data.get('categories'):
        description += f"""
        <h3>📋 Categories</h3>
        <ul>
            {''.join(f'<li>{category}</li>' for category in game_data['categories'])}
        </ul>
        """

    description += f"""
        <h3>✨ Description</h3>
        <p>{game_data['description']}</p>

        <div class="credits">
            <p><strong>Designed by:</strong> {', '.join(game_data['designers'])}</p>
            <p><strong>Artists:</strong> {', '.join(game_data['artists'])}</p>
            <p><strong>Published by:</strong> {', '.join(game_data['publishers'][:3])}</p>
        </div>
    </div>
</div>
    """
    
    return description

def get_locations(store_name, access_token):
    """
    Get all locations from Shopify.
    
    Args:
        store_name (str): The Shopify store name
        access_token (str): The Shopify access token
        
    Returns:
        list: List of location objects from Shopify
    """
    url = f"https://{store_name}.myshopify.com/admin/api/2024-01/locations.json"
    
    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.json()['locations']
    except requests.exceptions.RequestException as e:
        print(f"Error getting locations: {e}")
        if hasattr(e.response, 'text'):
            print(f"Response: {e.response.text}")
        raise

def adjust_inventory(store_name, access_token, inventory_item_id, location_id, quantity):
    """
    Adjust inventory level for a variant in Shopify.
    
    Args:
        store_name (str): The Shopify store name
        access_token (str): The Shopify access token
        inventory_item_id (str): The inventory item ID
        location_id (str): The location ID
        quantity (int): The quantity to adjust (negative for decrease)
        
    Returns:
        dict: The updated inventory level data from Shopify
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    url = f"https://{store_name}.myshopify.com/admin/api/2024-01/inventory_levels/adjust.json"
    
    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json'
    }
    
    # Convert inventory_item_id to int if it's a string
    try:
        inventory_item_id = int(inventory_item_id)
    except (ValueError, TypeError) as e:
        logger.error(f"Invalid inventory_item_id format: {inventory_item_id}, error: {e}")
        raise
        
    # Convert location_id to int if it's a string
    try:
        location_id = int(location_id)
    except (ValueError, TypeError) as e:
        logger.error(f"Invalid location_id format: {location_id}, error: {e}")
        raise
    
    data = {
        "location_id": location_id,
        "inventory_item_id": inventory_item_id,
        "available_adjustment": quantity
    }
    
    try:
        logger.info(f"Making Shopify API request to adjust inventory:")
        logger.info(f"URL: {url}")
        logger.info(f"Headers: {json.dumps(headers, indent=2)}")
        logger.info(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, data=json.dumps(data))
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response body: {response.text}")
        
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"Error adjusting inventory: {e}")
        if hasattr(e.response, 'text'):
            logger.error(f"Response: {e.response.text}")
        raise

def create_board_game_product(store_name, access_token, game_data):
    """
    Create a board game product in Shopify with all the necessary data and metafields.
    
    Args:
        store_name (str): The Shopify store name
        access_token (str): The Shopify access token
        game_data (dict): The board game data
        
    Returns:
        dict: The created product data from Shopify
    """
    # Prepare tags
    tags = []
    tags.extend(game_data.get('categories', []))
    tags.extend(game_data.get('mechanics', []))
    tags.extend([
        f"{game_data['min_players']}-{game_data['max_players']} Players",
        f"{game_data['min_age']}+",
        f"Weight {game_data['weight']:.1f}",
        f"{game_data['playing_time']} Minutes"
    ])
    
    # Create product data
    product_data = {
        "title": f"{game_data['name']} ({game_data['year_published']})",
        "body_html": format_board_game_description(game_data),
        "vendor": "Board Games",
        "product_type": "Board Games",
        "tags": tags,
        "status": "draft",  # Start as draft so you can review before publishing
        "images": [{"src": game_data['image_url']}] if game_data.get('image_url') else []
    }
    
    # Create the product
    product = create_shopify_product(store_name, access_token, product_data)
    
    # Add metafields
    metafields = {
        "bgg_id": str(game_data['id']),
        "year_published": str(game_data['year_published']),
        "min_players": str(game_data['min_players']),
        "max_players": str(game_data['max_players']),
        "playing_time": str(game_data['playing_time']),
        "min_age": str(game_data['min_age']),
        "weight": f"{game_data['weight']:.2f}",
        "bgg_rank": str(game_data['rank']),
        "strategy_rank": str(game_data['strategygames_rank']),
        "average_rating": f"{game_data['average']:.2f}",
        "users_rated": str(game_data['usersrated']),
        "designers": ", ".join(game_data['designers']),
        "artists": ", ".join(game_data['artists']),
        "publishers": ", ".join(game_data['publishers'][:3])  # Limit to first 3 publishers
    }
    
    for key, value in metafields.items():
        create_metafield(store_name, access_token, product['id'], key, value)
    
    return product

