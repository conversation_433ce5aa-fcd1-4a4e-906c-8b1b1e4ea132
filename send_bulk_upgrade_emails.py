from config.config import Config
import time
from send_simple_upgrade_email import send_subscription_upgrade_email

# List of email addresses
email_addresses = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
]

# Send emails with progress tracking
total_emails = len(email_addresses)
successful = 0
failed = 0

print(f"Starting to send {total_emails} upgrade notification emails...")

for i, email in enumerate(email_addresses, 1):
    print(f"\nSending email {i}/{total_emails} to {email}...")
    
    if send_subscription_upgrade_email(email):
        successful += 1
        print(f"✓ Successfully sent to {email}")
    else:
        failed += 1
        print(f"✗ Failed to send to {email}")
    
    # Add a small delay between sends (1 second)
    if i < total_emails:
        time.sleep(1)

print(f"\nEmail sending completed!")
print(f"Successfully sent: {successful}")
print(f"Failed: {failed}")
print(f"Total processed: {total_emails}")

