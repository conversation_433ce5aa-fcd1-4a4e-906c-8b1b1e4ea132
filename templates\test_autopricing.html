<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopify Autopricing Test</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Set the background color to match dashboard */
        body {
            background-color: #6b21a8; /* Purple background to match dashboard */
        }

        .main-content {
            background-color: #6b21a8;
        }

        /* Card styling to match dashboard */
        .card {
            background-color: #1e293b;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .card-header {
            background-color: #1a202c;
            color: white;
            border-bottom: 2px solid #4a5568;
            padding: 15px 20px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        /* Form controls styling */
        .form-control, .form-select, select {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 8px;
        }

        .form-control:focus, .form-select:focus, select:focus {
            background-color: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
            box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
        }

        /* Dropdown styling */
        .form-select option, select option {
            background-color: #1e293b !important;
            color: white !important;
        }

        /* Table styling */
        .table {
            color: white;
            background-color: #1e293b;
        }

        .table thead th {
            background-color: #1a202c;
            color: white;
            border-bottom: 2px solid #4a5568;
            padding: 15px;
            font-weight: 600;
        }

        .table tbody tr {
            background-color: #1e293b;
        }

        .table tbody tr:hover {
            background-color: #3a4a5c;
        }

        .table td, .table th {
            border-color: #4a5568;
            padding: 15px;
            vertical-align: middle;
        }

        /* Button styling */
        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* Modal styling */
        .modal-content {
            background-color: #1e293b;
            color: white;
            border: none;
            border-radius: 16px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .modal-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-footer {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* List group styling */
        .list-group-item {
            background-color: #1e293b;
            color: white;
            border-color: rgba(255, 255, 255, 0.1);
        }

        /* Input group styling */
        .input-group-text {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white my-4">Shopify Autopricing Test</h1>
        
        <!-- Price Type Preference Section -->
        <div class="card mb-4">
            <div class="card-header" style="background: linear-gradient(135deg, #e91e63, #c2185b);">
                <h5 class="mb-0 text-white">Price Type Preference</h5>
            </div>
            <div class="card-body">
                <div class="alert" style="background-color: rgba(52, 152, 219, 0.1); border-color: rgba(52, 152, 219, 0.3); color: white;">
                    <i class="fas fa-info-circle me-2"></i>
                    Use the arrow buttons to arrange price types in your preferred order. The system will try to use prices in this order.
                </div>
                <div id="priceTypeList" class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center" data-type="lowPrice" style="background-color: #1e293b; color: white; border-color: rgba(255, 255, 255, 0.1); margin-bottom: 5px; transition: all 0.3s ease;">
                        <span>Low Price</span>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-light move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                            <button class="btn btn-sm btn-outline-light move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center" data-type="marketPrice" style="background-color: #1e293b; color: white; border-color: rgba(255, 255, 255, 0.1); margin-bottom: 5px; transition: all 0.3s ease;">
                        <span>Market Price</span>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-light move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                            <button class="btn btn-sm btn-outline-light move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center" data-type="midPrice" style="background-color: #1e293b; color: white; border-color: rgba(255, 255, 255, 0.1); margin-bottom: 5px; transition: all 0.3s ease;">
                        <span>Mid Price</span>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-light move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                            <button class="btn btn-sm btn-outline-light move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center" data-type="highPrice" style="background-color: #1e293b; color: white; border-color: rgba(255, 255, 255, 0.1); margin-bottom: 5px; transition: all 0.3s ease;">
                        <span>High Price</span>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-light move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                            <button class="btn btn-sm btn-outline-light move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                        </div>
                    </div>
                </div>
                <button class="btn mt-3" id="savePriceTypePreference" style="background-color: #e91e63; border-color: #e91e63; color: white;">Save Preference</button>
            </div>
        </div>

        <!-- Default Condition Settings -->
        <div class="card mb-4">
            <div class="card-header" style="background: linear-gradient(135deg, #e91e63, #c2185b);">
                <h5 class="mb-0 text-white">Default Pricing Settings</h5>
            </div>
            <div class="card-body">
                <div class="form-check form-switch mb-3">
                    <input type="checkbox" class="form-check-input" id="useSkuIdPricing">
                    <label class="form-check-label text-light" for="useSkuIdPricing">Use SKU ID Pricing</label>
                </div>
                <div class="alert" id="skuIdWarning" style="display: none; background-color: rgba(243, 156, 18, 0.1); border-color: rgba(243, 156, 18, 0.3); color: white;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Warning: SKU ID pricing can result in lower condition variants being priced higher than higher conditions. If SKU ID pricing is unavailable for a product, it will automatically revert to using your standard condition settings, so please ensure all settings are configured.
                </div>

                <div class="card mb-3" style="background-color: rgba(30, 41, 59, 0.5); border: 1px solid rgba(255, 255, 255, 0.05);">
                    <div class="card-header" style="background-color: rgba(26, 32, 44, 0.5); border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                        <div class="form-check form-switch">
                            <input type="checkbox" class="form-check-input" id="priceRoundingEnabled">
                            <label class="form-check-label text-light" for="priceRoundingEnabled">Enable Price Rounding</label>
                        </div>
                    </div>
                    <div class="card-body" id="priceRoundingSettings" style="display: none;">
                        <div class="alert" style="background-color: rgba(52, 152, 219, 0.1); border-color: rgba(52, 152, 219, 0.3); color: white;">
                            <i class="fas fa-info-circle me-2"></i>
                            Enter decimal values (0-99) to round prices to. For example, entering 49 and 99 will round prices to the nearest .49 or .99
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-light">Rounding Thresholds</label>
                            <div id="thresholdInputs" class="d-flex gap-2 align-items-center">
                                <input type="number" class="form-control threshold-input" min="0" max="99" placeholder="e.g. 49">
                                <input type="number" class="form-control threshold-input" min="0" max="99" placeholder="e.g. 99">
                                <button class="btn btn-outline-primary" id="addThreshold">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="card bg-dark">
                            <div class="card-body">
                                <label for="nmInput" class="text-light">Near Mint (%):</label>
                                <input type="number" id="nmInput" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-dark">
                            <div class="card-body">
                                <label for="lpInput" class="text-light">Lightly Played (%):</label>
                                <input type="number" id="lpInput" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-dark">
                            <div class="card-body">
                                <label for="mpInput" class="text-light">Moderately Played (%):</label>
                                <input type="number" id="mpInput" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-3 mt-2">
                    <div class="col-md-4">
                        <div class="card bg-dark">
                            <div class="card-body">
                                <label for="hpInput" class="text-light">Heavily Played (%):</label>
                                <input type="number" id="hpInput" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-dark">
                            <div class="card-body">
                                <label for="dmInput" class="text-light">Damaged (%):</label>
                                <input type="number" id="dmInput" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-dark">
                            <div class="card-body">
                                <label for="minPriceInput" class="text-light">Minimum Price ({{ user_currency }}):</label>
                                <input type="number" id="minPriceInput" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <button id="saveAutopricingSettings" class="btn" style="background-color: #e91e63; border-color: #e91e63; color: white;">Save Default Settings</button>
                </div>
            </div>
        </div>

        <!-- Price Comparison Settings -->
        <div class="card mb-4">
            <div class="card-header" style="background: linear-gradient(135deg, #e91e63, #c2185b);">
                <h5 class="mb-0 text-white">Price Comparison Settings</h5>
            </div>
            <div class="card-body">
                <div class="form-check form-switch mb-3">
                    <input type="checkbox" class="form-check-input" id="useHighestPrice">
                    <label class="form-check-label text-light" for="useHighestPrice" data-bs-toggle="tooltip" data-bs-placement="right" title="When enabled, the system will compare each pair of prices and use the highest value">
                        Use Highest Price from Pairs
                    </label>
                </div>

                <div id="priceComparisonPairs" class="mb-3">
                    <label class="form-label text-light">Price Comparison Pairs</label>
                    <div class="price-pair-container">
                        <!-- Price pairs will be added here -->
                    </div>
                    <button class="btn mt-2" id="addPricePair" style="background-color: rgba(233, 30, 99, 0.1); border-color: #e91e63; color: #e91e63;">
                        <i class="fas fa-plus"></i> Add Price Pair
                    </button>
                    <div class="mt-2 text-muted small">
                        Compare prices between pairs and use the highest value when enabled above.
                    </div>
                </div>

                <div id="priceModifiers" class="mb-3">
                    <label class="form-label text-light">Price Modifiers</label>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Enter the percentage to increase prices. For example, enter "10" to increase price by 10%. Enter "0" for no change.
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <div class="input-group">
                                <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="Enter percentage to increase price. E.g. 10 = +10%">Low Price</span>
                                <input type="number" class="form-control price-modifier" data-type="lowPrice" min="0" step="1" value="0">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <div class="input-group">
                                <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="Enter percentage to increase price. E.g. 10 = +10%">Market Price</span>
                                <input type="number" class="form-control price-modifier" data-type="marketPrice" min="0" step="1" value="0">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <div class="input-group">
                                <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="Enter percentage to increase price. E.g. 10 = +10%">Mid Price</span>
                                <input type="number" class="form-control price-modifier" data-type="midPrice" min="0" step="1" value="0">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <div class="input-group">
                                <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="Enter percentage to increase price. E.g. 10 = +10%">High Price</span>
                                <input type="number" class="form-control price-modifier" data-type="highPrice" min="0" step="1" value="0">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="btn" id="savePriceComparisonSettings" style="background-color: #e91e63; border-color: #e91e63; color: white;">Save Price Comparison Settings</button>
            </div>
        </div>

        <!-- Game-Specific Minimum Prices Section -->
        <div class="card mb-4">
            <div class="card-header" style="background: linear-gradient(135deg, #e91e63, #c2185b);">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-white">Game-Specific Minimum Prices</h5>
                    <button class="btn btn-sm" style="background-color: rgba(255, 255, 255, 0.2); color: white;" data-bs-toggle="collapse" data-bs-target="#gameMinPricesContent">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="card-body collapse show" id="gameMinPricesContent">
                <div class="alert mb-4" style="background-color: rgba(52, 152, 219, 0.1); border-color: rgba(52, 152, 219, 0.3); color: white;">
                    <i class="fas fa-info-circle me-2"></i>
                    Set minimum prices for different print types and rarities within each game. These settings will override the default minimum price when repricing cards.
                </div>

                <!-- Game Selection -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text bg-dark text-light border-secondary">
                                <i class="fas fa-gamepad"></i>
                            </span>
                            <select id="gameSelect" class="form-select bg-dark text-light border-secondary">
                                <option value="">Select Game</option>
                            </select>
                            <button id="addNewGame" class="btn" style="background-color: rgba(233, 30, 99, 0.1); border-color: #e91e63; color: #e91e63;">
                                <i class="fas fa-plus"></i> Add Game
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text bg-dark text-light border-secondary">
                                <i class="fas fa-dollar-sign"></i>
                            </span>
                            <input type="number" id="defaultMinPrice" class="form-control bg-dark text-light border-secondary" placeholder="Default Min Price" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- Game Settings Container -->
                <div id="gameSettingsContainer" style="display: none;">
                    <div class="row">
                        <!-- Print Type Minimum Prices -->
                        <div class="col-md-6">
                            <div class="card bg-dark h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-print me-2"></i>
                                        Print Type Minimums
                                    </h6>
                                    <button id="addPrintType" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="printTypeInputs" class="row g-3">
                                        <!-- Print type inputs will be dynamically added here -->
                                    </div>
                                    <div id="noPrintTypesMessage" class="alert alert-info" style="display: none;">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No print types have been added yet. Click the "+" button above to add a print type.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Rarity Minimum Prices -->
                        <div class="col-md-6">
                            <div class="card bg-dark h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-star me-2"></i>
                                        Rarity Minimums
                                    </h6>
                                    <button id="addRarity" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="rarityInputs" class="row g-3">
                                        <!-- Rarity inputs will be dynamically added here -->
                                    </div>
                                    <div id="noRaritiesMessage" class="alert alert-info" style="display: none;">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No rarities have been added yet. Click the "+" button above to add a rarity.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 text-center">
                        <button id="saveGameSettings" class="btn btn-lg" style="background-color: #e91e63; border-color: #e91e63; color: white;">
                            <i class="fas fa-save me-2"></i>
                            Save Game Settings
                        </button>
                        <button id="deleteGameSettings" class="btn btn-lg ms-3" style="background-color: rgba(220, 53, 69, 0.1); border-color: #dc3545; color: #dc3545;">
                            <i class="fas fa-trash me-2"></i>
                            Delete Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="settingsToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true" style="background-color: #1e293b; color: white; border: 1px solid rgba(255, 255, 255, 0.1);">
            <div class="d-flex">
                <div class="toast-body" id="toastMessage"></div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Add debug script to check if scripts are loading -->
    <script>
        console.log('Main script block loaded');
        
        // Currency symbol helper function
        function getCurrencySymbol(currency) {
            const symbols = {
                'USD': '$',
                'EUR': '€',
                'GBP': '£',
                // Add more currency symbols as needed
            };
            return symbols[currency] || currency;
        }

        // Initialize user currency
        const userCurrency = '{{ user_currency }}';
        console.log('User currency:', userCurrency);
    </script>

    <!-- Load the autopricing scripts directly -->
    <script src="{{ url_for('static', filename='js/shopify_autopricing.js') }}"></script>
    <script src="{{ url_for('static', filename='js/game_minimum_prices.js') }}"></script>

    <!-- Initialize tooltips after DOM is loaded -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded event fired in main template');
            
            // Initialize tooltips
            try {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
                console.log('Tooltips initialized in main template');
            } catch (error) {
                console.error('Error initializing tooltips in main template:', error);
            }
        });
        
        // Also add window.onload event
        window.onload = function() {
            console.log('Window onload event fired in main template');
            console.log('All scripts loaded:', document.querySelectorAll('script').length);
        };
    </script>
</body>
</html>
