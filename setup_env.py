from config.config import Config
import paramiko

def setup_environment():
    # Server credentials
    hostname = "*************"
    username = "ubuntu"
    password = "Reggie2805!"

    try:
        # Initialize SSH client
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        print("Connecting to server...")
        ssh.connect(hostname=hostname, username=username, password=password)
        
        print("Setting up environment and installing requirements...")
        
        # Create a single command that chains all operations
        setup_command = """
        cd /home/<USER>/app && \
        sudo apt-get update && \
        sudo DEBIAN_FRONTEND=noninteractive apt-get install -y python3.10-venv python3-tk && \
        python3 -m venv venv && \
        . venv/bin/activate && \
        pip install --no-cache-dir -r requirements.txt && \
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && \
        sudo DEBIAN_FRONTEND=noninteractive apt-get install -y nodejs && \
        npm install
        """
        
        # Execute the chained commands
        print("Executing setup commands...")
        stdin, stdout, stderr = ssh.exec_command(setup_command)
        
        # Print output in real-time
        while True:
            line = stdout.readline()
            if not line:
                break
            print(line.strip())
            
        # Print any errors
        for line in stderr.readlines():
            print(f"Error: {line.strip()}")
        
        print("Environment setup completed!")
        
    except Exception as e:
        print(f"Setup failed: {str(e)}")
        
    finally:
        try:
            ssh.close()
        except:
            pass

if __name__ == '__main__':
    setup_environment()

