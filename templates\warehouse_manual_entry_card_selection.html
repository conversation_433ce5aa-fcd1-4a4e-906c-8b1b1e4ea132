{% extends "base.html" %}

{% block title %}Manual Entry - Select Cards{% endblock %}

{% block content %}
<div class="container mt-5">
    <h2>Manual Entry - {{ game }}</h2>
    <h3>{{ expansion }}</h3>
    <form method="POST">
        <table class="table text-white">
            <thead>
                <tr>
                    <th>Card Name</th>
                    <th>Number</th>
                    <th>SKU ID</th>
                    <th>Quantity</th>
                </tr>
            </thead>
            <tbody>
                {% for card in cards %}
                <tr>
                    <td>{{ card.name }}</td>
                    <td>{{ card.number }}</td>
                    <td>{{ card.skus[0].skuId }}</td>
                    <td>
                        <input type="number" name="quantity_{{ card.skus[0].skuId }}" min="0" value="0" class="form-control">
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        <button type="submit" class="btn btn-primary">Add to Staged Inventory</button>
    </form>
</div>
{% endblock %}
