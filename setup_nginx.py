from config.config import Config
import paramiko
import os

# Connection details
host = '**************'
username = 'ubuntu'
password = 'zI5zkxmubreicic'
port = 22

# Create SSH client
ssh = paramiko.SSHClient()
ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

try:
    # Connect to the server
    print('Connecting to the server...')
    ssh.connect(host, port=port, username=username, password=password)
    print('Connected successfully')
    
    # Check NGINX configuration to see if there's a reverse proxy for the history service
    print('Checking NGINX configuration...')
    stdin, stdout, stderr = ssh.exec_command('sudo cat /etc/nginx/sites-enabled/*')
    output = stdout.read().decode()
    print('NGINX configuration:')
    print(output)
    
    # Create templates directory if it doesn't exist
    print('Creating templates directory if it doesn\'t exist...')
    stdin, stdout, stderr = ssh.exec_command('mkdir -p /home/<USER>/templates')
    
    # Check if we need to create a reverse proxy for the history service
    print('Checking if we need to create a reverse proxy...')
    stdin, stdout, stderr = ssh.exec_command('sudo grep -r "product_history" /etc/nginx/sites-enabled/')
    output = stdout.read().decode()
    if not output:
        print('No reverse proxy configuration found for product_history. Creating one...')
        
        # Create a new NGINX configuration file
        nginx_config = '''
server {
    listen 80;
    server_name **************;

    location /api/history {
        proxy_pass http://127.0.0.1:5050/api/history;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
'''
        
        # Write the configuration to a temporary file locally
        with open('product_history.conf', 'w') as f:
            f.write(nginx_config)
            
        # Upload the file to the server
        sftp = ssh.open_sftp()
        sftp.put('product_history.conf', '/home/<USER>/product_history.conf')
        sftp.close()
        
        # Move the file to the NGINX sites-available directory
        stdin, stdout, stderr = ssh.exec_command('sudo mv /home/<USER>/product_history.conf /etc/nginx/sites-available/')
        exit_status = stdout.channel.recv_exit_status()
        
        # Create a symbolic link to enable the site
        stdin, stdout, stderr = ssh.exec_command('sudo ln -sf /etc/nginx/sites-available/product_history.conf /etc/nginx/sites-enabled/')
        exit_status = stdout.channel.recv_exit_status()
        
        # Reload NGINX
        stdin, stdout, stderr = ssh.exec_command('sudo nginx -t && sudo systemctl reload nginx')
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status != 0:
            error = stderr.read().decode()
            print(f'Error configuring NGINX: {error}')
        else:
            print('NGINX configured successfully with reverse proxy for product_history')
    else:
        print('Reverse proxy already configured for product_history')
    
except Exception as e:
    print(f'Error: {str(e)}')
finally:
    # Close connections
    ssh.close()
    print('Connection closed')
    
    # Clean up local file
    if os.path.exists('product_history.conf'):
        os.remove('product_history.conf')

