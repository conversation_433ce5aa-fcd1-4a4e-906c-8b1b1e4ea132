#!/usr/bin/env python3
"""
MongoDB Connection Test Script

This script tests the connection to MongoDB with different settings and provides
detailed diagnostic information. It uses the mongo_connection_manager module for
better error handling and retry logic.

Usage:
    python test_mongo_connection.py [--uri ****************************:port/] [--timeout 60000]
"""

import os
import sys
import time
import logging
import argparse
from pymongo import MongoClient, errors
from pymongo.server_api import ServerApi
from mongo_connection_manager import get_mongo_client, execute_with_retry

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mongo_connection_test")

def test_direct_connection(uri, timeout_ms=30000):
    """
    Test a direct connection to MongoDB without using the connection manager.
    
    Args:
        uri (str): MongoDB connection URI
        timeout_ms (int): Connection timeout in milliseconds
        
    Returns:
        bool: True if connection successful, False otherwise
    """
    logger.info(f"Testing direct connection with timeout {timeout_ms}ms...")
    
    try:
        # Create MongoDB client with specified timeout
        client = MongoClient(
            uri,
            serverSelectionTimeoutMS=timeout_ms,
            connectTimeoutMS=timeout_ms,
            socketTimeoutMS=timeout_ms,
            server_api=ServerApi('1')
        )
        
        # Force a connection to verify it works
        client.admin.command('ping')
        logger.info("Direct connection successful!")
        
        # Get server info
        server_info = client.server_info()
        logger.info(f"MongoDB version: {server_info.get('version', 'unknown')}")
        
        # Clean up
        client.close()
        return True
        
    except errors.ServerSelectionTimeoutError as e:
        logger.error(f"Server selection timeout: {str(e)}")
        return False
    except errors.ConnectionFailure as e:
        logger.error(f"Connection failure: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return False

def test_connection_manager(uri=None, timeout_ms=30000):
    """
    Test connection using the mongo_connection_manager module.
    
    Args:
        uri (str, optional): MongoDB connection URI
        timeout_ms (int): Connection timeout in milliseconds
        
    Returns:
        bool: True if connection successful, False otherwise
    """
    logger.info(f"Testing connection using mongo_connection_manager with timeout {timeout_ms}ms...")
    
    try:
        # Use the connection manager with custom parameters
        client = get_mongo_client(
            uri=uri,
            serverSelectionTimeoutMS=timeout_ms,
            connectTimeoutMS=timeout_ms,
            socketTimeoutMS=timeout_ms
        )
        
        # Test with retry logic
        def ping_server():
            return client.admin.command('ping')
        
        result = execute_with_retry(ping_server, max_retries=3, retry_delay=2)
        
        if result:
            logger.info("Connection using mongo_connection_manager successful!")
            
            # Get server info
            server_info = client.server_info()
            logger.info(f"MongoDB version: {server_info.get('version', 'unknown')}")
            
            return True
        else:
            logger.error("Connection using mongo_connection_manager failed")
            return False
            
    except Exception as e:
        logger.error(f"Error using mongo_connection_manager: {str(e)}")
        return False

def check_network_connectivity(host, port):
    """
    Check basic network connectivity to the MongoDB server.
    
    Args:
        host (str): MongoDB host
        port (int): MongoDB port
        
    Returns:
        bool: True if network is reachable, False otherwise
    """
    import socket
    
    logger.info(f"Checking network connectivity to {host}:{port}...")
    
    try:
        # Create a socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5 second timeout
        
        # Attempt to connect
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            logger.info(f"Network connectivity to {host}:{port} successful!")
            return True
        else:
            logger.error(f"Network connectivity to {host}:{port} failed with error code {result}")
            return False
            
    except socket.error as e:
        logger.error(f"Socket error when connecting to {host}:{port}: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error checking network connectivity: {str(e)}")
        return False

def extract_host_port(uri):
    """
    Extract host and port from MongoDB URI.
    
    Args:
        uri (str): MongoDB connection URI
        
    Returns:
        tuple: (host, port)
    """
    try:
        # Extract host and port from URI
        if '@' in uri:
            host_part = uri.split('@')[1].split('/')[0]
        else:
            host_part = uri.split('//')[1].split('/')[0]
            
        if ':' in host_part:
            host, port_str = host_part.split(':')
            if '?' in port_str:
                port_str = port_str.split('?')[0]
            port = int(port_str)
        else:
            host = host_part
            port = 27017  # Default MongoDB port
            
        return host, port
    except Exception as e:
        logger.error(f"Error extracting host and port from URI: {str(e)}")
        return None, None

def main():
    """Main function to run the tests"""
    parser = argparse.ArgumentParser(description='MongoDB Connection Test Script')
    parser.add_argument('--uri', help='MongoDB connection URI')
    parser.add_argument('--timeout', type=int, default=30000, help='Connection timeout in milliseconds')
    args = parser.parse_args()
    
# Get MongoDB URI from arguments or environment
    uri = args.uri or os.environ.get('MONGO_URI', 'mongodb+srv://admin:<EMAIL>/admin?replicaSet=replicaset&tls=true')
    timeout_ms = args.timeout
    
    print("\n" + "="*80)
    print("MONGODB CONNECTION TEST")
    print("="*80)
    print(f"URI: {uri.split('@')[0]}@{'@'.join(uri.split('@')[1:])}")  # Mask password in output
    print(f"Timeout: {timeout_ms}ms")
    print("-"*80)
    
    # Extract host and port for network connectivity test
    host, port = extract_host_port(uri)
    if host and port:
        # Check basic network connectivity
        network_ok = check_network_connectivity(host, port)
        
        if not network_ok:
            print("\nNETWORK CONNECTIVITY ISSUE DETECTED")
            print("Possible causes:")
            print("1. The MongoDB server is down")
            print("2. There is a network issue (firewall, routing, etc.)")
            print("3. The host or port is incorrect")
            print("\nRecommendations:")
            print("1. Verify the MongoDB server is running")
            print("2. Check firewall settings")
            print("3. Verify the connection string is correct")
            print("4. Try connecting from a different network")
    
    # Test direct connection with different timeouts
    timeouts = [5000, 10000, 30000, 60000]
    direct_success = False
    
    print("\nTESTING DIRECT CONNECTIONS WITH DIFFERENT TIMEOUTS")
    print("-"*80)
    
    for timeout in timeouts:
        if test_direct_connection(uri, timeout):
            direct_success = True
            print(f"Direct connection successful with {timeout}ms timeout")
            break
        else:
            print(f"Direct connection failed with {timeout}ms timeout")
    
    # Test connection using mongo_connection_manager
    print("\nTESTING CONNECTION USING MONGO_CONNECTION_MANAGER")
    print("-"*80)
    
    manager_success = test_connection_manager(uri, timeout_ms)
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Network connectivity: {'SUCCESS' if network_ok else 'FAILED'}")
    print(f"Direct connection: {'SUCCESS' if direct_success else 'FAILED'}")
    print(f"Connection manager: {'SUCCESS' if manager_success else 'FAILED'}")
    
    # Recommendations
    print("\nRECOMMENDATIONS:")
    if not network_ok:
        print("- Check network connectivity to the MongoDB server")
        print("- Verify the MongoDB server is running")
        print("- Check firewall settings")
    elif not direct_success and not manager_success:
        print("- Increase connection timeout settings")
        print("- Check MongoDB authentication settings")
        print("- Verify the MongoDB server is properly configured")
    elif not direct_success and manager_success:
        print("- Use mongo_connection_manager.py for all MongoDB connections")
        print("- Update config.py and config/config.py to use mongo_connection_manager")
    
    # Update application recommendation
    print("\nTo update your application to use mongo_connection_manager:")
    print("1. Modify config/config.py to use mongo_connection_manager.get_mongo_client()")
    print("2. Update connection timeout settings in mongo_connection_manager.py")
    print("3. Consider implementing a connection pool with proper retry logic")

if __name__ == "__main__":
    main()
