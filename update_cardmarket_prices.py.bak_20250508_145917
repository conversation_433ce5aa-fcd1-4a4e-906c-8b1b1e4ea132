from pymongo import MongoClient
import logging
from datetime import datetime
import time
from requests_oauthlib import OAuth1Session
import requests
import json

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'cardmarket_update_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_dbname = 'test'
client = MongoClient(mongo_uri)
db = client[mongo_dbname]

# API base URL
api_base_url = "https://api.cardmarket.com/ws/v2.0/stock"

# Language mapping
LANGUAGE_IDS = {
    'English': 1,
    'French': 2,
    'German': 3,
    'Spanish': 4,
    'Italian': 5,
    'S-Chinese': 6,
    'Japanese': 7,
    'Portuguese': 8,
    'Russian': 9,
    'Korean': 10,
    'T-Chinese': 11
}

def create_oauth_session(api_url, user):
    """ Function to create an OAuth session """
    logger.info(f"Creating OAuth session for URL: {api_url}")
    app_token = user['cardmarketAppToken'].strip()
    app_secret = user['cardmarketAppSecret'].strip()
    access_token = user['cardmarketAccessToken'].strip()
    access_token_secret = user['cardmarketAccessTokenSecret'].strip()

    session = OAuth1Session(
        client_key=app_token,
        client_secret=app_secret,
        resource_owner_key=access_token,
        resource_owner_secret=access_token_secret,
        realm=api_url
    )
    return session

def api_call(url, user, method='PUT', data=None, headers=None):
    """ Make an API call to Cardmarket """
    oauth = create_oauth_session(url, user)
    try:
        if method == 'PUT':
            response = oauth.put(url, data=data, headers=headers)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        response.raise_for_status()
        logger.info(f"Rate Limit Stats: {response.headers.get('X-Request-Limit-Count', 'Unknown')}/{response.headers.get('X-Request-Limit-Max', 'Unknown')}")
        
        return response.text, response.status_code
        
    except requests.exceptions.RequestException as e:
        logger.error(f"API call failed: {e}")
        if hasattr(e, 'response'):
            logger.error(f"Error response content: {e.response.text}")
        return str(e), e.response.status_code if e.response else None

def update_prices():
    try:
        logger.info("Starting price update process")
        start_time = datetime.now()

        # Get API credentials from admintcg user
        api_user = db.user.find_one({'username': 'admintcg'})
        if not api_user:
            raise Exception("API user (admintcg) not found")

        logger.info("Found API user credentials")

        # Find products that need updating
        pipeline = [
            {
                "$match": {
                    "username": "TCGCARDSUK",
                    "needRepricing": True
                }
            },
            {
                "$lookup": {
                    "from": "priceguides",
                    "localField": "idProduct",
                    "foreignField": "idProduct",
                    "as": "priceGuide"
                }
            },
            {
                "$addFields": {
                    "priceGuide": {"$arrayElemAt": ["$priceGuide", 0]},
                    "trendPrice": {
                        "$cond": [
                            {"$eq": ["$isFoil", True]},
                            {
                                "$cond": [
                                    {"$isArray": "$priceGuide.trend-foil"},
                                    {"$arrayElemAt": ["$priceGuide.trend-foil", 0]},
                                    "$priceGuide.trend-foil"
                                ]
                            },
                            {
                                "$cond": [
                                    {"$isArray": "$priceGuide.trend"},
                                    {"$arrayElemAt": ["$priceGuide.trend", 0]},
                                    "$priceGuide.trend"
                                ]
                            }
                        ]
                    }
                }
            },
            {
                "$project": {
                    "_id": 1,
                    "idArticle": 1,
                    "currentPrice": 1,
                    "trendPrice": 1,
                    "count": 1,
                    "language": 1
                }
            }
        ]

        products = list(db.exportedcm.aggregate(pipeline))
        logger.info(f"Found {len(products)} products to update")

        # Process in batches of 75
        batch_size = 75
        total_updated = 0
        total_failed = 0

        for i in range(0, len(products), batch_size):
            batch = products[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1} of {(len(products) + batch_size - 1)//batch_size}")

            # Construct XML for batch update
            xml_data = ['<?xml version="1.0" encoding="UTF-8" ?>', '<request>']
            
            for product in batch:
                xml_data.append('<article>')
                xml_data.append(f'<idArticle>{product["idArticle"]}</idArticle>')
                # Round trend price to 2 decimal places
                trend_price = round(float(product["trendPrice"]), 2)
                xml_data.append(f'<price>{trend_price}</price>')
                xml_data.append(f'<count>{product["count"]}</count>')
                xml_data.append('</article>')
            
            xml_data.append('</request>')
            xml_string = '\n'.join(xml_data)
            headers = {'Content-Type': 'application/xml'}

            # Send update to Cardmarket
            logger.info(f"Sending batch update to Cardmarket")
            response_data, status_code = api_call(api_base_url, api_user, method='PUT', data=xml_string, headers=headers)

            if status_code == 200:
                # Check if response indicates success
                if '<updatedArticles>' in response_data and not '<notUpdatedArticles>' in response_data:
                    logger.info(f"Successfully updated batch on Cardmarket")
                    
                    # Update local database for successful batch
                    for product in batch:
                        db.exportedcm.update_one(
                            {'_id': product['_id']},
                            {
                                '$set': {
                                    'price': product['trendPrice'],
                                    'needRepricing': False,
                                    'lastUpdated': datetime.now()
                                }
                            }
                        )
                    total_updated += len(batch)
                else:
                    logger.warning(f"Cardmarket reported issues with batch update: {response_data}")
                    total_failed += len(batch)
            else:
                logger.error(f"Failed to update batch. Status: {status_code}, Response: {response_data}")
                total_failed += len(batch)

            # Sleep between batches to respect rate limits
            if i + batch_size < len(products):
                logger.info("Sleeping for 2 seconds between batches")
                time.sleep(2)

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"Update completed in {duration:.2f} seconds")
        logger.info(f"Successfully updated {total_updated} products")
        logger.info(f"Failed to update {total_failed} products")
        
        return {
            'updated_count': total_updated,
            'failed_count': total_failed,
            'duration_seconds': duration
        }

    except Exception as e:
        logger.error(f"An error occurred during price update: {str(e)}")
        raise
    finally:
        client.close()

if __name__ == "__main__":
    try:
        logger.info("Starting price update script")
        result = update_prices()
        logger.info(f"Price update completed successfully:")
        logger.info(f"- Updated {result['updated_count']} products")
        logger.info(f"- Failed {result['failed_count']} products")
        logger.info(f"- Duration: {result['duration_seconds']:.2f} seconds")
    except Exception as e:
        logger.error(f"Script failed: {str(e)}")
