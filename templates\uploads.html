{% extends "base.html" %}

{% block content %}
<div class="container-fluid mt-4 px-4 w-100">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title mb-4">Uploaded Files</h4>
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Upload Files</h5>
                            <form id="uploadForm" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <div class="custom-file-upload">
                                        <input type="file" class="file-input" id="fileInput" name="files[]" accept="image/*" capture="environment" multiple>
                                        <label for="fileInput" class="file-label">
                                            <i class="fas fa-cloud-upload-alt"></i> Choose files or use camera
                                        </label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">Upload</button>
                            </form>
                        </div>
                    </div>
                    <div id="uploadProgress" class="progress mb-3" style="height: 20px; display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="mb-3">
                        <h5>Uploaded Files</h5>
                    </div>
                    {% if files or folders %}
                        <div class="mb-3">
                            <button id="selectAllBtn" class="btn btn-secondary">Select All</button>
                            <button id="deleteSelectedBtn" class="btn btn-danger" disabled>Delete Selected</button>
                        </div>
                        <div class="table-responsive">
                            <table class="table text-white">
                                <thead>
                                    <tr>
                                        <th>Select</th>
                                        <th>Type</th>
                                        <th>Name</th>
                                        <th>Size</th>
                                        <th>Upload Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for file in files %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="file-checkbox" data-filename="{{ (current_path + '/' if current_path else '') + file.name }}">
                                            </td>
                                            <td>
                                                {% if file.is_image %}
                                                    <img src="{{ url_for('uploaded_file', filename=current_path + '/' + file.name) }}" alt="Thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                                {% else %}
                                                    <i class="fas fa-file text-info"></i>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="#" onclick="viewFile('{{ current_path + '/' + file.name }}'); return false;">{{ file.name }}</a>
                                            </td>
                                            <td>{{ (file.size / 1024)|round(2) }} KB</td>
                                            <td>{{ file.upload_date.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                            <td>
                                                <button onclick="deleteFile('{{ current_path + '/' + file.name }}', false)" class="btn btn-sm btn-danger">Delete</button>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p>This folder is empty.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Move File Modal -->
<div class="modal fade" id="moveFileModal" tabindex="-1" aria-labelledby="moveFileModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="moveFileModalLabel">Move File</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="moveFileForm">
                    <div class="mb-3">
                        <label for="destinationFolder" class="form-label">Select or Create Destination Folder</label>
                        <select class="form-select bg-secondary text-white" id="destinationFolder" name="destination_folder">
                            <option value="">Select a folder</option>
                        </select>
                    </div>
                    <input type="hidden" id="sourceFile" name="source_file">
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="moveFiles()">Move Files</button>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    function showToast(message, type = 'info') {
        const background = type === 'success' ? '#28a745' : 
                           type === 'error' ? '#dc3545' : 
                           type === 'warning' ? '#ffc107' : '#17a2b8';
        Toastify({
            text: message,
            duration: 3000,
            close: true,
            gravity: "top",
            position: "right",
            backgroundColor: background,
            stopOnFocus: true
        }).showToast();
    }

    function deleteFile(filename, isFolder) {
        const itemType = isFolder ? 'folder' : 'file';
        let confirmMessage = `Are you sure you want to delete this ${itemType}?`;
        if (isFolder) {
            confirmMessage += ' All files within this folder will also be deleted.';
        }
        if (confirm(confirmMessage)) {
            fetch('/delete_file/' + encodeURIComponent(filename), {
                method: 'POST',
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`${itemType.charAt(0).toUpperCase() + itemType.slice(1)} deleted successfully`, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast(`Error deleting ${itemType}: ` + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast(`An error occurred while deleting the ${itemType}.`, 'error');
            });
        }
    }

    function deleteSelectedFiles() {
        const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked')).map(cb => cb.dataset.filename);
        
        if (selectedFiles.length === 0) {
            showToast('Please select files to delete', 'warning');
            return;
        }

        if (confirm(`Are you sure you want to delete ${selectedFiles.length} file(s)?`)) {
            const deletePromises = selectedFiles.map(filename => 
                fetch('/delete_file/' + encodeURIComponent(filename), { method: 'POST' })
                    .then(response => response.json())
            );

            Promise.all(deletePromises)
                .then(results => {
                    const successCount = results.filter(result => result.success).length;
                    showToast(`${successCount} file(s) deleted successfully`, 'success');
                    setTimeout(() => location.reload(), 1000);
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('An error occurred while deleting the files.', 'error');
                });
        }
    }

    const selectAllBtn = document.getElementById('selectAllBtn');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
    const fileCheckboxes = document.querySelectorAll('.file-checkbox');
    const uploadForm = document.getElementById('uploadForm');

    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            const isAllSelected = fileCheckboxes.length === document.querySelectorAll('.file-checkbox:checked').length;
            fileCheckboxes.forEach(cb => cb.checked = !isAllSelected);
            updateDeleteButtonState();
        });
    }

    fileCheckboxes.forEach(cb => {
        cb.addEventListener('change', updateDeleteButtonState);
    });

    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', deleteSelectedFiles);
    }

    function updateDeleteButtonState() {
        const selectedCount = document.querySelectorAll('.file-checkbox:checked').length;
        if (deleteSelectedBtn) {
            deleteSelectedBtn.disabled = selectedCount === 0;
            deleteSelectedBtn.textContent = `Delete Selected (${selectedCount})`;
        }
    }

    if (uploadForm) {
        uploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const files = document.getElementById('fileInput').files;
            
            if (files.length === 0) {
                showToast('Please select at least one file', 'warning');
                return;
            }

            const progressBar = document.querySelector('#uploadProgress .progress-bar');
            const uploadProgress = document.getElementById('uploadProgress');
            if (uploadProgress) {
                uploadProgress.style.display = 'block';
            }

            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/upload', true);

            xhr.upload.onprogress = function(e) {
                if (e.lengthComputable && progressBar) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                    progressBar.setAttribute('aria-valuenow', percentComplete);
                }
            };

            xhr.onload = function() {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        showToast(`${response.uploaded_count} file(s) uploaded successfully`, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showToast('Error uploading files: ' + response.error, 'error');
                    }
                } else {
                    showToast('An error occurred while uploading the files.', 'error');
                }
                if (uploadProgress) {
                    uploadProgress.style.display = 'none';
                }
            };

            xhr.onerror = function() {
                console.error('Error:', xhr.status);
                showToast('An error occurred while uploading the files.', 'error');
                if (uploadProgress) {
                    uploadProgress.style.display = 'none';
                }
            };

            xhr.send(formData);
        });
    }

    // Expose functions to global scope for inline event handlers
    window.deleteFile = deleteFile;
    window.viewFile = function(filename) {
        window.open('{{ url_for('uploaded_file', filename='') }}' + filename, '_blank');
    };
});
</script>
{% endblock %}
