from config.config import Config
#!/usr/bin/env python3
"""
Script to test connectivity to the repricing service.

This script tests connectivity to the repricing service using different methods.

Usage:
    python test_connectivity.py [--host HOST] [--port PORT]

Options:
    --host HOST          The hostname or IP address of the server [default: **************]
    --port PORT          The port to test [default: 5001]
"""

import argparse
import sys
import socket
import time
import requests
import subprocess

def print_step(message):
    """Print a step message."""
    print(f"\n\033[1;34m=== {message} ===\033[0m")

def print_success(message):
    """Print a success message."""
    print(f"\033[1;32m✓ {message}\033[0m")

def print_error(message):
    """Print an error message."""
    print(f"\033[1;31m✗ {message}\033[0m")

def print_info(message):
    """Print an info message."""
    print(f"\033[1;36mℹ {message}\033[0m")

def test_socket_connection(host, port, timeout=5):
    """Test a socket connection to the host and port."""
    print_step(f"Testing socket connection to {host}:{port}")
    try:
        start_time = time.time()
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        end_time = time.time()
        
        if result == 0:
            print_success(f"Socket connection successful (took {end_time - start_time:.2f} seconds)")
            return True
        else:
            print_error(f"Socket connection failed with error code {result} (took {end_time - start_time:.2f} seconds)")
            return False
    except socket.gaierror:
        print_error(f"Hostname could not be resolved: {host}")
        return False
    except socket.error as e:
        print_error(f"Socket error: {e}")
        return False
    finally:
        if 'sock' in locals():
            sock.close()

def test_http_connection(host, port, timeout=5):
    """Test an HTTP connection to the host and port."""
    url = f"http://{host}:{port}/api/health"
    print_step(f"Testing HTTP connection to {url}")
    try:
        start_time = time.time()
        response = requests.get(url, timeout=timeout)
        end_time = time.time()
        
        print_info(f"Response status code: {response.status_code}")
        print_info(f"Response time: {end_time - start_time:.2f} seconds")
        print_info(f"Response content: {response.text}")
        
        if response.status_code == 200:
            print_success("HTTP connection successful")
            return True
        else:
            print_error(f"HTTP connection failed with status code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_error(f"HTTP connection failed: {e}")
        return False

def test_ping(host, count=4):
    """Test ping to the host."""
    print_step(f"Testing ping to {host}")
    try:
        if sys.platform == "win32":
            # Windows
            ping_cmd = ["ping", "-n", str(count), host]
        else:
            # Linux/Mac
            ping_cmd = ["ping", "-c", str(count), host]
        
        process = subprocess.Popen(ping_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            print_success("Ping successful")
            print_info(stdout.decode('utf-8'))
            return True
        else:
            print_error("Ping failed")
            print_info(stderr.decode('utf-8'))
            return False
    except Exception as e:
        print_error(f"Ping failed: {e}")
        return False

def test_traceroute(host):
    """Test traceroute to the host."""
    print_step(f"Testing traceroute to {host}")
    try:
        if sys.platform == "win32":
            # Windows
            traceroute_cmd = ["tracert", host]
        else:
            # Linux/Mac
            traceroute_cmd = ["traceroute", host]
        
        process = subprocess.Popen(traceroute_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        
        print_info(stdout.decode('utf-8'))
        if stderr:
            print_info(stderr.decode('utf-8'))
        
        print_success("Traceroute completed")
        return True
    except Exception as e:
        print_error(f"Traceroute failed: {e}")
        return False

def test_curl(host, port):
    """Test curl to the host and port."""
    url = f"http://{host}:{port}/api/health"
    print_step(f"Testing curl to {url}")
    try:
        if sys.platform == "win32":
            # Windows
            curl_cmd = ["curl", "-v", url]
        else:
            # Linux/Mac
            curl_cmd = ["curl", "-v", url]
        
        process = subprocess.Popen(curl_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        
        print_info(stdout.decode('utf-8'))
        print_info(stderr.decode('utf-8'))
        
        if process.returncode == 0:
            print_success("Curl successful")
            return True
        else:
            print_error("Curl failed")
            return False
    except Exception as e:
        print_error(f"Curl failed: {e}")
        return False

def test_domain_resolution(domain):
    """Test domain resolution."""
    print_step(f"Testing domain resolution for {domain}")
    try:
        ip = socket.gethostbyname(domain)
        print_success(f"Domain resolution successful: {domain} resolves to {ip}")
        return True
    except socket.gaierror:
        print_error(f"Domain resolution failed: {domain}")
        return False

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test connectivity to the repricing service.')
    parser.add_argument('--host', default='**************',
                        help='The hostname or IP address of the server')
    parser.add_argument('--port', type=int, default=5001,
                        help='The port to test')
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    print_step("Starting connectivity tests")
    print_info(f"Host: {args.host}")
    print_info(f"Port: {args.port}")
    
    # Test ping
    test_ping(args.host)
    
    # Test traceroute
    test_traceroute(args.host)
    
    # Test socket connection
    test_socket_connection(args.host, args.port)
    
    # Test HTTP connection
    test_http_connection(args.host, args.port)
    
    # Test curl
    test_curl(args.host, args.port)
    
    # Test domain resolution
    test_domain_resolution("webhooks.tcgsync.com")
    
    print_step("Connectivity tests complete")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

