{% extends "base.html" %}

{% block title %}Manual Entry{% endblock %}

{% block content %}
<div class="container mt-5">
    <h2>Manual Entry</h2>
    <form method="POST" id="filterForm">
        <div class="row mb-3">
            <div class="col-md-10">
                <button type="button" id="pushToStaged" class="btn btn-primary">Push to Staged Inventory</button>
            </div>
        </div>
        <div class="row">
            <div class="col-md-2">
                <div class="form-group">
                    <label for="game_name">Game</label>
                    <select class="form-control" id="game_name" name="game_name" required>
                        <option value="">Select a game</option>
                        {% for game in games %}
                        <option value="{{ game }}" {% if game == selected_game %}selected{% endif %}>{{ game }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="expansion_name">Expansion</label>
                    <select class="form-control" id="expansion_name" name="expansion_name">
                        <option value="">All Expansions</option>
                        {% for expansion in expansions %}
                        <option value="{{ expansion }}" {% if expansion == selected_expansion %}selected{% endif %}>{{ expansion }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="rarity">Rarity</label>
                    <select class="form-control" id="rarity" name="rarity">
                        <option value="">All Rarities</option>
                        {% for rarity in rarities %}
                        <option value="{{ rarity }}" {% if rarity == selected_rarity %}selected{% endif %}>{{ rarity }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="type">Type</label>
                    <select class="form-control" id="type" name="type">
                        <option value="">All Types</option>
                        <option value="single" {% if selected_type == 'single' %}selected{% endif %}>Single</option>
                        <option value="sealed" {% if selected_type == 'sealed' %}selected{% endif %}>Sealed</option>
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="language">Language</label>
                    <select class="form-control" id="language" name="language">
                        {% for language in languages %}
                        <option value="{{ language }}" {% if language == 'EN' %}selected{% endif %}>{{ language }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="condition">Condition</label>
                    <select class="form-control" id="condition" name="condition">
                        {% for condition in conditions %}
                        <option value="{{ condition }}" {% if condition == 'NM' %}selected{% endif %}>{{ condition }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
    </form>
        
        <div id="cardTable"></div>
        <div id="pagination"></div>
        <button type="submit" name="submit" value="1" class="btn btn-primary">Add to Staged Inventory</button>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.21.1/axios.min.js"></script>
<script>
    let currentPage = 1;
    const itemsPerPage = 50;

    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('pushToStaged').addEventListener('click', pushToStagedInventory);
        const form = document.getElementById('filterForm');
        const gameSelect = document.getElementById('game_name');
        const expansionSelect = document.getElementById('expansion_name');
        const raritySelect = document.getElementById('rarity');
        const typeSelect = document.getElementById('type');
        const languageSelect = document.getElementById('language');
        const conditionSelect = document.getElementById('condition');

        gameSelect.addEventListener('change', function() {
            fetchOptions('expansion_name', { game_name: this.value });
            fetchOptions('rarity', { game_name: this.value });
            fetchOptions('language', { game_name: this.value });
            fetchOptions('condition', { game_name: this.value });
            currentPage = 1;
            loadCards();
        });

        expansionSelect.addEventListener('change', loadCards);
        raritySelect.addEventListener('change', loadCards);
        typeSelect.addEventListener('change', loadCards);
        languageSelect.addEventListener('change', loadCards);
        conditionSelect.addEventListener('change', loadCards);

        // Initial load
        if (gameSelect.value) {
            fetchOptions('expansion_name', { game_name: gameSelect.value });
            fetchOptions('rarity', { game_name: gameSelect.value });
            fetchOptions('language', { game_name: gameSelect.value });
            fetchOptions('condition', { game_name: gameSelect.value });
        }
        loadCards();
    });

    function fetchOptions(selectId, params) {
        axios.get(`/warehouse/manual_entry/options/${selectId}`, { params: params })
            .then(response => {
                const select = document.getElementById(selectId);
                select.innerHTML = '<option value="">All</option>';
                response.data.options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = option;
                    select.appendChild(optionElement);
                });
            })
            .catch(error => console.error('Error fetching options:', error));
    }

    function loadCards() {
        const formData = new FormData(document.getElementById('filterForm'));
        formData.append('page', currentPage);
        formData.append('per_page', itemsPerPage);

        axios.post('/warehouse/manual_entry/cards', formData)
            .then(response => {
                const { cards, total_pages } = response.data;
                renderTable(cards);
                renderPagination(total_pages);
            })
            .catch(error => console.error('Error:', error));
    }

    function renderTable(cards) {
        let tableHtml = `
            <table class="table">
                <thead>
                    <tr>
                        <th>Card Name</th>
                        <th>Number</th>
                        <th>SKU ID</th>
                        <th>Rarity</th>
                        <th>Type</th>
                        <th>Printing</th>
                        <th>Language</th>
                        <th>Condition</th>
                        <th>Price</th>
                        <th>Fetch Price</th>
                        <th>Quantity</th>
                    </tr>
                </thead>
                <tbody>
        `;

        cards.forEach(card => {
            card.skus.forEach(sku => {
                tableHtml += `
                    <tr>
                        <td>${card.name}</td>
                        <td>${card.number}</td>
                        <td>${sku.skuId}</td>
                        <td>${card.rarity}</td>
                        <td>${card.isSingle ? 'Single' : (card.isSealed ? 'Sealed' : 'Unknown')}</td>
                        <td>${sku.printingName}</td>
                        <td>${sku.langAbbr}</td>
                        <td>${sku.condAbbr}</td>
                        <td>${sku.lowPrice ? parseFloat(sku.lowPrice).toFixed(2) : 'N/A'}</td>
                        <td>
                            ${!sku.lowPrice ? `<button onclick="fetchLivePrice('${sku.skuId}')" class="btn btn-sm btn-secondary">Fetch Price</button>` : ''}
                        </td>
                        <td>
                            <input type="number" name="quantity_${sku.skuId}" min="0" value="0" class="form-control">
                        </td>
                    </tr>
                `;
            });
        });

        tableHtml += `
                </tbody>
            </table>
        `;

        document.getElementById('cardTable').innerHTML = tableHtml;
    }

    function renderPagination(totalPages) {
        let paginationHtml = '';
        for (let i = 1; i <= totalPages; i++) {
            paginationHtml += `<button onclick="changePage(${i})" ${i === currentPage ? 'disabled' : ''}>${i}</button>`;
        }
        document.getElementById('pagination').innerHTML = paginationHtml;
    }

    function changePage(page) {
        currentPage = page;
        loadCards();
    }

    function fetchLivePrice(skuId) {
        axios.get(`/warehouse/fetch_live_price/${skuId}`)
            .then(response => {
                const price = parseFloat(response.data.price).toFixed(2);
                const priceCell = document.querySelector(`tr:has(button[onclick="fetchLivePrice('${skuId}')"]) td:nth-child(9)`);
                const fetchCell = document.querySelector(`tr:has(button[onclick="fetchLivePrice('${skuId}')"]) td:nth-child(10)`);
                priceCell.innerHTML = `${price} <span class="text-success">(Updated)</span>`;
                fetchCell.innerHTML = '';
            })
            .catch(error => {
                console.error('Error fetching live price:', error);
                alert('Failed to fetch live price. Please try again.');
            });
    }

    function pushToStagedInventory() {
        const formData = new FormData();
        let itemsAdded = false;

        document.querySelectorAll('input[name^="quantity_"]').forEach(input => {
            const quantity = parseInt(input.value);
            if (quantity > 0) {
                formData.append(input.name, quantity);
                itemsAdded = true;
            }
        });

        if (!itemsAdded) {
            alert('No items selected. Please add quantities to at least one item.');
            return;
        }

        axios.post('/warehouse/manual_entry', formData)
            .then(response => {
                if (response.data.status === 'success') {
                    alert(response.data.message);
                    // Reset all quantity inputs to 0
                    document.querySelectorAll('input[name^="quantity_"]').forEach(input => {
                        input.value = '0';
                    });
                } else {
                    alert('Error: ' + response.data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while pushing to staged inventory');
            });
    }
</script>
{% endblock %}
