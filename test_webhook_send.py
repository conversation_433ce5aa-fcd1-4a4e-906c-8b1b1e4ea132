from config.config import Config
#!/usr/bin/env python3
"""
Test Webhook Sender

This script sends a test webhook to the Shopify webhook endpoint to verify
that webhooks are being properly received and processed without HMAC validation.
"""

import requests
import json
import sys
import uuid
from datetime import datetime

def send_test_webhook(username, webhook_url=None, topic="products/update"):
    """Send a test webhook to the specified URL."""
    if not webhook_url:
        # Default to local development server
        webhook_url = f"http://localhost:5000/webhook/{username}"
    
    # Create a unique product ID for testing
    product_id = str(uuid.uuid4())
    
    # Create a test product payload
    payload = {
        "id": product_id,
        "title": f"Test Product {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "body_html": f"<div class='catalogMetaData' data-tcgid='123456'>Test product created for webhook testing</div>",
        "vendor": "Test Vendor",
        "product_type": "Test",
        "created_at": datetime.now().isoformat(),
        "handle": f"test-product-{product_id}",
        "updated_at": datetime.now().isoformat(),
        "published_at": datetime.now().isoformat(),
        "template_suffix": None,
        "status": "active",
        "published_scope": "web",
        "tags": "test, webhook",
        "admin_graphql_api_id": f"gid://shopify/Product/{product_id}",
        "variants": [
            {
                "id": str(uuid.uuid4()),
                "product_id": product_id,
                "title": "Default Title",
                "price": "19.99",
                "sku": f"TEST-SKU-{product_id[:8]}",
                "position": 1,
                "inventory_policy": "deny",
                "compare_at_price": "24.99",
                "fulfillment_service": "manual",
                "inventory_management": "shopify",
                "option1": "Default Title",
                "option2": None,
                "option3": None,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "taxable": True,
                "barcode": "",
                "grams": 0,
                "image_id": None,
                "weight": 0.0,
                "weight_unit": "kg",
                "inventory_item_id": str(uuid.uuid4()),
                "inventory_quantity": 10,
                "old_inventory_quantity": 10,
                "requires_shipping": True,
                "admin_graphql_api_id": f"gid://shopify/ProductVariant/{str(uuid.uuid4())}"
            }
        ],
        "options": [
            {
                "id": str(uuid.uuid4()),
                "product_id": product_id,
                "name": "Title",
                "position": 1,
                "values": ["Default Title"]
            }
        ],
        "images": [],
        "image": None
    }
    
    # Set headers to mimic Shopify webhook
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Topic": topic,
        "X-Shopify-Hmac-Sha256": "dummy-hmac-signature",  # This will be ignored now
        "X-Shopify-Shop-Domain": "test-store.myshopify.com",
        "X-Shopify-API-Version": "2023-01"
    }
    
    print(f"Sending test webhook to {webhook_url}")
    print(f"Topic: {topic}")
    print(f"Product ID: {product_id}")
    
    try:
        response = requests.post(webhook_url, json=payload, headers=headers)
        print(f"Response status code: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("Webhook sent successfully!")
            return True
        else:
            print(f"Failed to send webhook. Status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error sending webhook: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_webhook_send.py <username> [webhook_url] [topic]")
        sys.exit(1)
    
    username = sys.argv[1]
    webhook_url = sys.argv[2] if len(sys.argv) > 2 else None
    topic = sys.argv[3] if len(sys.argv) > 3 else "products/update"
    
    send_test_webhook(username, webhook_url, topic)

