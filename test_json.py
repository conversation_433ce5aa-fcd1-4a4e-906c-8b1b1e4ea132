from config.config import Config
import json
import requests

data = {
    "id": 224517,
    "name": "Brass: Birmingham",
    "yearpublished": 2018,
    "rank": 1,
    "bayesaverage": 8.40836,
    "average": 8.58515,
    "usersrated": 49940,
    "is_expansion": 0,
    "strategygames_rank": 1,
    "artists": [
        "<PERSON><PERSON> Brown",
        "<PERSON><PERSON>ssette",
        "David Forest",
        "Gui Landgraf",
        "<PERSON> Mammoliti",
        "<PERSON>"
    ],
    "categories": [
        "Age of Reason",
        "Economic",
        "Industry / Manufacturing",
        "Post-Napoleonic",
        "Trains",
        "Transportation"
    ],
    "description": "Brass: Birmingham is an economic strategy game sequel to <PERSON> 2007 masterpiece, Brass. Birmingham tells the story of competing entrepreneurs in Birmingham during the industrial revolution between the years of 1770 and 1870.",
    "designers": [
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>"
    ],
    "image_url": "https://cf.geekdo-images.com/x3zxjr-Vw5iU4yDPg70Jgw__original/img/FpyxH41Y6_ROoePAilPNEhXnzO8=/0x0/filters:format(jpeg)/pic3490053.jpg",
    "max_players": "4",
    "mechanics": [
        "Hand Management",
        "Income",
        "Loans",
        "Market",
        "Multi-Use Cards",
        "Network and Route Building",
        "Tags",
        "Tech Trees / Tech Tracks",
        "Turn Order: Stat-Based",
        "Variable Set-up"
    ],
    "min_age": "14",
    "min_players": "2",
    "playing_time": "120",
    "publishers": [
        "Roxley",
        "Arclight Games",
        "Board Game Rookie"
    ],
    "weight": 3.8706,
    "age": "14",
    "year_published": 2018
}

response = requests.post(
    "http://localhost:5011/api/board-games/preview",
    headers={"Content-Type": "application/json"},
    json=data
)

print(f"Status Code: {response.status_code}")
print("Response:")
print(json.dumps(response.json(), indent=2))

