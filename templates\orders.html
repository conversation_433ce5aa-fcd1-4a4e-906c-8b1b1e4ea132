{% extends "base.html" %}

{% block content %}
<!-- Loading Screen -->
<div id="loadingScreen" class="loading-screen" style="display: none;">
    <div class="loading-content">
        <h3 class="text-white mb-4">Refreshing Orders</h3>
        <div class="progress mb-3" style="height: 25px; width: 300px;">
            <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                 role="progressbar" style="width: 0%;">0%</div>
        </div>
        <div class="text-white">
            <span id="ordersProcessed">0</span> / <span id="totalOrders">0</span> orders processed
        </div>
    </div>
</div>

<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="text-white">Orders</h1>
            {% if total_fees %}
            <div class="text-white mt-2">
                <strong>Total Fees Due: <span class="text-warning">{{ total_fees }}</span></strong>
            </div>
            {% endif %}
        </div>
        <div>
            <button class="btn btn-success me-2" onclick="pullNewOrders()">
                <i class="fas fa-sync me-1"></i> Pull New Orders
            </button>
            <button class="btn btn-primary me-2" onclick="refreshSelectedOrders()">Refresh Selected Orders</button>
            <div class="btn-group me-2">
                <button class="btn btn-primary" onclick="printSelectedOrders(false)">Print Selected Orders</button>
                <button class="btn btn-primary" onclick="printSelectedOrders(true)">Print Combined Pick List</button>
            </div>
            <button class="btn btn-primary">Create order</button>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-3">
            <select class="form-select bg-dark text-white border-secondary" id="dateFilter" onchange="applyFilters()">
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="last7" selected>Last 7 Days</option>
                <option value="last30">Last 30 Days</option>
                <option value="ytd">Year to Date</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select bg-dark text-white border-secondary" id="paymentFilter" onchange="applyFilters()">
                <option value="all">All Payment Status</option>
                <option value="paid">Paid</option>
                <option value="pending">Pending</option>
                <option value="refunded">Refunded</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select bg-dark text-white border-secondary" id="fulfillmentFilter" onchange="applyFilters()">
                <option value="all">All Fulfillment Status</option>
                <option value="fulfilled">Fulfilled</option>
                <option value="unfulfilled">Unfulfilled</option>
                <option value="partial">Partially Fulfilled</option>
            </select>
        </div>
        <div class="col-md-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="hideInStore" onchange="applyFilters()" checked>
                <label class="form-check-label text-white" for="hideInStore">
                    Hide In-Store Orders
                </label>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="table-responsive">
        <table class="table table-dark table-hover">
            <thead>
                <tr>
                    <th><input type="checkbox" class="form-check-input" onclick="toggleAllOrders(this)"></th>
                    <th>Order</th>
                    <th>Date</th>
                    <th>Customer</th>
                    <th>Total</th>
                    <th>Fees</th>
                    <th>Payment status</th>
                    <th>Fulfillment status</th>
                    <th>Items</th>
                    <th>Delivery status</th>
                </tr>
            </thead>
            <tbody>
                {% for order in orders %}
                <tr class="order-row" style="cursor: pointer;">
                    <td onclick="event.stopPropagation()">
                        <input type="checkbox" class="form-check-input order-checkbox" 
                               value="{{ order.id }}"
                               data-shopify-id="{{ order.shopify_id }}"
                               data-order-number="{{ order.order_number }}">
                    </td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">{{ order.order_number }}</td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">{{ order.date }}</td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">{{ order.customer }}</td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">{{ order.total }}</td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">
                        <span class="text-warning">{{ order.fees }}</span>
                    </td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">
                        <span class="badge bg-{{ 'success' if order.payment_status == 'paid' else 'warning' }}">
                            {{ order.payment_status }}
                        </span>
                    </td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">
                        <span class="badge bg-{{ 'success' if order.fulfillment_status == 'fulfilled' else 'warning' }}">
                            {{ order.fulfillment_status or 'unfulfilled' }}
                        </span>
                    </td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">{{ order.items_count }} items</td>
                    <td onclick="window.location.href='/shopify/orders/{{ order.id }}/details'">
                        <span class="badge bg-{{ 'success' if order.delivery_status == 'delivered' else 'info' }}">
                            {{ order.delivery_status }}
                        </span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<style>
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
.loading-content {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    background-color: #1a2634;
}
.form-select {
    background-color: #1a2634;
    color: #ffffff;
    border-color: #2c3e50;
}
.form-select:focus {
    background-color: #1a2634;
    color: #ffffff;
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}
.table-dark {
    background-color: #1a2634;
    color: #ffffff;
}
.table-dark thead th {
    border-bottom: 2px solid #2c3e50;
}
.table-dark td, .table-dark th {
    border-top: 1px solid #2c3e50;
}
.form-check-input {
    background-color: #2c3e50;
    border-color: #4a5568;
}
.form-check-input:checked {
    background-color: #3498db;
    border-color: #3498db;
}
</style>

<script>
function pullNewOrders() {
    // Show loading screen with custom message
    const loadingScreen = document.getElementById('loadingScreen');
    const progressBar = document.getElementById('progressBar');
    const ordersProcessed = document.getElementById('ordersProcessed');
    const totalOrders = document.getElementById('totalOrders');
    
    // Update loading screen content
    document.querySelector('.loading-content h3').textContent = 'Pulling New Orders';
    loadingScreen.style.display = 'flex';
    progressBar.style.width = '0%';
    progressBar.textContent = 'Processing...';
    ordersProcessed.textContent = '0';
    totalOrders.textContent = '?';
    
    console.log('Pulling last 250 orders...');
    
    // Call the API to pull new orders
    $.ajax({
        url: '/shopify/orders/pull-new',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({}),  // No parameters needed, just pull last 250 orders
        success: function(data) {
            console.log('Pull new orders response:', data);
            if (data.success) {
                // Update progress to 100%
                progressBar.style.width = '100%';
                progressBar.textContent = 'Complete!';
                
                // Show success message
                if (data.ordersProcessed !== undefined) {
                    ordersProcessed.textContent = data.ordersProcessed;
                    totalOrders.textContent = data.ordersProcessed;
                }
                
                // Reload page after a short delay
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            } else {
                // Hide loading screen and show error
                loadingScreen.style.display = 'none';
                alert('Error pulling orders: ' + (data.error || 'Unknown error'));
            }
        },
        error: function(xhr, status, error) {
            // Hide loading screen and show error
            loadingScreen.style.display = 'none';
            console.error('Error pulling orders:', error);
            alert('Error pulling orders: ' + error);
        }
    });
}

function toggleAllOrders(checkbox) {
    const orderCheckboxes = document.querySelectorAll('.order-checkbox');
    orderCheckboxes.forEach(cb => cb.checked = checkbox.checked);
}

// Helper function to process a batch of orders
async function processBatch(orders, startIndex, batchSize) {
    const batch = orders.slice(startIndex, startIndex + batchSize);
    if (batch.length === 0) return;

    try {
        const response = await fetch('/shopify/orders/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': window.csrf_token || ''
            },
            body: JSON.stringify({ order_numbers: batch })
        });
        
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.error);
        }
    } catch (error) {
        console.error('Error refreshing orders:', error);
        alert(`Error refreshing orders: ${error.message}`);
    }
}

// Helper function to delay execution
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function refreshSelectedOrders() {
    const selectedOrders = Array.from(document.querySelectorAll('.order-checkbox:checked'))
        .map(cb => cb.dataset.orderNumber.replace('#', ''));
    
    if (selectedOrders.length === 0) {
        alert('Please select at least one order to refresh');
        return;
    }

    // Show loading screen
    const loadingScreen = document.getElementById('loadingScreen');
    const progressBar = document.getElementById('progressBar');
    const ordersProcessed = document.getElementById('ordersProcessed');
    const totalOrders = document.getElementById('totalOrders');
    
    loadingScreen.style.display = 'flex';
    totalOrders.textContent = selectedOrders.length;
    ordersProcessed.textContent = '0';
    progressBar.style.width = '0%';
    progressBar.textContent = '0%';

    // Process orders in batches of 2 with 500ms delay between batches
    const batchSize = 2;
    let processed = 0;

    for (let i = 0; i < selectedOrders.length; i += batchSize) {
        await processBatch(selectedOrders, i, batchSize);
        
        // Update progress
        processed += Math.min(batchSize, selectedOrders.length - i);
        const progress = Math.round((processed / selectedOrders.length) * 100);
        progressBar.style.width = progress + '%';
        progressBar.textContent = progress + '%';
        ordersProcessed.textContent = processed;

        // Add delay between batches (500ms = 2 orders per second)
        if (i + batchSize < selectedOrders.length) {
            await delay(500);
        }
    }

    // Hide loading screen and reload page
    loadingScreen.style.display = 'none';
    window.location.reload();
}

function applyFilters() {
    const dateFilter = document.getElementById('dateFilter').value;
    const paymentFilter = document.getElementById('paymentFilter').value;
    const fulfillmentFilter = document.getElementById('fulfillmentFilter').value;
    const hideInStore = document.getElementById('hideInStore').checked;

    // Build query string
    const params = new URLSearchParams();
    params.set('date_range', dateFilter);
    params.set('payment_status', paymentFilter);
    params.set('fulfillment_status', fulfillmentFilter);
    params.set('hide_in_store', hideInStore);

    // Reload page with filters
    window.location.href = `${window.location.pathname}?${params.toString()}`;
}

function printSelectedOrders(combined = false) {
    const selectedOrders = Array.from(document.querySelectorAll('.order-checkbox:checked'))
        .map(cb => cb.dataset.shopifyId);
    
    if (selectedOrders.length === 0) {
        alert('Please select at least one order to print');
        return;
    }

    // Send POST request to get print content
    fetch('/shopify/orders/print', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': window.csrf_token || ''
        },
        body: JSON.stringify({ 
            order_ids: selectedOrders,
            combined: combined
        })
    })
    .then(response => response.text())
    .then(html => {
        // Create a new window for printing
        const printWindow = window.open('', '_blank');
        printWindow.document.write(html);
        printWindow.document.close();

        // Wait for images to load then print
        printWindow.onload = function() {
            printWindow.print();
            // Close window after printing (optional)
            printWindow.onafterprint = function() {
                printWindow.close();
            };
        };
    })
    .catch(error => {
        console.error('Error printing orders:', error);
        alert('Error printing orders. Please try again.');
    });
}

// Set initial filter values from URL params and apply filters if needed
document.addEventListener('DOMContentLoaded', function() {
    const params = new URLSearchParams(window.location.search);
    let filtersChanged = false;
    
    const dateFilter = params.get('date_range');
    if (dateFilter) {
        document.getElementById('dateFilter').value = dateFilter;
    }
    
    const paymentFilter = params.get('payment_status');
    if (paymentFilter) {
        document.getElementById('paymentFilter').value = paymentFilter;
    }
    
    const fulfillmentFilter = params.get('fulfillment_status');
    if (fulfillmentFilter) {
        document.getElementById('fulfillmentFilter').value = fulfillmentFilter;
    }

    // Only update checkbox if hide_in_store is explicitly set to false
    const hideInStore = params.get('hide_in_store');
    if (hideInStore === 'false') {
        document.getElementById('hideInStore').checked = false;
        filtersChanged = true;
    }

    // If no filters in URL and checkbox is checked, apply filters
    if (!params.toString() && document.getElementById('hideInStore').checked) {
        applyFilters();
    }
});
</script>
{% endblock %}
